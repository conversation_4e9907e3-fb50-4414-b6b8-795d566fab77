/**
 * Multilingual Hub Types
 * 
 * These types match the backend models for seamless integration
 * between the media service API and the frontend application.
 */

import { Language, TranslationStatus } from './common'

// Re-export for convenience
export { Language, TranslationStatus }

export enum HubStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DRAFT = "draft",
  ARCHIVED = "archived"
}

export enum HubVisibility {
  PUBLIC = "public",
  PRIVATE = "private",
  RESTRICTED = "restricted"
}

export interface HubContent {
  title: string;
  description?: string;
  translation_status: TranslationStatus;
  is_original: boolean;
  created_at: string;
  updated_at: string;
}

export interface MultilingualHub {
  hub_id: string;
  content: Record<Language, HubContent>;
  default_language: Language;
  image_url?: string;
  tags: string[];
  category?: string;
  visibility: HubVisibility;
  status: HubStatus;
  created_by?: string;
  created_at: string;
  updated_at: string;
  content_count: number;
  view_count: number;
}

export interface HubListItem {
  hub_id: string;
  content: Record<Language, HubContent>;
  image_url?: string;
  category?: string;
  status: HubStatus;
  content_count: number;
  created_at: string;
}

export interface HubListResponse {
  data: HubListItem[];
  total: number;
  limit: number;
  has_more: boolean;
  next_key?: string;
}

export interface HubCreateRequest {
  title: string;
  description?: string;
  tags: string[];
  category?: string;
  visibility: HubVisibility;
  created_by?: string;
  image?: File;
}

// Hub update request
export interface HubUpdateRequest {
  language: Language;
  title?: string;
  description?: string;
  tags?: string[];
  category?: string;
  visibility?: HubVisibility;
  status?: HubStatus;
  image?: File;
}

// Hub response from API
export interface HubResponse {
  hub_id: string;
  content: Record<Language, HubContent>;
  default_language: Language;
  image_url?: string;
  tags: string[];
  category?: string;
  visibility: HubVisibility;
  status: HubStatus;
  created_by?: string;
  created_at: string;
  updated_at: string;
  content_count: number;
  view_count: number;
}

// Hub list query parameters
export interface HubListParams {
  limit?: number;
  last_key?: string;
  category?: string;
  status?: HubStatus;
  created_by?: string;
} 