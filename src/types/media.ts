import { Language } from './common'
import type { MultilingualText, MultilingualTags } from './common'

// Enums matching the working implementation
export enum MediaType {
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  DOCUMENT = 'document'
}

export enum MediaStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  FAILED = 'failed'
}

export enum ContentStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROCESSING = 'processing',
  FAILED = 'failed',
  ARCHIVED = 'archived'
}

export enum ContentType {
  VIDEO = 'video',
  IMAGE = 'image',
  DOCUMENT = 'document',
  MIXED = 'mixed'
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  ALL = 'all'
}

// Constants
export const STATUS_LABELS = {
  [MediaStatus.DRAFT]: 'Draft',
  [MediaStatus.PROCESSING]: 'Processing',
  [MediaStatus.PUBLISHED]: 'Published',
  [MediaStatus.ARCHIVED]: 'Archived',
  [MediaStatus.FAILED]: 'Failed'
} as const

export const CONTENT_STATUS_LABELS = {
  [ContentStatus.DRAFT]: 'Draft',
  [ContentStatus.ACTIVE]: 'Active',
  [ContentStatus.INACTIVE]: 'Inactive',
  [ContentStatus.PROCESSING]: 'Processing',
  [ContentStatus.FAILED]: 'Failed',
  [ContentStatus.ARCHIVED]: 'Archived'
} as const

export const GENDER_LABELS = {
  [Gender.MALE]: 'Male',
  [Gender.FEMALE]: 'Female',
  [Gender.ALL]: 'All'
} as const

export const CONTENT_TYPE_LABELS = {
  [ContentType.VIDEO]: 'Video',
  [ContentType.IMAGE]: 'Image',
  [ContentType.DOCUMENT]: 'Document',
  [ContentType.MIXED]: 'Mixed'
} as const

// Core API Types (matching working implementation)
export interface MediaFile {
  file_id: string
  file_name: string
  file_type: string
  file_size: number
  s3_key: string
  s3_url: string
  width?: number
  height?: number
  duration?: number
  uploaded_at: string
}

export interface ContentResponse {
  media_id: string
  hub_id: string
  language: Language
  title: string
  description: string
  tags: string[]
  video_url?: string
  thumbnail_url?: string
  gallery_urls: string[]
  captions_available: Language[]
  caption_urls: Record<Language, string>
  content_type: ContentType
  gender: Gender
  status: ContentStatus
  created_at: string
  updated_at: string
  file_count: number
  total_size: number
  available_languages: Language[]
  translation_status: Record<Language, string>
}

export interface MediaResponse {
  content_id: string
  hub_id: string
  title: MultilingualText
  description: MultilingualText
  tags: MultilingualTags
  video_id: string
  thumbnail_id: string
  video_url: string
  thumbnail_url: string
  subtitle_urls: Record<string, string>
  subtitles_available: string[]
  available_languages: string[]
  gender: string
  status: string
  created_at: string
  updated_at: string
}

export interface ContentListResponse {
  data: ContentResponse[]
  total: number
  page: number
  limit: number
  has_more: boolean
  last_key?: string
}

export interface MediaListResponse {
  data: MediaResponse[]
  total: number
  limit: number
  last_key?: string | null
  has_more: boolean
}

// Media filtering interface for content queries
export interface MediaFilters {
  hub_id?: string
  status?: MediaStatus | ContentStatus
  media_type?: MediaType | ContentType
  language?: Language
  search?: string
  limit?: number
  last_key?: string
}

export interface GetContentParams {
  hub_id?: string
  language?: Language
  status?: ContentStatus
  limit?: number
  last_key?: string
  search?: string
}

export interface CreateContentRequest {
  hub_id: string
  title: string
  description: string
  default_language?: Language
  tags?: string[]
  gender?: Gender
  auto_translate?: boolean
  generate_captions?: boolean
  video_file_id?: string
  thumbnail_file_id?: string
  gallery_file_ids?: string[]
  video?: File
  thumbnail?: File
  gallery?: File[]
}

export interface MediaCreateRequest {
  hub_id: string
  title: string
  description: string
  default_language?: Language
  tags?: string[]
  gender?: Gender
  auto_translate?: boolean
  generate_captions?: boolean
  video_file_id?: string
  thumbnail_file_id?: string
  gallery_file_ids?: string[]
  video?: File
  thumbnail?: File
  gallery?: File[]
}

export interface UpdateContentRequest {
  title?: string
  description?: string
  tags?: string[]
  gender?: Gender
  status?: ContentStatus
  thumbnail?: File
  gallery?: File[]
}

export interface MediaUpdateRequest {
  title?: string
  description?: string
  tags?: string[]
  gender?: Gender
  status?: MediaStatus
  thumbnail?: File
  gallery?: File[]
}

export interface FileUploadResponse {
  file_id: string
  file_name: string
  s3_url: string
  file_type: string
  file_size: number
  message: string
}

export interface TranslationRequest {
  target_languages: string[]
  include_captions?: boolean
}

export interface CaptionGenerationRequest {
  languages?: string[]
  format?: string
}

// Modern Types (for new components)
export interface MediaFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  duration?: number
  dimensions?: {
    width: number
    height: number
  }
  createdAt: string
  updatedAt: string
}

export interface MediaContent {
  id: string
  title: MultilingualText
  description: MultilingualText
  tags: MultilingualTags
  type: MediaType
  status: MediaStatus
  file: MediaFile
  hubId?: string
  metadata: MediaMetadata
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface MediaMetadata {
  resolution?: string
  bitrate?: number
  codec?: string
  frameRate?: number
  aspectRatio?: string
  colorSpace?: string
  audioChannels?: number
  audioSampleRate?: number
  subtitles?: SubtitleTrack[]
}

export interface SubtitleTrack {
  language: Language
  url: string
  format: 'srt' | 'vtt' | 'ass'
  isDefault?: boolean
}

export interface CreateMediaRequest {
  title: MultilingualText
  description: MultilingualText
  tags: MultilingualTags
  type: MediaType
  hubId?: string
  file: File
}

export interface UpdateMediaRequest {
  title?: MultilingualText
  description?: MultilingualText
  tags?: MultilingualTags
  status?: MediaStatus
  hubId?: string
}

export interface MediaListParams {
  page?: number
  limit?: number
  search?: string
  type?: MediaType
  status?: MediaStatus
  hubId?: string
  language?: Language
  sortBy?: 'createdAt' | 'updatedAt' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface MediaUploadState {
  isUploading: boolean
  progress: UploadProgress
  error?: string
}

// Helper Functions
export const getContentInLanguage = (
  content: MultilingualText | MultilingualTags, 
  language: Language = Language.ENGLISH
): string | string[] => {
  if (!content) return ''
  return content[language] || content[Language.ENGLISH] || ''
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

export const getLanguageFlag = (language: Language): string => {
  switch (language) {
    case Language.ENGLISH: return '🇺🇸'
    case Language.ARABIC: return '🇸🇦'
    case Language.HINDI: return '🇮🇳'
    default: return '🏳️'
  }
}

export const getStatusColor = (status: ContentStatus): string => {
  switch (status) {
    case ContentStatus.ACTIVE: return 'green'
    case ContentStatus.INACTIVE: return 'gray'
    case ContentStatus.DRAFT: return 'yellow'
    case ContentStatus.PROCESSING: return 'blue'
    case ContentStatus.FAILED: return 'red'
    case ContentStatus.ARCHIVED: return 'purple'
    default: return 'gray'
  }
}

// Re-exports
export { Language, LANGUAGE_LABELS, LANGUAGE_FLAGS } from './common'
export type { MultilingualText, MultilingualTags } from './common' 