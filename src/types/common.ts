/**
 * Common Types and Enums
 * 
 * Shared types used across multiple modules to avoid circular dependencies
 */

export enum Language {
  ENGLISH = 'en',
  ARABIC = 'ar',
  HINDI = 'hi'
}

export enum TranslationStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface MultilingualText {
  [Language.ENGLISH]: string
  [Language.ARABIC]: string
  [Language.HINDI]: string
}

export interface MultilingualTags {
  [Language.ENGLISH]: string[]
  [Language.ARABIC]: string[]
  [Language.HINDI]: string[]
}

export const LANGUAGE_LABELS = {
  [Language.ENGLISH]: 'English',
  [Language.ARABIC]: 'العربية',
  [Language.HINDI]: 'हिंदी'
} as const

export const LANGUAGE_FLAGS = {
  [Language.ENGLISH]: '🇺🇸',
  [Language.ARABIC]: '🇸🇦',
  [Language.HINDI]: '🇮🇳'
} as const 