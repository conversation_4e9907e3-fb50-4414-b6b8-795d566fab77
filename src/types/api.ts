// Base API Response Structure
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface ApiError {
  message: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
  statusCode?: number;
}

// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string | null;
  department?: string | null;
  major?: string | null;
  occupation?: string | null;
  bio?: string | null;
  avatar?: string | null;
  role: 'SYSTEM_ADMIN' | 'HR_ADMIN' | 'EMPLOYEE' | 'CONSULTANT';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'BLOCKED';
  companyId?: string | null;
  languages: string[];
  isDeleted: boolean;
  isLeader: boolean;
  createdAt?: number | null;
  updatedAt?: number | null;
}

// Auth Request/Response Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  isEnableSupport: boolean;
}

export interface RegisterRequest {
  name: string;           // HR admin personal name
  company_name: string;   // Company name
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  is_share_data: boolean; // Fixed field name to match backend
}

export interface RegisterResponse {
  user: User;
  token: string;
  message: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface SetPasswordRequest {
  token: string;
  password: string;
  confirm_password?: string; // Backend expects snake_case
  role?: 'employee' | 'consultant';
}

export interface SetPasswordResponse {
  message: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ChangePasswordResponse {
  message: string;
}

export interface VerifyTokenRequest {
  token: string;
}

export interface VerifyTokenResponse {
  valid: boolean;
  user?: Partial<User>;
  role?: string;
}

export interface RefreshTokenResponse {
  token: string;
  user: User;
}

// Profile Update Types
export interface UpdateProfileRequest {
  name?: string;
  phone?: string;
  avatar?: File;
}

export interface UpdateProfileResponse {
  user: User;
  message: string;
}

// Company Types
export interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  logo?: string;
  settings?: CompanySettings;
  createdAt: string;
  updatedAt: string;
}

export interface CompanySettings {
  allowDataSharing: boolean;
  enableNotifications: boolean;
  timezone: string;
  language: string;
}

// Employee Types
export interface Employee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position?: string;
  department?: string;
  avatar?: string;
  role?: string;
  status?: string; // 'ACTIVE', 'PENDING', 'INACTIVE', etc.
  company_id?: string;
  companyId?: string; // Alternative field name
  isActive?: boolean; // Computed from status
  isLeader?: boolean;
  invitedAt?: string;
  joinedAt?: string;
  lastLoginAt?: string;
  createdAt?: string | number;
  updatedAt?: string | number;
  // DynamoDB fields (for compatibility)
  PK?: string;
  SK?: string;
}

export interface InviteEmployeeRequest {
  email: string;
  name: string;
  position?: string;
  department?: string;
}

export interface InviteEmployeeResponse {
  employee: Employee;
  invitationSent: boolean;
  message: string;
}

// Consultant Types
export interface Consultant {
  _id: string;
  name: string;
  email: string;
  phone: string;
  bio?: string;
  major?: string;
  image?: string;
  languages?: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED';
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateConsultantRequest {
  name: string;
  email: string;
  phone: string;
  bio?: string;
  major?: string;
  image?: string;
  languages?: string[];
}

export interface UpdateConsultantRequest {
  name?: string;
  email?: string;
  phone?: string;
  bio?: string;
  major?: string;
  image?: string;
  languages?: string[];
  status?: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED';
}

export interface ConsultantsListResponse {
  data: Consultant[];
  total: number;
  page: number;
  limit: number;
}

// Assessment Types
export interface Assessment {
  id: string;
  title: string;
  description?: string;
  type: 'survey' | 'quiz' | 'evaluation';
  status: 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
  questions: AssessmentQuestion[];
  createdAt: string;
  updatedAt: string;
}

export interface AssessmentQuestion {
  id: string;
  text: string;
  type: 'multiple-choice' | 'single-choice' | 'text' | 'rating';
  options?: string[];
  required: boolean;
  order: number;
}

export interface AssessmentResponse {
  id: string;
  assessmentId: string;
  employeeId: string;
  answers: AssessmentAnswer[];
  completedAt: string;
  score?: number;
}

export interface AssessmentAnswer {
  questionId: string;
  answer: string | string[] | number;
}

// File Upload Types
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Dashboard Types
export interface DashboardStats {
  totalEmployees: number;
  activeAssessments: number;
  completedAssessments: number;
  averageScore: number;
  recentActivities: Activity[];
}

export interface Activity {
  id: string;
  type: 'assessment_completed' | 'employee_invited' | 'report_generated';
  description: string;
  createdAt: string;
  user?: Partial<User>;
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

// Reports Types
export interface ReportRequest {
  type: 'assessment' | 'employee' | 'company';
  filters?: {
    dateFrom?: string;
    dateTo?: string;
    departmentId?: string;
    assessmentId?: string;
  };
  format: 'pdf' | 'excel' | 'csv';
}

export interface ReportResponse {
  id: string;
  status: 'generating' | 'ready' | 'failed';
  downloadUrl?: string;
  generatedAt?: string;
  expiresAt?: string;
}