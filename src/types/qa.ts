// QA Service Types - Aligned with sehatti-qa-service backend

// Base types
export interface TranslatedContent {
  en: string;
  ar?: string;
  hi?: string;
}

// Question types enum - matching backend exactly
export const QuestionType = {
  PRE_ASSESSMENT: 'pre_assessment',
  POST_ASSESSMENT: 'post_assessment', 
  ONGOING: 'ongoing'
} as const;

export type QuestionTypeValue = typeof QuestionType[keyof typeof QuestionType];

// Question status enum
export const QuestionStatus = {
  ACTIVE: 'active',
  DRAFT: 'draft', 
  INACTIVE: 'inactive'
} as const;

export type QuestionStatusValue = typeof QuestionStatus[keyof typeof QuestionStatus];

// Entity types for tabs
export type EntityType = 'divisions' | 'targets' | 'questions';

// Modal types
export type ModalType = 'create' | 'edit' | 'view' | 'delete';

// Division interface
export interface Division {
  id: string;
  name: TranslatedContent;
  description?: TranslatedContent;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// SubDivision interface (called "Targets" in UI)
export interface Target {
  id: string;
  division_id: string;
  name: TranslatedContent;
  description?: TranslatedContent;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Question option interface
export interface QuestionOption {
  text: TranslatedContent;
  value: number;
}

// Question interface
export interface Question {
  id: string;
  division_id: string;
  subdivision_id: string; // This is the target_id
  question_type: QuestionTypeValue;
  question_text: TranslatedContent;
  options: QuestionOption[];
  corporate_ids: string[];
  status: QuestionStatusValue;
  created_at: string;
  updated_at: string;
  // Optional related entities for display
  division?: Division;
  subdivision?: Target;
}

// Paginated response interface
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Query parameter interfaces
export interface DivisionQueryParams {
  page?: number;
  size?: number;
  search?: string;
}

export interface TargetQueryParams {
  page?: number;
  size?: number;
  division_id?: string;
  search?: string;
}

export interface QuestionQueryParams {
  page?: number;
  size?: number;
  division_id?: string;
  subdivision_id?: string;
  question_type?: QuestionTypeValue;
  corporate_id?: string;
  status?: QuestionStatusValue;
  search?: string;
}

// Request interfaces for creating entities
export interface CreateDivisionRequest {
  name: string;
  description?: string;
}

export interface CreateTargetRequest {
  division_id: string;
  name: string;
  description?: string;
}

export interface CreateQuestionRequest {
  division_id?: string;
  division_name?: string;
  division_description?: string;
  subdivision_id?: string;
  subdivision_name?: string;
  subdivision_description?: string;
  question_type: QuestionTypeValue;
  question_text: string;
  options: Array<{
    text: string;
    value: number;
  }>;
  corporate_ids: string[];
  status: QuestionStatusValue;
}

// Update request interfaces
export interface UpdateDivisionRequest {
  name?: TranslatedContent;
  description?: TranslatedContent;
  is_active?: boolean;
}

export interface UpdateTargetRequest {
  division_id?: string;
  name?: TranslatedContent;
  description?: TranslatedContent;
  is_active?: boolean;
}

export interface UpdateQuestionRequest {
  division_id?: string;
  subdivision_id?: string;
  question_type?: QuestionTypeValue;
  question_text?: TranslatedContent;
  options?: Array<{
    text: TranslatedContent;
    value: number;
  }>;
  corporate_ids?: string[];
  status?: QuestionStatusValue;
}

// Corporate interface (external entity)
export interface Corporate {
  id: string;
  name: string;
  code?: string;
  is_active: boolean;
}

// Legacy corporate response for migration
export interface LegacyCorporateResponse {
  data: Corporate[];
  total: number;
  page: number;
  limit: number;
}

// QA Management state interface
export interface QAManagementState {
  activeTab: EntityType;
  questionTypeFilter: QuestionTypeValue | 'all';
  selectedDivisionId: string;
  selectedTargetId: string;
  selectedCorporateId: string;
  search: string;
  page: number;
  size: number;
  showModal: boolean;
  modalType: ModalType;
  selectedEntity: any;
  showMigrationModal: boolean;
}

// Stats interface for dashboard
export interface QAStats {
  totalDivisions: number;
  totalTargets: number;
  totalQuestions: number;
  activeDivisions: number;
  activeTargets: number;
  activeQuestions: number;
  questionsByType: {
    pre_assessment: number;
    post_assessment: number;
    ongoing: number;
  };
} 