import { Language } from './common'

export type ArticleStatus = 'active' | 'inactive' | 'draft' | 'published' | 'archived'

export interface ArticleContent {
  title: string
  description: string
  summary?: string
  content?: string
  translation_status: 'pending' | 'completed' | 'failed'
  is_original: boolean
  created_at: string
  updated_at: string
}

export interface ArticleResponse {
  article_id: string
  hub_id?: string
  content: Record<string, ArticleContent>
  thumbnail_url?: string
  image_urls?: string[]
  tags: string[]
  category?: string
  author?: string
  status: ArticleStatus
  views_count?: number
  likes_count?: number
  comments_count?: number
  available_languages: string[]
  created_at: string
  updated_at: string
  published_at?: string
}

export interface ArticleListResponse {
  data: ArticleResponse[]
  total: number
  limit: number
  has_more: boolean
  next_key?: string
}

export interface ArticleCreateRequest {
  hub_id?: string
  title: string
  description: string
  summary?: string
  content?: string
  default_language?: Language
  tags?: string[]
  category?: string
  author?: string
  thumbnail?: File
  images?: File[]
  auto_translate?: boolean
  status?: ArticleStatus
}

export interface ArticleUpdateRequest {
  title?: string
  description?: string
  summary?: string
  content?: string
  tags?: string[]
  category?: string
  author?: string
  thumbnail?: File
  images?: File[]
  status?: ArticleStatus
  language?: Language
}

export interface ArticleFilters {
  hub_id?: string
  status?: ArticleStatus
  category?: string
  language?: Language
  author?: string
  search?: string
  tags?: string[]
  limit?: number
  last_key?: string
  created_after?: string
  created_before?: string
}

export interface ArticleTranslationRequest {
  target_languages: Language[]
  overwrite_existing?: boolean
}

export interface ArticlePublishRequest {
  publish_immediately?: boolean
  scheduled_publish_date?: string
  notify_subscribers?: boolean
}

export interface ArticleSearchRequest {
  query: string
  filters?: ArticleFilters
  sort_by?: 'relevance' | 'created_at' | 'updated_at' | 'views_count'
  sort_order?: 'asc' | 'desc'
}

export interface ArticleSearchResponse {
  data: ArticleResponse[]
  total: number
  query: string
  took: number
  suggestions?: string[]
}

export interface ArticleStats {
  total_articles: number
  published_articles: number
  draft_articles: number
  archived_articles: number
  total_views: number
  total_likes: number
  most_popular: ArticleResponse[]
  recent_articles: ArticleResponse[]
}

export { Language } 