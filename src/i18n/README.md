# i18next Translation System

This project uses i18next with React for internationalization support. The system supports English (EN), Arabic (AR), and Hindi (HI) languages with full RTL support for Arabic.

## Setup

The translation system is automatically initialized when the app starts. The configuration is in `src/i18n/index.ts`.

## Usage

### Basic Usage

```tsx
import { useTranslation } from '@/hooks/useTranslation'

function MyComponent() {
  const { t, currentLanguage, changeLanguage, isRTL, rtlClasses } = useTranslation()

  return (
    <div className={rtlClasses.direction} dir={isRTL ? 'rtl' : 'ltr'}>
      <h1 className={rtlClasses.textAlign}>{t.common.welcome}</h1>
      <p>{t.dashboard.questionsWaiting}</p>
      
      {/* Language switcher */}
      <button onClick={() => changeLanguage('EN')}>English</button>
      <button onClick={() => changeLanguage('AR')}>العربية</button>
      <button onClick={() => changeLanguage('HI')}>हिन्दी</button>
    </div>
  )
}
```

### Available Translation Keys

The translation system provides type-safe access to all translation keys:

#### Common
- `t.common.welcome` - Welcome message
- `t.common.language` - Language label
- `t.common.score` - Score label
- `t.common.questions` - Questions label
- `t.common.articles` - Articles label
- `t.common.dimensions` - Dimensions label

#### Navigation
- `t.navigation.overview` - Overview tab
- `t.navigation.checkin` - Check-in tab
- `t.navigation.dimensions` - Dimensions tab
- `t.navigation.profile` - Profile tab
- `t.navigation.qrcode` - QR Code tab

#### Dashboard
- `t.dashboard.questionsWaiting` - Questions waiting message
- `t.dashboard.journeyProgress` - Journey progress title
- `t.dashboard.congratulations` - Congratulations message

#### Assessment
- `t.assessment.webinarIntro` - Webinar introduction
- `t.assessment.preAssessment` - Pre-assessment
- `t.assessment.scientificAssessment` - Scientific assessment title
- `t.assessment.fivePointScale` - Five point scale instruction
- `t.assessment.responseRecorded` - Response recorded message
- `t.assessment.completeCheckin` - Complete check-in button
- `t.assessment.submitResponses` - Submit responses button

#### Wellness Divisions
- `t.wellnessDivisions.physical.name` - Physical wellbeing name
- `t.wellnessDivisions.physical.description` - Physical wellbeing description
- `t.wellnessDivisions.emotional.name` - Emotional wellbeing name
- `t.wellnessDivisions.emotional.description` - Emotional wellbeing description
- And so on for all 7 wellness divisions...

### RTL Support

The system automatically handles RTL layout for Arabic:

```tsx
const { isRTL, rtlClasses } = useTranslation()

return (
  <div className={rtlClasses.direction} dir={isRTL ? 'rtl' : 'ltr'}>
    <div className={`flex ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
      <span className={rtlClasses.textAlign}>Text content</span>
    </div>
  </div>
)
```

### Language Persistence

The selected language is automatically saved to localStorage and restored on app reload.

## Adding New Translations

1. Add the new key to all language files:
   - `src/i18n/locales/en.json`
   - `src/i18n/locales/ar.json`
   - `src/i18n/locales/hi.json`

2. Update the TypeScript interface in `src/hooks/useTranslation.ts`:
   ```tsx
   export interface TranslationKeys {
     // Add your new section
     myNewSection: {
       myNewKey: string
     }
   }
   ```

3. Update the translation function in the hook:
   ```tsx
   const t = useMemo((): TranslationKeys => {
     return {
       // ... existing translations
       myNewSection: {
         myNewKey: i18nT('myNewSection.myNewKey')
       }
     }
   }, [i18nT])
   ```

## File Structure

```
src/i18n/
├── index.ts              # i18next configuration
├── locales/
│   ├── en.json          # English translations
│   ├── ar.json          # Arabic translations
│   └── hi.json          # Hindi translations
└── README.md            # This file

src/hooks/
└── useTranslation.ts    # Custom translation hook
```

## Language Codes

- **EN**: English (LTR)
- **AR**: Arabic (RTL)
- **HI**: Hindi (LTR)

## Demo Component

A demo component is available at `src/components/demo/TranslationDemo.tsx` to test all translations and RTL functionality.

## Migration from Old System

The old translation system using `UI_TRANSLATIONS` has been replaced with this i18next-based system. The new system provides:

- Better performance with memoized translations
- Type safety for all translation keys
- Automatic language persistence
- Proper RTL support
- Industry-standard i18next features
- Better organization of translation files
