import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Import translation files
import enTranslations from './locales/en.json'
import arTranslations from './locales/ar.json'
import hiTranslations from './locales/hi.json'

// Language detection and persistence
const LANGUAGE_STORAGE_KEY = 'sehatti-user-language'

// Get stored language or detect browser language
const getStoredLanguage = (): string => {
  try {
    const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY)
    if (stored && ['en', 'ar', 'hi'].includes(stored)) {
      return stored
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error)
  }
  
  // Auto-detect browser language
  const browserLang = navigator.language || navigator.languages?.[0] || 'en'
  if (browserLang.startsWith('ar')) return 'ar'
  if (browserLang.startsWith('hi')) return 'hi'
  return 'en'
}

// Initialize i18next
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslations
      },
      ar: {
        translation: arTranslations
      },
      hi: {
        translation: hiTranslations
      }
    },
    lng: getStoredLanguage(),
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false // React already does escaping
    },
    
    // Namespace configuration
    defaultNS: 'translation',
    ns: ['translation'],
    
    // React specific options
    react: {
      useSuspense: false,
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i']
    }
  })

// Listen for language changes and persist to localStorage
i18n.on('languageChanged', (lng) => {
  try {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, lng)
    
    // Update document direction for RTL support
    const isRTL = lng === 'ar'
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr'
    document.documentElement.lang = lng
    
    // Add/remove RTL classes
    if (isRTL) {
      document.documentElement.classList.add('rtl')
      document.documentElement.classList.remove('ltr')
      document.body.classList.add('rtl')
      document.body.classList.remove('ltr')
    } else {
      document.documentElement.classList.add('ltr')
      document.documentElement.classList.remove('rtl')
      document.body.classList.add('ltr')
      document.body.classList.remove('rtl')
    }
  } catch (error) {
    console.warn('Failed to persist language:', error)
  }
})

export default i18n
