{"common": {"welcome": "Welcome back", "refresh": "Refresh", "language": "Language", "score": "Score", "questions": "questions", "articles": "articles", "dimensions": "dimensions", "minutes": "min read", "readMore": "Read More", "of": "of", "great": "Great", "okay": "Okay", "notGood": "Not Good"}, "navigation": {"overview": "Overview", "checkin": "Check-in", "dimensions": "Dimensions", "profile": "Profile", "qrcode": "QR Code", "wellnessOverview": "Wellness Overview", "dailyCheckin": "Daily Check-in", "wellnessDimensions": "Wellness Dimensions", "profileSettings": "Profile Settings", "myQRCode": "My QR Code", "mediaHub": "Media Hub", "consultantChat": "Consultant <PERSON><PERSON>", "myProgress": "My Progress"}, "dashboard": {"questionsWaiting": "questions waiting for your input", "overallScore": "Overall Score", "journeyProgress": "Your Wellness Journey Progress", "congratulations": "🎉 Congratulations! You now have full access to all wellness content and resources."}, "assessment": {"webinarIntro": "Webinar Introduction", "webinarBooking": "Webinar Booking", "preAssessment": "Pre-Assessment", "contentAccess": "Content Access", "scientificAssessment": "Daily Wellness Check-in - Scientific Assessment", "questionOf": "Question", "fivePointScale": "Rate on a scale of 1-5 (1 = Strongly Disagree, 5 = Strongly Agree)", "responseRecorded": "✓ Response recorded", "completeCheckin": "Complete Check-in", "submitResponses": "Submit Responses"}, "checkin": {"todaysCheckin": "Today's Check-in", "howDoYouFeel": "How do you feel today?"}, "content": {"exploreContent": "Explore Wellness Content", "relatedContent": "Related Content", "sevenDimensions": "Seven Wellness Dimensions"}, "chat": {"connectConsultants": "Connect with Wellness Consultants", "personalizedGuidance": "Get personalized guidance and support from our certified wellness experts.", "startConsultation": "Start Consultation"}, "wellnessDivisions": {"physical": {"name": "Physical Wellbeing", "description": "Maintaining a healthy body through regular exercise, balanced nutrition, adequate sleep, and preventive healthcare measures."}, "emotional": {"name": "Emotional Wellbeing", "description": "Understanding and managing one's emotions, coping effectively with stress, and maintaining a positive outlook."}, "social": {"name": "Social Wellbeing", "description": "Developing a sense of connection, belonging, and a well-established support system."}, "occupational": {"name": "Occupational Wellbeing", "description": "Deriving personal satisfaction and enrichment from one's work while maintaining a healthy work-life balance."}, "intellectual": {"name": "Intellectual Wellbeing", "description": "Engaging in creative and mentally stimulating activities to expand knowledge and skills."}, "environmental": {"name": "Environmental Wellbeing", "description": "Recognizing the responsibility to preserve, protect, and improve the environment and appreciating one's surroundings."}, "financial": {"name": "Financial Wellbeing", "description": "Managing financial resources effectively to live within one's means, set realistic goals, and prepare for future financial needs."}}, "mediaHub": {"title": "Media Hub", "subtitle": "Explore wellness content and learning resources", "tabs": {"media": "Media", "articles": "Articles"}, "search": {"placeholder": "Search content...", "placeholderArticles": "Search articles...", "placeholderHubs": "Search hubs...", "noResults": "No results found", "tryDifferentTerms": "Try adjusting your search terms or filters"}, "filters": {"allCategories": "All Categories", "allDivisions": "All Divisions", "sortBy": "Sort by", "newest": "Newest First", "popular": "Most Popular", "shortestRead": "Shortest Read", "highestRated": "Highest Rated", "clearFilters": "Clear Filters"}, "categories": {"fundamentals": "Wellness Fundamentals", "workplace": "Workplace Wellness", "mindfulness": "Mindfulness & Mental Health", "physical": "Physical Fitness", "social": "Social Connections", "environment": "Environmental Wellness", "financial": "Financial Wellness"}, "stats": {"showing": "Showing", "of": "of", "articles": "articles", "videos": "videos", "bookmarked": "bookmarked", "liked": "liked", "views": "views", "likes": "likes"}, "actions": {"readArticle": "Read Article", "watchVideo": "Watch Video", "share": "Share", "bookmark": "Bookmark", "like": "Like", "download": "Download", "copyLink": "Copy Link", "copied": "Copied!", "helpful": "Helpful"}, "sharing": {"title": "Share Article", "shareVia": "Share via:", "twitter": "Twitter", "linkedin": "LinkedIn", "whatsapp": "WhatsApp", "copyLink": "Copy Link"}, "viewModes": {"grid": "Grid View", "list": "List View"}, "loading": {"loadingContent": "Loading content...", "loadingArticles": "Loading articles...", "loadingVideos": "Loading videos..."}, "errors": {"failedToLoad": "Failed to load content", "tryAgain": "Try again", "networkError": "Network error occurred"}}}