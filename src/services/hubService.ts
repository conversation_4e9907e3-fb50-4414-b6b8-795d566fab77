// Enhanced Hub Service for Sehatti Modern Frontend
// Incorporates multilingual support and advanced features from legacy system

import { getMediaServiceUrl } from '../config/api'
import type { 
  HubResponse, 
  HubListResponse, 
  HubCreateRequest, 
  HubUpdateRequest, 
  HubListParams, 
  HubStatus, 
  Language, 
  HubVisibility,
  MultilingualHub
} from '../types/hub'

const API_BASE_URL = getMediaServiceUrl()

interface HubSearchParams {
  q: string
  language?: Language
  limit?: number
  category?: string
}

interface HubStatsResponse {
  hub_id: string
  total_content: number
  content_by_type: Record<string, number>
  total_views: number
  total_likes: number
  engagement_rate: number
  created_at: string
  updated_at: string
}

class HubService {
  private baseURL: string

  constructor() {
    this.baseURL = API_BASE_URL
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        message: 'Network error',
        detail: `HTTP ${response.status}`
      }))
      throw new Error(error.message || error.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  private async requestFormData<T>(endpoint: string, formData: FormData, method: 'POST' | 'PATCH' = 'POST'): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      method,
      body: formData,
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Don't set Content-Type for FormData - browser will set it with boundary
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        message: 'Network error',
        detail: `HTTP ${response.status}`
      }))
      throw new Error(error.message || error.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  /**
   * Create a new multilingual hub
   */
  async createHub(data: HubCreateRequest): Promise<HubResponse> {
    const formData = new FormData()
    formData.append('title', data.title)
    if (data.description) formData.append('description', data.description)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.category) formData.append('category', data.category)
    formData.append('visibility', data.visibility)
    if (data.created_by) formData.append('created_by', data.created_by)
    if (data.image) formData.append('image', data.image)

    return this.requestFormData<HubResponse>('/api/v1/hubs/', formData, 'POST')
  }

  /**
   * Get hub by ID with all language versions
   */
  async getHub(hubId: string, includeContentCount = true): Promise<HubResponse> {
    const searchParams = new URLSearchParams()
    searchParams.set('include_content_count', includeContentCount.toString())
    
    const endpoint = `/api/v1/hubs/${hubId}?${searchParams.toString()}`
    return this.request<HubResponse>(endpoint)
  }

  /**
   * List hubs with pagination and filtering
   */
  async listHubs(params: HubListParams = {}): Promise<HubListResponse> {
    const searchParams = new URLSearchParams()
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.last_key) searchParams.set('last_key', params.last_key)
    if (params.category) searchParams.set('category', params.category)
    if (params.status) searchParams.set('status', params.status)
    if (params.created_by) searchParams.set('created_by', params.created_by)
    
    const endpoint = `/api/v1/hubs${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return this.request<HubListResponse>(endpoint)
  }

  /**
   * Update hub content for a specific language
   */
  async updateHub(hubId: string, data: HubUpdateRequest): Promise<HubResponse> {
    const formData = new FormData()
    formData.append('language', data.language)
    if (data.title) formData.append('title', data.title)
    if (data.description) formData.append('description', data.description)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.category) formData.append('category', data.category)
    if (data.visibility) formData.append('visibility', data.visibility)
    if (data.status) formData.append('status', data.status)
    if (data.image) formData.append('image', data.image)

    return this.requestFormData<HubResponse>(`/api/v1/hubs/${hubId}`, formData, 'PATCH')
  }

  /**
   * Delete hub and all associated resources
   */
  async deleteHub(hubId: string, force = false): Promise<void> {
    const searchParams = new URLSearchParams()
    if (force) searchParams.set('force', 'true')
    
    const endpoint = `/api/v1/hubs/${hubId}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    await this.request<void>(endpoint, { method: 'DELETE' })
  }

  /**
   * Get hub statistics
   */
  async getHubStats(hubId: string): Promise<HubStatsResponse> {
    return this.request<HubStatsResponse>(`/api/v1/hubs/${hubId}/stats`)
  }

  /**
   * Change hub status
   */
  async changeHubStatus(
    hubId: string, 
    newStatus: HubStatus, 
    reason?: string
  ): Promise<HubResponse> {
    const formData = new FormData()
    formData.append('new_status', newStatus)
    if (reason) formData.append('reason', reason)

    return this.requestFormData<HubResponse>(`/api/v1/hubs/${hubId}/status`, formData, 'POST')
  }

  /**
   * Search hubs by title and description
   */
  async searchHubs(params: HubSearchParams): Promise<HubResponse[]> {
    const searchParams = new URLSearchParams()
    searchParams.set('q', params.q)
    if (params.language) searchParams.set('language', params.language)
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.category) searchParams.set('category', params.category)

    const endpoint = `/api/v1/hubs/search?${searchParams.toString()}`
    const results = await this.request<HubResponse[]>(endpoint)
    
    return Array.isArray(results) ? results : []
  }

  /**
   * Retranslate hub content using AI
   */
  async retranslateHub(
    hubId: string,
    sourceLanguage: Language = Language.ENGLISH,
    targetLanguages: Language[] = [Language.ARABIC, Language.HINDI]
  ): Promise<HubResponse> {
    const formData = new FormData()
    formData.append('source_language', sourceLanguage)
    formData.append('target_languages', targetLanguages.join(','))

    return this.requestFormData<HubResponse>(`/api/v1/hubs/${hubId}/retranslate`, formData, 'POST')
  }

  /**
   * Bulk operations on multiple hubs
   */
  async bulkOperation(
    hubIds: string[], 
    operation: 'activate' | 'deactivate' | 'delete' | 'archive',
    options?: { force?: boolean; reason?: string }
  ): Promise<{ success: string[]; failed: { id: string; error: string }[] }> {
    const formData = new FormData()
    formData.append('hub_ids', hubIds.join(','))
    formData.append('operation', operation)
    if (options?.force) formData.append('force', 'true')
    if (options?.reason) formData.append('reason', options.reason)

    return this.requestFormData(`/api/v1/hubs/bulk`, formData, 'POST')
  }

  /**
   * Get hubs by category
   */
  async getHubsByCategory(category: string, limit = 20): Promise<HubListResponse> {
    return this.listHubs({ category, limit })
  }

  /**
   * Get user's hubs
   */
  async getUserHubs(userId: string, limit = 20): Promise<HubListResponse> {
    return this.listHubs({ created_by: userId, limit })
  }

  /**
   * Legacy method for backward compatibility
   */
  async legacyListHubs(params: any = {}): Promise<any> {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.set(key, value.toString())
      }
    })

    const endpoint = `/api/v1/hubs/legacy${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  /**
   * Health check for hub service
   */
  async healthCheck(): Promise<{ 
    status: string
    service: string
    version: string
    timestamp: number
    checks: Record<string, string>
  }> {
    return this.request('/api/media/health')
  }
}

// Utility functions for working with hubs in the frontend
export const HubServiceUtils = {
  /**
   * Get user's preferred language from browser/app settings
   */
  getUserLanguage: (): Language => {
    const stored = localStorage.getItem('preferredLanguage')
    if (stored && Object.values(Language).includes(stored as Language)) {
      return stored as Language
    }
    
    // Fallback to browser language
    const browserLang = navigator.language.split('-')[0]
    switch (browserLang) {
      case 'ar': return Language.ARABIC
      case 'hi': return Language.HINDI
      default: return Language.ENGLISH
    }
  },

  /**
   * Format hub for display in a specific language
   */
  formatHubForDisplay: (hub: HubResponse, language?: Language) => {
    const displayLang = language || HubServiceUtils.getUserLanguage()
    
    return {
      ...hub,
      displayTitle: hub.content[displayLang]?.title || hub.content[Language.ENGLISH]?.title || 'Untitled',
      displayDescription: hub.content[displayLang]?.description || hub.content[Language.ENGLISH]?.description || '',
      displayLanguage: displayLang,
      availableLanguages: Object.keys(hub.content) as Language[],
      isTranslationComplete: hub.content[displayLang]?.translation_status === 'completed'
    }
  },

  /**
   * Check if user can edit a hub
   */
  canEditHub: (hub: HubResponse, userId?: string): boolean => {
    if (!userId) return false
    return hub.created_by === userId || hub.status !== HubStatus.ARCHIVED
  },

  /**
   * Get hub status display information
   */
  getStatusDisplay: (status: HubStatus) => {
    const statusMap = {
      [HubStatus.ACTIVE]: { label: 'Active', color: 'success', icon: '✅' },
      [HubStatus.INACTIVE]: { label: 'Inactive', color: 'secondary', icon: '⏸️' },
      [HubStatus.DRAFT]: { label: 'Draft', color: 'warning', icon: '📝' },
      [HubStatus.ARCHIVED]: { label: 'Archived', color: 'destructive', icon: '🗄️' }
    }
    
    return statusMap[status] || statusMap[HubStatus.ACTIVE]
  },

  /**
   * Validate hub data before submission
   */
  validateHubData: (data: Partial<HubCreateRequest>): string[] => {
    const errors: string[] = []
    
    if (!data.title || data.title.trim().length === 0) {
      errors.push('Title is required')
    }
    
    if (data.title && data.title.length > 200) {
      errors.push('Title must be less than 200 characters')
    }
    
    if (data.description && data.description.length > 1000) {
      errors.push('Description must be less than 1000 characters')
    }
    
    return errors
  }
}

// Export singleton instance
export const hubService = new HubService()
export default hubService 