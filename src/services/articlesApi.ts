// Compatibility wrapper for legacy ArticlesAPI usage
// Since articles are part of media service, we'll map to media endpoints

import { 
  useGetMediaFilesQuery,
  useUploadMediaMutation,
  useDeleteMediaFileMutation,
} from '../store/api/mediaApi';

// Articles query hook - maps to media files with article filter
export const useGetArticlesQuery = (params: any = {}) => {
  return useGetMediaFilesQuery({
    ...params,
    type: 'article', // Filter for article type
  });
};

// Articles mutation hooks
export const useCreateArticleMutation = useUploadMediaMutation;
export const useUpdateArticleMutation = useUploadMediaMutation; 
export const useDeleteArticleMutation = useDeleteMediaFileMutation;
export const usePublishArticleMutation = useUploadMediaMutation;
export const useRetranslateArticleMutation = useUploadMediaMutation;
export const useUploadArticleThumbnailMutation = useUploadMediaMutation;

// Export for compatibility
export { useGetArticlesQuery as default }; 