// Compatibility wrapper for legacy MediaAPI usage
// This re-exports media service hooks in the old format

import { 
  useGetMediaFilesQuery,
  useUploadMediaMutation,
  useGetStreamingSessionsQuery,
  useCreateStreamingSessionMutation,
  useGetHubsQuery,
  useGetHubQuery,
  useAddHubMutation,
  useUpdateHubMutation,
  useDeleteHubMutation,
} from '../store/api/mediaApi';

import {
  useListContentQuery,
  useCreateMultilingualContentMutation,
  useDeleteContentMutation,
  useUploadFileMutation,
  useUploadThumbnailMutation,
  useGetContentByIdQuery
} from '../features/API/hubApi';

// Legacy MediaAPI object structure
export const MediaAPI = {
  // Map old method names to new hooks
  useGetHubsQuery: useGetHubsQuery,
  useGetHubQuery: useGetHubQuery,
  useAddHubMutation: useAddHubMutation,
  useUpdateHubMutation: useUpdateHubMutation,
  useDeleteHubMutation: useDeleteHubMutation,
  useListContentQuery: useGetMediaFilesQuery,
  useUploadContentMutation: useUploadMediaMutation,
  useGetStreamingSessionsQuery: useGetStreamingSessionsQuery,
  useCreateStreamingSessionMutation: useCreateStreamingSessionMutation,
  
  // Video/Content related hooks (mapped to content APIs)
  useGetVideosQuery: useListContentQuery,
  useCreateVideoMutation: useCreateMultilingualContentMutation,
  useUpdateVideoMutation: useCreateMultilingualContentMutation, // No update available, use create as fallback
  useDeleteVideoMutation: useDeleteContentMutation,
  useUploadFileMutation: useUploadFileMutation,
  useUploadThumbnailMutation: useUploadThumbnailMutation,
  useGetContentByIdQuery: useGetContentByIdQuery,
};

// Default export for compatibility
export default MediaAPI; 