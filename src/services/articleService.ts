// Enhanced Article Service for Sehatti Modern Frontend
// Provides comprehensive article management with multilingual support

import { getMediaServiceUrl } from '../config/api'
import type { 
  ArticleResponse, 
  ArticleListResponse, 
  ArticleCreateRequest, 
  ArticleUpdateRequest, 
  ArticleFilters,
  Language,
  ArticleStatus,
  FileUploadResponse
} from '../types/article'

const MEDIA_SERVICE_BASE_URL = getMediaServiceUrl()

// Enhanced interfaces for better article management
interface ArticleSearchParams {
  q: string
  language?: Language
  limit?: number
  category?: string
  author?: string
  hub_id?: string
}

interface ArticleStats {
  total_articles: number
  articles_by_status: Record<string, number>
  articles_by_category: Record<string, number>
  total_views: number
  total_likes: number
  total_comments: number
  engagement_rate: number
  top_performing: ArticleResponse[]
}

interface ArticleBulkOperation {
  article_ids: string[]
  operation: 'publish' | 'unpublish' | 'archive' | 'delete'
  options?: {
    force?: boolean
    reason?: string
    notify_subscribers?: boolean
  }
}

class ArticleService {
  private baseURL = MEDIA_SERVICE_BASE_URL

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        detail: `HTTP ${response.status}`,
        message: 'Network error'
      }))
      throw new Error(error.detail || error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  private async requestFormData<T>(endpoint: string, formData: FormData, method: 'POST' | 'PATCH' = 'POST'): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      method,
      body: formData,
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Don't set Content-Type for FormData
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        detail: `HTTP ${response.status}`,
        message: 'Network error'
      }))
      throw new Error(error.detail || error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  /**
   * List articles with advanced filtering and pagination
   */
  async listArticles(params: ArticleFilters = {}): Promise<ArticleListResponse> {
    const searchParams = new URLSearchParams()
    
    if (params.hub_id) searchParams.append('hub_id', params.hub_id)
    if (params.status) searchParams.append('status', params.status)
    if (params.category) searchParams.append('category', params.category)
    if (params.language) searchParams.append('language', params.language)
    if (params.author) searchParams.append('author', params.author)
    if (params.search) searchParams.append('search', params.search)
    if (params.tags) searchParams.append('tags', params.tags.join(','))
    if (params.limit) searchParams.append('limit', params.limit.toString())
    if (params.last_key) searchParams.append('last_key', params.last_key)
    if (params.created_after) searchParams.append('created_after', params.created_after)
    if (params.created_before) searchParams.append('created_before', params.created_before)

    return this.request<ArticleListResponse>(`/api/v1/articles?${searchParams}`)
  }

  /**
   * Get article by ID with language preference
   */
  async getArticle(articleId: string, language: Language = Language.ENGLISH): Promise<ArticleResponse> {
    return this.request<ArticleResponse>(`/api/v1/articles/${articleId}?language=${language}`)
  }

  /**
   * Create new article with comprehensive options
   */
  async createArticle(data: ArticleCreateRequest): Promise<ArticleResponse> {
    const formData = new FormData()
    
    // Required fields
    formData.append('title', data.title)
    formData.append('description', data.description)
    
    // Optional fields
    if (data.hub_id) formData.append('hub_id', data.hub_id)
    if (data.summary) formData.append('summary', data.summary)
    if (data.content) formData.append('content', data.content)
    if (data.default_language) formData.append('default_language', data.default_language)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.category) formData.append('category', data.category)
    if (data.author) formData.append('author', data.author)
    if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString())
    if (data.status) formData.append('status', data.status)
    
    // File uploads
    if (data.thumbnail) formData.append('thumbnail', data.thumbnail)
    if (data.images) {
      data.images.forEach(image => formData.append('images', image))
    }

    return this.requestFormData<ArticleResponse>('/api/v1/articles', formData, 'POST')
  }

  /**
   * Update existing article
   */
  async updateArticle(articleId: string, data: ArticleUpdateRequest): Promise<ArticleResponse> {
    const formData = new FormData()
    
    if (data.language) formData.append('language', data.language)
    if (data.title) formData.append('title', data.title)
    if (data.description) formData.append('description', data.description)
    if (data.summary) formData.append('summary', data.summary)
    if (data.content) formData.append('content', data.content)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.category) formData.append('category', data.category)
    if (data.author) formData.append('author', data.author)
    if (data.status) formData.append('status', data.status)
    
    // File uploads
    if (data.thumbnail) formData.append('thumbnail', data.thumbnail)
    if (data.images) {
      data.images.forEach(image => formData.append('images', image))
    }

    return this.requestFormData<ArticleResponse>(`/api/v1/articles/${articleId}`, formData, 'PATCH')
  }

  /**
   * Delete article
   */
  async deleteArticle(articleId: string, force = false): Promise<{ message: string; article_id: string }> {
    const searchParams = new URLSearchParams()
    if (force) searchParams.set('force', 'true')
    
    const endpoint = `/api/v1/articles/${articleId}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return this.request<{ message: string; article_id: string }>(endpoint, { method: 'DELETE' })
  }

  /**
   * Get articles by hub ID
   */
  async getArticlesByHub(
    hubId: string, 
    language: Language = Language.ENGLISH, 
    limit: number = 20, 
    lastKey?: string
  ): Promise<ArticleListResponse> {
    const searchParams = new URLSearchParams()
    searchParams.append('language', language)
    searchParams.append('limit', limit.toString())
    if (lastKey) searchParams.append('last_key', lastKey)

    return this.request<ArticleListResponse>(`/api/v1/articles/hub/${hubId}?${searchParams}`)
  }

  /**
   * Search articles across all hubs
   */
  async searchArticles(params: ArticleSearchParams): Promise<ArticleResponse[]> {
    const searchParams = new URLSearchParams()
    searchParams.set('q', params.q)
    if (params.language) searchParams.set('language', params.language)
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.category) searchParams.set('category', params.category)
    if (params.author) searchParams.set('author', params.author)
    if (params.hub_id) searchParams.set('hub_id', params.hub_id)

    const endpoint = `/api/v1/articles/search?${searchParams.toString()}`
    const results = await this.request<ArticleResponse[]>(endpoint)
    
    return Array.isArray(results) ? results : []
  }

  /**
   * Change article status (publish, unpublish, archive)
   */
  async changeArticleStatus(
    articleId: string, 
    newStatus: ArticleStatus, 
    reason?: string
  ): Promise<ArticleResponse> {
    const formData = new FormData()
    formData.append('new_status', newStatus)
    if (reason) formData.append('reason', reason)

    return this.requestFormData<ArticleResponse>(`/api/v1/articles/${articleId}/status`, formData, 'POST')
  }

  /**
   * Translate article content using AI
   */
  async translateArticle(
    articleId: string,
    sourceLanguage: Language = Language.ENGLISH,
    targetLanguages: Language[] = [Language.ARABIC, Language.HINDI]
  ): Promise<ArticleResponse> {
    const formData = new FormData()
    formData.append('source_language', sourceLanguage)
    formData.append('target_languages', targetLanguages.join(','))

    return this.requestFormData<ArticleResponse>(`/api/v1/articles/${articleId}/translate`, formData, 'POST')
  }

  /**
   * Get article statistics
   */
  async getArticleStats(articleId?: string, hubId?: string): Promise<ArticleStats> {
    const searchParams = new URLSearchParams()
    if (articleId) searchParams.append('article_id', articleId)
    if (hubId) searchParams.append('hub_id', hubId)
    
    return this.request<ArticleStats>(`/api/v1/articles/stats?${searchParams}`)
  }

  /**
   * Bulk operations on multiple articles
   */
  async bulkOperation(data: ArticleBulkOperation): Promise<{ 
    success: string[]
    failed: { id: string; error: string }[] 
  }> {
    const formData = new FormData()
    formData.append('article_ids', data.article_ids.join(','))
    formData.append('operation', data.operation)
    if (data.options?.force) formData.append('force', 'true')
    if (data.options?.reason) formData.append('reason', data.options.reason)
    if (data.options?.notify_subscribers !== undefined) {
      formData.append('notify_subscribers', data.options.notify_subscribers.toString())
    }

    return this.requestFormData(`/api/v1/articles/bulk`, formData, 'POST')
  }

  /**
   * Get trending articles
   */
  async getTrendingArticles(
    limit: number = 10,
    timeframe: 'day' | 'week' | 'month' = 'week',
    language?: Language
  ): Promise<ArticleResponse[]> {
    const searchParams = new URLSearchParams()
    searchParams.set('limit', limit.toString())
    searchParams.set('timeframe', timeframe)
    if (language) searchParams.set('language', language)

    return this.request<ArticleResponse[]>(`/api/v1/articles/trending?${searchParams}`)
  }

  /**
   * Get articles by category
   */
  async getArticlesByCategory(
    category: string, 
    limit: number = 20, 
    language?: Language
  ): Promise<ArticleListResponse> {
    const searchParams = new URLSearchParams()
    searchParams.set('limit', limit.toString())
    if (language) searchParams.set('language', language)

    return this.request<ArticleListResponse>(`/api/v1/articles/category/${category}?${searchParams}`)
  }

  /**
   * Get articles by author
   */
  async getArticlesByAuthor(
    author: string, 
    limit: number = 20, 
    language?: Language
  ): Promise<ArticleListResponse> {
    const searchParams = new URLSearchParams()
    searchParams.set('limit', limit.toString())
    if (language) searchParams.set('language', language)

    return this.request<ArticleListResponse>(`/api/v1/articles/author/${author}?${searchParams}`)
  }

  /**
   * Upload image for article
   */
  async uploadImage(
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<FileUploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('folder', 'articles')

    if (onProgress) {
      // Use XMLHttpRequest for progress tracking
      const xhr = new XMLHttpRequest()
      
      return new Promise((resolve, reject) => {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            onProgress(progress)
          }
        }

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              resolve(response)
            } catch (e) {
              reject(new Error('Invalid response format'))
            }
          } else {
            try {
              const error = JSON.parse(xhr.responseText)
              reject(new Error(error.detail || 'Upload failed'))
            } catch (e) {
              reject(new Error(`HTTP ${xhr.status}`))
            }
          }
        }

        xhr.onerror = () => reject(new Error('Network error'))
        xhr.ontimeout = () => reject(new Error('Upload timeout'))

        const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
        xhr.open('POST', `${this.baseURL}/api/v1/media/upload`)
        if (token) {
          xhr.setRequestHeader('Authorization', `Bearer ${token}`)
        }
        xhr.timeout = 300000 // 5 minutes
        xhr.send(formData)
      })
    } else {
      // Use fetch for simple upload without progress
      return this.requestFormData<FileUploadResponse>('/api/v1/media/upload', formData, 'POST')
    }
  }

  /**
   * Get related articles
   */
  async getRelatedArticles(
    articleId: string, 
    limit: number = 5,
    language?: Language
  ): Promise<ArticleResponse[]> {
    const searchParams = new URLSearchParams()
    searchParams.set('limit', limit.toString())
    if (language) searchParams.set('language', language)

    return this.request<ArticleResponse[]>(`/api/v1/articles/${articleId}/related?${searchParams}`)
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<{ 
    status: string
    service: string
    version: string
    timestamp: number
    checks: Record<string, string>
  }> {
    return this.request('/api/media/health')
  }

  /**
   * Legacy method for backward compatibility
   */
  async legacyListArticles(params: any = {}): Promise<any> {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.set(key, value.toString())
      }
    })

    return this.request(`/api/v1/articles/legacy?${searchParams}`)
  }
}

// Utility functions for article management
export const ArticleServiceUtils = {
  /**
   * Get user's preferred language
   */
  getUserLanguage: (): Language => {
    const stored = localStorage.getItem('preferredLanguage')
    if (stored && Object.values(Language).includes(stored as Language)) {
      return stored as Language
    }
    
    const browserLang = navigator.language.split('-')[0]
    switch (browserLang) {
      case 'ar': return Language.ARABIC
      case 'hi': return Language.HINDI
      default: return Language.ENGLISH
    }
  },

  /**
   * Format article for display in specific language
   */
  formatArticleForDisplay: (article: ArticleResponse, language?: Language) => {
    const displayLang = language || ArticleServiceUtils.getUserLanguage()
    
    return {
      ...article,
      displayTitle: article.content[displayLang]?.title || article.content[Language.ENGLISH]?.title || 'Untitled',
      displayDescription: article.content[displayLang]?.description || article.content[Language.ENGLISH]?.description || '',
      displaySummary: article.content[displayLang]?.summary || article.content[Language.ENGLISH]?.summary || '',
      displayContent: article.content[displayLang]?.content || article.content[Language.ENGLISH]?.content || '',
      displayLanguage: displayLang,
      availableLanguages: Object.keys(article.content) as Language[],
      isTranslationComplete: article.content[displayLang]?.translation_status === 'completed'
    }
  },

  /**
   * Get article status display information
   */
  getStatusDisplay: (status: ArticleStatus) => {
    const statusMap = {
      [ArticleStatus.PUBLISHED]: { label: 'Published', color: 'success', icon: '📰' },
      [ArticleStatus.DRAFT]: { label: 'Draft', color: 'warning', icon: '📝' },
      [ArticleStatus.REVIEW]: { label: 'Under Review', color: 'info', icon: '👀' },
      [ArticleStatus.ARCHIVED]: { label: 'Archived', color: 'secondary', icon: '🗄️' }
    }
    
    return statusMap[status] || statusMap[ArticleStatus.DRAFT]
  },

  /**
   * Validate article data
   */
  validateArticleData: (data: Partial<ArticleCreateRequest>): string[] => {
    const errors: string[] = []
    
    if (!data.title || data.title.trim().length === 0) {
      errors.push('Title is required')
    }
    
    if (data.title && data.title.length > 300) {
      errors.push('Title must be less than 300 characters')
    }
    
    if (!data.description || data.description.trim().length === 0) {
      errors.push('Description is required')
    }
    
    if (data.description && data.description.length > 500) {
      errors.push('Description must be less than 500 characters')
    }
    
    if (data.content && data.content.length > 50000) {
      errors.push('Content must be less than 50,000 characters')
    }
    
    return errors
  },

  /**
   * Extract reading time estimate
   */
  getReadingTime: (content: string): number => {
    const wordsPerMinute = 200
    const wordCount = content.trim().split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  },

  /**
   * Format reading time for display
   */
  formatReadingTime: (minutes: number): string => {
    if (minutes < 1) return 'Less than 1 min read'
    if (minutes === 1) return '1 min read'
    return `${minutes} min read`
  },

  /**
   * Get language flag emoji
   */
  getLanguageFlag: (language: Language): string => {
    const flags = {
      [Language.ENGLISH]: '🇺🇸',
      [Language.ARABIC]: '🇸🇦',
      [Language.HINDI]: '🇮🇳'
    }
    return flags[language] || '🌐'
  }
}

// Export singleton instance
export const articleService = new ArticleService()
export default articleService 