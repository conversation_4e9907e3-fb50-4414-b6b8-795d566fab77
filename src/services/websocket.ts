import { toast } from 'react-hot-toast'

export interface WebSocketMessage {
  event: string
  data: any
  timestamp?: string
}

export interface WebSocketConfig {
  url: string
  token?: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  onMessage?: (message: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
}

export class WebSocketService {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private reconnectAttempts = 0
  private reconnectTimeoutId: NodeJS.Timeout | null = null
  private isConnecting = false
  private isIntentionallyClosed = false

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      ...config
    }
  }

  connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    this.isIntentionallyClosed = false

    try {
      // Build WebSocket URL with token authentication
      const wsUrl = new URL(this.config.url)
      if (this.config.token) {
        wsUrl.searchParams.set('token', this.config.token)
      }

      console.log('🔌 Connecting to WebSocket:', wsUrl.toString())
      this.ws = new WebSocket(wsUrl.toString())

      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)

    } catch (error) {
      console.error('❌ WebSocket connection error:', error)
      this.isConnecting = false
      this.handleReconnect()
    }
  }

  disconnect(): void {
    this.isIntentionallyClosed = true
    this.clearReconnectTimeout()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  send(message: WebSocketMessage): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket not connected, cannot send message:', message)
      return false
    }

    try {
      this.ws.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error('❌ Failed to send WebSocket message:', error)
      return false
    }
  }

  // Convenience methods for common events
  sendMessage(content: string, receivers?: string[]): boolean {
    return this.send({
      event: 'sendMessage',
      data: { content, receivers }
    })
  }

  joinRoom(roomId: string): boolean {
    return this.send({
      event: 'joinRoom',
      data: { roomId }
    })
  }

  leaveRoom(roomId: string): boolean {
    return this.send({
      event: 'leaveRoom',
      data: { roomId }
    })
  }

  sendTyping(roomId?: string): boolean {
    return this.send({
      event: 'typing',
      data: { roomId }
    })
  }

  sendStopTyping(roomId?: string): boolean {
    return this.send({
      event: 'stopTyping',
      data: { roomId }
    })
  }

  private handleOpen(): void {
    console.log('✅ WebSocket connected successfully')
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.clearReconnectTimeout()
    
    this.config.onConnect?.()
    toast.success('Connected to real-time updates')
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      console.log('📨 WebSocket message received:', message)
      
      this.config.onMessage?.(message)
      
      // Handle system messages
      if (message.event === 'error') {
        console.error('❌ WebSocket server error:', message.data)
        toast.error(`Server error: ${message.data.message}`)
      }
      
    } catch (error) {
      console.error('❌ Failed to parse WebSocket message:', error)
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('🔌 WebSocket connection closed:', event.code, event.reason)
    this.ws = null
    this.isConnecting = false
    
    this.config.onDisconnect?.()
    
    if (!this.isIntentionallyClosed) {
      toast.error('Connection lost. Attempting to reconnect...')
      this.handleReconnect()
    }
  }

  private handleError(event: Event): void {
    console.error('❌ WebSocket error:', event)
    this.isConnecting = false
    
    this.config.onError?.(event)
    
    if (!this.isIntentionallyClosed) {
      toast.error('Connection error occurred')
    }
  }

  private handleReconnect(): void {
    if (this.isIntentionallyClosed || 
        this.reconnectAttempts >= (this.config.maxReconnectAttempts || 5)) {
      console.log('🛑 Max reconnection attempts reached or intentionally closed')
      toast.error('Unable to establish connection. Please refresh the page.')
      return
    }

    this.reconnectAttempts++
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`)
    
    this.reconnectTimeoutId = setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval || 5000)
  }

  private clearReconnectTimeout(): void {
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId)
      this.reconnectTimeoutId = null
    }
  }

  // Getters
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  get connectionState(): string {
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING'
      case WebSocket.OPEN: return 'OPEN'
      case WebSocket.CLOSING: return 'CLOSING'
      case WebSocket.CLOSED: return 'CLOSED'
      default: return 'UNKNOWN'
    }
  }
}

// WebSocket Hook for React Components
import { useEffect, useRef, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'

export function useWebSocket(
  enabled: boolean = true,
  onMessage?: (message: WebSocketMessage) => void
) {
  const { user, token } = useAuth()
  const [isConnected, setIsConnected] = useState(false)
  const [connectionState, setConnectionState] = useState<string>('CLOSED')
  const wsRef = useRef<WebSocketService | null>(null)

  useEffect(() => {
    if (!enabled || !user || !token) {
      return
    }

    // Determine WebSocket URL based on environment
    let wsUrl: string
    if (import.meta.env.PROD) {
      // Production: Use secure WebSocket (wss://)
      const domain = window.location.hostname
      wsUrl = `wss://apis.${domain}/api/v2/socket/ws`
    } else {
      // Development: Use local development WebSocket
      wsUrl = 'wss://apis.dev.sehatti.app/api/v2/socket/ws'
    }

    console.log('🚀 Initializing WebSocket connection to:', wsUrl)

    const ws = new WebSocketService({
      url: wsUrl,
      token: token,
      onConnect: () => {
        setIsConnected(true)
        setConnectionState('OPEN')
      },
      onDisconnect: () => {
        setIsConnected(false)
        setConnectionState('CLOSED')
      },
      onError: (error) => {
        console.error('WebSocket error:', error)
        setConnectionState('ERROR')
      },
      onMessage: onMessage
    })

    wsRef.current = ws
    ws.connect()

    // Update connection state periodically
    const stateInterval = setInterval(() => {
      if (wsRef.current) {
        setConnectionState(wsRef.current.connectionState)
        setIsConnected(wsRef.current.isConnected)
      }
    }, 1000)

    return () => {
      clearInterval(stateInterval)
      ws.disconnect()
      wsRef.current = null
    }
  }, [enabled, user, token, onMessage])

  return {
    ws: wsRef.current,
    isConnected,
    connectionState,
    sendMessage: (content: string, receivers?: string[]) => 
      wsRef.current?.sendMessage(content, receivers) || false,
    joinRoom: (roomId: string) => 
      wsRef.current?.joinRoom(roomId) || false,
    leaveRoom: (roomId: string) => 
      wsRef.current?.leaveRoom(roomId) || false,
    sendTyping: (roomId?: string) => 
      wsRef.current?.sendTyping(roomId) || false,
    sendStopTyping: (roomId?: string) => 
      wsRef.current?.sendStopTyping(roomId) || false
  }
}

export default WebSocketService 