// Enhanced Media Service for Sehatti Modern Frontend
// Incorporates comprehensive media management from legacy system

import { getMediaServiceUrl } from '../config/api'
import { 
  Language,
  MediaStatus,
  Gender
} from '../types/media'
import type { 
  MediaResponse, 
  MediaListResponse, 
  MediaCreateRequest, 
  MediaUpdateRequest, 
  MediaFilters,
  FileUploadResponse,
  TranslationRequest,
  CaptionGenerationRequest
} from '../types/media'

const MEDIA_SERVICE_BASE_URL = getMediaServiceUrl()

// Enhanced interfaces for better type safety
interface UploadProgress {
  file_id: string
  file_name: string
  progress: number
  status: 'uploading' | 'completed' | 'error'
  error?: string
}

interface BulkUploadResponse {
  message: string
  files: FileUploadResponse[]
  failed?: { file_name: string; error: string }[]
}

interface MediaStats {
  total_content: number
  content_by_type: Record<string, number>
  content_by_status: Record<string, number>
  total_storage_used: number
  total_views: number
  engagement_metrics: {
    likes: number
    shares: number
    comments: number
  }
}

class MediaService {
  private baseURL = MEDIA_SERVICE_BASE_URL

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        detail: `HTTP ${response.status}`,
        message: 'Network error'
      }))
      throw new Error(error.detail || error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  private async requestFormData<T>(endpoint: string, formData: FormData, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
    const url = `${this.baseURL}${endpoint}`

    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Don't set Content-Type for FormData
      },
      ...options
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ 
        detail: `HTTP ${response.status}`,
        message: 'Network error'
      }))
      throw new Error(error.detail || error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  /**
   * Upload a single file with progress tracking
   */
  async uploadFile(
    file: File, 
    folder: string = 'media',
    onProgress?: (progress: number) => void
  ): Promise<FileUploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('folder', folder)

    const xhr = new XMLHttpRequest()
    
    return new Promise((resolve, reject) => {
      if (onProgress) {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            onProgress(progress)
          }
        }
      }

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText)
            resolve(response)
          } catch (e) {
            reject(new Error('Invalid response format'))
          }
        } else {
          try {
            const error = JSON.parse(xhr.responseText)
            reject(new Error(error.detail || 'Upload failed'))
          } catch (e) {
            reject(new Error(`HTTP ${xhr.status}`))
          }
        }
      }

      xhr.onerror = () => reject(new Error('Network error'))
      xhr.ontimeout = () => reject(new Error('Upload timeout'))

      const token = localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')
      xhr.open('POST', `${this.baseURL}/api/v1/media/upload`)
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`)
      }
      xhr.timeout = 300000 // 5 minutes
      xhr.send(formData)
    })
  }

  /**
   * Upload multiple files with batch progress tracking
   */
  async uploadMultipleFiles(
    files: File[], 
    folder: string = 'media',
    onProgress?: (progress: UploadProgress[]) => void
  ): Promise<BulkUploadResponse> {
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    formData.append('folder', folder)

    // Set up abort controller with extended timeout for multiple files
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 600000) // 10 minutes

    try {
      const response = await fetch(`${this.baseURL}/api/v1/media/upload-multiple`, {
        method: 'POST',
        body: formData,
        signal: controller.signal,
        headers: {
          ...(localStorage.getItem('hr-auth-token') && { 
            Authorization: `Bearer ${localStorage.getItem('hr-auth-token') || localStorage.getItem('auth-token')}` 
          })
        }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to upload files')
      }

      return await response.json()
    } finally {
      clearTimeout(timeoutId)
    }
  }

  /**
   * List content with advanced filtering and pagination
   */
  async listContent(params: MediaFilters = {}): Promise<MediaListResponse> {
    const searchParams = new URLSearchParams()
    
    if (params.hub_id) searchParams.append('hub_id', params.hub_id)
    if (params.status) searchParams.append('status', params.status)
    if (params.media_type) searchParams.append('media_type', params.media_type)
    if (params.language) searchParams.append('language', params.language)
    if (params.search) searchParams.append('search', params.search)
    if (params.limit) searchParams.append('limit', params.limit.toString())
    if (params.last_key) searchParams.append('last_key', params.last_key)

    return this.request<MediaListResponse>(`/api/v1/content?${searchParams}`)
  }

  /**
   * Get content by Hub ID with language support
   */
  async getContentByHub(
    hubId: string, 
    language: Language = Language.ENGLISH, 
    limit: number = 20, 
    lastKey?: string
  ): Promise<MediaListResponse> {
    const searchParams = new URLSearchParams()
    searchParams.append('language', language)
    searchParams.append('limit', limit.toString())
    if (lastKey) searchParams.append('last_key', lastKey)

    return this.request<MediaListResponse>(`/api/v1/content/hub/${hubId}?${searchParams}`)
  }

  /**
   * Get content by ID with language preference
   */
  async getContentById(contentId: string, language: Language = Language.ENGLISH): Promise<MediaResponse> {
    return this.request<MediaResponse>(`/api/v1/content/${contentId}?language=${language}`)
  }

  /**
   * Create new media content with comprehensive options
   */
  async createContent(data: MediaCreateRequest): Promise<MediaResponse> {
    const formData = new FormData()
    
    // Required fields
    formData.append('hub_id', data.hub_id)
    formData.append('title', data.title)
    formData.append('description', data.description)
    
    // Optional fields
    if (data.default_language) formData.append('default_language', data.default_language)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.gender) formData.append('gender', data.gender)
    if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString())
    if (data.generate_captions !== undefined) formData.append('generate_captions', data.generate_captions.toString())
    
    // File references (progressive upload)
    if (data.video_file_id) formData.append('video_file_id', data.video_file_id)
    if (data.thumbnail_file_id) formData.append('thumbnail_file_id', data.thumbnail_file_id)
    if (data.gallery_file_ids) formData.append('gallery_file_ids', data.gallery_file_ids.join(','))
    
    // Direct file uploads (legacy support)
    if (data.video) formData.append('video', data.video)
    if (data.thumbnail) formData.append('thumbnail', data.thumbnail)
    if (data.gallery) {
      data.gallery.forEach(file => formData.append('gallery', file))
    }

    return this.requestFormData<MediaResponse>('/api/v1/content', formData)
  }

  /**
   * Update existing media content
   */
  async updateContent(contentId: string, data: MediaUpdateRequest): Promise<MediaResponse> {
    const formData = new FormData()
    
    if (data.title) formData.append('title', data.title)
    if (data.description !== undefined) formData.append('description', data.description)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.gender) formData.append('gender', data.gender)
    if (data.status) formData.append('status', data.status)
    
    // File uploads
    if (data.thumbnail) formData.append('thumbnail', data.thumbnail)
    if (data.gallery) {
      data.gallery.forEach(file => formData.append('gallery', file))
    }

    return this.requestFormData<MediaResponse>(`/api/v1/content/${contentId}`, formData, { method: 'PATCH' })
  }

  /**
   * Delete content and associated files
   */
  async deleteContent(contentId: string): Promise<{ message: string; content_id: string }> {
    return this.request<{ message: string; content_id: string }>(`/api/v1/content/${contentId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Trigger content translation to multiple languages
   */
  async translateContent(contentId: string, request: TranslationRequest): Promise<{
    message: string
    content_id: string
    target_languages: string[]
  }> {
    return this.request(`/api/v1/content/${contentId}/translate`, {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Generate captions for video content
   */
  async generateCaptions(contentId: string, request: CaptionGenerationRequest): Promise<{
    message: string
    content_id: string
    languages: string[]
  }> {
    return this.request(`/api/v1/content/${contentId}/captions`, {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get captions for content in specified language
   */
  async getCaptions(contentId: string, language: Language, format: string = 'vtt'): Promise<{
    caption_url: string
    language: Language
    format: string
  }> {
    return this.request(`/api/v1/content/${contentId}/captions/${language}?format=${format}`)
  }

  /**
   * Get video streaming URL
   */
  getStreamingUrl(contentId: string): string {
    return `${this.baseURL}/api/v1/content/${contentId}/stream`
  }

  /**
   * Get video stream info
   */
  async getVideoStreamInfo(contentId: string): Promise<{ video_url: string }> {
    return this.request(`/api/v1/content/${contentId}/stream`)
  }

  /**
   * Bulk operations on multiple content items
   */
  async bulkOperation(
    contentIds: string[], 
    operation: 'activate' | 'deactivate' | 'delete' | 'archive',
    options?: { force?: boolean; reason?: string }
  ): Promise<{ success: string[]; failed: { id: string; error: string }[] }> {
    const formData = new FormData()
    formData.append('content_ids', contentIds.join(','))
    formData.append('operation', operation)
    if (options?.force) formData.append('force', 'true')
    if (options?.reason) formData.append('reason', options.reason)

    return this.requestFormData(`/api/v1/content/bulk`, formData)
  }

  /**
   * Search content across all hubs
   */
  async searchContent(
    query: string, 
    filters?: Partial<MediaFilters>
  ): Promise<MediaListResponse> {
    const searchParams = new URLSearchParams()
    searchParams.append('q', query)
    
    if (filters?.hub_id) searchParams.append('hub_id', filters.hub_id)
    if (filters?.status) searchParams.append('status', filters.status)
    if (filters?.language) searchParams.append('language', filters.language)
    if (filters?.limit) searchParams.append('limit', filters.limit.toString())

    return this.request<MediaListResponse>(`/api/v1/content/search?${searchParams}`)
  }

  /**
   * Get media statistics
   */
  async getMediaStats(hubId?: string): Promise<MediaStats> {
    const searchParams = new URLSearchParams()
    if (hubId) searchParams.append('hub_id', hubId)
    
    return this.request<MediaStats>(`/api/v1/content/stats?${searchParams}`)
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<{ 
    status: string
    service: string
    version: string
    timestamp: number
    checks: Record<string, string>
  }> {
    return this.request('/api/media/health')
  }

  /**
   * Legacy method for backward compatibility
   */
  async legacyListContent(params: any = {}): Promise<any> {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.set(key, value.toString())
      }
    })

    return this.request(`/api/v1/content/legacy?${searchParams}`)
  }
}

// Utility functions for media management
export const MediaServiceUtils = {
  /**
   * Format file size for display
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * Get content type icon
   */
  getContentTypeIcon: (type: string): string => {
    const typeMap: Record<string, string> = {
      'video': '🎥',
      'audio': '🎵',
      'image': '🖼️',
      'document': '📄',
      'article': '📰'
    }
    return typeMap[type] || '📄'
  },

  /**
   * Validate media file
   */
  validateMediaFile: (file: File, type: 'video' | 'image' | 'audio' = 'video'): string[] => {
    const errors: string[] = []
    const maxSizes = {
      video: 100 * 1024 * 1024, // 100MB
      image: 10 * 1024 * 1024,  // 10MB
      audio: 50 * 1024 * 1024   // 50MB
    }
    
    const allowedTypes = {
      video: ['video/mp4', 'video/webm', 'video/ogg'],
      image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      audio: ['audio/mp3', 'audio/wav', 'audio/ogg']
    }

    if (file.size > maxSizes[type]) {
      errors.push(`File size must be less than ${MediaServiceUtils.formatFileSize(maxSizes[type])}`)
    }

    if (!allowedTypes[type].includes(file.type)) {
      errors.push(`File type ${file.type} is not supported for ${type}`)
    }

    return errors
  },

  /**
   * Get language flag emoji
   */
  getLanguageFlag: (language: Language): string => {
    const flags = {
      [Language.ENGLISH]: '🇺🇸',
      [Language.ARABIC]: '🇸🇦',
      [Language.HINDI]: '🇮🇳'
    }
    return flags[language] || '🌐'
  },

  /**
   * Format duration from seconds
   */
  formatDuration: (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// Export singleton instance
export const mediaService = new MediaService()
export default mediaService 