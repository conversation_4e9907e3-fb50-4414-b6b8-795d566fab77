import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define avatar variants
const avatarVariants = cva(
  // Base styles
  "relative flex shrink-0 overflow-hidden rounded-full",
  {
    variants: {
      size: {
        xs: "h-8 w-8",
        sm: "h-10 w-10",
        md: "h-12 w-12",
        lg: "h-14 w-14",
        xl: "h-16 w-16",
        "2xl": "h-20 w-20",
      },
      border: {
        none: "",
        thin: "border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        gold: "border-2 border-sehatti-gold-500",
      },
      glow: {
        true: "shadow-gold-glow",
        false: "",
      },
    },
    defaultVariants: {
      size: "md",
      border: "none",
      glow: false,
    },
  }
);

// Avatar component props
export interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof avatarVariants> {
  src?: string;
  alt?: string;
  fallback?: React.ReactNode;
}

// Avatar component
const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, size, border, glow, src, alt, fallback, ...props }, ref) => {
    const [hasError, setHasError] = React.useState(false);

    return (
      <div
        ref={ref}
        className={cn(avatarVariants({ size, border, glow }), className)}
        {...props}
      >
        {src && !hasError ? (
          <img
            src={src}
            alt={alt || "Avatar"}
            className="h-full w-full object-cover"
            onError={() => setHasError(true)}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-sehatti-warm-gray-100 dark:bg-sehatti-warm-gray-800">
            {fallback || (
              <span className="font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
                {alt ? getInitials(alt) : "?"}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }
);

Avatar.displayName = "Avatar";

// Avatar group component props
export interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  limit?: number;
  total?: number;
}

// Avatar group component
const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
  ({ className, children, limit = 3, total, ...props }, ref) => {
    const childrenArray = React.Children.toArray(children);
    const visibleAvatars = limit ? childrenArray.slice(0, limit) : childrenArray;
    const remainingCount = total || childrenArray.length - visibleAvatars.length;

    return (
      <div
        ref={ref}
        className={cn("flex -space-x-2", className)}
        {...props}
      >
        {visibleAvatars}
        {remainingCount > 0 && (
          <div className="relative flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-sehatti-warm-gray-100 dark:bg-sehatti-warm-gray-800">
            <span className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
              +{remainingCount}
            </span>
          </div>
        )}
      </div>
    );
  }
);

AvatarGroup.displayName = "AvatarGroup";

// Helper function to get initials from a name
function getInitials(name: string): string {
  return name
    .split(" ")
    .map((part) => part[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

export { Avatar, AvatarGroup, avatarVariants }; 