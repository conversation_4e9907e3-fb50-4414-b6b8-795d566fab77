import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { FaSearch } from "react-icons/fa";

// Define search input variants
const searchInputVariants = cva(
  "flex w-full rounded-xl border-2 bg-white/70 dark:bg-sehatti-warm-gray-900/70 backdrop-blur-sm px-4 py-3 text-base transition-all duration-200 placeholder:text-sehatti-warm-gray-500 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-sehatti-warm-gray-300/60 dark:border-sehatti-warm-gray-600/60 focus:border-sehatti-gold-400 focus:bg-white dark:focus:bg-sehatti-warm-gray-900 focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)]",
        gold: "border-sehatti-gold-200/60 dark:border-sehatti-gold-700/60 focus:border-sehatti-gold-500 focus:bg-white dark:focus:bg-sehatti-warm-gray-900 focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)]",
      },
      size: {
        sm: "h-10 px-3 py-2 text-sm",
        md: "h-12 px-4 py-3 text-base",
        lg: "h-14 px-6 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

// Search input component props
export interface SearchInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof searchInputVariants> {
  onClear?: () => void;
  showClearButton?: boolean;
}

// Search input component
const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ 
    className, 
    variant, 
    size,
    placeholder = "🔍 Search...",
    onClear,
    showClearButton = true,
    value,
    ...props 
  }, ref) => {
    const handleClear = () => {
      if (onClear) {
        onClear();
      }
    };

    return (
      <div className="relative">
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-sehatti-warm-gray-400">
          <FaSearch className="w-4 h-4" />
        </div>
        
        <input
          ref={ref}
          type="text"
          placeholder={placeholder}
          value={value}
          className={cn(
            searchInputVariants({ variant, size }),
            "pl-10",
            showClearButton && value && "pr-10",
            className
          )}
          {...props}
        />
        
        {showClearButton && value && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-sehatti-warm-gray-400 hover:text-sehatti-warm-gray-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    );
  }
);

SearchInput.displayName = "SearchInput";

export { SearchInput, searchInputVariants }; 