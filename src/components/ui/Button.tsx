import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define button variants
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-xl text-sm font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        // Primary gold button with gradient
        default: "bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 text-white shadow-gold-md hover:shadow-gold-lg hover:-translate-y-0.5",
        
        // Secondary darker gold button
        secondary: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-700 text-white shadow-gold-md hover:shadow-gold-lg hover:-translate-y-0.5",
        
        // Outline button with gold border
        outline: "border-2 border-sehatti-gold-500 bg-white text-sehatti-gold-600 hover:bg-sehatti-gold-50 hover:border-sehatti-gold-600 transition-all duration-200 shadow-sm font-medium",
        
        // Ghost button with no background until hover
        ghost: "bg-transparent text-sehatti-gold-600 hover:bg-sehatti-gold-50",
        
        // Link style button
        link: "bg-transparent text-sehatti-gold-600 underline-offset-4 hover:underline shadow-none p-0 h-auto",
        
        // Destructive red button
        destructive: "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-[0_4px_15px_rgba(239,68,68,0.3)] hover:shadow-[0_8px_25px_rgba(239,68,68,0.4)] hover:-translate-y-0.5",
      },
      size: {
        default: "h-12 px-6 py-3 text-base",
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4 py-2 text-sm",
        lg: "h-14 px-8 text-lg",
        icon: "h-10 w-10",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
      fullWidth: false,
    },
  }
);

// Button component props
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    Omit<VariantProps<typeof buttonVariants>, "loading"> {
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  asChild?: boolean;
}

// Button component
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    fullWidth,
    isLoading = false,
    leftIcon,
    rightIcon,
    children, 
    disabled,
    asChild = false,
    ...props 
  }, ref) => {
    return (
      <button
        className={cn(
          buttonVariants({ 
            variant: isLoading && variant === "default" 
              ? "default" 
              : variant, 
            size, 
            loading: isLoading,
            fullWidth 
          }), 
          isLoading && variant === "default" && "bg-gradient-to-r from-sehatti-gold-500/50 to-sehatti-gold-600/50 shadow-gold-sm",
          className
        )}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <div 
            className="mr-2 h-[18px] w-[18px] animate-spin rounded-full border-2 border-white/30 border-t-white"
          />
        )}
        
        {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants };
