import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define tab list variants
const tabListVariants = cva(
  "inline-flex items-center justify-center",
  {
    variants: {
      variant: {
        // Default tabs with bottom border
        default: "border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        
        // Pill tabs with rounded corners
        pill: "p-1 bg-sehatti-warm-gray-100 dark:bg-sehatti-warm-gray-800 rounded-lg",
        
        // Underlined tabs with active indicator
        underlined: "border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        
        // Card tabs with borders
        card: "space-x-2",
      },
      size: {
        sm: "text-sm",
        md: "text-base",
        lg: "text-lg",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      fullWidth: false,
    },
  }
);

// Define tab variants
const tabVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sehatti-gold-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        // Default tabs with bottom border
        default: "border-b-2 border-transparent px-4 py-2 hover:text-sehatti-gold-600 data-[state=active]:border-sehatti-gold-500 data-[state=active]:text-sehatti-gold-700 dark:hover:text-sehatti-gold-400 dark:data-[state=active]:border-sehatti-gold-400 dark:data-[state=active]:text-sehatti-gold-300",
        
        // Pill tabs with rounded corners
        pill: "rounded-md px-4 py-2 hover:bg-sehatti-warm-gray-200/50 dark:hover:bg-sehatti-warm-gray-700/50 data-[state=active]:bg-white dark:data-[state=active]:bg-sehatti-warm-gray-900 data-[state=active]:text-sehatti-gold-700 dark:data-[state=active]:text-sehatti-gold-300 data-[state=active]:shadow-sm",
        
        // Underlined tabs with active indicator
        underlined: "relative px-4 py-2 after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:scale-x-0 after:bg-sehatti-gold-500 after:transition-transform hover:text-sehatti-gold-600 data-[state=active]:text-sehatti-gold-700 data-[state=active]:after:scale-x-100 dark:after:bg-sehatti-gold-400 dark:hover:text-sehatti-gold-400 dark:data-[state=active]:text-sehatti-gold-300",
        
        // Card tabs with borders
        card: "rounded-md border border-transparent px-4 py-2 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 data-[state=active]:border-sehatti-warm-gray-200 data-[state=active]:bg-white dark:data-[state=active]:border-sehatti-warm-gray-700 dark:data-[state=active]:bg-sehatti-warm-gray-900 data-[state=active]:text-sehatti-gold-700 dark:data-[state=active]:text-sehatti-gold-300",
      },
      size: {
        sm: "text-sm",
        md: "text-base",
        lg: "text-lg",
      },
      fullWidth: {
        true: "flex-1",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      fullWidth: false,
    },
  }
);

// Tabs container component
interface TabsProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}

const Tabs = React.forwardRef<HTMLDivElement, TabsProps>(
  ({ defaultValue, value, onValueChange, className, ...props }, ref) => {
    const [selectedTab, setSelectedTab] = React.useState<string>(defaultValue || "");

    // Handle controlled and uncontrolled tabs
    const handleTabChange = (tabValue: string) => {
      if (onValueChange) {
        onValueChange(tabValue);
      } else {
        setSelectedTab(tabValue);
      }
    };

    // Context to share tab state with children
    const tabsContext = React.useMemo(
      () => ({
        value: value || selectedTab,
        onValueChange: handleTabChange,
      }),
      [value, selectedTab, handleTabChange]
    );

    return (
      <TabsContext.Provider value={tabsContext}>
        <div ref={ref} className={cn("", className)} {...props} />
      </TabsContext.Provider>
    );
  }
);

Tabs.displayName = "Tabs";

// Tab list component
interface TabListProps extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof tabListVariants> {}

const TabList = React.forwardRef<HTMLDivElement, TabListProps>(
  ({ className, variant, size, fullWidth, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="tablist"
        className={cn(tabListVariants({ variant, size, fullWidth }), className)}
        {...props}
      />
    );
  }
);

TabList.displayName = "TabList";

// Tab component
interface TabProps extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof tabVariants> {
  value: string;
}

const Tab = React.forwardRef<HTMLButtonElement, TabProps>(
  ({ className, variant, size, fullWidth, value, ...props }, ref) => {
    const { value: selectedValue, onValueChange } = React.useContext(TabsContext);
    const isSelected = selectedValue === value;

    return (
      <button
        ref={ref}
        role="tab"
        type="button"
        aria-selected={isSelected}
        data-state={isSelected ? "active" : "inactive"}
        onClick={() => onValueChange(value)}
        className={cn(tabVariants({ variant, size, fullWidth }), className)}
        {...props}
      />
    );
  }
);

Tab.displayName = "Tab";

// Tab panels container
const TabPanels = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  return <div ref={ref} className={cn("mt-2", className)} {...props} />;
});

TabPanels.displayName = "TabPanels";

// Tab panel component
interface TabPanelProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}

const TabPanel = React.forwardRef<HTMLDivElement, TabPanelProps>(
  ({ className, value, ...props }, ref) => {
    const { value: selectedValue } = React.useContext(TabsContext);
    const isSelected = selectedValue === value;

    if (!isSelected) return null;

    return (
      <div
        ref={ref}
        role="tabpanel"
        tabIndex={0}
        className={cn("focus:outline-none", className)}
        {...props}
      />
    );
  }
);

TabPanel.displayName = "TabPanel";

// Tabs context
interface TabsContextValue {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = React.createContext<TabsContextValue>({
  value: "",
  onValueChange: () => {},
});

export { Tabs, TabList, Tab, TabPanels, TabPanel }; 