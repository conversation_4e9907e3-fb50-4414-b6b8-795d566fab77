import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define tooltip variants
const tooltipVariants = cva(
  "z-50 overflow-hidden rounded-md px-3 py-1.5 text-xs animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
  {
    variants: {
      variant: {
        // Default tooltip style
        default: "bg-sehatti-warm-gray-900 text-sehatti-warm-gray-50",
        
        // Gold tooltip style
        gold: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-500 text-white",
        
        // Light tooltip style
        light: "bg-white text-sehatti-warm-gray-900 border border-sehatti-warm-gray-200 shadow-sm",
        
        // Glass tooltip style
        glass: "bg-white/80 backdrop-blur-sm border border-sehatti-warm-gray-200/50 text-sehatti-warm-gray-900 dark:bg-sehatti-warm-gray-800/80 dark:border-sehatti-warm-gray-700/50 dark:text-sehatti-warm-gray-50",
      },
      position: {
        top: "",
        bottom: "",
        left: "",
        right: "",
      },
    },
    defaultVariants: {
      variant: "default",
      position: "top",
    },
  }
);

// Tooltip component props
export interface TooltipProps {
  content: React.ReactNode;
  variant?: VariantProps<typeof tooltipVariants>["variant"];
  position?: VariantProps<typeof tooltipVariants>["position"];
  className?: string;
  children: React.ReactNode;
}

// Tooltip component
const Tooltip = ({
  content,
  variant,
  position,
  className,
  children,
}: TooltipProps) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [coords, setCoords] = React.useState({ x: 0, y: 0 });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const childRef = React.useRef<HTMLDivElement>(null);

  // Show tooltip
  const showTooltip = () => {
    if (childRef.current) {
      const rect = childRef.current.getBoundingClientRect();
      
      // Calculate position based on the position prop
      let x = 0;
      let y = 0;
      
      switch (position) {
        case "top":
          x = rect.left + rect.width / 2;
          y = rect.top;
          break;
        case "bottom":
          x = rect.left + rect.width / 2;
          y = rect.bottom;
          break;
        case "left":
          x = rect.left;
          y = rect.top + rect.height / 2;
          break;
        case "right":
          x = rect.right;
          y = rect.top + rect.height / 2;
          break;
        default:
          x = rect.left + rect.width / 2;
          y = rect.top;
      }
      
      setCoords({ x, y });
      setIsVisible(true);
    }
  };

  // Hide tooltip
  const hideTooltip = () => {
    setIsVisible(false);
  };

  // Calculate tooltip position styles
  const getPositionStyles = () => {
    if (!tooltipRef.current) return {};
    
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const offset = 8; // Distance from the element
    
    switch (position) {
      case "top":
        return {
          left: `${coords.x - tooltipRect.width / 2}px`,
          top: `${coords.y - tooltipRect.height - offset}px`,
        };
      case "bottom":
        return {
          left: `${coords.x - tooltipRect.width / 2}px`,
          top: `${coords.y + offset}px`,
        };
      case "left":
        return {
          left: `${coords.x - tooltipRect.width - offset}px`,
          top: `${coords.y - tooltipRect.height / 2}px`,
        };
      case "right":
        return {
          left: `${coords.x + offset}px`,
          top: `${coords.y - tooltipRect.height / 2}px`,
        };
      default:
        return {
          left: `${coords.x - tooltipRect.width / 2}px`,
          top: `${coords.y - tooltipRect.height - offset}px`,
        };
    }
  };

  return (
    <>
      <div
        ref={childRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="inline-block"
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className={cn(tooltipVariants({ variant, position }), className)}
          style={{
            position: "fixed",
            ...getPositionStyles(),
            zIndex: 9999,
          }}
          role="tooltip"
        >
          {content}
        </div>
      )}
    </>
  );
};

export { Tooltip, tooltipVariants }; 