import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { Select } from "./Select";

// Define filter select variants
const filterSelectVariants = cva(
  "w-full bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-sehatti-warm-gray-300/60 dark:border-sehatti-warm-gray-600/60 focus:border-sehatti-gold-400",
        gold: "border-sehatti-gold-200/60 dark:border-sehatti-gold-700/60 focus:border-sehatti-gold-500",
        glass: "bg-white/80 dark:bg-sehatti-warm-gray-800/80 backdrop-blur-sm border-sehatti-warm-gray-200/50 dark:border-sehatti-warm-gray-700/50",
      },
      size: {
        sm: "h-10 px-3 py-2 text-sm",
        md: "h-12 px-4 py-3 text-base",
        lg: "h-14 px-6 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

// Filter select option interface
export interface FilterOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Filter select component props
export interface FilterSelectProps
  extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, "size">,
    VariantProps<typeof filterSelectVariants> {
  options: FilterOption[];
  placeholder?: string;
  label?: string;
  icon?: React.ReactNode;
}

// Filter select component
const FilterSelect = React.forwardRef<HTMLSelectElement, FilterSelectProps>(
  ({ 
    className, 
    variant, 
    size,
    options,
    placeholder = "Select...",
    label,
    icon,
    ...props 
  }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label className="flex items-center gap-2 text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300">
            {icon && (
              <div className="text-sehatti-gold-600 dark:text-sehatti-gold-400">
                {icon}
              </div>
            )}
            {label}
          </label>
        )}
        
        <Select
          ref={ref}
          variant={variant === "glass" ? "default" : variant}
          selectSize={size}
          className={cn(filterSelectVariants({ variant, size }), className)}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </Select>
      </div>
    );
  }
);

FilterSelect.displayName = "FilterSelect";

export { FilterSelect, filterSelectVariants }; 