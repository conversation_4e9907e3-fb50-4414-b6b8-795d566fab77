import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define badge variants
const badgeVariants = cva(
  // Base styles
  "inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors",
  {
    variants: {
      variant: {
        // Default gold badge
        default: "bg-sehatti-gold-500 text-white",
        
        // Secondary darker gold badge
        secondary: "bg-sehatti-gold-600 text-white",
        
        // Outline badge with gold border
        outline: "bg-transparent border border-sehatti-gold-300 text-sehatti-gold-700",
        
        // Subtle badge with light gold background
        subtle: "bg-sehatti-gold-50 text-sehatti-gold-700",
        
        // Destructive red badge
        destructive: "bg-red-500 text-white",
        
        // Success green badge
        success: "bg-green-500 text-white",
        
        // Info blue badge
        info: "bg-blue-500 text-white",
        
        // Warning yellow badge
        warning: "bg-amber-500 text-white",
      },
      size: {
        sm: "text-[10px] px-1.5 py-0.5",
        md: "text-xs px-2.5 py-0.5",
        lg: "text-sm px-3 py-1",
      },
      glow: {
        // Add gold glow effect
        true: "shadow-gold-glow",
        false: "",
      },
      gradient: {
        // Add gold gradient effect
        true: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      glow: false,
      gradient: false,
    },
  }
);

// Badge component props
export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

// Badge component
function Badge({
  className,
  variant,
  size,
  glow,
  gradient,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size, glow, gradient }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants }; 