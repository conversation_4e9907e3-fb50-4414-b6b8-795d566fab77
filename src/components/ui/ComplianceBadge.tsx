import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Security icon component
const SecurityIcon = () => (
  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
  </svg>
);

// Define compliance badge variants
const complianceBadgeVariants = cva(
  "inline-flex items-center gap-1.5 px-3 py-2 rounded-lg border transition-colors duration-200",
  {
    variants: {
      variant: {
        default: "bg-[rgba(210,179,122,0.05)] border-[rgba(210,179,122,0.1)] text-[#6b7280]",
        gold: "bg-[rgba(210,179,122,0.1)] border-[rgba(210,179,122,0.2)] text-[#d2b37a]",
        subtle: "bg-gray-50 border-gray-200 text-gray-600",
      },
      size: {
        sm: "text-[10px] px-2 py-1",
        default: "text-[11px] px-3 py-2", // 11px to match original
        lg: "text-xs px-4 py-2",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// Compliance badge component props
export interface ComplianceBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof complianceBadgeVariants> {
  children: React.ReactNode;
  icon?: React.ReactNode;
}

// Compliance badge component
const ComplianceBadge = React.forwardRef<HTMLDivElement, ComplianceBadgeProps>(
  ({ className, variant, size, children, icon, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(complianceBadgeVariants({ variant, size }), className)}
        {...props}
      >
        {icon || <SecurityIcon />}
        <span className="font-semibold">{children}</span>
      </div>
    );
  }
);

ComplianceBadge.displayName = "ComplianceBadge";

export { ComplianceBadge, complianceBadgeVariants }; 