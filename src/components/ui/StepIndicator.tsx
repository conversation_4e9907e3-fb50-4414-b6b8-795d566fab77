import React from 'react'
import { FaCheck } from 'react-icons/fa'
import type { IconType } from 'react-icons'

export interface Step {
  id: string
  title: string
  icon: IconType
  description?: string
}

interface StepIndicatorProps {
  steps: Step[]
  currentStepIndex: number
  className?: string
  orientation?: 'horizontal' | 'vertical'
  size?: 'sm' | 'md' | 'lg'
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStepIndex,
  className = '',
  orientation = 'horizontal',
  size = 'md'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'w-6 h-6',
          icon: 'w-3 h-3',
          text: 'text-xs'
        }
      case 'md':
        return {
          container: 'w-8 h-8',
          icon: 'w-4 h-4',
          text: 'text-sm'
        }
      case 'lg':
        return {
          container: 'w-10 h-10',
          icon: 'w-5 h-5',
          text: 'text-base'
        }
      default:
        return {
          container: 'w-8 h-8',
          icon: 'w-4 h-4',
          text: 'text-sm'
        }
    }
  }

  const sizeClasses = getSizeClasses()

  const getStepStatus = (index: number) => {
    if (index < currentStepIndex) return 'completed'
    if (index === currentStepIndex) return 'current'
    return 'upcoming'
  }

  const getStepClasses = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 text-white border-green-500'
      case 'current':
        return 'bg-sehatti-gold text-white border-sehatti-gold'
      case 'upcoming':
        return 'bg-gray-100 text-gray-400 border-gray-300'
      default:
        return 'bg-gray-100 text-gray-400 border-gray-300'
    }
  }

  const containerClass = orientation === 'horizontal' 
    ? 'flex justify-between items-start' 
    : 'flex flex-col space-y-4'

  const stepContainerClass = orientation === 'horizontal'
    ? 'flex flex-col items-center'
    : 'flex items-center space-x-3'

  return (
    <div className={`${containerClass} ${className}`}>
      {steps.map((step, index) => {
        const Icon = step.icon
        const status = getStepStatus(index)
        const stepClasses = getStepClasses(status)
        
        return (
          <div key={step.id} className={stepContainerClass}>
            <div className={`
              ${sizeClasses.container} rounded-full flex items-center justify-center 
              transition-all duration-300 border-2 ${stepClasses}
            `}>
              {status === 'completed' ? (
                <FaCheck className={sizeClasses.icon} />
              ) : (
                <Icon className={sizeClasses.icon} />
              )}
            </div>
            
            <div className={orientation === 'horizontal' ? 'text-center mt-2' : 'flex-1'}>
              <span className={`
                ${sizeClasses.text} font-medium block
                ${status === 'current' ? 'text-sehatti-gold' : 
                  status === 'completed' ? 'text-green-600' : 'text-gray-500'}
              `}>
                {step.title}
              </span>
              {step.description && orientation === 'vertical' && (
                <span className={`${sizeClasses.text} text-gray-500 block mt-1`}>
                  {step.description}
                </span>
              )}
            </div>

            {/* Connector line for vertical orientation */}
            {orientation === 'vertical' && index < steps.length - 1 && (
              <div className={`
                w-px h-8 ml-4 
                ${index < currentStepIndex ? 'bg-green-500' : 'bg-gray-300'}
              `} />
            )}
          </div>
        )
      })}
    </div>
  )
} 