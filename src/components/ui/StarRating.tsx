import React, { useState } from 'react'
import { FaStar } from 'react-icons/fa'

interface StarRatingProps {
  rating: number
  onRatingChange?: (rating: number) => void
  maxRating?: number
  size?: 'sm' | 'md' | 'lg'
  readonly?: boolean
  showValue?: boolean
  className?: string
  color?: 'gold' | 'yellow' | 'orange' | 'red'
}

export const StarRating: React.FC<StarRatingProps> = ({
  rating,
  onRatingChange,
  maxRating = 5,
  size = 'md',
  readonly = false,
  showValue = false,
  className = '',
  color = 'gold'
}) => {
  const [hoveredRating, setHoveredRating] = useState(0)

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-5 h-5 sm:w-4 sm:h-4'
      case 'md':
        return 'w-6 h-6 sm:w-5 sm:h-5'
      case 'lg':
        return 'w-8 h-8 sm:w-6 sm:h-6'
      default:
        return 'w-6 h-6 sm:w-5 sm:h-5'
    }
  }

  const getSpacingClasses = () => {
    switch (size) {
      case 'sm':
        return 'space-x-1 sm:space-x-1'
      case 'md':
        return 'space-x-2 sm:space-x-1'
      case 'lg':
        return 'space-x-3 sm:space-x-2'
      default:
        return 'space-x-2 sm:space-x-1'
    }
  }

  const getColorClasses = (filled: boolean) => {
    if (!filled) return 'text-gray-300'
    
    switch (color) {
      case 'gold':
        return 'text-sehatti-gold'
      case 'yellow':
        return 'text-yellow-400'
      case 'orange':
        return 'text-orange-400'
      case 'red':
        return 'text-red-400'
      default:
        return 'text-sehatti-gold'
    }
  }

  const handleStarClick = (starRating: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starRating)
    }
  }

  const handleStarHover = (starRating: number) => {
    if (!readonly) {
      setHoveredRating(starRating)
    }
  }

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoveredRating(0)
    }
  }

  const getRatingText = (rating: number) => {
    if (rating === 0) return 'No rating'
    if (rating === 1) return 'Poor'
    if (rating === 2) return 'Fair'
    if (rating === 3) return 'Good'
    if (rating === 4) return 'Very Good'
    if (rating === 5) return 'Excellent'
    return `${rating} stars`
  }

  return (
    <div className={`flex items-center ${getSpacingClasses()} ${className}`}>
      <div 
        className={`flex items-center ${getSpacingClasses()}`}
        onMouseLeave={handleMouseLeave}
      >
        {[...Array(maxRating)].map((_, index) => {
          const starRating = index + 1
          const isFilled = starRating <= (hoveredRating || rating)
          
          return (
            <FaStar
              key={index}
              className={`
                ${getSizeClasses()} 
                ${getColorClasses(isFilled)}
                ${readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110 active:scale-95 touch-manipulation'}
                transition-all duration-200
                ${!readonly ? 'p-1 -m-1' : ''}
              `}
              onClick={() => handleStarClick(starRating)}
              onMouseEnter={() => handleStarHover(starRating)}
            />
          )
        })}
      </div>
      
      {showValue && (
        <div className="flex items-center space-x-1 sm:space-x-2 ml-2 sm:ml-3">
          <span className="text-sm sm:text-base font-medium text-gray-700">
            {rating.toFixed(1)}
          </span>
          <span className="text-xs sm:text-sm text-gray-500">
            ({getRatingText(rating)})
          </span>
        </div>
      )}
    </div>
  )
} 