import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define select variants
const selectVariants = cva(
  "flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        // Default select style
        default: "border-sehatti-warm-gray-200 focus-visible:ring-sehatti-gold-500 dark:border-sehatti-warm-gray-700",
        
        // Gold select style
        gold: "border-sehatti-gold-200 focus-visible:ring-sehatti-gold-500 dark:border-sehatti-gold-800/30",
        
        // Outline select style
        outline: "border-sehatti-warm-gray-200 bg-transparent hover:bg-sehatti-warm-gray-50 focus-visible:ring-sehatti-gold-500 dark:border-sehatti-warm-gray-700 dark:hover:bg-sehatti-warm-gray-800/50",
        
        // Underlined select style
        underlined: "rounded-none border-x-0 border-t-0 border-b-2 border-sehatti-warm-gray-200 px-1 hover:border-sehatti-gold-400 focus-visible:border-sehatti-gold-500 focus-visible:ring-0 dark:border-sehatti-warm-gray-700",
      },
      selectSize: {
        sm: "h-8 text-xs px-2.5 py-1.5",
        md: "h-10 text-sm px-3 py-2",
        lg: "h-12 text-base px-4 py-2.5",
      },
      state: {
        default: "",
        error: "border-red-500 focus-visible:ring-red-500",
        success: "border-green-500 focus-visible:ring-green-500",
      },
    },
    defaultVariants: {
      variant: "default",
      selectSize: "md",
      state: "default",
    },
  }
);

// Select component props
export interface SelectProps
  extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, "size"> {
  label?: string;
  helperText?: string;
  error?: string;
  variant?: VariantProps<typeof selectVariants>["variant"];
  selectSize?: VariantProps<typeof selectVariants>["selectSize"];
  state?: VariantProps<typeof selectVariants>["state"];
}

// Select component
const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ 
    className, 
    variant, 
    selectSize, 
    state, 
    label, 
    helperText, 
    error, 
    children, 
    ...props 
  }, ref) => {
    // Determine if the select has an error
    const hasError = !!error || state === "error";
    
    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={props.id} 
            className={cn(
              "block text-sm font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100",
              props.disabled && "opacity-50"
            )}
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          <select
            title="Select an option"
            ref={ref}
            className={cn(
              selectVariants({ 
                variant, 
                selectSize, 
                state: hasError ? "error" : state,
              }),
              "appearance-none pr-10",
              className
            )}
            {...props}
          >
            {children}
          </select>
          
          {/* Dropdown arrow */}
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <svg 
              className="h-4 w-4 text-sehatti-warm-gray-500" 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        
        {(helperText || error) && (
          <p className={cn(
            "text-xs",
            error 
              ? "text-red-500" 
              : "text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400"
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = "Select";

// Select option component
const SelectOption = React.forwardRef<
  HTMLOptionElement,
  React.OptionHTMLAttributes<HTMLOptionElement>
>(({ className, ...props }, ref) => (
  <option
    ref={ref}
    className={cn("text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100", className)}
    {...props}
  />
));

SelectOption.displayName = "SelectOption";

// Select optgroup component
const SelectOptGroup = React.forwardRef<
  HTMLOptGroupElement,
  React.OptgroupHTMLAttributes<HTMLOptGroupElement>
>(({ className, ...props }, ref) => (
  <optgroup
    ref={ref}
    className={cn("text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100", className)}
    {...props}
  />
));

SelectOptGroup.displayName = "SelectOptGroup";

export { Select, SelectOption, SelectOptGroup, selectVariants }; 