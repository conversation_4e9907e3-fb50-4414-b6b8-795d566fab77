import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { Button } from "./Button";

// Define empty state variants
const emptyStateVariants = cva(
  "text-center py-8 sm:py-12 px-4",
  {
    variants: {
      variant: {
        default: "",
        subtle: "bg-sehatti-warm-gray-50/50 dark:bg-sehatti-warm-gray-900/50 rounded-lg",
        bordered: "border-2 border-dashed border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg",
        card: "bg-white dark:bg-sehatti-warm-gray-900 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg shadow-sm",
      },
      size: {
        sm: "py-6 sm:py-8",
        md: "py-8 sm:py-12",
        lg: "py-12 sm:py-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

// Icon variants
const iconVariants = cva(
  "mx-auto mb-4 text-sehatti-warm-gray-400",
  {
    variants: {
      size: {
        sm: "w-8 h-8 sm:w-10 sm:h-10",
        md: "w-12 h-12 sm:w-16 sm:h-16",
        lg: "w-16 h-16 sm:w-20 sm:h-20",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
);

// Empty state component props
export interface EmptyStateProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof emptyStateVariants> {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    variant?: "default" | "outline" | "ghost";
  };
  iconSize?: VariantProps<typeof iconVariants>["size"];
}

// Empty state component
const EmptyState = React.forwardRef<HTMLDivElement, EmptyStateProps>(
  ({ 
    className, 
    variant, 
    size,
    icon,
    title,
    description,
    action,
    iconSize,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(emptyStateVariants({ variant, size }), className)}
        {...props}
      >
        {icon && (
          <div className={cn(iconVariants({ size: iconSize || size }))}>
            {icon}
          </div>
        )}
        
        <h3 className="text-lg sm:text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-2">
          {title}
        </h3>
        
        {description && (
          <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-6 max-w-md mx-auto">
            {description}
          </p>
        )}
        
        {action && (
          <Button
            onClick={action.onClick}
            disabled={action.disabled}
            variant={action.variant || "default"}
            size="lg"
            className="w-full sm:w-auto"
          >
            {action.label}
          </Button>
        )}
      </div>
    );
  }
);

EmptyState.displayName = "EmptyState";

export { EmptyState, emptyStateVariants }; 