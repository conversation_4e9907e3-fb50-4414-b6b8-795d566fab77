import * as React from "react";
import { Input, type InputProps } from "./Input";
import { cn } from "@/lib/utils";

// Icons for show/hide password
const EyeIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

const EyeOffIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
    <line x1="1" y1="1" x2="23" y2="23"/>
  </svg>
);

// Password input component props
export interface PasswordInputProps extends Omit<InputProps, "type" | "rightElement"> {
  showPasswordToggle?: boolean;
}

// Password input component
const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ showPasswordToggle = true, className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    const toggleButton = showPasswordToggle ? (
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className={cn(
          "p-2 rounded-md transition-colors duration-200 text-[#6b7280] hover:text-[#d2b37a] focus:outline-none focus:text-[#d2b37a]",
          "flex items-center justify-center"
        )}
        tabIndex={-1}
      >
        {showPassword ? <EyeOffIcon /> : <EyeIcon />}
      </button>
    ) : undefined;

    return (
      <Input
        {...props}
        ref={ref}
        type={showPassword ? "text" : "password"}
        rightElement={toggleButton}
        className={className}
      />
    );
  }
);

PasswordInput.displayName = "PasswordInput";

export { PasswordInput }; 