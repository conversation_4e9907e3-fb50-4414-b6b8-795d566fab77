import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define alert variants
const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4",
  {
    variants: {
      variant: {
        default: "bg-white dark:bg-sehatti-warm-gray-950 text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-50 border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        gold: "bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 text-sehatti-gold-800 dark:text-sehatti-gold-300 border-sehatti-gold-200 dark:border-sehatti-gold-800/30",
        destructive: "bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800/30",
        success: "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800/30",
        warning: "bg-amber-50 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 border-amber-200 dark:border-amber-800/30",
        info: "bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800/30",
        glass: "bg-white/80 dark:bg-sehatti-warm-gray-950/80 backdrop-blur-sm border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        goldGlass: "bg-sehatti-gold-50/80 dark:bg-sehatti-gold-900/20 backdrop-blur-sm border-sehatti-gold-200 dark:border-sehatti-gold-800/30",
      },
      size: {
        sm: "p-3 text-sm",
        md: "p-4",
        lg: "p-5 text-lg",
      },
      withIcon: {
        true: "[&>svg~*]:pl-7 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4",
        false: "",
      },
      glow: {
        true: "shadow-gold-glow",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      withIcon: false,
      glow: false,
    },
  }
);

// Alert component props
export interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertVariants> {
  icon?: React.ReactNode;
  title?: string;
  description?: React.ReactNode;
  action?: React.ReactNode;
}

// Alert component
const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className, variant, size, withIcon, glow, icon, title, description, action, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="alert"
        className={cn(alertVariants({ variant, size, withIcon: !!icon, glow }), className)}
        {...props}
      >
        {icon && <span className="mr-3">{icon}</span>}
        
        <div className="flex flex-col space-y-1">
          {title && (
            <h5 className="font-medium leading-none tracking-tight">
              {title}
            </h5>
          )}
          
          {description && (
            <div className={cn("text-sm opacity-90", title ? "mt-1" : "")}>
              {description}
            </div>
          )}
          
          {children}
        </div>
        
        {action && (
          <div className="mt-3 flex items-center justify-end">
            {action}
          </div>
        )}
      </div>
    );
  }
);

Alert.displayName = "Alert";

export { Alert, alertVariants }; 