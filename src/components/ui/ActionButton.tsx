import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { Button } from "./Button";

// Define action button variants
const actionButtonVariants = cva(
  "flex items-center gap-2 font-medium transition-all duration-200",
  {
    variants: {
      intent: {
        create: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-500 text-white hover:from-sehatti-gold-700 hover:to-sehatti-gold-600",
        view: "bg-sehatti-gold-50 text-sehatti-gold-700 border border-sehatti-gold-200 hover:bg-sehatti-gold-100 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 dark:border-sehatti-gold-800/30",
        edit: "bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800/30",
        delete: "bg-red-50 text-red-700 border border-red-200 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800/30",
        upload: "bg-green-50 text-green-700 border border-green-200 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800/30",
        download: "bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800/30",
      },
      buttonSize: {
        sm: "px-3 py-2 text-sm h-8",
        md: "px-4 py-2.5 text-sm h-10",
        lg: "px-6 py-3 text-base h-12",
      },
      shape: {
        rounded: "rounded-lg",
        pill: "rounded-full",
        square: "rounded-md",
      },
      glow: {
        true: "shadow-md hover:shadow-lg",
        false: "",
      },
    },
    defaultVariants: {
      intent: "create",
      buttonSize: "md",
      shape: "rounded",
      glow: false,
    },
  }
);

// Action button component props
export interface ActionButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "size">,
    VariantProps<typeof actionButtonVariants> {
  icon?: React.ReactNode;
  children: React.ReactNode;
  isLoading?: boolean;
  fullWidth?: boolean;
}

// Action button component
const ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(
  ({ 
    className, 
    intent,
    buttonSize,
    shape,
    glow,
    icon,
    children,
    isLoading = false,
    fullWidth = false,
    ...props 
  }, ref) => {
    // Map intent to Button variant for consistency
    const getButtonVariant = () => {
      switch (intent) {
        case 'delete':
          return 'destructive';
        case 'view':
        case 'edit':
        case 'upload':
        case 'download':
          return 'outline';
        case 'create':
        default:
          return 'default';
      }
    };

    return (
      <Button
        ref={ref}
        variant={getButtonVariant()}
        size={buttonSize}
        isLoading={isLoading}
        fullWidth={fullWidth}
        className={cn(actionButtonVariants({ intent, buttonSize, shape, glow }), className)}
        {...props}
      >
        {!isLoading && icon && (
          <span className="flex-shrink-0">
            {icon}
          </span>
        )}
        <span>{children}</span>
      </Button>
    );
  }
);

ActionButton.displayName = "ActionButton";

export { ActionButton, actionButtonVariants }; 