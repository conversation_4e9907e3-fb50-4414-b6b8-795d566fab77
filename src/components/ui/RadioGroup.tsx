import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define radio group variants
const radioGroupVariants = cva(
  "space-y-3",
  {
    variants: {
      orientation: {
        vertical: "space-y-3",
        horizontal: "flex flex-wrap gap-4 space-y-0",
      },
      size: {
        sm: "space-y-2",
        default: "space-y-3",
        lg: "space-y-4",
      },
    },
    defaultVariants: {
      orientation: "vertical",
      size: "default",
    },
  }
);

// Define radio item variants
const radioItemVariants = cva(
  "relative flex items-start cursor-pointer p-4 border rounded-lg transition-all duration-200 hover:border-sehatti-gold-300 hover:bg-sehatti-gold-50/50 touch-manipulation active:scale-95",
  {
    variants: {
      checked: {
        true: "border-sehatti-gold bg-sehatti-gold-50 ring-1 ring-sehatti-gold-200",
        false: "border-gray-200 bg-white hover:border-sehatti-gold-200",
      },
      size: {
        sm: "p-3 min-h-[3rem]",
        default: "p-4 min-h-[3.5rem]",
        lg: "p-5 min-h-[4rem]",
      },
      disabled: {
        true: "opacity-50 cursor-not-allowed hover:border-gray-200 hover:bg-white active:scale-100",
        false: "",
      },
    },
    defaultVariants: {
      checked: false,
      size: "default",
      disabled: false,
    },
  }
);

// Radio button indicator variants
const radioIndicatorVariants = cva(
  "flex-shrink-0 rounded-full border-2 transition-all duration-200 flex items-center justify-center",
  {
    variants: {
      size: {
        sm: "w-5 h-5 border-2",
        default: "w-6 h-6 border-2",
        lg: "w-7 h-7 border-2",
      },
      checked: {
        true: "border-sehatti-gold bg-sehatti-gold",
        false: "border-gray-400 bg-white",
      },
      disabled: {
        true: "border-gray-300 bg-gray-100",
        false: "",
      },
    },
    defaultVariants: {
      size: "default",
      checked: false,
      disabled: false,
    },
  }
);

// Radio option interface
export interface RadioOption {
  value: string | number;
  label: React.ReactNode;
  description?: React.ReactNode;
  disabled?: boolean;
}

// RadioGroup component props
export interface RadioGroupProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "onChange">,
    VariantProps<typeof radioGroupVariants> {
  options: RadioOption[];
  value?: string | number | null;
  onChange?: (value: string | number) => void;
  name?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  label?: string;
  description?: string;
}

// RadioGroup component
const RadioGroup = React.forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ 
    className, 
    orientation,
    size,
    options = [],
    value,
    onChange,
    name,
    error,
    disabled = false,
    required = false,
    label,
    description,
    ...props 
  }, ref) => {
    const groupName = name || `radio-group-${Math.random().toString(36).substr(2, 9)}`;

    const handleOptionChange = (optionValue: string | number) => {
      if (!disabled && onChange) {
        onChange(optionValue);
      }
    };

    const getIndicatorSize = () => {
      switch (size) {
        case 'sm': return { dot: 'w-2 h-2', container: 'mr-3 sm:mr-3' };
        case 'default': return { dot: 'w-2.5 h-2.5', container: 'mr-3 sm:mr-4' };
        case 'lg': return { dot: 'w-3 h-3', container: 'mr-4 sm:mr-5' };
        default: return { dot: 'w-2.5 h-2.5', container: 'mr-3 sm:mr-4' };
      }
    };

    const indicatorSize = getIndicatorSize();

    return (
      <div className="space-y-3">
        {/* Label and Description */}
        {(label || description) && (
          <div className="space-y-1">
            {label && (
              <label className="block text-sm font-medium text-gray-900">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
              </label>
            )}
            {description && (
              <p className="text-sm text-gray-600">
                {description}
              </p>
            )}
          </div>
        )}

        {/* Radio Options */}
        <div
          ref={ref}
          className={cn(radioGroupVariants({ orientation, size }), className)}
          role="radiogroup"
          aria-label={label}
          aria-required={required}
          {...props}
        >
          {options.map((option, index) => {
            const isChecked = value === option.value;
            const isDisabled = disabled || option.disabled;
            const optionId = `${groupName}-option-${index}`;

            return (
              <label
                key={option.value}
                htmlFor={optionId}
                className={cn(
                  radioItemVariants({ 
                    checked: isChecked, 
                    size, 
                    disabled: isDisabled 
                  })
                )}
              >
                {/* Hidden radio input for accessibility */}
                <input
                  type="radio"
                  id={optionId}
                  name={groupName}
                  value={option.value}
                  checked={isChecked}
                  onChange={() => handleOptionChange(option.value)}
                  disabled={isDisabled}
                  className="sr-only"
                  required={required}
                />

                {/* Visual radio indicator */}
                <div className={cn(indicatorSize.container)}>
                  <div
                    className={cn(
                      radioIndicatorVariants({ 
                        size, 
                        checked: isChecked, 
                        disabled: isDisabled 
                      })
                    )}
                  >
                    {isChecked && !isDisabled && (
                      <div className={cn("rounded-full bg-white", indicatorSize.dot)} />
                    )}
                  </div>
                </div>

                {/* Option content */}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">
                    {option.label}
                  </div>
                  {option.description && (
                    <div className="text-sm text-gray-600 mt-1">
                      {option.description}
                    </div>
                  )}
                </div>
              </label>
            );
          })}
        </div>

        {/* Error message */}
        {error && (
          <p className="text-sm text-red-600 mt-2">
            {error}
          </p>
        )}
      </div>
    );
  }
);

RadioGroup.displayName = "RadioGroup";

export { RadioGroup, radioGroupVariants, radioItemVariants, radioIndicatorVariants }; 