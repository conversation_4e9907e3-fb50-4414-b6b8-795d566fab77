import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define link variants
const linkVariants = cva(
  "transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#d2b37a]/20",
  {
    variants: {
      variant: {
        // Default gold link
        default: "text-[#d2b37a] hover:text-[#c19648] font-medium",
        
        // Subtle link
        subtle: "text-[#6b7280] hover:text-[#d2b37a] font-medium",
        
        // Underlined link
        underline: "text-[#d2b37a] hover:text-[#c19648] font-medium underline underline-offset-4",
        
        // Button-like link
        button: "inline-flex items-center justify-center rounded-xl px-6 py-3 bg-[rgba(210,179,122,0.1)] text-[#d2b37a] border-2 border-[rgba(210,179,122,0.2)] hover:bg-[rgba(210,179,122,0.15)] hover:border-[rgba(210,179,122,0.4)] font-semibold",
      },
      size: {
        sm: "text-sm",
        default: "text-base",
        lg: "text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// Link component props
export interface LinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof linkVariants> {
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

// Link component
const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ className, variant, size, children, leftIcon, rightIcon, ...props }, ref) => {
    return (
      <a
        ref={ref}
        className={cn(linkVariants({ variant, size }), className)}
        {...props}
      >
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </a>
    );
  }
);

Link.displayName = "Link";

export { Link, linkVariants }; 