import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { Card } from "./Card";

// Define stat card variants
const statCardVariants = cva(
  "p-4 sm:p-5 lg:p-6 hover:shadow-md transition-shadow",
  {
    variants: {
      variant: {
        default: "",
        gold: "bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 border-sehatti-gold-200 dark:border-sehatti-gold-800/30",
        glass: "bg-white/80 dark:bg-sehatti-warm-gray-950/80 backdrop-blur-sm",
      },
      glow: {
        true: "shadow-gold-glow",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      glow: false,
    },
  }
);

// Stat card component props
export interface StatCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statCardVariants> {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: string | number;
    direction: 'up' | 'down' | 'neutral';
    label?: string;
  };
}

// Stat card component
const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  ({ 
    className, 
    variant, 
    glow,
    title,
    value,
    subtitle,
    icon,
    trend,
    ...props 
  }, ref) => {
    return (
      <Card
        ref={ref}
        className={cn(statCardVariants({ variant, glow }), className)}
        {...props}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-1">
              {title}
            </p>
            <p className="text-xl sm:text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-0.5">
              {value}
            </p>
            {subtitle && (
              <p className="text-xs text-sehatti-warm-gray-500">
                {subtitle}
              </p>
            )}
            {trend && (
              <div className="flex items-center gap-1 mt-1">
                <span className={cn(
                  "text-xs font-medium",
                  trend.direction === 'up' && "text-green-600",
                  trend.direction === 'down' && "text-red-600",
                  trend.direction === 'neutral' && "text-sehatti-warm-gray-500"
                )}>
                  {trend.direction === 'up' && '↗'}
                  {trend.direction === 'down' && '↘'}
                  {trend.direction === 'neutral' && '→'}
                  {trend.value}
                </span>
                {trend.label && (
                  <span className="text-xs text-sehatti-warm-gray-500">
                    {trend.label}
                  </span>
                )}
              </div>
            )}
          </div>
          {icon && (
            <div className="p-2.5 sm:p-3 bg-sehatti-gold-100 dark:bg-sehatti-gold-900/20 rounded-lg ml-3">
              <div className="w-5 h-5 sm:w-6 sm:h-6 text-sehatti-gold-600 dark:text-sehatti-gold-400">
                {icon}
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  }
);

StatCard.displayName = "StatCard";

export { StatCard, statCardVariants }; 