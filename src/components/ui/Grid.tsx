import React from "react";
import { cn } from "@/lib/utils";

// Grid container component
export interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16;
  rowGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16;
  colGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16;
  mdCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  lgCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  xlCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  flow?: "row" | "col";
  autoRows?: string;
  autoFit?: boolean;
  autoFill?: boolean;
  minChildWidth?: string;
}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ 
    className, 
    cols, 
    gap = 4, 
    rowGap, 
    colGap, 
    mdCols, 
    lgCols, 
    xlCols, 
    flow,
    autoRows,
    autoFit,
    autoFill,
    minChildWidth,
    children,
    ...props 
  }, ref) => {
    // Determine if we're using auto-fit/auto-fill or explicit columns
    const isAutoGrid = autoFit || autoFill || minChildWidth;
    
    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          // Grid template columns based on cols prop
          cols === 1 && !isAutoGrid && "grid-cols-1",
          cols === 2 && !isAutoGrid && "grid-cols-2",
          cols === 3 && !isAutoGrid && "grid-cols-3",
          cols === 4 && !isAutoGrid && "grid-cols-4",
          cols === 5 && !isAutoGrid && "grid-cols-5",
          cols === 6 && !isAutoGrid && "grid-cols-6",
          cols === 7 && !isAutoGrid && "grid-cols-7",
          cols === 8 && !isAutoGrid && "grid-cols-8",
          cols === 9 && !isAutoGrid && "grid-cols-9",
          cols === 10 && !isAutoGrid && "grid-cols-10",
          cols === 11 && !isAutoGrid && "grid-cols-11",
          cols === 12 && !isAutoGrid && "grid-cols-12",
          
          // Responsive columns
          mdCols === 1 && "md:grid-cols-1",
          mdCols === 2 && "md:grid-cols-2",
          mdCols === 3 && "md:grid-cols-3",
          mdCols === 4 && "md:grid-cols-4",
          mdCols === 5 && "md:grid-cols-5",
          mdCols === 6 && "md:grid-cols-6",
          mdCols === 7 && "md:grid-cols-7",
          mdCols === 8 && "md:grid-cols-8",
          mdCols === 9 && "md:grid-cols-9",
          mdCols === 10 && "md:grid-cols-10",
          mdCols === 11 && "md:grid-cols-11",
          mdCols === 12 && "md:grid-cols-12",
          
          lgCols === 1 && "lg:grid-cols-1",
          lgCols === 2 && "lg:grid-cols-2",
          lgCols === 3 && "lg:grid-cols-3",
          lgCols === 4 && "lg:grid-cols-4",
          lgCols === 5 && "lg:grid-cols-5",
          lgCols === 6 && "lg:grid-cols-6",
          lgCols === 7 && "lg:grid-cols-7",
          lgCols === 8 && "lg:grid-cols-8",
          lgCols === 9 && "lg:grid-cols-9",
          lgCols === 10 && "lg:grid-cols-10",
          lgCols === 11 && "lg:grid-cols-11",
          lgCols === 12 && "lg:grid-cols-12",
          
          xlCols === 1 && "xl:grid-cols-1",
          xlCols === 2 && "xl:grid-cols-2",
          xlCols === 3 && "xl:grid-cols-3",
          xlCols === 4 && "xl:grid-cols-4",
          xlCols === 5 && "xl:grid-cols-5",
          xlCols === 6 && "xl:grid-cols-6",
          xlCols === 7 && "xl:grid-cols-7",
          xlCols === 8 && "xl:grid-cols-8",
          xlCols === 9 && "xl:grid-cols-9",
          xlCols === 10 && "xl:grid-cols-10",
          xlCols === 11 && "xl:grid-cols-11",
          xlCols === 12 && "xl:grid-cols-12",
          
          // Gap
          gap === 0 && "gap-0",
          gap === 1 && "gap-1",
          gap === 2 && "gap-2",
          gap === 3 && "gap-3",
          gap === 4 && "gap-4",
          gap === 5 && "gap-5",
          gap === 6 && "gap-6",
          gap === 8 && "gap-8",
          gap === 10 && "gap-10",
          gap === 12 && "gap-12",
          gap === 16 && "gap-16",
          
          // Row Gap
          rowGap === 0 && "gap-y-0",
          rowGap === 1 && "gap-y-1",
          rowGap === 2 && "gap-y-2",
          rowGap === 3 && "gap-y-3",
          rowGap === 4 && "gap-y-4",
          rowGap === 5 && "gap-y-5",
          rowGap === 6 && "gap-y-6",
          rowGap === 8 && "gap-y-8",
          rowGap === 10 && "gap-y-10",
          rowGap === 12 && "gap-y-12",
          rowGap === 16 && "gap-y-16",
          
          // Column Gap
          colGap === 0 && "gap-x-0",
          colGap === 1 && "gap-x-1",
          colGap === 2 && "gap-x-2",
          colGap === 3 && "gap-x-3",
          colGap === 4 && "gap-x-4",
          colGap === 5 && "gap-x-5",
          colGap === 6 && "gap-x-6",
          colGap === 8 && "gap-x-8",
          colGap === 10 && "gap-x-10",
          colGap === 12 && "gap-x-12",
          colGap === 16 && "gap-x-16",
          
          // Flow direction
          flow === "row" && "grid-flow-row",
          flow === "col" && "grid-flow-col",
          
          className
        )}
        style={{
          // Auto grid properties
          ...(isAutoGrid && {
            gridTemplateColumns: `repeat(${autoFit ? 'auto-fit' : 'auto-fill'}, minmax(${minChildWidth || '250px'}, 1fr))`,
          }),
          ...(autoRows && {
            gridAutoRows: autoRows,
          }),
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Grid.displayName = "Grid";

// Grid Item component
export interface GridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  colSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | "full";
  rowSpan?: 1 | 2 | 3 | 4 | 5 | 6;
  colStart?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | "auto";
  rowStart?: 1 | 2 | 3 | 4 | 5 | 6 | "auto";
  mdColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | "full";
  lgColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | "full";
  xlColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | "full";
}

const GridItem = React.forwardRef<HTMLDivElement, GridItemProps>(
  ({ 
    className, 
    colSpan, 
    rowSpan, 
    colStart, 
    rowStart,
    mdColSpan,
    lgColSpan,
    xlColSpan,
    children,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Column span
          colSpan === 1 && "col-span-1",
          colSpan === 2 && "col-span-2",
          colSpan === 3 && "col-span-3",
          colSpan === 4 && "col-span-4",
          colSpan === 5 && "col-span-5",
          colSpan === 6 && "col-span-6",
          colSpan === 7 && "col-span-7",
          colSpan === 8 && "col-span-8",
          colSpan === 9 && "col-span-9",
          colSpan === 10 && "col-span-10",
          colSpan === 11 && "col-span-11",
          colSpan === 12 && "col-span-12",
          colSpan === "full" && "col-span-full",
          
          // Row span
          rowSpan === 1 && "row-span-1",
          rowSpan === 2 && "row-span-2",
          rowSpan === 3 && "row-span-3",
          rowSpan === 4 && "row-span-4",
          rowSpan === 5 && "row-span-5",
          rowSpan === 6 && "row-span-6",
          
          // Column start
          colStart === 1 && "col-start-1",
          colStart === 2 && "col-start-2",
          colStart === 3 && "col-start-3",
          colStart === 4 && "col-start-4",
          colStart === 5 && "col-start-5",
          colStart === 6 && "col-start-6",
          colStart === 7 && "col-start-7",
          colStart === 8 && "col-start-8",
          colStart === 9 && "col-start-9",
          colStart === 10 && "col-start-10",
          colStart === 11 && "col-start-11",
          colStart === 12 && "col-start-12",
          colStart === "auto" && "col-start-auto",
          
          // Row start
          rowStart === 1 && "row-start-1",
          rowStart === 2 && "row-start-2",
          rowStart === 3 && "row-start-3",
          rowStart === 4 && "row-start-4",
          rowStart === 5 && "row-start-5",
          rowStart === 6 && "row-start-6",
          rowStart === "auto" && "row-start-auto",
          
          // Responsive column spans
          mdColSpan === 1 && "md:col-span-1",
          mdColSpan === 2 && "md:col-span-2",
          mdColSpan === 3 && "md:col-span-3",
          mdColSpan === 4 && "md:col-span-4",
          mdColSpan === 5 && "md:col-span-5",
          mdColSpan === 6 && "md:col-span-6",
          mdColSpan === 7 && "md:col-span-7",
          mdColSpan === 8 && "md:col-span-8",
          mdColSpan === 9 && "md:col-span-9",
          mdColSpan === 10 && "md:col-span-10",
          mdColSpan === 11 && "md:col-span-11",
          mdColSpan === 12 && "md:col-span-12",
          mdColSpan === "full" && "md:col-span-full",
          
          lgColSpan === 1 && "lg:col-span-1",
          lgColSpan === 2 && "lg:col-span-2",
          lgColSpan === 3 && "lg:col-span-3",
          lgColSpan === 4 && "lg:col-span-4",
          lgColSpan === 5 && "lg:col-span-5",
          lgColSpan === 6 && "lg:col-span-6",
          lgColSpan === 7 && "lg:col-span-7",
          lgColSpan === 8 && "lg:col-span-8",
          lgColSpan === 9 && "lg:col-span-9",
          lgColSpan === 10 && "lg:col-span-10",
          lgColSpan === 11 && "lg:col-span-11",
          lgColSpan === 12 && "lg:col-span-12",
          lgColSpan === "full" && "lg:col-span-full",
          
          xlColSpan === 1 && "xl:col-span-1",
          xlColSpan === 2 && "xl:col-span-2",
          xlColSpan === 3 && "xl:col-span-3",
          xlColSpan === 4 && "xl:col-span-4",
          xlColSpan === 5 && "xl:col-span-5",
          xlColSpan === 6 && "xl:col-span-6",
          xlColSpan === 7 && "xl:col-span-7",
          xlColSpan === 8 && "xl:col-span-8",
          xlColSpan === 9 && "xl:col-span-9",
          xlColSpan === 10 && "xl:col-span-10",
          xlColSpan === 11 && "xl:col-span-11",
          xlColSpan === 12 && "xl:col-span-12",
          xlColSpan === "full" && "xl:col-span-full",
          
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

GridItem.displayName = "GridItem";

export { Grid, GridItem }; 