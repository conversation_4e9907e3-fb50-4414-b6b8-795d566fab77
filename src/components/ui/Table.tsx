import React from "react";
import { cn } from "@/lib/utils";

// Table root component
const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="relative w-full overflow-auto">
    <table
      ref={ref}
      className={cn("w-full caption-bottom text-sm", className)}
      {...props}
    />
  </div>
));
Table.displayName = "Table";

// Table header component
const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
));
TableHeader.displayName = "TableHeader";

// Table body component
const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));
TableBody.displayName = "TableBody";

// Table footer component
const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t bg-sehatti-warm-gray-50 font-medium text-sehatti-warm-gray-900 dark:bg-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-50",
      className
    )}
    {...props}
  />
));
TableFooter.displayName = "TableFooter";

// Table row component
const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement> & {
    isSelected?: boolean;
    isClickable?: boolean;
  }
>(({ className, isSelected, isClickable, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b border-sehatti-warm-gray-100 transition-colors dark:border-sehatti-warm-gray-800",
      isSelected && "bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20",
      isClickable && "cursor-pointer hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-800/50",
      className
    )}
    {...props}
  />
));
TableRow.displayName = "TableRow";

// Table head component
const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement> & {
    isSortable?: boolean;
    sortDirection?: "asc" | "desc" | null;
  }
>(({ className, isSortable, sortDirection, children, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 [&:has([role=checkbox])]:pr-0",
      isSortable && "cursor-pointer select-none",
      className
    )}
    {...props}
  >
    {isSortable ? (
      <div className="flex items-center gap-2">
        <span>{children}</span>
        {sortDirection === "asc" && (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
            <path d="m6 9 6-6 6 6"/>
            <path d="M6 12h12"/>
            <path d="m6 15 6 6 6-6"/>
          </svg>
        )}
        {sortDirection === "desc" && (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
            <path d="m6 9 6-6 6 6"/>
            <path d="M6 12h12"/>
            <path d="m6 15 6 6 6-6"/>
          </svg>
        )}
        {sortDirection === null && (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 opacity-50">
            <path d="m6 9 6-6 6 6"/>
            <path d="M6 12h12"/>
            <path d="m6 15 6 6 6-6"/>
          </svg>
        )}
      </div>
    ) : (
      children
    )}
  </th>
));
TableHead.displayName = "TableHead";

// Table cell component
const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}
    {...props}
  />
));
TableCell.displayName = "TableCell";

// Table caption component
const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400", className)}
    {...props}
  />
));
TableCaption.displayName = "TableCaption";

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}; 