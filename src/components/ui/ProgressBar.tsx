import React from 'react'

interface ProgressBarProps {
  percentage: number
  title?: string
  currentStep?: number
  totalSteps?: number
  className?: string
  variant?: 'primary' | 'secondary' | 'success'
  showPercentage?: boolean
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  percentage,
  title,
  currentStep,
  totalSteps,
  className = '',
  variant = 'primary',
  showPercentage = false
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-sehatti-gold'
      case 'secondary':
        return 'bg-blue-500'
      case 'success':
        return 'bg-green-500'
      default:
        return 'bg-sehatti-gold'
    }
  }

  return (
    <div className={`w-full ${className}`}>
      {(title || currentStep || showPercentage) && (
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {title}
          </h2>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            {currentStep && totalSteps && (
              <span>Step {currentStep} of {totalSteps}</span>
            )}
            {showPercentage && (
              <span>{Math.round(percentage)}%</span>
            )}
          </div>
        </div>
      )}
      
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <div 
          className={`h-2 rounded-full transition-all duration-500 ease-out ${getVariantClasses()}`}
          style={{ width: `${Math.min(100, Math.max(0, percentage))}%` }}
        />
      </div>
    </div>
  )
} 