import React, { useState, useRef, useEffect } from "react"
import { FaChevronDown } from "react-icons/fa"

export interface Language {
  code: "EN" | "AR" | "HI"
  name: string
  nativeName: string
  flag: string
  isRTL: boolean
}

interface LanguagePickerProps {
  languages: Language[]
  currentLanguage: "EN" | "AR" | "HI"
  onLanguageChange: (language: "EN" | "AR" | "HI") => void
  className?: string
  variant?: "default" | "compact" | "minimal"
}

export const LanguagePicker: React.FC<LanguagePickerProps> = ({
  languages,
  currentLanguage,
  onLanguageChange,
  className = "",
  variant = "default"
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const currentLang = languages.find((lang) => lang.code === currentLanguage)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleLanguageSelect = (languageCode: "EN" | "AR" | "HI") => {
    onLanguageChange(languageCode)
    setIsOpen(false)
  }

  const getVariantClasses = () => {
    switch (variant) {
      case "compact":
        return {
          button: "px-2 py-1 text-sm",
          dropdown: "min-w-[120px]",
          item: "px-3 py-2 text-sm"
        }
      case "minimal":
        return {
          button: "px-3 py-2 border-0 bg-transparent hover:bg-gray-50",
          dropdown: "min-w-[140px]",
          item: "px-3 py-2"
        }
      default:
        return {
          button: "px-4 py-2",
          dropdown: "min-w-[160px]",
          item: "px-4 py-3"
        }
    }
  }

  const variantClasses = getVariantClasses()

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          ${variantClasses.button}
          flex items-center gap-2 bg-white border border-gray-300 rounded-lg
          hover:border-sehatti-gold-400 focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 focus:ring-offset-1
          transition-all duration-200 font-medium text-gray-700
          ${
            isOpen
              ? "border-sehatti-gold-400 ring-2 ring-sehatti-gold-500 ring-offset-1"
              : ""
          }
        `}
      >
        {currentLang && (
          <>
            <span className="text-lg">{currentLang.flag}</span>
            <span className="hidden sm:inline">{currentLang.name}</span>
            <span className="sm:hidden">{currentLang.code}</span>
          </>
        )}
        <FaChevronDown
          className={`w-3 h-3 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={`
          absolute top-full left-0 mt-1 ${variantClasses.dropdown} bg-white border border-gray-200 rounded-lg shadow-lg z-50
          animate-in fade-in-0 zoom-in-95 duration-100
        `}
        >
          <div className="py-1">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageSelect(language.code)}
                className={`
                  ${variantClasses.item}
                  w-full flex items-center gap-3 hover:bg-sehatti-gold-50 transition-colors duration-150
                  ${
                    currentLanguage === language.code
                      ? "bg-sehatti-gold-100 text-sehatti-gold-700"
                      : "text-gray-700"
                  }
                `}
              >
                <span className="text-lg">{language.flag}</span>
                <div className="flex flex-col items-start">
                  <span className="font-medium">{language.name}</span>
                  <span className="text-xs text-gray-500">
                    {language.nativeName}
                  </span>
                </div>
                {language.isRTL && (
                  <span className="ml-auto text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                    RTL
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default LanguagePicker
