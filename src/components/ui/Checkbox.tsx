import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define checkbox variants
const checkboxVariants = cva(
  "peer h-4 w-4 shrink-0 rounded border-2 transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-[#e5e7eb] data-[state=checked]:bg-[#d2b37a] data-[state=checked]:border-[#d2b37a] data-[state=checked]:text-white focus-visible:ring-[#d2b37a]/20",
        gold: "border-[#d2b37a]/30 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#d2b37a] data-[state=checked]:to-[#c19648] data-[state=checked]:border-[#d2b37a] data-[state=checked]:text-white focus-visible:ring-[#d2b37a]/20",
      },
      size: {
        sm: "h-3 w-3",
        default: "h-4 w-4",
        lg: "h-5 w-5",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// Checkbox component props
export interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof checkboxVariants> {
  label?: string;
  description?: string;
  error?: string;
}

// Checkbox component
const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, variant, size, label, description, error, id, ...props }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={checkboxId}
            ref={ref}
            className={cn(
              checkboxVariants({ variant, size }),
              "accent-[#d2b37a]",
              className
            )}
            {...props}
          />
          
          {label && (
            <label
              htmlFor={checkboxId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              {label}
            </label>
          )}
        </div>
        
        {description && (
          <p className="text-sm text-muted-foreground ml-6">
            {description}
          </p>
        )}
        
        {error && (
          <p className="text-sm text-red-500 ml-6">
            {error}
          </p>
        )}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export { Checkbox, checkboxVariants }; 