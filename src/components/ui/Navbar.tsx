import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  FaHome, 
  FaUsers, 
  FaCog, 
  FaChartBar, 
  FaSignOutAlt, 
  FaBuilding,
  FaBell,
  FaUserShield,
  FaBookOpen,
  FaRobot,
  FaUserTie,
  FaClipboardList,
  FaChevronDown,
  FaBars,
  FaTimes
} from 'react-icons/fa';
import { MdOutlineSpaceDashboard } from 'react-icons/md';
import { cn } from '@/lib/utils';
import { Avatar } from './Avatar';
import { Button } from './Button';
import { GradientText } from './GradientText';
import { Badge } from './Badge';

// Navigation item interface
export interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  submenu?: NavItem[];
  badge?: number;
  permission?: string[];
}

// User interface
export interface NavUser {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

// Navbar props
export interface NavbarProps {
  user?: NavUser;
  navItems?: NavItem[];
  onLogout?: () => void;
  className?: string;
  variant?: 'default' | 'glass' | 'solid';
  showNotifications?: boolean;
  notificationCount?: number;
  onNotificationClick?: () => void;
  logo?: {
    src?: string;
    text?: string;
    href?: string;
  };
}

// Default admin navigation items
const defaultAdminNavItems: NavItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
    href: '/dashboard'
  },
  {
    id: 'corporate',
    label: 'Corporate Management',
    icon: <FaClipboardList className="w-5 h-5" />,
    href: '/admin/corporate'
  },
  {
    id: 'content',
    label: 'Content',
    icon: <FaBookOpen className="w-5 h-5" />,
    submenu: [
      { id: 'hubs', label: 'Hubs', icon: <FaBuilding className="w-4 h-4" />, href: '/admin/hubs' },
      { id: 'hub-content', label: 'Hub Content', icon: <FaBookOpen className="w-4 h-4" />, href: '/admin/hub-content' },
      { id: 'articles', label: 'Articles', icon: <FaBookOpen className="w-4 h-4" />, href: '/admin/articles' }
    ]
  },
  {
    id: 'automation',
    label: 'Automation',
    icon: <FaRobot className="w-5 h-5" />,
    submenu: [
      { id: 'invitations', label: 'Invitations', icon: <FaUsers className="w-4 h-4" />, href: '/admin/invitations' },
      { id: 'recommendations', label: 'Recommendations', icon: <FaChartBar className="w-4 h-4" />, href: '/admin/recommendations' }
    ]
  },
  {
    id: 'consultants',
    label: 'Consultants',
    icon: <FaUserTie className="w-5 h-5" />,
    href: '/admin/consultants'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <FaChartBar className="w-5 h-5" />,
    href: '/admin/analytics'
  }
];

// Dropdown menu component
const DropdownMenu: React.FC<{
  items: NavItem[];
  isOpen: boolean;
  onClose: () => void;
}> = ({ items, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 mt-2 w-56 bg-white dark:bg-sehatti-warm-gray-900 rounded-xl shadow-lg border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 py-2 z-50">
      {items.map((item) => (
        <Link
          key={item.id}
          to={item.href || '#'}
          onClick={onClose}
          className="flex items-center gap-3 px-4 py-3 text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600 transition-colors"
        >
          {item.icon}
          <span>{item.label}</span>
          {item.badge && item.badge > 0 && (
            <Badge variant="destructive" size="sm" className="ml-auto">
              {item.badge > 9 ? '9+' : item.badge}
            </Badge>
          )}
        </Link>
      ))}
    </div>
  );
};

// User menu component
const UserMenu: React.FC<{
  user: NavUser;
  isOpen: boolean;
  onClose: () => void;
  onLogout?: () => void;
}> = ({ user, isOpen, onClose, onLogout }) => {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full right-0 mt-2 w-64 bg-white dark:bg-sehatti-warm-gray-900 rounded-xl shadow-lg border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 py-2 z-50">
      {/* User Info */}
      <div className="px-4 py-3 border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
        <div className="flex items-center gap-3">
          <Avatar
            src={user.avatar}
            alt={user.name}
            size="md"
            fallback={user.name.charAt(0).toUpperCase()}
          />
          <div className="flex-1 min-w-0">
            <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 truncate">
              {user.name}
            </p>
            <p className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400 truncate">
              {user.email}
            </p>
            <Badge variant="subtle" size="sm" className="mt-1">
              {user.role}
            </Badge>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-2">
        <Link
          to="/admin/profile"
          onClick={onClose}
          className="flex items-center gap-3 px-4 py-3 text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600 transition-colors"
        >
          <FaUserShield className="w-4 h-4" />
          <span>Profile Settings</span>
        </Link>
        
        <Link
          to="/admin/settings"
          onClick={onClose}
          className="flex items-center gap-3 px-4 py-3 text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600 transition-colors"
        >
          <FaCog className="w-4 h-4" />
          <span>System Settings</span>
        </Link>
      </div>

      {/* Logout */}
      <div className="border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 pt-2">
        <button
          onClick={() => {
            onClose();
            onLogout?.();
          }}
          className="flex items-center gap-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
        >
          <FaSignOutAlt className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
};

// Main Navbar component
export const Navbar: React.FC<NavbarProps> = ({
  user,
  navItems = defaultAdminNavItems,
  onLogout,
  className,
  variant = 'default',
  showNotifications = true,
  notificationCount = 0,
  onNotificationClick,
  logo = { text: 'Sehatti Admin', href: '/dashboard' }
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      
      // Check if click is outside any dropdown
      const isOutsideDropdown = Object.values(dropdownRefs.current).every(
        ref => !ref?.contains(target)
      );
      
      const isOutsideUserMenu = !userMenuRef.current?.contains(target);

      if (isOutsideDropdown) {
        setOpenDropdown(null);
      }
      
      if (isOutsideUserMenu) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Check if current path matches nav item
  const isActiveItem = (item: NavItem): boolean => {
    if (item.href && location.pathname === item.href) return true;
    if (item.submenu) {
      return item.submenu.some(subItem => subItem.href === location.pathname);
    }
    return false;
  };

  const navbarVariants = {
    default: "bg-white dark:bg-sehatti-warm-gray-950 border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
    glass: "bg-white/80 dark:bg-sehatti-warm-gray-950/80 backdrop-blur-sm border-b border-sehatti-warm-gray-200/50 dark:border-sehatti-warm-gray-800/50",
    solid: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-500 text-white"
  };

  return (
    <nav className={cn(
      "sticky top-0 z-40 w-full transition-all duration-200",
      navbarVariants[variant],
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link
              to={logo.href || '/dashboard'}
              className="flex items-center gap-3 hover:opacity-80 transition-opacity"
            >
              {logo.src ? (
                <img src={logo.src} alt="Logo" className="h-8 w-auto" />
              ) : (
                <div className="w-8 h-8 bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">S</span>
                </div>
              )}
              {logo.text && (
                <GradientText
                  gradient="gold"
                  size="xl"
                  weight="bold"
                  className="hidden sm:block"
                >
                  {logo.text}
                </GradientText>
              )}
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-1">
            {navItems.map((item) => (
              <div
                key={item.id}
                className="relative"
                ref={el => dropdownRefs.current[item.id] = el}
              >
                {item.submenu ? (
                  <button
                    onClick={() => setOpenDropdown(openDropdown === item.id ? null : item.id)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                      isActiveItem(item)
                        ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                        : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600"
                    )}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    <FaChevronDown className={cn(
                      "w-3 h-3 transition-transform",
                      openDropdown === item.id && "rotate-180"
                    )} />
                    {item.badge && item.badge > 0 && (
                      <Badge variant="destructive" size="sm">
                        {item.badge > 9 ? '9+' : item.badge}
                      </Badge>
                    )}
                  </button>
                ) : (
                  <Link
                    to={item.href || '#'}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                      isActiveItem(item)
                        ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                        : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600"
                    )}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    {item.badge && item.badge > 0 && (
                      <Badge variant="destructive" size="sm">
                        {item.badge > 9 ? '9+' : item.badge}
                      </Badge>
                    )}
                  </Link>
                )}

                {/* Dropdown Menu */}
                {item.submenu && (
                  <DropdownMenu
                    items={item.submenu}
                    isOpen={openDropdown === item.id}
                    onClose={() => setOpenDropdown(null)}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center gap-3">
            {/* Notifications */}
            {showNotifications && (
              <button
                onClick={onNotificationClick}
                className="relative p-2 rounded-lg text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 transition-colors"
              >
                <FaBell className="w-5 h-5" />
                {notificationCount > 0 && (
                  <Badge
                    variant="destructive"
                    size="sm"
                    className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center"
                  >
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </Badge>
                )}
              </button>
            )}

            {/* User Menu */}
            {user && (
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center gap-2 p-1 rounded-lg hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 transition-colors"
                >
                  <Avatar
                    src={user.avatar}
                    alt={user.name}
                    size="sm"
                    fallback={user.name.charAt(0).toUpperCase()}
                  />
                  <FaChevronDown className={cn(
                    "w-3 h-3 text-sehatti-warm-gray-500 transition-transform hidden sm:block",
                    isUserMenuOpen && "rotate-180"
                  )} />
                </button>

                <UserMenu
                  user={user}
                  isOpen={isUserMenuOpen}
                  onClose={() => setIsUserMenuOpen(false)}
                  onLogout={onLogout}
                />
              </div>
            )}

            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 transition-colors"
            >
              {isMobileMenuOpen ? (
                <FaTimes className="w-5 h-5" />
              ) : (
                <FaBars className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 py-4">
            <div className="space-y-2">
              {navItems.map((item) => (
                <div key={item.id}>
                  {item.submenu ? (
                    <div>
                      <button
                        onClick={() => setOpenDropdown(openDropdown === item.id ? null : item.id)}
                        className={cn(
                          "flex items-center justify-between w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                          isActiveItem(item)
                            ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                            : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800"
                        )}
                      >
                        <div className="flex items-center gap-3">
                          {item.icon}
                          <span>{item.label}</span>
                          {item.badge && item.badge > 0 && (
                            <Badge variant="destructive" size="sm">
                              {item.badge > 9 ? '9+' : item.badge}
                            </Badge>
                          )}
                        </div>
                        <FaChevronDown className={cn(
                          "w-3 h-3 transition-transform",
                          openDropdown === item.id && "rotate-180"
                        )} />
                      </button>
                      
                      {openDropdown === item.id && (
                        <div className="ml-6 mt-2 space-y-1">
                          {item.submenu.map((subItem) => (
                            <Link
                              key={subItem.id}
                              to={subItem.href || '#'}
                              onClick={() => setIsMobileMenuOpen(false)}
                              className={cn(
                                "flex items-center gap-3 px-4 py-2 text-sm rounded-lg transition-colors",
                                location.pathname === subItem.href
                                  ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                                  : "text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800"
                              )}
                            >
                              {subItem.icon}
                              <span>{subItem.label}</span>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.href || '#'}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={cn(
                        "flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                        isActiveItem(item)
                          ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                          : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800"
                      )}
                    >
                      {item.icon}
                      <span>{item.label}</span>
                      {item.badge && item.badge > 0 && (
                        <Badge variant="destructive" size="sm" className="ml-auto">
                          {item.badge > 9 ? '9+' : item.badge}
                        </Badge>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}; 