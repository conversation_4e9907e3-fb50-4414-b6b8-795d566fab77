import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define spinner variants
const spinnerVariants = cva(
  "animate-spin rounded-full border-current border-solid border-t-transparent",
  {
    variants: {
      variant: {
        // Default spinner
        default: "text-sehatti-warm-gray-300 dark:text-sehatti-warm-gray-700",
        
        // Gold spinner
        gold: "text-sehatti-gold-500 dark:text-sehatti-gold-400",
        
        // Primary spinner
        primary: "text-blue-600 dark:text-blue-400",
        
        // White spinner (for dark backgrounds)
        white: "text-white/70",
        
        // Destructive spinner
        destructive: "text-red-600 dark:text-red-400",
      },
      size: {
        xs: "h-3 w-3 border-[2px]",
        sm: "h-4 w-4 border-[2px]",
        md: "h-6 w-6 border-2",
        lg: "h-8 w-8 border-[3px]",
        xl: "h-10 w-10 border-4",
        "2xl": "h-12 w-12 border-4",
      },
      speed: {
        slow: "animate-spin-slow", // 2s
        normal: "animate-spin", // 1s
        fast: "animate-spin-fast", // 0.5s
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      speed: "normal",
    },
  }
);

// Spinner component props
export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

// Spinner component
const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, variant, size, speed, label, ...props }, ref) => {
    return (
      <div ref={ref} role="status" className={cn("inline-flex items-center", className)} {...props}>
        <div
          className={cn(spinnerVariants({ variant, size, speed }))}
          aria-hidden="true"
        />
        {label && (
          <span className="ml-2 text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
            {label}
          </span>
        )}
        <span className="sr-only">{label || "Loading..."}</span>
      </div>
    );
  }
);

Spinner.displayName = "Spinner";

// Gold spinner with glow effect
const GoldSpinner = React.forwardRef<HTMLDivElement, Omit<SpinnerProps, 'variant'>>(
  ({ className, size, speed, label, ...props }, ref) => {
    return (
      <div ref={ref} role="status" className={cn("inline-flex items-center", className)} {...props}>
        <div
          className={cn(
            spinnerVariants({ variant: "gold", size, speed }),
            "shadow-gold-glow"
          )}
          aria-hidden="true"
        />
        {label && (
          <span className="ml-2 text-sm text-sehatti-gold-600 dark:text-sehatti-gold-400">
            {label}
          </span>
        )}
        <span className="sr-only">{label || "Loading..."}</span>
      </div>
    );
  }
);

GoldSpinner.displayName = "GoldSpinner";

export { Spinner, GoldSpinner, spinnerVariants }; 