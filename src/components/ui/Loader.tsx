import React from 'react';
import { cn } from '@/lib/utils';

export interface LoaderProps {
  variant?: 'default' | 'minimal' | 'dots' | 'pulse' | 'spinner';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'gold' | 'gray' | 'blue' | 'white';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export const Loader: React.FC<LoaderProps> = ({
  variant = 'default',
  size = 'md',
  color = 'gold',
  text,
  className,
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    gold: 'text-sehatti-gold-500 border-sehatti-gold-500',
    gray: 'text-sehatti-warm-gray-500 border-sehatti-warm-gray-500',
    blue: 'text-blue-500 border-blue-500',
    white: 'text-white border-white'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const renderLoader = () => {
    switch (variant) {
      case 'minimal':
        return (
          <div className={cn(
            'animate-spin rounded-full border-2 border-transparent border-t-current',
            sizeClasses[size],
            colorClasses[color]
          )} />
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'rounded-full animate-pulse',
                  size === 'sm' ? 'w-1.5 h-1.5' : 
                  size === 'md' ? 'w-2 h-2' :
                  size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                  color === 'gold' ? 'bg-sehatti-gold-500' :
                  color === 'gray' ? 'bg-sehatti-warm-gray-500' :
                  color === 'blue' ? 'bg-blue-500' : 'bg-white'
                )}
                style={{
                  animationDelay: `${i * 0.15}s`,
                  animationDuration: '0.8s'
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div className={cn(
            'animate-pulse rounded-full',
            sizeClasses[size],
            color === 'gold' ? 'bg-sehatti-gold-500' :
            color === 'gray' ? 'bg-sehatti-warm-gray-500' :
            color === 'blue' ? 'bg-blue-500' : 'bg-white'
          )} />
        );

      case 'spinner':
        return (
          <div className={cn(
            'animate-spin rounded-full border-4 border-gray-200',
            sizeClasses[size],
            color === 'gold' ? 'border-t-sehatti-gold-500' :
            color === 'gray' ? 'border-t-sehatti-warm-gray-500' :
            color === 'blue' ? 'border-t-blue-500' : 'border-t-white'
          )} />
        );

      default:
        return (
          <div className="relative">
            <div className={cn(
              'animate-spin rounded-full border-2 border-transparent border-t-current',
              sizeClasses[size],
              colorClasses[color]
            )} />
            <div className={cn(
              'absolute inset-0 animate-ping rounded-full border border-current opacity-20',
              sizeClasses[size],
              colorClasses[color]
            )} />
          </div>
        );
    }
  };

  const content = (
    <div className={cn(
      'flex flex-col items-center justify-center gap-3',
      className
    )}>
      {renderLoader()}
      {text && (
        <p className={cn(
          'font-medium animate-pulse',
          textSizeClasses[size],
          color === 'gold' ? 'text-sehatti-gold-600' :
          color === 'gray' ? 'text-sehatti-warm-gray-600' :
          color === 'blue' ? 'text-blue-600' : 'text-white'
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white/80 dark:bg-sehatti-warm-gray-950/80 backdrop-blur-sm flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
};

// Preset components for common use cases
export const PageLoader: React.FC<{ text?: string }> = ({ text = "Loading..." }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <Loader variant="default" size="lg" text={text} />
  </div>
);

export const FullScreenLoader: React.FC<{ text?: string }> = ({ text = "Loading..." }) => (
  <Loader variant="default" size="xl" text={text} fullScreen />
);

export const InlineLoader: React.FC<{ text?: string; size?: 'sm' | 'md' }> = ({ 
  text, 
  size = 'sm' 
}) => (
  <Loader variant="minimal" size={size} text={text} className="inline-flex" />
);

export const ButtonLoader: React.FC = () => (
  <Loader variant="minimal" size="sm" color="white" />
);

export default Loader; 