import React, { useState, useRef } from 'react'
import { FaPlay, FaPause } from 'react-icons/fa'

interface VideoPlayerProps {
  src: string
  poster?: string
  title?: string
  description?: string
  className?: string
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onStart?: () => void
  onTimeUpdate?: (currentTime: number) => void
  onLoadedMetadata?: (duration: number) => void
  onError?: (error: any) => void
  showControls?: boolean
  autoPlay?: boolean
  preload?: 'none' | 'metadata' | 'auto'
  playsInline?: boolean
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  title,
  description,
  className = '',
  onPlay,
  onPause,
  onEnded,
  onStart,
  onTimeUpdate,
  onLoadedMetadata,
  onError,
  showControls = true,
  autoPlay = false,
  preload = 'metadata',
  playsInline = true
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [hasStarted, setHasStarted] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [showOverlay, setShowOverlay] = useState(true)

  const handlePlay = () => {
    setIsPlaying(true)
    setShowOverlay(false)
    
    // Call onStart only on first play
    if (!hasStarted) {
      setHasStarted(true)
      onStart?.()
    }
    
    onPlay?.()
  }

  const handlePause = () => {
    setIsPlaying(false)
    onPause?.()
  }

  const handleTimeUpdate = (e: React.SyntheticEvent<HTMLVideoElement>) => {
    const time = e.currentTarget.currentTime
    setCurrentTime(time)
    onTimeUpdate?.(time)
  }

  const handleLoadedMetadata = (e: React.SyntheticEvent<HTMLVideoElement>) => {
    const dur = e.currentTarget.duration
    setDuration(dur)
    onLoadedMetadata?.(dur)
  }

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement>) => {
    console.error('Video error:', e)
    onError?.(e)
  }

  const handleEnded = () => {
    setIsPlaying(false)
    setShowOverlay(true)
    onEnded?.()
  }

  const handlePlayClick = () => {
    if (videoRef.current) {
      videoRef.current.play().catch(err => {
        console.error('Manual play failed:', err)
        onError?.(err)
      })
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={`relative bg-gray-900 rounded-lg overflow-hidden aspect-video ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        controls={showControls}
        autoPlay={autoPlay}
        preload={preload}
        playsInline={playsInline}
        poster={poster}
        onPlay={handlePlay}
        onPause={handlePause}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={handleEnded}
        onError={handleError}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      
      {/* Play Overlay */}
      {showOverlay && !isPlaying && (
        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center transition-opacity">
          <div className="text-center text-white">
            <button
              onClick={handlePlayClick}
              className="w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center
                         hover:bg-opacity-30 transition-all duration-200 cursor-pointer"
            >
              <FaPlay className="w-6 h-6 ml-1" />
            </button>
            
            {title && (
              <p className="text-lg font-semibold mb-2">{title}</p>
            )}
            
            {description && (
              <p className="text-sm opacity-80 mb-2">{description}</p>
            )}
            
            {duration > 0 && (
              <p className="text-sm opacity-60">
                Duration: {formatTime(duration)}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Time Display */}
      {duration > 0 && !showOverlay && (
        <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-sm px-2 py-1 rounded">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      )}
    </div>
  )
} 