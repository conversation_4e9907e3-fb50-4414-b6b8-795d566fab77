import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { FaUpload, FaCheckCircle, FaTimes } from "react-icons/fa";

// Define file upload variants
const fileUploadVariants = cva(
  "border-2 border-dashed rounded-lg transition-all duration-200 cursor-pointer",
  {
    variants: {
      variant: {
        default: "border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 hover:border-sehatti-gold-400 dark:hover:border-sehatti-gold-600",
        success: "border-green-400 bg-green-50 dark:bg-green-900/20",
        error: "border-red-400 bg-red-50 dark:bg-red-900/20",
      },
      size: {
        sm: "p-4",
        md: "p-4 sm:p-6",
        lg: "p-6 sm:p-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

// File upload component props
export interface FileUploadProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size" | "onChange">,
    VariantProps<typeof fileUploadVariants> {
  onFileSelect?: (file: File | null) => void;
  selectedFile?: File | null;
  acceptedTypes?: string[];
  maxSize?: number; // in MB
  label?: string;
  description?: string;
  errorMessage?: string;
}

// File upload component
const FileUpload = React.forwardRef<HTMLInputElement, FileUploadProps>(
  ({ 
    className, 
    variant, 
    size,
    onFileSelect,
    selectedFile,
    acceptedTypes = ['.xlsx', '.xls'],
    maxSize = 10,
    label = "Excel File",
    description = ".xlsx or .xls files only",
    errorMessage,
    accept,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `file-upload-${Math.random().toString(36).substr(2, 9)}`;
    const [isDragOver, setIsDragOver] = React.useState(false);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        // Validate file type
        const isValidType = acceptedTypes.some(type => 
          file.name.toLowerCase().endsWith(type.toLowerCase()) ||
          file.type.includes(type.replace('.', ''))
        );
        
        if (!isValidType) {
          onFileSelect?.(null);
          return;
        }
        
        // Validate file size
        if (file.size > maxSize * 1024 * 1024) {
          onFileSelect?.(null);
          return;
        }
        
        onFileSelect?.(file);
      }
    };

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      
      const file = e.dataTransfer.files[0];
      if (file) {
        // Create a fake input event to reuse the same validation logic
        const fakeEvent = {
          target: { files: [file] }
        } as React.ChangeEvent<HTMLInputElement>;
        handleFileChange(fakeEvent);
      }
    };

    const clearFile = () => {
      onFileSelect?.(null);
      // Reset the input value
      if (ref && 'current' in ref && ref.current) {
        ref.current.value = '';
      }
    };

    const currentVariant = errorMessage ? 'error' : selectedFile ? 'success' : variant;

    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300">
            📎 {label} *
          </label>
        )}
        
        <div
          className={cn(
            fileUploadVariants({ variant: currentVariant, size }),
            isDragOver && "border-sehatti-gold-500 bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20",
            className
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={ref}
            type="file"
            id={inputId}
            accept={accept || acceptedTypes.join(',')}
            onChange={handleFileChange}
            className="hidden"
            {...props}
          />
          
          <label htmlFor={inputId} className="cursor-pointer block text-center">
            {selectedFile ? (
              <div className="space-y-2">
                <FaCheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-green-600 mx-auto" />
                <p className="text-sm font-medium text-green-700 dark:text-green-400">
                  ✅ {selectedFile.name}
                </p>
                <p className="text-xs text-green-600 dark:text-green-500">
                  File ready for upload • Click to change
                </p>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    clearFile();
                  }}
                  className="inline-flex items-center gap-1 text-xs text-red-600 hover:text-red-700 mt-2"
                >
                  <FaTimes className="w-3 h-3" />
                  Remove file
                </button>
              </div>
            ) : (
              <div className="space-y-2">
                <FaUpload className="w-6 h-6 sm:w-8 sm:h-8 text-sehatti-warm-gray-400 mx-auto" />
                <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                  <span className="font-medium">Click to upload</span> or drag and drop
                </p>
                {description && (
                  <p className="text-xs text-sehatti-warm-gray-500">
                    {description}
                  </p>
                )}
              </div>
            )}
          </label>
        </div>
        
        {errorMessage && (
          <p className="text-sm text-red-500">
            {errorMessage}
          </p>
        )}
      </div>
    );
  }
);

FileUpload.displayName = "FileUpload";

export { FileUpload, fileUploadVariants }; 