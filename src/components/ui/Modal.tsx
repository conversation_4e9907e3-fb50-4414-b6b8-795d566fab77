import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define modal variants
const modalVariants = cva(
  "fixed inset-0 z-50 flex items-center justify-center px-4 py-6 pb-24",
  {
    variants: {
      variant: {
        default: "",
        centered: "items-center justify-center",
        top: "items-start justify-center pt-20 pb-24",
        bottom: "items-end justify-center pb-32",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const modalContentVariants = cva(
  "relative bg-white dark:bg-sehatti-warm-gray-900 rounded-2xl shadow-[0_25px_50px_rgba(0,0,0,0.4)] max-w-md w-full max-h-[80vh] overflow-auto border border-sehatti-warm-gray-200/60 dark:border-sehatti-warm-gray-700/60",
  {
    variants: {
      size: {
        sm: "max-w-sm",
        default: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-xl",
        "2xl": "max-w-2xl",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
);

// Modal component props
export interface ModalProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modalVariants> {
  open?: boolean;
  onClose?: () => void;
  children: React.ReactNode;
  size?: VariantProps<typeof modalContentVariants>["size"];
  closeOnBackdropClick?: boolean;
}

// Modal component
const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  ({ 
    className, 
    variant, 
    size,
    open = false, 
    onClose, 
    children, 
    closeOnBackdropClick = true,
    ...props 
  }, ref) => {
    const [isVisible, setIsVisible] = React.useState(open);

    React.useEffect(() => {
      setIsVisible(open);
    }, [open]);

    React.useEffect(() => {
      if (isVisible) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'unset';
      }

      return () => {
        document.body.style.overflow = 'unset';
      };
    }, [isVisible]);

    const handleBackdropClick = (e: React.MouseEvent) => {
      if (closeOnBackdropClick && e.target === e.currentTarget && onClose) {
        onClose();
      }
    };

    if (!isVisible) return null;

    return (
      <div
        ref={ref}
        className={cn(modalVariants({ variant }), className)}
        onClick={handleBackdropClick}
        {...props}
      >
        {/* Backdrop */}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />
        
        {/* Modal Content */}
        <div className={cn(modalContentVariants({ size }), "relative z-10")}>
          {children}
        </div>
      </div>
    );
  }
);

Modal.displayName = "Modal";

// Modal header component
const ModalHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("px-6 py-4 border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700", className)}
    {...props}
  />
));
ModalHeader.displayName = "ModalHeader";

// Modal content component
const ModalContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("px-6 py-4", className)} {...props} />
));
ModalContent.displayName = "ModalContent";

// Modal footer component
const ModalFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("px-6 py-4 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700", className)}
    {...props}
  />
));
ModalFooter.displayName = "ModalFooter";

// Modal title component
const ModalTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2
    ref={ref}
    className={cn("text-lg font-semibold text-sehatti-warm-gray-900 dark:text-white", className)}
    {...props}
  />
));
ModalTitle.displayName = "ModalTitle";

// Modal description component
const ModalDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400", className)}
    {...props}
  />
));
ModalDescription.displayName = "ModalDescription";

// Modal close component
const ModalClose = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, ...props }, ref) => (
  <button
    ref={ref}
    className={cn(
      "absolute top-4 right-4 p-2 rounded-lg text-sehatti-warm-gray-400 hover:text-sehatti-warm-gray-600 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 transition-colors",
      className
    )}
    {...props}
  />
));
ModalClose.displayName = "ModalClose";

export { 
  Modal, 
  ModalHeader, 
  ModalContent, 
  ModalFooter,
  ModalTitle,
  ModalDescription,
  ModalClose,
  modalVariants 
};