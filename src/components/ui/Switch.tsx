import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define switch variants - matching original dimensions exactly
const switchVariants = cva(
  "relative cursor-pointer transition-all duration-300 focus:outline-none flex-shrink-0",
  {
    variants: {
      size: {
        sm: "w-9 h-5",
        default: "w-11 h-6", // 44px x 24px to match original
        lg: "w-13 h-7",
      },
      variant: {
        default: "",
        success: "",
        warning: "",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
);

// Switch component props
export interface SwitchProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "size">,
    VariantProps<typeof switchVariants> {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
}

// Switch component - matching original design exactly
const Switch = React.forwardRef<HTMLDivElement, SwitchProps>(
  ({ 
    className, 
    size, 
    variant,
    checked = false, 
    onCheckedChange, 
    label,
    description,
    disabled = false,
    id,
    ...props 
  }, ref) => {
    const switchId = id || `switch-${Math.random().toString(36).substr(2, 9)}`;

    const handleClick = () => {
      if (!disabled && onCheckedChange) {
        onCheckedChange(!checked);
      }
    };

    if (label || description) {
      return (
        <div className="flex items-start gap-3">
          {/* Toggle Switch - exactly matching original */}
          <div
            ref={ref}
            className={cn(
              "relative w-11 h-6 rounded-xl cursor-pointer transition-all duration-300 flex-shrink-0 mt-0.5",
              checked ? "bg-[#d2b37a]" : "bg-[#e5e7eb]",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
            onClick={handleClick}
            {...props}
          >
            {/* Toggle Thumb - exactly matching original */}
            <div 
              className={cn(
                "absolute top-0.5 w-5 h-5 bg-white rounded-full transition-all duration-300 shadow-[0_2px_4px_rgba(0,0,0,0.2)]",
                checked ? "left-[22px]" : "left-0.5"
              )}
            />
          </div>
          
          <div className="flex-1">
            {label && (
              <label
                id={`${switchId}-label`}
                className="text-sm font-semibold text-[#374151] cursor-pointer block mb-1"
                onClick={handleClick}
              >
                {label}
              </label>
            )}
            {description && (
              <p
                id={`${switchId}-description`}
                className="text-xs text-[#6b7280] leading-relaxed m-0"
              >
                {description}
              </p>
            )}
          </div>
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative w-11 h-6 rounded-xl cursor-pointer transition-all duration-300",
          checked ? "bg-[#d2b37a]" : "bg-[#e5e7eb]",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {/* Toggle Thumb */}
        <div 
          className={cn(
            "absolute top-0.5 w-5 h-5 bg-white rounded-full transition-all duration-300 shadow-[0_2px_4px_rgba(0,0,0,0.2)]",
            checked ? "left-[22px]" : "left-0.5"
          )}
        />
      </div>
    );
  }
);

Switch.displayName = "Switch";

export { Switch, switchVariants }; 