import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define gradient text variants
const gradientTextVariants = cva(
  "bg-clip-text text-transparent",
  {
    variants: {
      gradient: {
        // Gold gradient
        gold: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400",
        
        // Gold rich gradient
        goldRich: "bg-gradient-to-r from-sehatti-gold-700 via-sehatti-gold-500 to-sehatti-gold-300",
        
        // Gold to warm gradient
        goldWarm: "bg-gradient-to-r from-sehatti-gold-600 to-amber-500",
        
        // Blue to purple gradient
        bluePurple: "bg-gradient-to-r from-blue-600 to-purple-600",
        
        // Green to blue gradient
        greenBlue: "bg-gradient-to-r from-green-500 to-blue-500",
        
        // Red to orange gradient
        redOrange: "bg-gradient-to-r from-red-600 to-orange-500",
      },
      size: {
        xs: "text-xs",
        sm: "text-sm",
        md: "text-base",
        lg: "text-lg",
        xl: "text-xl",
        "2xl": "text-2xl",
        "3xl": "text-3xl",
        "4xl": "text-4xl",
        "5xl": "text-5xl",
      },
      weight: {
        normal: "font-normal",
        medium: "font-medium",
        semibold: "font-semibold",
        bold: "font-bold",
        extrabold: "font-extrabold",
      },
    },
    defaultVariants: {
      gradient: "gold",
      size: "lg",
      weight: "semibold",
    },
  }
);

// Gradient text component props
export interface GradientTextProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof gradientTextVariants> {
  as?: React.ElementType;
}

// Gradient text component
const GradientText = React.forwardRef<HTMLSpanElement, GradientTextProps>(
  ({ className, gradient, size, weight, as: Component = "span", ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={cn(gradientTextVariants({ gradient, size, weight }), className)}
        {...props}
      />
    );
  }
);

GradientText.displayName = "GradientText";

export { GradientText, gradientTextVariants }; 