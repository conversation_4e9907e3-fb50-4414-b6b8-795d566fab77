import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { FaCheckCircle, FaTimes, FaExclamationTriangle } from "react-icons/fa";
import { SearchInput } from "./SearchInput";
import { Badge } from "./Badge";
import { Alert } from "./Alert";

// Define corporate selector variants
const corporateSelectorVariants = cva(
  "space-y-4",
  {
    variants: {
      variant: {
        default: "",
        compact: "space-y-3",
        expanded: "space-y-6",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

// Corporate interface
export interface Corporate {
  _id?: string;
  id?: string;
  company_id?: string;
  name?: string;
  company_name?: string;
  companyName?: string;
  email?: string;
  company_email?: string;
  companyEmail?: string;
}

// Corporate selector component props
export interface CorporateSelectorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof corporateSelectorVariants> {
  corporates: Corporate[];
  selectedCorporates: string[];
  onSelectionChange: (corporateIds: string[]) => void;
  title?: string;
  description?: string;
  searchPlaceholder?: string;
  required?: boolean;
  maxSelections?: number;
}

// Corporate selector component
const CorporateSelector = React.forwardRef<HTMLDivElement, CorporateSelectorProps>(
  ({ 
    className, 
    variant,
    corporates = [],
    selectedCorporates = [],
    onSelectionChange,
    title = "🏢 Corporate Assignment",
    description,
    searchPlaceholder = "🔍 Search corporates by name or email...",
    required = false,
    maxSelections,
    ...props 
  }, ref) => {
    const [searchTerm, setSearchTerm] = React.useState("");

    // Helper function to extract corporate data safely
    const getCorporateData = (corporate: Corporate) => {
      const id = corporate._id || corporate.id || corporate.company_id || '';
      const name = corporate.name || corporate.company_name || corporate.companyName || 'Unknown Corporate';
      const email = corporate.email || corporate.company_email || corporate.companyEmail || 'No email';
      return { id, name, email };
    };

    // Filter corporates based on search term
    const filteredCorporates = corporates.filter(corporate => {
      if (!corporate) return false;
      
      const { name, email } = getCorporateData(corporate);
      const searchLower = searchTerm.toLowerCase();
      
      return (
        name.toLowerCase().includes(searchLower) ||
        email.toLowerCase().includes(searchLower)
      );
    });

    // Toggle corporate selection
    const toggleCorporate = (corporateId: string) => {
      if (selectedCorporates.includes(corporateId)) {
        // Remove from selection
        onSelectionChange(selectedCorporates.filter(id => id !== corporateId));
      } else {
        // Add to selection (check max limit)
        if (!maxSelections || selectedCorporates.length < maxSelections) {
          onSelectionChange([...selectedCorporates, corporateId]);
        }
      }
    };

    // Clear all selections
    const clearAll = () => {
      onSelectionChange([]);
    };

    // Clear search
    const clearSearch = () => {
      setSearchTerm("");
    };

    return (
      <div
        ref={ref}
        className={cn(corporateSelectorVariants({ variant }), className)}
        {...props}
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <div>
            <h3 className="text-base sm:text-lg font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              {title} {required && "*"}
            </h3>
            {description && (
              <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mt-1">
                {description}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              size="sm" 
              className="bg-sehatti-gold-50 text-sehatti-gold-700 border-sehatti-gold-200 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 dark:border-sehatti-gold-800/30"
            >
              {selectedCorporates.length} selected
              {maxSelections && ` / ${maxSelections}`}
            </Badge>
          </div>
        </div>

        {/* Search Input */}
        <SearchInput
          placeholder={searchPlaceholder}
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
          onClear={clearSearch}
          size="md"
        />

        {/* Selected Corporates Display */}
        {selectedCorporates.length > 0 && (
          <div className="bg-gradient-to-r from-sehatti-gold-50 to-sehatti-gold-100 dark:from-sehatti-gold-900/20 dark:to-sehatti-gold-800/20 rounded-lg p-3 border border-sehatti-gold-200 dark:border-sehatti-gold-800/30">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-sehatti-gold-700 dark:text-sehatti-gold-300 uppercase tracking-wide">
                ✨ Selected Corporates
              </span>
              <button
                type="button"
                onClick={clearAll}
                className="text-xs text-sehatti-gold-600 hover:text-sehatti-gold-800 dark:text-sehatti-gold-400 dark:hover:text-sehatti-gold-200 transition-colors"
              >
                Clear all
              </button>
            </div>
            <div className="flex flex-wrap gap-2 max-h-20 overflow-y-auto">
              {selectedCorporates.map((corporateId) => {
                const corporate = corporates.find(c => {
                  const { id } = getCorporateData(c);
                  return id === corporateId;
                });
                
                const { name } = corporate ? getCorporateData(corporate) : { name: 'Unknown Corporate' };
                
                return (
                  <Badge
                    key={corporateId}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 px-2 py-1 bg-white/80 text-sehatti-gold-800 border-sehatti-gold-300 dark:bg-sehatti-warm-gray-800/80 dark:text-sehatti-gold-300"
                  >
                    <span className="max-w-[100px] truncate">{name}</span>
                    <button
                      type="button"
                      onClick={() => toggleCorporate(corporateId)}
                      className="hover:bg-sehatti-gold-200 dark:hover:bg-sehatti-gold-800/50 rounded-full p-0.5 transition-colors"
                    >
                      <FaTimes className="h-2.5 w-2.5" />
                    </button>
                  </Badge>
                );
              })}
            </div>
          </div>
        )}

        {/* Corporate List */}
        {corporates.length === 0 ? (
          <Alert variant="warning" className="text-center">
            <FaExclamationTriangle className="w-4 h-4" />
            <div className="ml-2">
              <p className="font-medium">No corporates available</p>
              <p className="text-sm">Please add corporates before proceeding</p>
            </div>
          </Alert>
        ) : (
          <>
            {searchTerm.trim() ? (
              <div className="max-h-48 sm:max-h-60 overflow-y-auto border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg bg-white dark:bg-sehatti-warm-gray-800">
                {filteredCorporates.slice(0, 50).map((corporate) => {
                  const { id, name, email } = getCorporateData(corporate);
                  const isSelected = selectedCorporates.includes(id);
                  const isDisabled = maxSelections && !isSelected && selectedCorporates.length >= maxSelections;
                  
                  return (
                    <label 
                      key={id} 
                      className={cn(
                        "flex items-center space-x-3 cursor-pointer p-3 border-b border-sehatti-warm-gray-100 dark:border-sehatti-warm-gray-700 last:border-b-0 transition-colors",
                        isDisabled 
                          ? "opacity-50 cursor-not-allowed" 
                          : "hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-700"
                      )}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => !isDisabled && toggleCorporate(id)}
                        disabled={isDisabled}
                        className="h-4 w-4 text-sehatti-gold-600 focus:ring-sehatti-gold-500 border-sehatti-warm-gray-300 rounded disabled:opacity-50"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 truncate">
                          {name}
                        </div>
                        <div className="text-xs text-sehatti-warm-gray-500 truncate">
                          {email}
                        </div>
                      </div>
                      {isSelected && (
                        <FaCheckCircle className="w-4 h-4 text-sehatti-gold-600 flex-shrink-0" />
                      )}
                    </label>
                  );
                })}
                {filteredCorporates.length === 0 && (
                  <div className="text-sm text-sehatti-warm-gray-500 text-center py-8">
                    🔍 No corporates found matching "{searchTerm}"
                  </div>
                )}
                {filteredCorporates.length > 50 && (
                  <div className="text-xs text-sehatti-warm-gray-500 text-center py-2 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
                    Showing first 50 results. Refine your search for more.
                  </div>
                )}
              </div>
            ) : (
              <div className="border border-dashed border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 rounded-lg p-6 sm:p-8 text-center bg-sehatti-warm-gray-50/50 dark:bg-sehatti-warm-gray-800/50">
                <div className="space-y-3">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 text-sehatti-warm-gray-400 mx-auto">
                    🔍
                  </div>
                  <div>
                    <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 font-medium text-sm sm:text-base">
                      Search to find corporates
                    </p>
                    <p className="text-xs sm:text-sm text-sehatti-warm-gray-500 mt-1">
                      Type in the search box above to browse and select
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  }
);

CorporateSelector.displayName = "CorporateSelector";

export { CorporateSelector, corporateSelectorVariants }; 