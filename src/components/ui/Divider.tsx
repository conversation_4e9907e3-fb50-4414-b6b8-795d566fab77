import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define divider variants
const dividerVariants = cva(
  "relative flex items-center",
  {
    variants: {
      variant: {
        // Default with gradient line
        default: "my-6",
        
        // Subtle divider
        subtle: "my-4",
        
        // Bold divider
        bold: "my-8",
      },
      orientation: {
        horizontal: "w-full flex-row",
        vertical: "h-full flex-col",
      },
    },
    defaultVariants: {
      variant: "default",
      orientation: "horizontal",
    },
  }
);

// Divider component props
export interface DividerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof dividerVariants> {
  label?: string;
  children?: React.ReactNode;
}

// Divider component
const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ className, variant, orientation, label, children, ...props }, ref) => {
    const content = label || children;

    if (orientation === "vertical") {
      return (
        <div
          ref={ref}
          className={cn(dividerVariants({ variant, orientation }), className)}
          {...props}
        >
          <div className="w-px bg-gradient-to-b from-transparent via-[#e5e7eb] to-transparent flex-1" />
          {content && (
            <>
              <div className="px-2 text-sm font-medium text-[#9ca3af]">
                {content}
              </div>
              <div className="w-px bg-gradient-to-b from-transparent via-[#e5e7eb] to-transparent flex-1" />
            </>
          )}
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(dividerVariants({ variant, orientation }), className)}
        {...props}
      >
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#e5e7eb] to-transparent" />
        {content && (
          <>
            <span className="px-4 text-sm font-medium text-[#9ca3af] bg-white">
              {content}
            </span>
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#e5e7eb] to-transparent" />
          </>
        )}
      </div>
    );
  }
);

Divider.displayName = "Divider";

export { Divider, dividerVariants }; 