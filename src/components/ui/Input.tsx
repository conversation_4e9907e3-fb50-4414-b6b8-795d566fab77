import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define input variants
const inputVariants = cva(
  "flex w-full rounded-xl border-2 bg-[#fafafa] px-4 py-3 text-[15px] transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
          variant: {
      default: "border-[#e5e7eb] focus:border-[#d2b37a] focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)]",
      error: "border-red-300 focus:border-red-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(239,68,68,0.1)]",
      success: "border-green-300 focus:border-green-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)]",
      gold: "border-[#d2b37a] focus:border-[#c19648] focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)]",
    },
      inputSize: {
        sm: "h-8 px-3 py-1 text-sm",
        default: "h-12 px-4 py-3 text-[15px]",
        lg: "h-14 px-6 py-4 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      inputSize: "default",
    },
  }
);

// Input component props
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: React.ReactNode;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  rightElement?: React.ReactNode;
}

// Input component
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type, 
    variant, 
    inputSize,
    label,
    helperText,
    error,
    leftIcon,
    rightIcon,
    rightElement,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;
    const finalVariant = hasError ? "error" : variant;

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-semibold text-[#374151]"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            id={inputId}
            className={cn(
              inputVariants({ variant: finalVariant, inputSize }),
              leftIcon && "pl-10",
              (rightIcon || rightElement) && "pr-12",
              className
            )}
            ref={ref}
            {...props}
          />
          
          {(rightIcon || rightElement) && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {rightElement || (
                <div className="text-muted-foreground">
                  {rightIcon}
                </div>
              )}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p className={cn(
            "text-sm",
            hasError ? "text-red-500" : "text-muted-foreground"
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input, inputVariants };
