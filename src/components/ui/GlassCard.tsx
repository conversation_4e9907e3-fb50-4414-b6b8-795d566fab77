import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

// Define glass card variants
const glassCardVariants = cva(
  "relative overflow-hidden transition-all duration-300",
  {
    variants: {
      variant: {
        // Main login card style
        default: "bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-[20px] border border-[rgba(210,179,122,0.2)] shadow-[0_25px_60px_rgba(210,179,122,0.15),0_12px_24px_rgba(210,179,122,0.1)]",
        
        // Secondary glass style
        secondary: "bg-gradient-to-br from-white/90 to-white/80 backdrop-blur-[16px] border border-[rgba(210,179,122,0.15)] shadow-[0_20px_40px_rgba(210,179,122,0.1)]",
        
        // Subtle glass style
        subtle: "bg-gradient-to-br from-white/80 to-white/70 backdrop-blur-[12px] border border-[rgba(210,179,122,0.1)] shadow-[0_10px_30px_rgba(210,179,122,0.05)]",
        
        // Dark glass style
        dark: "bg-gradient-to-br from-black/20 to-black/10 backdrop-blur-[20px] border border-white/10 shadow-[0_25px_60px_rgba(0,0,0,0.3)]",
      },
      size: {
        sm: "p-4 rounded-lg",
        default: "p-8 rounded-3xl",
        lg: "p-12 rounded-3xl",
        xl: "p-16 rounded-3xl",
      },
      glow: {
        true: "before:absolute before:inset-0 before:rounded-[inherit] before:p-[1px] before:bg-gradient-to-r before:from-[#d2b37a] before:to-[#c19648] before:-z-10 before:opacity-50",
        false: "",
      },
      decorativeTop: {
        true: "before:absolute before:top-0 before:left-0 before:right-0 before:h-1 before:bg-gradient-to-r before:from-[#d2b37a] before:via-[#c19648] before:to-[#d2b37a]",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      glow: false,
      decorativeTop: false,
    },
  }
);

// Glass card component props
export interface GlassCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof glassCardVariants> {
  children: React.ReactNode;
}

// Glass card component
const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ className, variant, size, glow, decorativeTop, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(glassCardVariants({ variant, size, glow, decorativeTop }), className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

GlassCard.displayName = "GlassCard";

// Glass card header component
const GlassCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-center mb-8", className)}
    {...props}
  />
));
GlassCardHeader.displayName = "GlassCardHeader";

// Glass card content component
const GlassCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
));
GlassCardContent.displayName = "GlassCardContent";

// Glass card footer component
const GlassCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("mt-8", className)}
    {...props}
  />
));
GlassCardFooter.displayName = "GlassCardFooter";

export { 
  GlassCard, 
  GlassCardHeader, 
  GlassCardContent, 
  GlassCardFooter, 
  glassCardVariants 
}; 