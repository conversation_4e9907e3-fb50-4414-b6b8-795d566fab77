import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// Define card variants
const cardVariants = cva(
  "rounded-lg",
  {
    variants: {
      variant: {
        // Default card style
        default: "bg-white dark:bg-sehatti-warm-gray-950 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
        
        // Gold themed card
        gold: "bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 border border-sehatti-gold-200 dark:border-sehatti-gold-800/30",
        
        // Glass effect card
        glass: "bg-white/80 dark:bg-sehatti-warm-gray-950/80 backdrop-blur-sm border border-sehatti-warm-gray-200/50 dark:border-sehatti-warm-gray-800/50",
        
        // Gold glass effect card
        goldGlass: "bg-sehatti-gold-50/80 dark:bg-sehatti-gold-900/20 backdrop-blur-sm border border-sehatti-gold-200/50 dark:border-sehatti-gold-800/30",
        
        // Solid card without border
        solid: "bg-white dark:bg-sehatti-warm-gray-950",
        
        // Outline card with border only
        outline: "border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800 bg-transparent",
      },
      size: {
        sm: "p-4",
        md: "p-6",
        lg: "p-8",
      },
      shadow: {
        none: "",
        sm: "shadow-sm",
        md: "shadow-md",
        lg: "shadow-lg",
        gold: "shadow-gold",
        goldGlow: "shadow-gold-glow",
      },
      hover: {
        true: "transition-all duration-200 hover:shadow-md hover:-translate-y-1",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      shadow: "sm",
      hover: false,
    },
  }
)

// Card component props
export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

// Card component
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, shadow, hover, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, shadow, hover }), className)}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

// Card header component
const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 pb-4", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

// Card title component
const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("text-lg font-semibold leading-none tracking-tight", className)}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

// Card description component
const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

// Card content component
const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
))
CardContent.displayName = "CardContent"

// Card footer component
const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-4", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  cardVariants,
}
