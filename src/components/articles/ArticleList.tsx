import React, { useState, useEffect, useCallback, useRef } from 'react'
import { 
  FaPlus, 
  FaSearch, 
  FaFilter, 
  FaEye, 
  FaEdit, 
  FaTrash, 
  FaClock, 
  FaUser, 
  FaTag,
  FaNewspaper,
  FaGlobe,
  FaImage
} from 'react-icons/fa'
import { 
  Card, 
  CardContent, 
  Button, 
  Input, 
  Select, 
  Badge, 
  Switch,
  Checkbox,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  EmptyState
} from '../ui'
import toast from 'react-hot-toast'
import { articleService } from '../../services/articleService'
import type { ArticleResponse, ArticleFilters, ArticleStatus } from '../../types/article'
import { Language } from '../../types/article'

// Language flags mapping
const LANGUAGE_FLAGS = {
  en: '🇺🇸',
  ar: '🇸🇦', 
  hi: '🇮🇳'
}

// Article status colors
const STATUS_COLORS = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  draft: 'bg-yellow-100 text-yellow-800',
  published: 'bg-blue-100 text-blue-800',
  archived: 'bg-purple-100 text-purple-800'
}

interface ArticleListProps {
  hubId?: string
  showSearch?: boolean
  showFilters?: boolean
  showActions?: boolean
  onCreateClick?: () => void
  onViewClick?: (articleId: string) => void
  onEditClick?: (articleId: string) => void
  onDeleteClick?: (articleId: string) => void
}

export const ArticleList: React.FC<ArticleListProps> = ({
  hubId,
  showSearch = true,
  showFilters = true,
  showActions = true,
  onCreateClick,
  onViewClick,
  onEditClick,
  onDeleteClick
}) => {
  // State management
  const [articles, setArticles] = useState<ArticleResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<ArticleResponse[]>([])
  const [isSearchMode, setIsSearchMode] = useState(false)
  const [selectedArticles, setSelectedArticles] = useState<string[]>([])
  const [filters, setFilters] = useState<ArticleFilters>({
    language: Language.ENGLISH,
    limit: 20
  })
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    type: 'default' as 'default' | 'danger',
    onConfirm: () => {},
    isLoading: false
  })

  const loadingRef = useRef(false)
  const defaultParams = { language: Language.ENGLISH, limit: 20 }

  // Utility functions
  const getStatusBadge = (status: ArticleStatus) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Published' },
      inactive: { color: 'bg-gray-100 text-gray-800', label: 'Inactive' },
      draft: { color: 'bg-yellow-100 text-yellow-800', label: 'Draft' },
      published: { color: 'bg-blue-100 text-blue-800', label: 'Published' },
      archived: { color: 'bg-purple-100 text-purple-800', label: 'Archived' }
    }

    const config = statusConfig[status] || statusConfig.inactive
    return (
      <Badge className={`${config.color} text-xs font-medium`}>
        {config.label}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getArticleDisplayContent = (article: ArticleResponse) => {
    const content = article.content?.[filters.language || Language.ENGLISH] || 
                   article.content?.en || 
                   Object.values(article.content || {})[0]
    
    return {
      title: content?.title || 'Untitled Article',
      description: content?.description || 'No description available',
      summary: content?.summary || ''
    }
  }

  // Load articles data
  const loadArticles = useCallback(async () => {
    if (loadingRef.current) return
    
    try {
      loadingRef.current = true
      setLoading(true)
      setError(null)

      const params = { ...defaultParams, ...filters }
      if (hubId) {
        params.hub_id = hubId
      }

      const response = await articleService.listArticles(params)
      setArticles(response.data || [])
    } catch (err) {
      console.error('Failed to load articles:', err)
      setError(err instanceof Error ? err.message : 'Failed to load articles')
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }, [hubId])

  // Initialize data
  useEffect(() => {
    loadArticles()
  }, [])

  // Handle filter changes
  useEffect(() => {
    if (!loadingRef.current) {
      loadArticles()
    }
  }, [filters])

  // Search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setIsSearchMode(false)
      setSearchResults([])
      return
    }

    setIsSearchMode(true)
    const filtered = articles.filter(article => {
      const content = getArticleDisplayContent(article)
      return content.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
             content.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
             content.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
             article.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    })
    setSearchResults(filtered)
  }, [searchQuery, articles, filters.language])

  // Data to display
  const dataToDisplay = isSearchMode ? searchResults : articles

  // Selection handlers
  const handleSelectArticle = (articleId: string) => {
    setSelectedArticles(prev =>
      prev.includes(articleId)
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    )
  }

  const handleSelectAll = () => {
    if (selectedArticles.length === dataToDisplay.length) {
      setSelectedArticles([])
    } else {
      setSelectedArticles(dataToDisplay.map(item => item.article_id))
    }
  }

  const isAllSelected = selectedArticles.length > 0 && selectedArticles.length === dataToDisplay.length

  // Status toggle
  const handleStatusToggle = async (articleId: string, currentStatus: ArticleStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
      await articleService.updateArticle(articleId, { 
        status: newStatus as ArticleStatus
      })
      
      setArticles(prev =>
        prev.map(item =>
          item.article_id === articleId
            ? { ...item, status: newStatus as ArticleStatus }
            : item
        )
      )
      
      toast.success(`Article ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`)
    } catch (err) {
      console.error('Failed to update status:', err)
      toast.error('Failed to update article status')
    }
  }

  // Delete handler
  const handleDelete = (articleId: string, title: string) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Article',
      message: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
      type: 'danger',
      onConfirm: () => confirmDelete(articleId),
      isLoading: false
    })
  }

  const confirmDelete = async (articleId: string) => {
    try {
      setConfirmDialog(prev => ({ ...prev, isLoading: true }))
      
      await articleService.deleteArticle(articleId)
      
      setArticles(prev => prev.filter(item => item.article_id !== articleId))
      setSelectedArticles(prev => prev.filter(id => id !== articleId))
      setConfirmDialog(prev => ({ ...prev, isOpen: false }))
      
      toast.success('Article deleted successfully')
      
      if (onDeleteClick) {
        onDeleteClick(articleId)
      }
    } catch (err) {
      console.error('Failed to delete article:', err)
      toast.error('Failed to delete article')
      setConfirmDialog(prev => ({ ...prev, isLoading: false }))
    }
  }

  // Bulk actions
  const handleBulkDelete = () => {
    if (selectedArticles.length === 0) return

    setConfirmDialog({
      isOpen: true,
      title: 'Delete Articles',
      message: `Are you sure you want to delete ${selectedArticles.length} articles? This action cannot be undone.`,
      type: 'danger',
      onConfirm: executeBulkDelete,
      isLoading: false
    })
  }

  const executeBulkDelete = async () => {
    try {
      setConfirmDialog(prev => ({ ...prev, isLoading: true }))

      await Promise.all(selectedArticles.map(id => articleService.deleteArticle(id)))
      
      setArticles(prev => prev.filter(item => !selectedArticles.includes(item.article_id)))
      setSelectedArticles([])
      setConfirmDialog(prev => ({ ...prev, isOpen: false }))
      
      toast.success(`${selectedArticles.length} articles deleted successfully`)
    } catch (err) {
      console.error('Failed to delete articles:', err)
      toast.error('Failed to delete articles')
      setConfirmDialog(prev => ({ ...prev, isLoading: false }))
    }
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-600 mb-4">
          <FaNewspaper className="h-12 w-12 mx-auto mb-2" />
          <p className="text-lg font-semibold">Error Loading Articles</p>
          <p className="text-sm">{error}</p>
        </div>
        <Button onClick={loadArticles} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-sehatti-warm-gray-900">Articles</h2>
          <p className="text-sehatti-warm-gray-600">
            Manage your published articles and content
          </p>
        </div>
        
        {onCreateClick && (
          <Button onClick={onCreateClick} className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700">
            <FaPlus className="h-4 w-4 mr-2" />
            Create Article
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <div className="flex flex-col sm:flex-row gap-4">
          {showSearch && (
            <div className="flex-1">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sehatti-warm-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          )}
          
          {showFilters && (
            <div className="flex gap-3">
              <Select
                value={filters.language || Language.ENGLISH}
                onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value as Language }))}
                className="w-32"
              >
                <option value={Language.ENGLISH}>English</option>
                <option value={Language.ARABIC}>العربية</option>
                <option value={Language.HINDI}>हिंदी</option>
              </Select>
              
              <Select
                value={filters.status || 'all'}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value === 'all' ? undefined : e.target.value as ArticleStatus }))}
                className="w-32"
              >
                <option value="all">All Status</option>
                <option value="active">Published</option>
                <option value="draft">Draft</option>
                <option value="inactive">Inactive</option>
                <option value="archived">Archived</option>
              </Select>
            </div>
          )}
        </div>
      )}

      {/* Bulk Actions */}
      {selectedArticles.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-sehatti-gold-50 rounded-lg border border-sehatti-gold-200">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={isAllSelected}
              indeterminate={selectedArticles.length > 0 && !isAllSelected}
              onChange={handleSelectAll}
            />
            <span className="text-sm font-medium text-sehatti-warm-gray-700">
              {selectedArticles.length} article{selectedArticles.length !== 1 ? 's' : ''} selected
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkDelete}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <FaTrash className="h-3 w-3 mr-1" />
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {/* Articles List */}
      <div className="space-y-4">
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="h-48 bg-sehatti-warm-gray-200 rounded-t-lg"></div>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-sehatti-warm-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-sehatti-warm-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-sehatti-warm-gray-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : dataToDisplay.length === 0 ? (
          <EmptyState
            icon={<FaNewspaper className="h-12 w-12" />}
            title={isSearchMode ? "No articles found" : "No articles yet"}
            description={
              isSearchMode 
                ? `No articles match "${searchQuery}". Try adjusting your search terms.`
                : "Get started by creating your first article."
            }
            action={
              !isSearchMode && onCreateClick ? (
                <Button onClick={onCreateClick} className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700">
                  <FaPlus className="h-4 w-4 mr-2" />
                  Create Article
                </Button>
              ) : undefined
            }
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dataToDisplay.map((article) => {
              const isSelected = selectedArticles.includes(article.article_id)
              const content = getArticleDisplayContent(article)
              
              return (
                <Card
                  key={article.article_id}
                  className={`group cursor-pointer transition-all duration-200 hover:shadow-lg border-2 ${
                    isSelected 
                      ? 'border-sehatti-gold-300 bg-sehatti-gold-50' 
                      : 'border-sehatti-warm-gray-200 hover:border-sehatti-gold-200'
                  }`}
                  onClick={() => onViewClick?.(article.article_id)}
                >
                  {/* Selection Checkbox */}
                  <div 
                    className="absolute top-4 left-4 z-10"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleSelectArticle(article.article_id)}
                      className="bg-white shadow-sm"
                    />
                  </div>

                  {/* Article Thumbnail */}
                  <div className="relative h-48 bg-gradient-to-br from-sehatti-gold-100 to-sehatti-gold-200 flex items-center justify-center">
                    {article.thumbnail_url ? (
                      <img 
                        src={article.thumbnail_url} 
                        alt={content.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex flex-col items-center gap-2">
                        <div className="p-3 rounded-lg bg-white/20 backdrop-blur-sm">
                          <FaNewspaper className="h-8 w-8 text-sehatti-gold-600" />
                        </div>
                        <span className="text-xs text-sehatti-warm-gray-600 font-medium">
                          ARTICLE
                        </span>
                      </div>
                    )}
                    
                    {/* Status Badge */}
                    <div className="absolute top-3 right-3">
                      {getStatusBadge(article.status)}
                    </div>
                  </div>

                  <CardContent className="p-6">
                    {/* Article Title and Description */}
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold text-lg text-sehatti-warm-gray-900 line-clamp-1">
                          {content.title}
                        </h3>
                        {content.description && (
                          <p className="text-sehatti-warm-gray-600 text-sm mt-1 line-clamp-2">
                            {content.description}
                          </p>
                        )}
                      </div>

                      {/* Tags */}
                      {article.tags && article.tags.length > 0 && (
                        <div className="flex items-center gap-1 flex-wrap">
                          <FaTag className="h-3 w-3 text-sehatti-warm-gray-400" />
                          {article.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {article.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{article.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-sehatti-warm-gray-500">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <FaClock className="h-3 w-3" />
                            <span>{formatDate(article.created_at)}</span>
                          </div>
                          {article.author && (
                            <div className="flex items-center gap-1">
                              <FaUser className="h-3 w-3" />
                              <span className="truncate max-w-20">{article.author}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Languages */}
                        <div className="flex items-center gap-1">
                          {article.available_languages?.map((lang) => (
                            <span key={lang} className="text-sm" title={lang}>
                              {LANGUAGE_FLAGS[lang as keyof typeof LANGUAGE_FLAGS] || '🏳️'}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    {showActions && (
                      <div className="mt-4 pt-4 border-t border-sehatti-warm-gray-100">
                        <div className="flex items-center justify-between">
                          {/* Status Toggle */}
                          <div 
                            className="flex items-center gap-2"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Switch
                              checked={article.status === 'active'}
                              onChange={() => handleStatusToggle(article.article_id, article.status)}
                              size="sm"
                            />
                            <span className="text-xs text-sehatti-warm-gray-500">
                              {article.status === 'active' ? 'Published' : 'Inactive'}
                            </span>
                          </div>

                          {/* Action Menu */}
                          <div 
                            className="flex items-center gap-1"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onViewClick?.(article.article_id)}
                              className="p-2 h-8 w-8"
                            >
                              <FaEye className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onEditClick?.(article.article_id)}
                              className="p-2 h-8 w-8"
                            >
                              <FaEdit className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(article.article_id, content.title)}
                              className="p-2 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <FaTrash className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Confirmation Dialog */}
      <Modal open={confirmDialog.isOpen} onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <ModalHeader>
          <h3 className="text-lg font-semibold">{confirmDialog.title}</h3>
        </ModalHeader>
        <ModalContent>
          <p className="text-sehatti-warm-gray-600">{confirmDialog.message}</p>
        </ModalContent>
        <ModalFooter className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
          >
            Cancel
          </Button>
          <Button
            variant={confirmDialog.type === 'danger' ? 'destructive' : 'default'}
            onClick={confirmDialog.onConfirm}
            isLoading={confirmDialog.isLoading}
          >
            Confirm
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  )
}

export default ArticleList 