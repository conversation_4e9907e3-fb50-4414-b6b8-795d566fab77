import React, { useState, useEffect } from 'react'
import { 
  FaFileAlt, 
  FaPlus, 
  FaEye, 
  FaEdit, 
  FaTrash, 
  FaSearch,
  FaFilter,
  FaLanguage,
  FaCalendarAlt,
  FaUser
} from 'react-icons/fa'
import toast from 'react-hot-toast'

import { <PERSON><PERSON> } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import { Select } from '../ui/Select'
import { Badge } from '../ui/Badge'
import { EmptyState } from '../ui/EmptyState'

interface Article {
  id: string
  title: string
  description: string
  content: string
  status: 'draft' | 'published' | 'archived'
  language: 'en' | 'ar' | 'hi'
  author: string
  created_at: string
  updated_at: string
  tags: string[]
}

interface ArticleListProps {
  onCreateClick?: () => void
  onViewClick?: (articleId: string) => void
  onEditClick?: (articleId: string) => void
  onDeleteClick?: (articleId: string) => void
  showActions?: boolean
  showSearch?: boolean
  showFilters?: boolean
}

// Mock data for demonstration
const mockArticles: Article[] = [
  {
    id: '1',
    title: 'Getting Started with Sehatti Platform',
    description: 'A comprehensive guide to using the Sehatti platform for corporate wellness.',
    content: 'Lorem ipsum dolor sit amet...',
    status: 'published',
    language: 'en',
    author: 'Admin User',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    tags: ['guide', 'tutorial', 'wellness']
  },
  {
    id: '2',
    title: 'دليل المنصة للشركات',
    description: 'دليل شامل لاستخدام منصة صحتي للرفاهية المؤسسية.',
    content: 'نص تجريبي باللغة العربية...',
    status: 'draft',
    language: 'ar',
    author: 'Admin User',
    created_at: '2024-01-14T09:00:00Z',
    updated_at: '2024-01-14T09:00:00Z',
    tags: ['دليل', 'تعليمي', 'صحة']
  },
  {
    id: '3',
    title: 'कॉर्पोरेट वेलनेस गाइड',
    description: 'कॉर्पोरेट कल्याण के लिए सेहत्ती प्लेटफॉर्म का उपयोग करने के लिए एक व्यापक गाइड।',
    content: 'हिंदी में नमूना पाठ...',
    status: 'published',
    language: 'hi',
    author: 'Admin User',
    created_at: '2024-01-13T08:00:00Z',
    updated_at: '2024-01-13T08:00:00Z',
    tags: ['गाइड', 'ट्यूटोरियल', 'स्वास्थ्य']
  }
]

export const ArticleList: React.FC<ArticleListProps> = ({
  onCreateClick,
  onViewClick,
  onEditClick,
  onDeleteClick,
  showActions = true,
  showSearch = true,
  showFilters = true
}) => {
  const [articles, setArticles] = useState<Article[]>(mockArticles)
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [languageFilter, setLanguageFilter] = useState('all')

  // Filter articles based on search and filters
  const filteredArticles = articles.filter(article => {
    const matchesSearch = searchQuery === '' || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesStatus = statusFilter === 'all' || article.status === statusFilter
    const matchesLanguage = languageFilter === 'all' || article.language === languageFilter

    return matchesSearch && matchesStatus && matchesLanguage
  })

  const getStatusBadge = (status: Article['status']) => {
    const variants = {
      published: 'success',
      draft: 'warning',
      archived: 'secondary'
    } as const

    return (
      <Badge variant={variants[status]} size="sm">
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getLanguageFlag = (language: Article['language']) => {
    const flags = {
      en: '🇺🇸',
      ar: '🇸🇦',
      hi: '🇮🇳'
    }
    return flags[language]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleView = (article: Article) => {
    if (onViewClick) {
      onViewClick(article.id)
    } else {
      toast.success(`Viewing: ${article.title}`)
    }
  }

  const handleEdit = (article: Article) => {
    if (onEditClick) {
      onEditClick(article.id)
    } else {
      toast.success(`Editing: ${article.title}`)
    }
  }

  const handleDelete = (article: Article) => {
    if (onDeleteClick) {
      onDeleteClick(article.id)
    } else {
      // Simple confirmation and removal for demo
      if (window.confirm(`Are you sure you want to delete "${article.title}"?`)) {
        setArticles(prev => prev.filter(a => a.id !== article.id))
        toast.success('Article deleted successfully!')
      }
    }
  }

  const handleCreate = () => {
    if (onCreateClick) {
      onCreateClick()
    } else {
      toast.success('Create article functionality coming soon!')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Articles
          </h2>
          <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
            Manage your article content and publications
          </p>
        </div>
        
        {showActions && (
          <Button onClick={handleCreate} className="flex items-center gap-2">
            <FaPlus className="w-4 h-4" />
            Create Article
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {showSearch && (
              <div className="flex-1">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sehatti-warm-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search articles..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            )}
            
            {showFilters && (
              <div className="flex gap-4">
                <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
                  <option value="all">All Status</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </Select>
                
                <Select value={languageFilter} onChange={(e) => setLanguageFilter(e.target.value)}>
                  <option value="all">All Languages</option>
                  <option value="en">English</option>
                  <option value="ar">العربية</option>
                  <option value="hi">हिन्दी</option>
                </Select>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Articles Grid */}
      {filteredArticles.length === 0 ? (
        <EmptyState
          icon={<FaFileAlt className="w-16 h-16 text-sehatti-gold-400" />}
          title="No Articles Found"
          description={searchQuery ? "No articles match your search criteria." : "No articles have been created yet."}
          action={{
            label: "Create Article",
            onClick: handleCreate
          }}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredArticles.map((article) => (
            <Card key={article.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2 mb-2">
                      {article.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      <span className="text-lg">{getLanguageFlag(article.language)}</span>
                      <FaUser className="w-3 h-3" />
                      <span>{article.author}</span>
                    </div>
                  </div>
                  {getStatusBadge(article.status)}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 line-clamp-3">
                  {article.description}
                </p>
                
                {article.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {article.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" size="sm">
                        {tag}
                      </Badge>
                    ))}
                    {article.tags.length > 3 && (
                      <Badge variant="outline" size="sm">
                        +{article.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
                
                <div className="flex items-center justify-between pt-2 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
                  <div className="flex items-center gap-1 text-xs text-sehatti-warm-gray-500">
                    <FaCalendarAlt className="w-3 h-3" />
                    <span>{formatDate(article.created_at)}</span>
                  </div>
                  
                  {showActions && (
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleView(article)}
                        className="p-2"
                      >
                        <FaEye className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEdit(article)}
                        className="p-2"
                      >
                        <FaEdit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDelete(article)}
                        className="p-2 text-red-600 hover:text-red-700"
                      >
                        <FaTrash className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

export default ArticleList 