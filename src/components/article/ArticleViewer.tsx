import React, { useState, useEffect } from 'react'
import { 
  FaArrowLeft, 
  FaClock, 
  FaBookOpen, 
  FaHeart, 
  FaThumbsUp, 
  FaShare, 
  FaGlobe,
  FaUser,
  FaCalendarAlt,
  FaTag,
  FaChevronUp,
  FaChevronDown,
  FaBookmark,
  FaPrint,
  FaTextHeight
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'

interface WellnessArticle {
  id: string
  titleEN: string
  titleAR: string
  titleHI: string
  descriptionEN: string
  descriptionAR: string
  descriptionHI: string
  category: string
  division: string
  readTime: number
  source: string
}

interface ArticleViewerProps {
  article: WellnessArticle
  language: 'en' | 'ar' | 'hi'
  onBack: () => void
  onLanguageChange: (lang: 'en' | 'ar' | 'hi') => void
}

// Extended content for each article (simulating full article content)
const EXTENDED_CONTENT = {
  'EW01': {
    en: {
      sections: [
        {
          title: "The Silent Impact of Your Work Environment",
          content: "Research consistently shows that the physical environment where we work has a profound impact on our cognitive performance, emotional well-being, and overall productivity. Yet many organizations overlook this critical factor in employee wellness and performance optimization."
        },
        {
          title: "Key Environmental Factors",
          content: "Several environmental elements directly influence work performance:\n\n• **Natural Light**: Exposure to natural light regulates circadian rhythms and improves mood\n• **Air Quality**: Poor ventilation leads to decreased cognitive function and increased fatigue\n• **Noise Levels**: Excessive noise disrupts concentration and increases stress hormones\n• **Temperature Control**: Optimal temperature ranges (68-72°F) maximize comfort and focus\n• **Ergonomic Design**: Proper furniture reduces physical strain and supports long-term health"
        },
        {
          title: "The Science Behind Environmental Wellness",
          content: "Studies from Harvard T.H. Chan School of Public Health demonstrate that employees in well-ventilated offices with low chemical pollutants scored 61% higher on cognitive function tests. Additionally, workers near windows reported 15% higher well-being scores and 6% higher productivity levels."
        },
        {
          title: "Creating Your Optimal Work Environment",
          content: "Whether working from home or in an office, you can optimize your environment:\n\n1. **Maximize Natural Light**: Position your workspace near windows when possible\n2. **Add Plants**: Indoor plants improve air quality and reduce stress\n3. **Control Clutter**: A organized space promotes mental clarity\n4. **Personalize Thoughtfully**: Add meaningful items that inspire without creating distractions\n5. **Consider Color Psychology**: Blues and greens promote calm focus, while yellows stimulate creativity"
        }
      ]
    },
    ar: {
      sections: [
        {
          title: "التأثير الصامت لبيئة العمل",
          content: "تُظهر الأبحاث باستمرار أن البيئة المادية التي نعمل فيها لها تأثير عميق على أدائنا المعرفي ورفاهيتنا العاطفية وإنتاجيتنا الإجمالية. ومع ذلك، تتجاهل العديد من المنظمات هذا العامل الحاسم في عافية الموظفين وتحسين الأداء."
        },
        {
          title: "العوامل البيئية الرئيسية",
          content: "تؤثر عدة عناصر بيئية مباشرة على أداء العمل:\n\n• **الضوء الطبيعي**: التعرض للضوء الطبيعي ينظم الإيقاعات اليومية ويحسن المزاج\n• **جودة الهواء**: التهوية السيئة تؤدي إلى انخفاض الوظيفة المعرفية وزيادة التعب\n• **مستويات الضوضاء**: الضوضاء المفرطة تعطل التركيز وتزيد من هرمونات التوتر\n• **التحكم في درجة الحرارة**: نطاقات درجة الحرارة المثلى تزيد من الراحة والتركيز\n• **التصميم المريح**: الأثاث المناسب يقلل من الإجهاد الجسدي ويدعم الصحة طويلة المدى"
        },
        {
          title: "العلم وراء العافية البيئية",
          content: "تُظهر الدراسات من كلية هارفارد للصحة العامة أن الموظفين في المكاتب جيدة التهوية مع ملوثات كيميائية منخفضة سجلوا درجات أعلى بنسبة 61% في اختبارات الوظيفة المعرفية."
        },
        {
          title: "إنشاء بيئة العمل المثلى",
          content: "سواء كنت تعمل من المنزل أو في المكتب، يمكنك تحسين بيئتك:\n\n1. **زيادة الضوء الطبيعي**: ضع مساحة عملك بالقرب من النوافذ عند الإمكان\n2. **أضف النباتات**: النباتات الداخلية تحسن جودة الهواء وتقلل التوتر\n3. **تحكم في الفوضى**: المساحة المنظمة تعزز الوضوح الذهني\n4. **شخص بعناية**: أضف عناصر ذات معنى تلهم دون إنشاء تشتيت\n5. **فكر في علم نفس الألوان**: الأزرق والأخضر يعززان التركيز الهادئ"
        }
      ]
    },
    hi: {
      sections: [
        {
          title: "आपके कार्य वातावरण का मौन प्रभाव",
          content: "अनुसंधान लगातार दिखाता है कि जिस भौतिक वातावरण में हम काम करते हैं, उसका हमारे संज्ञानात्मक प्रदर्शन, भावनात्मक कल्याण और समग्र उत्पादकता पर गहरा प्रभाव पड़ता है। फिर भी कई संगठन कर्मचारी कल्याण और प्रदर्शन अनुकूलन में इस महत्वपूर्ण कारक की अनदेखी करते हैं।"
        },
        {
          title: "मुख्य पर्यावरणीय कारक",
          content: "कई पर्यावरणीय तत्व सीधे कार्य प्रदर्शन को प्रभावित करते हैं:\n\n• **प्राकृतिक प्रकाश**: प्राकृतिक प्रकाश के संपर्क में आना सर्कैडियन रिदम को नियंत्रित करता है और मूड में सुधार करता है\n• **वायु गुणवत्ता**: खराब वेंटिलेशन संज्ञानात्मक कार्य में कमी और बढ़ी हुई थकान का कारण बनता है\n• **शोर के स्तर**: अत्यधिक शोर एकाग्रता में बाधा डालता है और तनाव हार्मोन बढ़ाता है\n• **तापमान नियंत्रण**: इष्टतम तापमान रेंज आराम और फोकस को अधिकतम करती है\n• **एर्गोनॉमिक डिज़ाइन**: उचित फर्नीचर शारीरिक तनाव को कम करता है और दीर्घकालिक स्वास्थ्य का समर्थन करता है"
        },
        {
          title: "पर्यावरणीय कल्याण के पीछे का विज्ञान",
          content: "हार्वर्ड टी.एच. चान स्कूल ऑफ पब्लिक हेल्थ के अध्ययन दिखाते हैं कि कम रासायनिक प्रदूषकों के साथ अच्छी तरह हवादार कार्यालयों में कर्मचारियों ने संज्ञानात्मक कार्य परीक्षणों में 61% अधिक अंक प्राप्त किए।"
        },
        {
          title: "अपना इष्टतम कार्य वातावरण बनाना",
          content: "चाहे आप घर से काम कर रहे हों या कार्यालय में, आप अपने वातावरण को अनुकूलित कर सकते हैं:\n\n1. **प्राकृतिक प्रकाश को अधिकतम करें**: जब संभव हो तो अपने कार्यक्षेत्र को खिड़कियों के पास रखें\n2. **पौधे जोड़ें**: इनडोर पौधे वायु गुणवत्ता में सुधार करते हैं और तनाव कम करते हैं\n3. **अव्यवस्था को नियंत्रित करें**: एक संगठित स्थान मानसिक स्पष्टता को बढ़ावा देता है\n4. **सोच-समझकर व्यक्तिगत बनाएं**: ऐसी सार्थक वस्तुएं जोड़ें जो विकर्षण पैदा किए बिना प्रेरणा दें\n5. **रंग मनोविज्ञान पर विचार करें**: नीला और हरा शांत फोकस को बढ़ावा देता है"
        }
      ]
    }
  },
  'EW02': {
    en: {
      sections: [
        {
          title: "The Power of Environmental Details",
          content: "While we often focus on major workplace changes, research reveals that small environmental adjustments can yield significant improvements in performance, mood, and overall well-being. These 'micro-environmental' factors are often the most cost-effective interventions an organization can make."
        },
        {
          title: "Light: Your Productivity's Best Friend",
          content: "Natural lighting is perhaps the most impactful environmental factor:\n\n• **Circadian Regulation**: Natural light helps maintain healthy sleep-wake cycles\n• **Vitamin D Production**: Exposure supports immune function and mood regulation\n• **Eye Health**: Reduces digital eye strain and fatigue\n• **Energy Savings**: Reduces dependence on artificial lighting\n\nThe University of Oregon study found that workers with access to natural light reported 84% fewer symptoms of eyestrain, headaches, and blurred vision."
        },
        {
          title: "Ventilation: The Invisible Performance Booster",
          content: "Air quality directly impacts cognitive performance:\n\n• **Oxygen Levels**: Proper ventilation ensures adequate oxygen for brain function\n• **CO2 Reduction**: High CO2 levels (>1000 ppm) impair decision-making\n• **Pollutant Removal**: Filters remove particles that cause fatigue and illness\n• **Humidity Control**: Optimal humidity (40-60%) prevents respiratory issues\n\nStudies show that doubling ventilation rates can improve cognitive performance by up to 15%."
        },
        {
          title: "Color Psychology in the Workplace",
          content: "Colors influence mood, energy, and productivity:\n\n• **Blue**: Enhances focus and mental clarity, ideal for analytical work\n• **Green**: Reduces eye strain and promotes balance, perfect for long hours\n• **Yellow**: Stimulates creativity and optimism, great for brainstorming areas\n• **Red**: Increases urgency and attention to detail, suitable for error-checking tasks\n• **Neutral Tones**: Provide calming backdrop without distraction\n\nThe key is using colors strategically based on the type of work being performed."
        }
      ]
    },
    ar: {
      sections: [
        {
          title: "قوة التفاصيل البيئية",
          content: "بينما نركز غالباً على التغييرات الكبيرة في مكان العمل، تكشف الأبحاث أن التعديلات البيئية الصغيرة يمكن أن تحقق تحسينات كبيرة في الأداء والمزاج والرفاهية العامة."
        },
        {
          title: "الضوء: أفضل صديق لإنتاجيتك",
          content: "الإضاءة الطبيعية هي ربما العامل البيئي الأكثر تأثيراً:\n\n• **تنظيم الإيقاع اليومي**: الضوء الطبيعي يساعد في الحفاظ على دورات نوم-يقظة صحية\n• **إنتاج فيتامين د**: التعرض يدعم وظيفة المناعة وتنظيم المزاج\n• **صحة العين**: يقلل من إجهاد العين الرقمي والتعب\n• **توفير الطاقة**: يقلل الاعتماد على الإضاءة الاصطناعية"
        },
        {
          title: "التهوية: معزز الأداء غير المرئي",
          content: "جودة الهواء تؤثر مباشرة على الأداء المعرفي:\n\n• **مستويات الأكسجين**: التهوية المناسبة تضمن كمية كافية من الأكسجين لوظيفة الدماغ\n• **تقليل ثاني أكسيد الكربون**: المستويات العالية تضعف اتخاذ القرارات\n• **إزالة الملوثات**: المرشحات تزيل الجسيمات التي تسبب التعب والمرض\n• **التحكم في الرطوبة**: الرطوبة المثلى تمنع مشاكل الجهاز التنفسي"
        },
        {
          title: "علم نفس الألوان في مكان العمل",
          content: "الألوان تؤثر على المزاج والطاقة والإنتاجية:\n\n• **الأزرق**: يعزز التركيز والوضوح الذهني\n• **الأخضر**: يقلل إجهاد العين ويعزز التوازن\n• **الأصفر**: يحفز الإبداع والتفاؤل\n• **الأحمر**: يزيد الإلحاح والانتباه للتفاصيل\n• **الألوان المحايدة**: توفر خلفية مهدئة دون تشتيت"
        }
      ]
    },
    hi: {
      sections: [
        {
          title: "पर्यावरणीय विवरण की शक्ति",
          content: "जबकि हम अक्सर कार्यक्षेत्र में बड़े बदलावों पर ध्यान देते हैं, अनुसंधान से पता चलता है कि छोटे पर्यावरणीय समायोजन प्रदर्शन, मूड और समग्र कल्याण में महत्वपूर्ण सुधार ला सकते हैं।"
        },
        {
          title: "प्रकाश: आपकी उत्पादकता का सबसे अच्छा मित्र",
          content: "प्राकृतिक प्रकाश शायद सबसे प्रभावशाली पर्यावरणीय कारक है:\n\n• **सर्कैडियन नियंत्रण**: प्राकृतिक प्रकाश स्वस्थ नींद-जागने के चक्र को बनाए रखने में मदद करता है\n• **विटामिन डी उत्पादन**: एक्सपोज़र प्रतिरक्षा कार्य और मूड नियंत्रण का समर्थन करता है\n• **आंखों का स्वास्थ्य**: डिजिटल आंखों के तनाव और थकान को कम करता है\n• **ऊर्जा की बचत**: कृत्रिम प्रकाश पर निर्भरता कम करता है"
        },
        {
          title: "वेंटिलेशन: अदृश्य प्रदर्शन बूस्टर",
          content: "वायु गुणवत्ता सीधे संज्ञानात्मक प्रदर्शन को प्रभावित करती है:\n\n• **ऑक्सीजन स्तर**: उचित वेंटिलेशन मस्तिष्क के कार्य के लिए पर्याप्त ऑक्सीजन सुनिश्चित करता है\n• **CO2 में कमी**: उच्च CO2 स्तर निर्णय लेने की क्षमता को बिगाड़ता है\n• **प्रदूषक हटाना**: फिल्टर उन कणों को हटाते हैं जो थकान और बीमारी का कारण बनते हैं\n• **आर्द्रता नियंत्रण**: इष्टतम आर्द्रता श्वसन संबंधी समस्याओं को रोकती है"
        },
        {
          title: "कार्यक्षेत्र में रंग मनोविज्ञान",
          content: "रंग मूड, ऊर्जा और उत्पादकता को प्रभावित करते हैं:\n\n• **नीला**: फोकस और मानसिक स्पष्टता बढ़ाता है\n• **हरा**: आंखों के तनाव को कम करता है और संतुलन को बढ़ावा देता है\n• **पीला**: रचनात्मकता और आशावाद को उत्तेजित करता है\n• **लाल**: तात्कालिकता और विवरण पर ध्यान बढ़ाता है\n• **तटस्थ टोन**: बिना विकर्षण के शांत पृष्ठभूमि प्रदान करते हैं"
        }
      ]
    }
  }
}

export const ArticleViewer: React.FC<ArticleViewerProps> = ({
  article,
  language,
  onBack,
  onLanguageChange
}) => {
  const [fontSize, setFontSize] = useState<'small' | 'medium' | 'large'>('medium')
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(Math.floor(Math.random() * 100) + 50)

  const isRTL = language === 'ar'
  const textAlign = isRTL ? 'text-right' : 'text-left'
  const rtlClass = isRTL ? 'rtl' : 'ltr'

  // Get localized content
  const getLocalizedTitle = () => {
    return language === 'en' ? article.titleEN : 
           language === 'ar' ? article.titleAR : article.titleHI
  }

  const getLocalizedDescription = () => {
    return language === 'en' ? article.descriptionEN : 
           language === 'ar' ? article.descriptionAR : article.descriptionHI
  }

  const getExtendedContent = () => {
    const langKey = language === 'en' ? 'en' : language === 'ar' ? 'ar' : 'hi'
    return EXTENDED_CONTENT[article.id as keyof typeof EXTENDED_CONTENT]?.[langKey] || 
           EXTENDED_CONTENT[article.id as keyof typeof EXTENDED_CONTENT]?.en
  }

  // Track reading progress
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = (scrollTop / docHeight) * 100
      setReadingProgress(Math.min(progress, 100))
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleLike = () => {
    setIsLiked(!isLiked)
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1)
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: getLocalizedTitle(),
        text: getLocalizedDescription(),
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const fontSizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  }

  const extendedContent = getExtendedContent()

  return (
    <div className={`min-h-screen bg-gray-50 ${rtlClass}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div 
          className="h-full bg-sehatti-gold-500 transition-all duration-150"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Button 
              variant="ghost" 
              onClick={onBack}
              className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <FaArrowLeft className="w-4 h-4" />
              Back to Hub
            </Button>

            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              {/* Language Selector */}
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaGlobe className="w-4 h-4 text-gray-600" />
                <select 
                  value={language} 
                  onChange={(e) => onLanguageChange(e.target.value as 'en' | 'ar' | 'hi')}
                  className="px-2 py-1 border rounded text-sm"
                >
                  <option value="en">English</option>
                  <option value="ar">العربية</option>
                  <option value="hi">हिंदी</option>
                </select>
              </div>

              {/* Font Size Control */}
              <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaTextHeight className="w-4 h-4 text-gray-600" />
                <select 
                  value={fontSize} 
                  onChange={(e) => setFontSize(e.target.value as 'small' | 'medium' | 'large')}
                  className="px-2 py-1 border rounded text-sm"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>

              {/* Action Buttons */}
              <Button variant="ghost" size="sm" onClick={() => setIsBookmarked(!isBookmarked)}>
                <FaBookmark className={`w-4 h-4 ${isBookmarked ? 'text-sehatti-gold-500' : 'text-gray-400'}`} />
              </Button>
              
              <Button variant="ghost" size="sm" onClick={handlePrint}>
                <FaPrint className="w-4 h-4" />
              </Button>
              
              <Button variant="ghost" size="sm" onClick={handleShare}>
                <FaShare className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <article className={`bg-white rounded-lg shadow-sm p-8 ${textAlign}`}>
          {/* Article Header */}
          <header className="mb-8">
            <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Badge variant="outline">{article.division}</Badge>
              <Badge variant="secondary" className="text-xs">
                <FaClock className="w-3 h-3 mr-1" />
                {article.readTime} min read
              </Badge>
              <Badge variant="secondary" className="text-xs">
                <FaTag className="w-3 h-3 mr-1" />
                {article.category}
              </Badge>
            </div>

            <h1 className={`text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight ${fontSizeClasses[fontSize]} ${textAlign}`}>
              {getLocalizedTitle()}
            </h1>

            <div className={`flex items-center gap-4 text-gray-600 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaUser className="w-4 h-4" />
                <span className="text-sm">{article.source}</span>
              </div>
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaCalendarAlt className="w-4 h-4" />
                <span className="text-sm">Updated recently</span>
              </div>
            </div>

            <p className={`text-lg text-gray-700 leading-relaxed ${fontSizeClasses[fontSize]} ${textAlign}`}>
              {getLocalizedDescription()}
            </p>
          </header>

          {/* Article Body */}
          <div className={`prose prose-lg max-w-none ${fontSizeClasses[fontSize]} ${textAlign}`}>
            {extendedContent?.sections.map((section, index) => (
              <section key={index} className="mb-8">
                <h2 className={`text-2xl font-semibold text-gray-900 mb-4 ${textAlign}`}>
                  {section.title}
                </h2>
                <div className={`text-gray-700 leading-relaxed whitespace-pre-line ${textAlign}`}>
                  {section.content.split('\n').map((paragraph, pIndex) => {
                    if (paragraph.trim().startsWith('•')) {
                      return (
                        <ul key={pIndex} className={`list-disc ${isRTL ? 'list-inside mr-6' : 'list-inside ml-6'} my-4`}>
                          <li className={`mb-2 ${textAlign}`}>{paragraph.replace('•', '').trim()}</li>
                        </ul>
                      )
                    } else if (paragraph.includes('**')) {
                      return (
                        <p key={pIndex} className={`mb-4 ${textAlign}`}>
                          {paragraph.split('**').map((part, partIndex) => 
                            partIndex % 2 === 1 ? <strong key={partIndex}>{part}</strong> : part
                          )}
                        </p>
                      )
                    } else if (paragraph.trim()) {
                      return <p key={pIndex} className={`mb-4 ${textAlign}`}>{paragraph}</p>
                    }
                    return null
                  })}
                </div>
              </section>
            ))}
          </div>

          {/* Article Footer */}
          <footer className="mt-12 pt-8 border-t border-gray-200">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Button 
                  variant={isLiked ? "default" : "outline"} 
                  size="sm"
                  onClick={handleLike}
                  className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
                >
                  <FaThumbsUp className="w-4 h-4" />
                  <span>{likeCount}</span>
                </Button>
                
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <FaShare className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>

              <div className={`text-sm text-gray-500 ${textAlign}`}>
                Source: {article.source}
              </div>
            </div>
          </footer>
        </article>

        {/* Related Articles Suggestion */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FaBookOpen className="w-5 h-5 text-sehatti-gold-500" />
              Continue Your Wellness Journey
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className={`text-gray-600 mb-4 ${textAlign}`}>
              Explore more articles in the {article.division} category to deepen your understanding.
            </p>
            <Button onClick={onBack}>
              <FaArrowLeft className="w-4 h-4 mr-2" />
              Back to Media Hub
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 