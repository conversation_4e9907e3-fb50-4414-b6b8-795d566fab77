import React, { useState, useEffect } from 'react';
import { FaTimes, FaSpinner } from 'react-icons/fa';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import type { 
  EntityType, 
  ModalType,
  Corporate,
  Division,
  Target,
  Question,
  Assessment,
  PaginatedResponse,
  TranslatedContent,
  CreateDivisionRequest,
  UpdateDivisionRequest,
  CreateTargetRequest,
  UpdateTargetRequest,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  CreateAssessmentRequest,
  UpdateAssessmentRequest,
  QuestionType,
  AssessmentType
} from '../../types/qa';

interface QAEntityModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: EntityType;
  modalType: ModalType;
  entity: any;
  corporatesData: PaginatedResponse<Corporate> | null;
  divisionsData: PaginatedResponse<Division> | null;
  targetsData: PaginatedResponse<Target> | null;
  questionsData: PaginatedResponse<Question> | null;
  selectedCorporateId: string;
  selectedDivisionId: string;
  selectedTargetId: string;
  onSuccess: () => void;
  mutations: {
    deleteCorporate: { mutate: any; loading: boolean };
    createDivision: { mutate: any; loading: boolean };
    updateDivision: { mutate: any; loading: boolean };
    deleteDivision: { mutate: any; loading: boolean };
    createTarget: { mutate: any; loading: boolean };
    updateTarget: { mutate: any; loading: boolean };
    deleteTarget: { mutate: any; loading: boolean };
    createQuestion: { mutate: any; loading: boolean };
    updateQuestion: { mutate: any; loading: boolean };
    deleteQuestion: { mutate: any; loading: boolean };
    createAssessment: { mutate: any; loading: boolean };
    updateAssessment: { mutate: any; loading: boolean };
    deleteAssessment: { mutate: any; loading: boolean };
  };
  getTranslatedText: (content: TranslatedContent | string | undefined) => string;
  t: (key: string) => string;
}

interface FormData {
  [key: string]: any;
}

const questionTypes: { value: QuestionType; label: string }[] = [
  { value: 'multiple_choice', label: 'Multiple Choice' },
  { value: 'text', label: 'Text' },
  { value: 'rating', label: 'Rating' },
  { value: 'boolean', label: 'Yes/No' },
  { value: 'file_upload', label: 'File Upload' },
];

const assessmentTypes: { value: AssessmentType; label: string }[] = [
  { value: 'quiz', label: 'Quiz' },
  { value: 'survey', label: 'Survey' },
  { value: 'evaluation', label: 'Evaluation' },
  { value: 'feedback', label: 'Feedback' },
];

export const QAEntityModal: React.FC<QAEntityModalProps> = ({
  isOpen,
  onClose,
  entityType,
  modalType,
  entity,
  corporatesData,
  divisionsData,
  targetsData,
  questionsData,
  selectedCorporateId,
  selectedDivisionId,
  selectedTargetId,
  onSuccess,
  mutations,
  getTranslatedText,
  t,
}) => {
  const [formData, setFormData] = useState<FormData>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when entity changes
  useEffect(() => {
    if (modalType === 'create') {
      setFormData(getDefaultFormData());
    } else if (entity) {
      setFormData(getEntityFormData(entity));
    }
  }, [entity, modalType, entityType]);

  const getDefaultFormData = (): FormData => {
    switch (entityType) {
      case 'divisions':
        return {
          corporate_id: selectedCorporateId,
          name_en: '',
          name_ar: '',
          name_hi: '',
          description_en: '',
          description_ar: '',
          description_hi: '',
          status: 'active',
        };
      case 'targets':
        return {
          division_id: selectedDivisionId,
          name_en: '',
          name_ar: '',
          name_hi: '',
          description_en: '',
          description_ar: '',
          description_hi: '',
          status: 'active',
          order_index: 0,
        };
      case 'questions':
        return {
          division_id: selectedDivisionId,
          target_id: selectedTargetId,
          corporate_id: selectedCorporateId,
          question_text_en: '',
          question_text_ar: '',
          question_text_hi: '',
          question_type: 'multiple_choice',
          required: false,
          status: 'active',
          order_index: 0,
          options: [],
        };
      case 'assessments':
        return {
          division_id: selectedDivisionId,
          target_id: selectedTargetId,
          corporate_id: selectedCorporateId,
          title_en: '',
          title_ar: '',
          title_hi: '',
          description_en: '',
          description_ar: '',
          description_hi: '',
          assessment_type: 'quiz',
          questions: [],
          is_active: true,
          time_limit: 60,
          passing_score: 70,
          max_attempts: 3,
        };
      default:
        return {};
    }
  };

  const getEntityFormData = (entity: any): FormData => {
    switch (entityType) {
      case 'divisions':
        return {
          corporate_id: entity.corporate_id,
          name_en: entity.name?.en || '',
          name_ar: entity.name?.ar || '',
          name_hi: entity.name?.hi || '',
          description_en: entity.description?.en || '',
          description_ar: entity.description?.ar || '',
          description_hi: entity.description?.hi || '',
          status: entity.status,
        };
      case 'targets':
        return {
          division_id: entity.division_id,
          name_en: entity.name?.en || '',
          name_ar: entity.name?.ar || '',
          name_hi: entity.name?.hi || '',
          description_en: entity.description?.en || '',
          description_ar: entity.description?.ar || '',
          description_hi: entity.description?.hi || '',
          status: entity.status,
          order_index: entity.order_index || 0,
        };
      case 'questions':
        return {
          division_id: entity.division_id,
          target_id: entity.target_id,
          corporate_id: entity.corporate_id,
          question_text_en: entity.question_text?.en || '',
          question_text_ar: entity.question_text?.ar || '',
          question_text_hi: entity.question_text?.hi || '',
          question_type: entity.question_type,
          required: entity.required,
          status: entity.status,
          order_index: entity.order_index || 0,
          options: entity.options || [],
        };
      case 'assessments':
        return {
          division_id: entity.division_id,
          target_id: entity.target_id,
          corporate_id: entity.corporate_id,
          title_en: entity.title?.en || '',
          title_ar: entity.title?.ar || '',
          title_hi: entity.title?.hi || '',
          description_en: entity.description?.en || '',
          description_ar: entity.description?.ar || '',
          description_hi: entity.description?.hi || '',
          assessment_type: entity.assessment_type,
          questions: entity.questions || [],
          is_active: entity.is_active,
          time_limit: entity.time_limit || 60,
          passing_score: entity.passing_score || 70,
          max_attempts: entity.max_attempts || 3,
        };
      default:
        return {};
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (modalType === 'delete') {
        await handleDelete();
      } else if (modalType === 'create') {
        await handleCreate();
      } else if (modalType === 'edit') {
        await handleUpdate();
      }
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Operation failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    switch (entityType) {
      case 'corporates':
        await mutations.deleteCorporate.mutate(entity.id);
        break;
      case 'divisions':
        await mutations.deleteDivision.mutate(entity.id);
        break;
      case 'targets':
        await mutations.deleteTarget.mutate(entity.id);
        break;
      case 'questions':
        await mutations.deleteQuestion.mutate(entity.id);
        break;
      case 'assessments':
        await mutations.deleteAssessment.mutate(entity.id);
        break;
    }
  };

  const handleCreate = async () => {
    const requestData = formatRequestData(formData);
    
    switch (entityType) {
      case 'divisions':
        await mutations.createDivision.mutate(requestData);
        break;
      case 'targets':
        await mutations.createTarget.mutate(requestData);
        break;
      case 'questions':
        await mutations.createQuestion.mutate(requestData);
        break;
      case 'assessments':
        await mutations.createAssessment.mutate(requestData);
        break;
    }
  };

  const handleUpdate = async () => {
    const requestData = formatRequestData(formData);
    
    switch (entityType) {
      case 'divisions':
        await mutations.updateDivision.mutate({ id: entity.id, data: requestData });
        break;
      case 'targets':
        await mutations.updateTarget.mutate({ id: entity.id, data: requestData });
        break;
      case 'questions':
        await mutations.updateQuestion.mutate({ id: entity.id, data: requestData });
        break;
      case 'assessments':
        await mutations.updateAssessment.mutate({ id: entity.id, data: requestData });
        break;
    }
  };

  const formatRequestData = (data: FormData) => {
    const formatted: any = {};

    // Handle translated content fields
    if (entityType === 'divisions' || entityType === 'targets') {
      formatted.name = {
        en: data.name_en,
        ar: data.name_ar,
        hi: data.name_hi,
      };
      
      if (data.description_en || data.description_ar || data.description_hi) {
        formatted.description = {
          en: data.description_en,
          ar: data.description_ar,
          hi: data.description_hi,
        };
      }
    }

    if (entityType === 'questions') {
      formatted.question_text = {
        en: data.question_text_en,
        ar: data.question_text_ar,
        hi: data.question_text_hi,
      };
      formatted.question_type = data.question_type;
      formatted.required = data.required;
      formatted.options = data.options;
    }

    if (entityType === 'assessments') {
      formatted.title = {
        en: data.title_en,
        ar: data.title_ar,
        hi: data.title_hi,
      };
      
      if (data.description_en || data.description_ar || data.description_hi) {
        formatted.description = {
          en: data.description_en,
          ar: data.description_ar,
          hi: data.description_hi,
        };
      }
      
      formatted.assessment_type = data.assessment_type;
      formatted.questions = data.questions;
      formatted.is_active = data.is_active;
      formatted.time_limit = data.time_limit;
      formatted.passing_score = data.passing_score;
      formatted.max_attempts = data.max_attempts;
    }

    // Copy other fields
    Object.keys(data).forEach(key => {
      if (!key.includes('_en') && !key.includes('_ar') && !key.includes('_hi') && 
          !['name', 'description', 'title', 'question_text'].includes(key)) {
        formatted[key] = data[key];
      }
    });

    return formatted;
  };

  const getModalTitle = () => {
    const entityName = entityType.slice(0, -1); // Remove 's' from plural
    switch (modalType) {
      case 'create':
        return `${t('Create')} ${t(entityName)}`;
      case 'edit':
        return `${t('Edit')} ${t(entityName)}`;
      case 'delete':
        return `${t('Delete')} ${t(entityName)}`;
      case 'view':
        return `${t('View')} ${t(entityName)}`;
      default:
        return '';
    }
  };

  const renderFormFields = () => {
    if (modalType === 'view') {
      return renderViewContent();
    }

    if (modalType === 'delete') {
      return renderDeleteConfirmation();
    }

    return renderEditForm();
  };

  const renderViewContent = () => (
    <div className="space-y-4">
      <Card className="p-4">
        <pre className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 whitespace-pre-wrap">
          {JSON.stringify(entity, null, 2)}
        </pre>
      </Card>
    </div>
  );

  const renderDeleteConfirmation = () => (
    <div className="space-y-4">
      <div className="text-center">
        <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
          {t('Are you sure you want to delete this')} {entityType.slice(0, -1)}?
        </p>
        {entity && (
          <div className="mt-4 p-4 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg">
            <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              {getTranslatedText(entity.name || entity.title || entity.question_text) || entity.name || entity.email}
            </p>
          </div>
        )}
      </div>
    </div>
  );

  const renderEditForm = () => (
    <div className="space-y-6">
      {/* Corporate Selection */}
      {(entityType === 'divisions' || entityType === 'questions' || entityType === 'assessments') && (
        <div>
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
            {t('Corporate')}
          </label>
          <Select
            value={formData.corporate_id || ''}
            onChange={(value) => handleInputChange('corporate_id', value)}
            placeholder={t('Select Corporate')}
            disabled={modalType === 'edit'}
          >
            {corporatesData?.items.map((corporate) => (
              <option key={corporate.id} value={corporate.id}>
                {corporate.name}
              </option>
            ))}
          </Select>
        </div>
      )}

      {/* Division Selection */}
      {(entityType === 'targets' || entityType === 'questions' || entityType === 'assessments') && (
        <div>
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
            {t('Division')}
          </label>
          <Select
            value={formData.division_id || ''}
            onChange={(value) => handleInputChange('division_id', value)}
            placeholder={t('Select Division')}
            disabled={modalType === 'edit'}
          >
            {divisionsData?.items.map((division) => (
              <option key={division.id} value={division.id}>
                {getTranslatedText(division.name)}
              </option>
            ))}
          </Select>
        </div>
      )}

      {/* Target Selection */}
      {(entityType === 'questions' || entityType === 'assessments') && (
        <div>
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
            {t('Target')} ({t('Optional')})
          </label>
          <Select
            value={formData.target_id || ''}
            onChange={(value) => handleInputChange('target_id', value)}
            placeholder={t('Select Target')}
          >
            <option value="">{t('No Target')}</option>
            {targetsData?.items.map((target) => (
              <option key={target.id} value={target.id}>
                {getTranslatedText(target.name)}
              </option>
            ))}
          </Select>
        </div>
      )}

      {/* Name/Title Fields */}
      {(entityType === 'divisions' || entityType === 'targets' || entityType === 'assessments') && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Name')} (EN)
            </label>
            <Input
              value={formData[entityType === 'assessments' ? 'title_en' : 'name_en'] || ''}
              onChange={(e) => handleInputChange(entityType === 'assessments' ? 'title_en' : 'name_en', e.target.value)}
              placeholder={t('Enter name in English')}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Name')} (AR)
            </label>
            <Input
              value={formData[entityType === 'assessments' ? 'title_ar' : 'name_ar'] || ''}
              onChange={(e) => handleInputChange(entityType === 'assessments' ? 'title_ar' : 'name_ar', e.target.value)}
              placeholder={t('Enter name in Arabic')}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Name')} (HI)
            </label>
            <Input
              value={formData[entityType === 'assessments' ? 'title_hi' : 'name_hi'] || ''}
              onChange={(e) => handleInputChange(entityType === 'assessments' ? 'title_hi' : 'name_hi', e.target.value)}
              placeholder={t('Enter name in Hindi')}
            />
          </div>
        </div>
      )}

      {/* Question Text Fields */}
      {entityType === 'questions' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Question')} (EN)
            </label>
            <Input
              value={formData.question_text_en || ''}
              onChange={(e) => handleInputChange('question_text_en', e.target.value)}
              placeholder={t('Enter question in English')}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Question')} (AR)
            </label>
            <Input
              value={formData.question_text_ar || ''}
              onChange={(e) => handleInputChange('question_text_ar', e.target.value)}
              placeholder={t('Enter question in Arabic')}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Question')} (HI)
            </label>
            <Input
              value={formData.question_text_hi || ''}
              onChange={(e) => handleInputChange('question_text_hi', e.target.value)}
              placeholder={t('Enter question in Hindi')}
            />
          </div>
        </div>
      )}

      {/* Question Type */}
      {entityType === 'questions' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Question Type')}
            </label>
            <Select
              value={formData.question_type || 'multiple_choice'}
              onChange={(value) => handleInputChange('question_type', value)}
            >
              {questionTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </Select>
          </div>
          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.required || false}
                onChange={(e) => handleInputChange('required', e.target.checked)}
                className="mr-2"
              />
              {t('Required')}
            </label>
          </div>
        </div>
      )}

      {/* Assessment Type */}
      {entityType === 'assessments' && (
        <div>
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
            {t('Assessment Type')}
          </label>
          <Select
            value={formData.assessment_type || 'quiz'}
            onChange={(value) => handleInputChange('assessment_type', value)}
          >
            {assessmentTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </Select>
        </div>
      )}

      {/* Status */}
      {(entityType === 'divisions' || entityType === 'targets' || entityType === 'questions') && (
        <div>
          <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
            {t('Status')}
          </label>
          <Select
            value={formData.status || 'active'}
            onChange={(value) => handleInputChange('status', value)}
          >
            <option value="active">{t('Active')}</option>
            <option value="inactive">{t('Inactive')}</option>
            <option value="draft">{t('Draft')}</option>
          </Select>
        </div>
      )}

      {/* Assessment Settings */}
      {entityType === 'assessments' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Time Limit')} ({t('minutes')})
            </label>
            <Input
              type="number"
              value={formData.time_limit || 60}
              onChange={(e) => handleInputChange('time_limit', parseInt(e.target.value))}
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Passing Score')} (%)
            </label>
            <Input
              type="number"
              value={formData.passing_score || 70}
              onChange={(e) => handleInputChange('passing_score', parseInt(e.target.value))}
              min="0"
              max="100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              {t('Max Attempts')}
            </label>
            <Input
              type="number"
              value={formData.max_attempts || 3}
              onChange={(e) => handleInputChange('max_attempts', parseInt(e.target.value))}
              min="1"
            />
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Modal open={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {renderFormFields()}
        
        <div className="flex justify-end gap-4 pt-4 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
          <Button type="button" variant="ghost" onClick={onClose}>
            {t('Cancel')}
          </Button>
          
          {modalType !== 'view' && (
            <Button
              type="submit"
              variant={modalType === 'delete' ? 'destructive' : 'default'}
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting && <FaSpinner className="w-4 h-4 animate-spin" />}
              {modalType === 'delete' ? t('Delete') : modalType === 'create' ? t('Create') : t('Update')}
            </Button>
          )}
        </div>
      </form>
    </Modal>
  );
};

export default QAEntityModal; 