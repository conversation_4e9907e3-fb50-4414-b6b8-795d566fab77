import React, { useState, useMemo } from 'react';
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaEye, 
  FaSearch, 
  FaPlay,
  FaStop,
  FaPause,
  FaCalendarAlt,
  FaBroadcastTower,
  FaUsers,
  FaClock,
  FaQuestionCircle
} from 'react-icons/fa';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Textarea } from '../ui/Textarea';
import { 
  useGetBroadcastCampaignsQuery,
  useGetQuestionsQuery,
  useCreateBroadcastCampaignMutation,
  useUpdateBroadcastCampaignMutation,
  useDeleteBroadcastCampaignMutation,
  useStartBroadcastCampaignMutation,
  useStopBroadcastCampaignMutation,
  usePauseBroadcastCampaignMutation,
  useResumeBroadcastCampaignMutation,
  type BroadcastCampaign,
  type BroadcastStatus,
  type CreateBroadcastCampaignRequest,
  type UpdateBroadcastCampaignRequest,
  type RecurrenceType,
  type WeekdayPattern,
  type TimeSlotInfo
} from '../../store/api/qaApi';
import { toast } from 'react-hot-toast';
import { formatDate } from '../../utils/date';

interface BroadcastManagerProps {
  corporateId?: string;
  className?: string;
}

interface CampaignFormData {
  name: string;
  description?: string;
  question_pool_ids: string[];
  schedule: {
    recurrence_type: RecurrenceType;
    start_date: string;
    end_date?: string;
    weekday_pattern?: WeekdayPattern;
    custom_weekdays?: number[];
    time_slots?: TimeSlotInfo[];
  };
  distribution: {
    corporate_ids: string[];
    target_audience?: string;
  };
}

const BroadcastManager: React.FC<BroadcastManagerProps> = ({ 
  corporateId,
  className = ""
}) => {
  // State management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<BroadcastCampaign | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<BroadcastStatus | 'all'>('all');

  // Form state
  const [formData, setFormData] = useState<CampaignFormData>({
    name: '',
    description: '',
    question_pool_ids: [],
    schedule: {
      recurrence_type: 'daily',
      start_date: new Date().toISOString().split('T')[0],
      weekday_pattern: 'work_days_5',
      time_slots: [{ time: '09:00', questions_count: 1 }]
    },
    distribution: {
      corporate_ids: corporateId ? [corporateId] : [],
      target_audience: 'all_employees'
    }
  });

  // API queries
  const { 
    data: campaigns = [], 
    isLoading: campaignsLoading,
    error: campaignsError,
    refetch: refetchCampaigns
  } = useGetBroadcastCampaignsQuery({
    corporate_id: corporateId,
    status: filterStatus === 'all' ? undefined : filterStatus
  });

  const { data: questions = [] } = useGetQuestionsQuery({
    corporate_id: corporateId,
    status: 'active'
  });

  // Mutations
  const [createCampaign, { isLoading: creating }] = useCreateBroadcastCampaignMutation();
  const [updateCampaign, { isLoading: updating }] = useUpdateBroadcastCampaignMutation();
  const [deleteCampaign, { isLoading: deleting }] = useDeleteBroadcastCampaignMutation();
  const [startCampaign, { isLoading: starting }] = useStartBroadcastCampaignMutation();
  const [stopCampaign, { isLoading: stopping }] = useStopBroadcastCampaignMutation();
  const [pauseCampaign, { isLoading: pausing }] = usePauseBroadcastCampaignMutation();
  const [resumeCampaign, { isLoading: resuming }] = useResumeBroadcastCampaignMutation();

  // Filtered campaigns
  const filteredCampaigns = useMemo(() => {
    return campaigns.filter(campaign => {
      const matchesSearch = campaign.name.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          campaign.id.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }, [campaigns, searchTerm]);

  // Event handlers
  const handleCreateCampaign = async () => {
    try {
      const createData: CreateBroadcastCampaignRequest = {
        name: formData.name,
        description: formData.description,
        question_pool_ids: formData.question_pool_ids,
        schedule: formData.schedule,
        distribution: formData.distribution
      };

      await createCampaign(createData).unwrap();
      toast.success('Campaign created successfully');
      setIsCreateModalOpen(false);
      resetForm();
      refetchCampaigns();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to create campaign');
    }
  };

  const handleUpdateCampaign = async () => {
    if (!selectedCampaign) return;

    try {
      const updateData: UpdateBroadcastCampaignRequest = {
        name: { en: formData.name },
        description: formData.description ? { en: formData.description } : undefined,
        question_pool_ids: formData.question_pool_ids,
        schedule: formData.schedule,
        distribution: formData.distribution
      };

      await updateCampaign({
        campaignId: selectedCampaign.id,
        data: updateData
      }).unwrap();
      
      toast.success('Campaign updated successfully');
      setIsEditModalOpen(false);
      setSelectedCampaign(null);
      resetForm();
      refetchCampaigns();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to update campaign');
    }
  };

  const handleDeleteCampaign = async () => {
    if (!selectedCampaign) return;

    try {
      await deleteCampaign({
        campaignId: selectedCampaign.id,
        hardDelete: false
      }).unwrap();
      
      toast.success('Campaign deleted successfully');
      setIsDeleteModalOpen(false);
      setSelectedCampaign(null);
      refetchCampaigns();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to delete campaign');
    }
  };

  const handleCampaignAction = async (action: 'start' | 'stop' | 'pause' | 'resume', campaign: BroadcastCampaign) => {
    try {
      const actionMap = {
        start: startCampaign,
        stop: stopCampaign,
        pause: pauseCampaign,
        resume: resumeCampaign
      };

      await actionMap[action](campaign.id).unwrap();
      toast.success(`Campaign ${action}ed successfully`);
      refetchCampaigns();
    } catch (error: any) {
      toast.error(error?.data?.detail || `Failed to ${action} campaign`);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      question_pool_ids: [],
      schedule: {
        recurrence_type: 'daily',
        start_date: new Date().toISOString().split('T')[0],
        weekday_pattern: 'work_days_5',
        time_slots: [{ time: '09:00', questions_count: 1 }]
      },
      distribution: {
        corporate_ids: corporateId ? [corporateId] : [],
        target_audience: 'all_employees'
      }
    });
  };

  const openEditModal = (campaign: BroadcastCampaign) => {
    setSelectedCampaign(campaign);
    setFormData({
      name: campaign.name.en,
      description: campaign.description?.en || '',
      question_pool_ids: campaign.question_pool_ids,
      schedule: campaign.schedule as any,
      distribution: campaign.distribution as any
    });
    setIsEditModalOpen(true);
  };

  const openViewModal = (campaign: BroadcastCampaign) => {
    setSelectedCampaign(campaign);
    setIsViewModalOpen(true);
  };

  const openDeleteModal = (campaign: BroadcastCampaign) => {
    setSelectedCampaign(campaign);
    setIsDeleteModalOpen(true);
  };

  const addTimeSlot = () => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        time_slots: [
          ...(prev.schedule.time_slots || []),
          { time: '09:00', questions_count: 1 }
        ]
      }
    }));
  };

  const removeTimeSlot = (index: number) => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        time_slots: prev.schedule.time_slots?.filter((_, i) => i !== index)
      }
    }));
  };

  const updateTimeSlot = (index: number, field: 'time' | 'questions_count', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      schedule: {
        ...prev.schedule,
        time_slots: prev.schedule.time_slots?.map((slot, i) => 
          i === index ? { ...slot, [field]: value } : slot
        )
      }
    }));
  };

  const getStatusBadge = (status: BroadcastStatus) => {
    const statusConfig = {
      active: { variant: 'success' as const, label: 'Active' },
      inactive: { variant: 'secondary' as const, label: 'Inactive' },
      completed: { variant: 'info' as const, label: 'Completed' }
    };
    
    const config = statusConfig[status];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getCampaignActions = (campaign: BroadcastCampaign) => {
    const actions = [];
    
    if (campaign.status === 'inactive') {
      actions.push(
        <Button
          key="start"
          size="sm"
          variant="outline"
          onClick={() => handleCampaignAction('start', campaign)}
          disabled={starting}
          className="text-green-600 hover:text-green-700"
        >
          <FaPlay className="w-3 h-3 mr-1" />
          Start
        </Button>
      );
    }
    
    if (campaign.status === 'active') {
      actions.push(
        <Button
          key="pause"
          size="sm"
          variant="outline"
          onClick={() => handleCampaignAction('pause', campaign)}
          disabled={pausing}
          className="text-yellow-600 hover:text-yellow-700"
        >
          <FaPause className="w-3 h-3 mr-1" />
          Pause
        </Button>
      );
      actions.push(
        <Button
          key="stop"
          size="sm"
          variant="outline"
          onClick={() => handleCampaignAction('stop', campaign)}
          disabled={stopping}
          className="text-red-600 hover:text-red-700"
        >
          <FaStop className="w-3 h-3 mr-1" />
          Stop
        </Button>
      );
    }

    return actions;
  };

  if (campaignsLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-sehatti-warm-gray-900 flex items-center gap-2">
            <FaBroadcastTower className="text-sehatti-gold-600" />
            Broadcast Campaigns
          </h2>
          <p className="text-sehatti-warm-gray-600 mt-1">
            Manage and schedule question broadcast campaigns
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2"
        >
          <FaPlus className="w-4 h-4" />
          Create Campaign
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search campaigns..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as BroadcastStatus | 'all')}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="completed">Completed</option>
          </Select>
        </div>
      </Card>

      {/* Campaigns List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredCampaigns.map((campaign) => (
          <Card key={campaign.id} className="p-6 hover:shadow-lg transition-shadow">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  {getStatusBadge(campaign.status)}
                  <h3 className="font-semibold text-sehatti-warm-gray-900">
                    {campaign.name.en}
                  </h3>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openViewModal(campaign)}
                  >
                    <FaEye className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditModal(campaign)}
                  >
                    <FaEdit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openDeleteModal(campaign)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <FaTrash className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Description */}
              {campaign.description && (
                <p className="text-sm text-sehatti-warm-gray-600">
                  {campaign.description.en}
                </p>
              )}

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2 text-sehatti-warm-gray-600">
                  <FaQuestionCircle className="w-4 h-4" />
                  <span>{campaign.question_pool_ids.length} questions</span>
                </div>
                <div className="flex items-center gap-2 text-sehatti-warm-gray-600">
                  <FaUsers className="w-4 h-4" />
                  <span>{(campaign.distribution as any)?.corporate_ids?.length || 0} corporates</span>
                </div>
              </div>

              {/* Schedule Info */}
              <div className="text-sm text-sehatti-warm-gray-600">
                <div className="flex items-center gap-2 mb-1">
                  <FaCalendarAlt className="w-4 h-4" />
                  <span>
                    {(campaign.schedule as any)?.recurrence_type || 'Not scheduled'}
                  </span>
                </div>
                {(campaign.schedule as any)?.start_date && (
                  <div className="flex items-center gap-2">
                    <FaClock className="w-4 h-4" />
                    <span>
                      Starts: {new Date((campaign.schedule as any).start_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>

              {/* Campaign Actions */}
              <div className="flex gap-2 flex-wrap">
                {getCampaignActions(campaign)}
              </div>

              {/* Timestamps */}
              <div className="text-xs text-sehatti-warm-gray-500 space-y-1 pt-2 border-t">
                <div>Created: {formatDate(campaign.created_at)}</div>
                <div>Updated: {formatDate(campaign.updated_at)}</div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredCampaigns.length === 0 && (
        <Card className="p-12 text-center">
          <FaBroadcastTower className="mx-auto h-16 w-16 text-sehatti-warm-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">
            No campaigns found
          </h3>
          <p className="text-sehatti-warm-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all'
              ? 'Try adjusting your filters or search terms.'
              : 'Get started by creating your first broadcast campaign.'
            }
          </p>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <FaPlus className="w-4 h-4 mr-2" />
            Create Campaign
          </Button>
        </Card>
      )}

      {/* Create Campaign Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create Broadcast Campaign"
        size="lg"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Campaign Name
                <span className="text-sehatti-gold-600 ml-1">*</span>
              </label>
              <Input
                placeholder="Enter campaign name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Description
              </label>
              <Textarea
                placeholder="Enter campaign description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          {/* Question Pool */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Question Pool
              <span className="text-sehatti-gold-600 ml-1">*</span>
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto border rounded-lg p-3">
              {questions.map(question => (
                <label key={question.id} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.question_pool_ids.includes(question.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          question_pool_ids: [...prev.question_pool_ids, question.id]
                        }));
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          question_pool_ids: prev.question_pool_ids.filter(id => id !== question.id)
                        }));
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-sehatti-warm-gray-700">
                    {question.question_text.en}
                  </span>
                </label>
              ))}
            </div>
            {questions.length === 0 && (
              <p className="text-sm text-sehatti-warm-gray-500 mt-2">
                No active questions available. Create questions first.
              </p>
            )}
          </div>

          {/* Schedule */}
          <div>
            <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
              Schedule Configuration
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Recurrence Type
                </label>
                <Select
                  value={formData.schedule.recurrence_type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, recurrence_type: e.target.value as RecurrenceType }
                  }))}
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="custom">Custom</option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Weekday Pattern
                </label>
                <Select
                  value={formData.schedule.weekday_pattern}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, weekday_pattern: e.target.value as WeekdayPattern }
                  }))}
                >
                  <option value="work_days_5">Work Days (5 days)</option>
                  <option value="full_week_7">Full Week (7 days)</option>
                  <option value="custom_days">Custom Days</option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Start Date
                </label>
                <Input
                  type="date"
                  value={formData.schedule.start_date}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, start_date: e.target.value }
                  }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  End Date (Optional)
                </label>
                <Input
                  type="date"
                  value={formData.schedule.end_date || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, end_date: e.target.value || undefined }
                  }))}
                />
              </div>
            </div>

            {/* Time Slots */}
            <div className="mt-4">
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-sehatti-warm-gray-700">
                  Time Slots
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTimeSlot}
                >
                  <FaPlus className="w-3 h-3 mr-1" />
                  Add Slot
                </Button>
              </div>

              <div className="space-y-3">
                {formData.schedule.time_slots?.map((slot, index) => (
                  <div key={index} className="flex gap-3 items-center">
                    <div className="flex-1">
                      <Input
                        type="time"
                        value={slot.time}
                        onChange={(e) => updateTimeSlot(index, 'time', e.target.value)}
                      />
                    </div>
                    <div className="w-32">
                      <Input
                        type="number"
                        placeholder="Questions"
                        min="1"
                        value={slot.questions_count}
                        onChange={(e) => updateTimeSlot(index, 'questions_count', parseInt(e.target.value) || 1)}
                      />
                    </div>
                    {(formData.schedule.time_slots?.length || 0) > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTimeSlot(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <FaTrash className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Distribution */}
          <div>
            <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
              Distribution Settings
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Target Audience
              </label>
              <Input
                placeholder="Target audience description"
                value={formData.distribution.target_audience}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  distribution: { ...prev.distribution, target_audience: e.target.value }
                }))}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateCampaign}
              disabled={creating || !formData.name || formData.question_pool_ids.length === 0}
            >
              {creating ? 'Creating...' : 'Create Campaign'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Campaign Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Campaign"
        size="lg"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Campaign Name
                <span className="text-sehatti-gold-600 ml-1">*</span>
              </label>
              <Input
                placeholder="Enter campaign name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Description
              </label>
              <Textarea
                placeholder="Enter campaign description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          {/* Question Pool */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Question Pool
              <span className="text-sehatti-gold-600 ml-1">*</span>
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto border rounded-lg p-3">
              {questions.map(question => (
                <label key={question.id} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.question_pool_ids.includes(question.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          question_pool_ids: [...prev.question_pool_ids, question.id]
                        }));
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          question_pool_ids: prev.question_pool_ids.filter(id => id !== question.id)
                        }));
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-sehatti-warm-gray-700">
                    {question.question_text.en}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateCampaign}
              disabled={updating || !formData.name || formData.question_pool_ids.length === 0}
            >
              {updating ? 'Updating...' : 'Update Campaign'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* View Campaign Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Campaign Details"
        size="lg"
      >
        {selectedCampaign && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                {getStatusBadge(selectedCampaign.status)}
                <h3 className="text-xl font-semibold text-sehatti-warm-gray-900">
                  {selectedCampaign.name.en}
                </h3>
              </div>
              <div className="text-sm text-sehatti-warm-gray-500">
                ID: {selectedCampaign.id}
              </div>
            </div>

            {/* Description */}
            {selectedCampaign.description && (
              <div>
                <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-2">
                  Description
                </h4>
                <p className="text-sehatti-warm-gray-700">
                  {selectedCampaign.description.en}
                </p>
              </div>
            )}

            {/* Question Pool */}
            <div>
              <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Question Pool ({selectedCampaign.question_pool_ids.length} questions)
              </h4>
              <div className="space-y-2">
                {selectedCampaign.question_pool_ids.map((questionId, index) => {
                  const question = questions.find(q => q.id === questionId);
                  return (
                    <div key={index} className="flex items-center gap-3 p-3 bg-sehatti-warm-gray-50 rounded-lg">
                      <FaQuestionCircle className="w-4 h-4 text-sehatti-gold-600 flex-shrink-0" />
                      <span className="text-sm text-sehatti-warm-gray-700">
                        {question ? question.question_text.en : `Question ID: ${questionId}`}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Schedule */}
            <div>
              <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Schedule Configuration
              </h4>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-sehatti-warm-gray-700">Recurrence:</span>
                    <span className="ml-2 text-sm text-sehatti-warm-gray-900">
                      {(selectedCampaign.schedule as any)?.recurrence_type || 'Not set'}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-sehatti-warm-gray-700">Pattern:</span>
                    <span className="ml-2 text-sm text-sehatti-warm-gray-900">
                      {(selectedCampaign.schedule as any)?.weekday_pattern || 'Not set'}
                    </span>
                  </div>
                </div>
                
                {(selectedCampaign.schedule as any)?.start_date && (
                  <div>
                    <span className="text-sm font-medium text-sehatti-warm-gray-700">Start Date:</span>
                    <span className="ml-2 text-sm text-sehatti-warm-gray-900">
                      {new Date((selectedCampaign.schedule as any).start_date).toLocaleDateString()}
                    </span>
                  </div>
                )}

                {(selectedCampaign.schedule as any)?.time_slots && (
                  <div>
                    <span className="text-sm font-medium text-sehatti-warm-gray-700 block mb-2">Time Slots:</span>
                    <div className="space-y-2">
                      {(selectedCampaign.schedule as any).time_slots.map((slot: TimeSlotInfo, index: number) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-sehatti-warm-gray-50 rounded">
                          <FaClock className="w-4 h-4 text-sehatti-gold-600" />
                          <span className="text-sm">{slot.time}</span>
                          <Badge variant="secondary">{slot.questions_count} questions</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Distribution */}
            <div>
              <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Distribution Settings
              </h4>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium text-sehatti-warm-gray-700">Target Audience:</span>
                  <span className="ml-2 text-sm text-sehatti-warm-gray-900">
                    {(selectedCampaign.distribution as any)?.target_audience || 'Not specified'}
                  </span>
                </div>
                <div>
                  <span className="text-sm font-medium text-sehatti-warm-gray-700">Corporate IDs:</span>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {(selectedCampaign.distribution as any)?.corporate_ids?.map((corpId: string, index: number) => (
                      <Badge key={index} variant="outline">
                        {corpId}
                      </Badge>
                    )) || <span className="text-sm text-sehatti-warm-gray-500">None specified</span>}
                  </div>
                </div>
              </div>
            </div>

            {/* Campaign Actions */}
            <div>
              <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Campaign Actions
              </h4>
              <div className="flex gap-3 flex-wrap">
                {getCampaignActions(selectedCampaign)}
              </div>
            </div>

            {/* Timestamps */}
            <div>
              <h4 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Timestamps
              </h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-sehatti-warm-gray-700">Created:</span>
                  <span className="ml-2">{formatDate(selectedCampaign.created_at)}</span>
                </div>
                <div>
                  <span className="font-medium text-sehatti-warm-gray-700">Updated:</span>
                  <span className="ml-2">{formatDate(selectedCampaign.updated_at)}</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsViewModalOpen(false)}
              >
                Close
              </Button>
              <Button
                onClick={() => {
                  setIsViewModalOpen(false);
                  openEditModal(selectedCampaign);
                }}
              >
                Edit Campaign
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Campaign"
        size="sm"
      >
        {selectedCampaign && (
          <div className="space-y-4">
            <p className="text-sehatti-warm-gray-700">
              Are you sure you want to delete this campaign? This action cannot be undone.
            </p>
            
            <div className="bg-sehatti-warm-gray-50 rounded-lg p-4">
              <div className="text-sm font-medium text-sehatti-warm-gray-900 mb-1">
                Campaign:
              </div>
              <div className="text-sm text-sehatti-warm-gray-700">
                {selectedCampaign.name.en}
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsDeleteModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteCampaign}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Campaign'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BroadcastManager; 