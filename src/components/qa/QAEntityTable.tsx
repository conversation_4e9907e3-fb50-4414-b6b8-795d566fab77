import React from 'react';
import { 
  FaEdit, 
  FaTrash, 
  <PERSON>a<PERSON>ye, 
  FaLanguage,
  FaToggleOn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import type { 
  EntityType, 
  Corporate, 
  Division, 
  Target, 
  Question, 
  Assessment,
  PaginatedResponse,
  TranslatedContent
} from '../../types/qa';

interface QAEntityTableProps {
  entityType: EntityType;
  data: PaginatedResponse<any> | null;
  loading: boolean;
  onEdit: (entity: any) => void;
  onDelete: (entity: any) => void;
  onView: (entity: any) => void;
  onRetranslate?: (questionId: string) => void;
  onStatusChange?: (entity: any) => void;
  page: number;
  size: number;
  onPageChange: (page: number) => void;
  getTranslatedText: (content: TranslatedContent | string | undefined) => string;
  t: (key: string) => string;
}

const StatusBadge: React.FC<{ status: string; type?: string }> = ({ status, type = 'default' }) => {
  const getStatusVariant = (status: string) => {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
      case 'APPROVED':
        return 'success';
      case 'INACTIVE':
      case 'REJECTED':
        return 'destructive';
      case 'PENDING':
      case 'DRAFT':
        return 'warning';
      default:
        return 'subtle';
    }
  };

  return (
    <Badge variant={getStatusVariant(status)} size="sm">
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

const ActionButtons: React.FC<{
  entity: any;
  entityType: EntityType;
  onEdit: (entity: any) => void;
  onDelete: (entity: any) => void;
  onView: (entity: any) => void;
  onRetranslate?: (questionId: string) => void;
  onStatusChange?: (entity: any) => void;
  t: (key: string) => string;
}> = ({ entity, entityType, onEdit, onDelete, onView, onRetranslate, onStatusChange, t }) => (
  <div className="flex items-center gap-2">
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onView(entity)}
      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
      title={t("View")}
    >
      <FaEye className="w-4 h-4" />
    </Button>
    
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onEdit(entity)}
      className="text-green-600 hover:text-green-700 hover:bg-green-50"
      title={t("Edit")}
    >
      <FaEdit className="w-4 h-4" />
    </Button>

    {entityType === 'questions' && onRetranslate && (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onRetranslate(entity.id)}
        className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
        title={t("Retranslate")}
      >
        <FaLanguage className="w-4 h-4" />
      </Button>
    )}

    {entityType === 'corporates' && onStatusChange && (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onStatusChange(entity)}
        className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
        title={t("Change Status")}
      >
        {entity.status === 'ACTIVE' ? <FaToggleOn className="w-4 h-4" /> : <FaToggleOff className="w-4 h-4" />}
      </Button>
    )}
    
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onDelete(entity)}
      className="text-red-600 hover:text-red-700 hover:bg-red-50"
      title={t("Delete")}
    >
      <FaTrash className="w-4 h-4" />
    </Button>
  </div>
);

const CorporateRow: React.FC<{
  corporate: Corporate;
  onEdit: (entity: any) => void;
  onDelete: (entity: any) => void;
  onView: (entity: any) => void;
  onStatusChange?: (entity: any) => void;
  t: (key: string) => string;
}> = ({ corporate, onEdit, onDelete, onView, onStatusChange, t }) => (
  <tr className="hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-800 transition-colors">
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
        {corporate.name}
      </div>
      <div className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
        {corporate.email}
      </div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <StatusBadge status={corporate.status} />
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {corporate.consultant?.name || 'N/A'}
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {new Date(corporate.created_at).toLocaleDateString()}
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-right">
      <ActionButtons
        entity={corporate}
        entityType="corporates"
        onEdit={onEdit}
        onDelete={onDelete}
        onView={onView}
        onStatusChange={onStatusChange}
        t={t}
      />
    </td>
  </tr>
);

const DivisionRow: React.FC<{
  division: Division;
  onEdit: (entity: any) => void;
  onDelete: (entity: any) => void;
  onView: (entity: any) => void;
  getTranslatedText: (content: TranslatedContent | string | undefined) => string;
  t: (key: string) => string;
}> = ({ division, onEdit, onDelete, onView, getTranslatedText, t }) => (
  <tr className="hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-800 transition-colors">
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
        {getTranslatedText(division.name)}
      </div>
      <div className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
        {getTranslatedText(division.description) || 'No description'}
      </div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <StatusBadge status={division.status} />
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {division.targets_count || 0} targets
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {division.questions_count || 0} questions
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {new Date(division.created_at).toLocaleDateString()}
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-right">
      <ActionButtons
        entity={division}
        entityType="divisions"
        onEdit={onEdit}
        onDelete={onDelete}
        onView={onView}
        t={t}
      />
    </td>
  </tr>
);

const QuestionRow: React.FC<{
  question: Question;
  onEdit: (entity: any) => void;
  onDelete: (entity: any) => void;
  onView: (entity: any) => void;
  onRetranslate?: (questionId: string) => void;
  getTranslatedText: (content: TranslatedContent | string | undefined) => string;
  t: (key: string) => string;
}> = ({ question, onEdit, onDelete, onView, onRetranslate, getTranslatedText, t }) => (
  <tr className="hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-800 transition-colors">
    <td className="px-6 py-4">
      <div className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 max-w-xs truncate">
        {getTranslatedText(question.question_text)}
      </div>
      <div className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
        {question.question_type.replace('_', ' ').toUpperCase()}
      </div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <StatusBadge status={question.status} />
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-center">
      <Badge variant={question.required ? 'destructive' : 'subtle'} size="sm">
        {question.required ? 'Required' : 'Optional'}
      </Badge>
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {question.responses_count || 0} responses
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
      {new Date(question.created_at).toLocaleDateString()}
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-right">
      <ActionButtons
        entity={question}
        entityType="questions"
        onEdit={onEdit}
        onDelete={onDelete}
        onView={onView}
        onRetranslate={onRetranslate}
        t={t}
      />
    </td>
  </tr>
);

const Pagination: React.FC<{
  page: number;
  total: number;
  size: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  onPageChange: (page: number) => void;
  t: (key: string) => string;
}> = ({ page, total, size, hasNextPage, hasPreviousPage, onPageChange, t }) => {
  const totalPages = Math.ceil(total / size);
  const startItem = (page - 1) * size + 1;
  const endItem = Math.min(page * size, total);

  return (
    <div className="flex items-center justify-between px-6 py-4 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
      <div className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
        {t("Showing")} {startItem} {t("to")} {endItem} {t("of")} {total} {t("results")}
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(page - 1)}
          disabled={!hasPreviousPage}
          className="flex items-center gap-2"
        >
          <FaChevronLeft className="w-4 h-4" />
          {t("Previous")}
        </Button>
        
        <div className="flex items-center gap-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const pageNum = page <= 3 ? i + 1 : page + i - 2;
            if (pageNum > totalPages) return null;
            
            return (
              <Button
                key={pageNum}
                variant={pageNum === page ? "default" : "ghost"}
                size="sm"
                onClick={() => onPageChange(pageNum)}
                className="w-8 h-8 p-0"
              >
                {pageNum}
              </Button>
            );
          })}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(page + 1)}
          disabled={!hasNextPage}
          className="flex items-center gap-2"
        >
          {t("Next")}
          <FaChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export const QAEntityTable: React.FC<QAEntityTableProps> = ({
  entityType,
  data,
  loading,
  onEdit,
  onDelete,
  onView,
  onRetranslate,
  onStatusChange,
  page,
  size,
  onPageChange,
  getTranslatedText,
  t,
}) => {
  if (loading) {
    return (
      <Card className="p-8">
        <div className="flex items-center justify-center">
          <FaSpinner className="w-8 h-8 animate-spin text-sehatti-gold-500" />
          <span className="ml-3 text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
            {t("Loading...")}
          </span>
        </div>
      </Card>
    );
  }

  if (!data || !data.items.length) {
    return (
      <Card className="p-8">
        <div className="text-center text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
          {t("No data available")}
        </div>
      </Card>
    );
  }

  const getTableHeaders = () => {
    switch (entityType) {
      case 'corporates':
        return ['Name', 'Status', 'Consultant', 'Created', 'Actions'];
      case 'divisions':
        return ['Name', 'Status', 'Targets', 'Questions', 'Created', 'Actions'];
      case 'targets':
        return ['Name', 'Status', 'Questions', 'Order', 'Created', 'Actions'];
      case 'questions':
        return ['Question', 'Status', 'Required', 'Responses', 'Created', 'Actions'];
      case 'assessments':
        return ['Title', 'Type', 'Status', 'Questions', 'Created', 'Actions'];
      default:
        return [];
    }
  };

  const renderTableRow = (item: any, index: number) => {
    switch (entityType) {
      case 'corporates':
        return (
          <CorporateRow
            key={item.id}
            corporate={item}
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            onStatusChange={onStatusChange}
            t={t}
          />
        );
      case 'divisions':
        return (
          <DivisionRow
            key={item.id}
            division={item}
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            getTranslatedText={getTranslatedText}
            t={t}
          />
        );
      case 'questions':
        return (
          <QuestionRow
            key={item.id}
            question={item}
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            onRetranslate={onRetranslate}
            getTranslatedText={getTranslatedText}
            t={t}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800">
            <tr>
              {getTableHeaders().map((header, index) => (
                <th
                  key={index}
                  className="px-6 py-3 text-left text-xs font-medium text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400 uppercase tracking-wider"
                >
                  {t(header)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-sehatti-warm-gray-900 divide-y divide-sehatti-warm-gray-200 dark:divide-sehatti-warm-gray-700">
            {data.items.map(renderTableRow)}
          </tbody>
        </table>
      </div>

      <Pagination
        page={data.page}
        total={data.total}
        size={data.size}
        hasNextPage={data.hasNextPage}
        hasPreviousPage={data.hasPreviousPage}
        onPageChange={onPageChange}
        t={t}
      />
    </Card>
  );
};

export default QAEntityTable; 