import React from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaDatabase } from 'react-icons/fa';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import type { LegacyCorporateResponse } from '../../types/qa';

interface QAMigrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  legacyCorporatesData: LegacyCorporateResponse | null;
  onMigrate: () => Promise<void>;
  t: (key: string) => string;
}

export const QAMigrationModal: React.FC<QAMigrationModalProps> = ({
  isOpen,
  onClose,
  legacyCorporatesData,
  onMigrate,
  t,
}) => {
  const [isMigrating, setIsMigrating] = React.useState(false);

  const handleMigrate = async () => {
    setIsMigrating(true);
    try {
      await onMigrate();
    } finally {
      setIsMigrating(false);
    }
  };

  const corporateCount = legacyCorporatesData?.data?.data?.length || 0;

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      size="lg"
    >
      <div className="space-y-6">
        {/* Warning Section */}
        <Card className="p-4 border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20">
          <div className="flex items-start gap-3">
            <FaExclamationTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                {t('Important Notice')}
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                {t('This operation will migrate legacy corporate data to the QA service. Please ensure you have a backup before proceeding.')}
              </p>
            </div>
          </div>
        </Card>

        {/* Migration Overview */}
        <div>
          <h3 className="text-lg font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-4">
            {t('Migration Overview')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <FaDatabase className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                    {t('Legacy Corporates')}
                  </p>
                  <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    {corporateCount}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <FaDatabase className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                    {t('Ready for Migration')}
                  </p>
                  <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    {corporateCount}
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Corporate List Preview */}
        {legacyCorporatesData?.data?.data && legacyCorporatesData.data.data.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-4">
              {t('Corporates to Migrate')}
            </h3>
            
            <Card className="max-h-60 overflow-y-auto">
              <div className="divide-y divide-sehatti-warm-gray-200 dark:divide-sehatti-warm-gray-700">
                {legacyCorporatesData.data.data.slice(0, 10).map((corporate) => (
                  <div key={corporate.id} className="p-4 flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                        {corporate.name}
                      </p>
                      <p className="text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
                        {corporate.email}
                      </p>
                    </div>
                    <Badge 
                      variant={corporate.status === 'active' ? 'success' : 'subtle'}
                      size="sm"
                    >
                      {corporate.status}
                    </Badge>
                  </div>
                ))}
                
                {legacyCorporatesData.data.data.length > 10 && (
                  <div className="p-4 text-center text-sm text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400">
                    {t('And')} {legacyCorporatesData.data.data.length - 10} {t('more corporates...')}
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}

        {/* Migration Steps */}
        <div>
          <h3 className="text-lg font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-4">
            {t('Migration Process')}
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                {t('Extract corporate data from legacy system')}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                {t('Transform data to QA service format')}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                {t('Create divisions and organizational structure')}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                {t('Validate migrated data')}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-4 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
          <Button 
            type="button" 
            variant="ghost" 
            onClick={onClose}
            disabled={isMigrating}
          >
            {t('Cancel')}
          </Button>
          
          <Button
            type="button"
            variant="default"
            onClick={handleMigrate}
            disabled={isMigrating || corporateCount === 0}
            className="flex items-center gap-2"
          >
            {isMigrating && <FaSpinner className="w-4 h-4 animate-spin" />}
            {isMigrating ? t('Migrating...') : t('Start Migration')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default QAMigrationModal; 