import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { FaSite<PERSON>p, FaLanguage, FaSpinner } from "react-icons/fa";
import {
  Button,
  Input,
  Card,
  Badge
} from "@/components/ui";
import type { 
  Division, 
  CreateDivisionRequest, 
  UpdateDivisionRequest
} from "@/types/qa";

interface QADivisionFormProps {
  initialData?: Division | null;
  onSubmit: (data: CreateDivisionRequest | UpdateDivisionRequest) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  onFormSubmit?: (submitFn: () => void) => void;
}

interface FormData {
  name_en: string;
  name_ar: string;
  name_hi: string;
  description_en: string;
  description_ar: string;
  description_hi: string;
  is_active: boolean;
}

const QADivisionForm: React.FC<QADivisionFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  onFormSubmit
}) => {
  const [formData, setFormData] = useState<FormData>({
    name_en: '',
    name_ar: '',
    name_hi: '',
    description_en: '',
    description_ar: '',
    description_hi: '',
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const isEdit = !!initialData;

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      setFormData({
        name_en: initialData.name?.en || '',
        name_ar: initialData.name?.ar || '',
        name_hi: initialData.name?.hi || '',
        description_en: initialData.description?.en || '',
        description_ar: initialData.description?.ar || '',
        description_hi: initialData.description?.hi || '',
        is_active: initialData.is_active
      });
    }
  }, [initialData]);

  // Expose submit function to parent
  useEffect(() => {
    if (onFormSubmit) {
      onFormSubmit(() => {
        const form = document.querySelector('form[data-qa-division-form]') as HTMLFormElement;
        if (form) {
          form.requestSubmit();
        }
      });
    }
  }, [onFormSubmit]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Name in English is required
    if (!formData.name_en.trim()) {
      newErrors.name_en = 'English name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    try {
      if (isEdit) {
        // For updates, send multilingual content
        const updateData: UpdateDivisionRequest = {
          is_active: formData.is_active
        };

        // Add name if any language has content
        if (formData.name_en || formData.name_ar || formData.name_hi) {
          updateData.name = {
            en: formData.name_en,
            ar: formData.name_ar || undefined,
            hi: formData.name_hi || undefined
          };
        }

        // Add description if any language has content
        if (formData.description_en || formData.description_ar || formData.description_hi) {
          updateData.description = {
            en: formData.description_en,
            ar: formData.description_ar || undefined,
            hi: formData.description_hi || undefined
          };
        }

        await onSubmit(updateData);
      } else {
        // For creation, send simple English text (backend will translate)
        const createData: CreateDivisionRequest = {
          name: formData.name_en,
          description: formData.description_en || undefined
        };

        await onSubmit(createData);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-qa-division-form>

      {/* Name Section */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaLanguage className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Division Name
          </h4>
          <Badge variant="destructive" size="sm">Required</Badge>
        </div>
        
        <div className="space-y-4">
          <Input
            label="English Name *"
            value={formData.name_en}
            onChange={(e) => handleInputChange('name_en', e.target.value)}
            placeholder="Enter division name in English"
            error={errors.name_en}
            variant="gold"
            className="bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm"
            required
          />
          
          {isEdit && (
            <>
              <Input
                label="Arabic Name"
                value={formData.name_ar}
                onChange={(e) => handleInputChange('name_ar', e.target.value)}
                placeholder="اسم القسم بالعربية"
                variant="gold"
                className="bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm"
                dir="rtl"
              />
              
              <Input
                label="Hindi Name"
                value={formData.name_hi}
                onChange={(e) => handleInputChange('name_hi', e.target.value)}
                placeholder="डिवीजन का नाम हिंदी में"
                variant="gold"
                className="bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm"
              />
            </>
          )}
        </div>
      </Card>

      {/* Description Section */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaLanguage className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Description
          </h4>
          <Badge variant="outline" size="sm">Optional</Badge>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              English Description
            </label>
            <textarea
              value={formData.description_en}
              onChange={(e) => handleInputChange('description_en', e.target.value)}
              placeholder="Enter description in English"
              rows={3}
              className="w-full rounded-xl border-2 border-sehatti-gold-200/60 bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white dark:focus:bg-sehatti-warm-gray-900 focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-gold-700/60"
            />
          </div>
          
          {isEdit && (
            <>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Arabic Description
                </label>
                <textarea
                  value={formData.description_ar}
                  onChange={(e) => handleInputChange('description_ar', e.target.value)}
                  placeholder="وصف القسم بالعربية"
                  rows={3}
                  dir="rtl"
                  className="w-full rounded-xl border-2 border-sehatti-gold-200/60 bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white dark:focus:bg-sehatti-warm-gray-900 focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-gold-700/60"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Hindi Description
                </label>
                <textarea
                  value={formData.description_hi}
                  onChange={(e) => handleInputChange('description_hi', e.target.value)}
                  placeholder="डिवीजन का विवरण हिंदी में"
                  rows={3}
                  className="w-full rounded-xl border-2 border-sehatti-gold-200/60 bg-white/70 dark:bg-sehatti-warm-gray-800/70 backdrop-blur-sm px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white dark:focus:bg-sehatti-warm-gray-900 focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-gold-700/60"
                />
              </div>
            </>
          )}
        </div>
      </Card>

      {/* Status Section */}
      {isEdit && (
        <Card className="p-4">
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-4">
            Division Status
          </h4>
          
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="is_active"
                checked={formData.is_active === true}
                onChange={() => handleInputChange('is_active', true)}
                className="w-4 h-4 text-sehatti-gold-600 border-sehatti-warm-gray-300 focus:ring-sehatti-gold-500"
              />
              <span className="text-sm">Active</span>
              <Badge variant="success" size="sm">Visible</Badge>
            </label>
            
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="is_active"
                checked={formData.is_active === false}
                onChange={() => handleInputChange('is_active', false)}
                className="w-4 h-4 text-sehatti-gold-600 border-sehatti-warm-gray-300 focus:ring-sehatti-gold-500"
              />
              <span className="text-sm">Inactive</span>
              <Badge variant="destructive" size="sm">Hidden</Badge>
            </label>
          </div>
        </Card>
      )}

      {/* Translation Notice */}
      {!isEdit && (
        <div className="bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 border border-sehatti-gold-200 dark:border-sehatti-gold-800/30 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <FaLanguage className="w-5 h-5 text-sehatti-gold-600 dark:text-sehatti-gold-400 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-sehatti-gold-800 dark:text-sehatti-gold-300">
                Automatic Translation
              </p>
              <p className="text-xs text-sehatti-gold-700 dark:text-sehatti-gold-400 mt-1">
                Arabic and Hindi translations will be generated automatically when you create the division.
                You can edit them later if needed.
              </p>
            </div>
          </div>
        </div>
      )}

    </form>
  );
};

export default QADivisionForm; 