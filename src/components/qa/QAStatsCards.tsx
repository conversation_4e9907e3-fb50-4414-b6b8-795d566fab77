import React from 'react';
import { 
  FaB<PERSON>ing, 
  FaSitemap, 
  FaB<PERSON><PERSON>e, 
  FaQuestionCircle, 
  FaClipboardList 
} from 'react-icons/fa';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';

interface QAStatsCardsProps {
  corporatesCount: number;
  divisionsCount: number;
  targetsCount: number;
  questionsCount: number;
  assessmentsCount: number;
}

interface StatCardProps {
  title: string;
  count: number;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  count, 
  icon, 
  color, 
  bgColor, 
  description 
}) => (
  <Card className="p-6 hover:shadow-lg transition-all duration-200 border-l-4" style={{ borderLeftColor: color }}>
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="flex items-center gap-3 mb-2">
          <div 
            className="p-3 rounded-lg"
            style={{ backgroundColor: bgColor, color }}
          >
            {icon}
          </div>
          <div>
            <h3 className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              {title}
            </h3>
            <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              {count.toLocaleString()}
            </p>
          </div>
        </div>
        {description && (
          <p className="text-xs text-sehatti-warm-gray-500 dark:text-sehatti-warm-gray-400 mt-2">
            {description}
          </p>
        )}
      </div>
      <Badge 
        variant="subtle" 
        className="text-xs"
        style={{ backgroundColor: bgColor, color }}
      >
        Active
      </Badge>
    </div>
  </Card>
);

export const QAStatsCards: React.FC<QAStatsCardsProps> = ({
  corporatesCount,
  divisionsCount,
  targetsCount,
  questionsCount,
  assessmentsCount,
}) => {
  const stats = [
    {
      title: 'Total Corporates',
      count: corporatesCount,
      icon: <FaBuilding className="w-5 h-5" />,
      color: '#3B82F6',
      bgColor: '#EFF6FF',
      description: 'Registered corporate entities'
    },
    {
      title: 'Divisions',
      count: divisionsCount,
      icon: <FaSitemap className="w-5 h-5" />,
      color: '#10B981',
      bgColor: '#ECFDF5',
      description: 'Organizational divisions'
    },
    {
      title: 'Targets',
      count: targetsCount,
      icon: <FaBullseye className="w-5 h-5" />,
      color: '#F59E0B',
      bgColor: '#FFFBEB',
      description: 'Assessment targets'
    },
    {
      title: 'Questions',
      count: questionsCount,
      icon: <FaQuestionCircle className="w-5 h-5" />,
      color: '#8B5CF6',
      bgColor: '#F5F3FF',
      description: 'Total questions created'
    },
    {
      title: 'Assessments',
      count: assessmentsCount,
      icon: <FaClipboardList className="w-5 h-5" />,
      color: '#EF4444',
      bgColor: '#FEF2F2',
      description: 'Active assessments'
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export default QAStatsCards; 