import React, { useState, useMemo } from 'react';
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaEye, 
  FaSearch, 
  FaFilter,
  FaLanguage,
  FaQuestionCircle,
  FaBuilding,
  FaLayerGroup,
  FaTags
} from 'react-icons/fa';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Textarea } from '../ui/Textarea';
import { 
  useGetQuestionsQuery,
  useGetDivisionsQuery,
  useGetSubDivisionsQuery,
  useCreateQuestionMutation,
  useUpdateQuestionMutation,
  useDeleteQuestionMutation,
  type Question,
  type QuestionType,
  type QuestionStatus,
  type CreateQuestionRequest,
  type UpdateQuestionRequest,
  type QuestionOptionRequest
} from '../../store/api/qaApi';
import { useGetCompaniesWithConsultantsQuery } from '../../store/api/companyApi';
import { toast } from 'react-hot-toast';
import { formatDate } from '../../utils/date';

interface QuestionsManagerProps {
  corporateId?: string;
  className?: string;
}

interface QuestionFormData {
  division_name?: string;
  subdivision_name?: string;
  question_type: QuestionType;
  question_text: string;
  options: QuestionOptionRequest[];
  corporate_ids: string[];
  status: QuestionStatus;
}

const QuestionsManager: React.FC<QuestionsManagerProps> = ({ 
  corporateId,
  className = ""
}) => {
  // State management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<QuestionType | 'all'>('all');
  const [filterStatus, setFilterStatus] = useState<QuestionStatus | 'all'>('all');
  const [filterDivision, setFilterDivision] = useState<string>('all');

  // Form state
  const [formData, setFormData] = useState<QuestionFormData>({
    question_type: 'ongoing',
    question_text: '',
    options: [
      { text: '', value: 1 },
      { text: '', value: 2 }
    ],
    corporate_ids: corporateId ? [corporateId] : [],
    status: 'draft'
  });

  // API queries
  const { 
    data: questions = [], 
    isLoading: questionsLoading,
    error: questionsError,
    refetch: refetchQuestions
  } = useGetQuestionsQuery({
    corporate_id: corporateId,
    question_type: filterType === 'all' ? undefined : filterType,
    status: filterStatus === 'all' ? undefined : filterStatus
  });

  const { data: divisions = [] } = useGetDivisionsQuery();
  const { data: subdivisions = [] } = useGetSubDivisionsQuery({});

  // Load corporates for assignment
  const { 
    data: corporatesData, 
    isLoading: corporatesLoading,
    error: corporatesError 
  } = useGetCompaniesWithConsultantsQuery({});

  // Extract corporates list
  const corporates = corporatesData?.data?.data || [];

  // Mutations
  const [createQuestion, { isLoading: creating }] = useCreateQuestionMutation();
  const [updateQuestion, { isLoading: updating }] = useUpdateQuestionMutation();
  const [deleteQuestion, { isLoading: deleting }] = useDeleteQuestionMutation();

  // Filtered questions
  const filteredQuestions = useMemo(() => {
    return questions.filter(question => {
      const matchesSearch = question.question_text.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          question.id.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDivision = filterDivision === 'all' || question.division_id === filterDivision;
      
      return matchesSearch && matchesDivision;
    });
  }, [questions, searchTerm, filterDivision]);

  // Event handlers
  const handleCreateQuestion = async () => {
    try {
      const createData: CreateQuestionRequest = {
        ...formData,
        options: formData.options.filter(opt => opt.text.trim() !== '')
      };

      await createQuestion(createData).unwrap();
      toast.success('Question created successfully');
      setIsCreateModalOpen(false);
      resetForm();
      refetchQuestions();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to create question');
    }
  };

  const handleUpdateQuestion = async () => {
    if (!selectedQuestion) return;

    try {
      const updateData: UpdateQuestionRequest = {
        question_text: { en: formData.question_text },
        question_type: formData.question_type,
        status: formData.status,
        corporate_ids: formData.corporate_ids,
        options: formData.options.filter(opt => opt.text.trim() !== '')
      };

      await updateQuestion({
        questionId: selectedQuestion.id,
        data: updateData
      }).unwrap();
      
      toast.success('Question updated successfully');
      setIsEditModalOpen(false);
      setSelectedQuestion(null);
      resetForm();
      refetchQuestions();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to update question');
    }
  };

  const handleDeleteQuestion = async () => {
    if (!selectedQuestion) return;

    try {
      await deleteQuestion({
        questionId: selectedQuestion.id,
        hardDelete: false
      }).unwrap();
      
      toast.success('Question deleted successfully');
      setIsDeleteModalOpen(false);
      setSelectedQuestion(null);
      refetchQuestions();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to delete question');
    }
  };

  const resetForm = () => {
    setFormData({
      question_type: 'ongoing',
      question_text: '',
      options: [
        { text: '', value: 1 },
        { text: '', value: 2 }
      ],
      corporate_ids: corporateId ? [corporateId] : [],
      status: 'draft'
    });
  };

  const openEditModal = (question: Question) => {
    setSelectedQuestion(question);
    setFormData({
      division_name: question.division?.name.en,
      subdivision_name: question.subdivision?.name.en,
      question_type: question.question_type,
      question_text: question.question_text.en,
      options: question.options.map(opt => ({
        text: opt.text.en,
        value: opt.value
      })),
      corporate_ids: question.corporate_ids,
      status: question.status
    });
    setIsEditModalOpen(true);
  };

  const openViewModal = (question: Question) => {
    setSelectedQuestion(question);
    setIsViewModalOpen(true);
  };

  const openDeleteModal = (question: Question) => {
    setSelectedQuestion(question);
    setIsDeleteModalOpen(true);
  };

  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { text: '', value: prev.options.length + 1 }]
    }));
  };

  const removeOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const updateOption = (index: number, field: 'text' | 'value', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => 
        i === index ? { ...opt, [field]: value } : opt
      )
    }));
  };

  const getStatusBadge = (status: QuestionStatus) => {
    const statusConfig = {
      active: { variant: 'success' as const, label: 'Active' },
      draft: { variant: 'warning' as const, label: 'Draft' },
      inactive: { variant: 'secondary' as const, label: 'Inactive' }
    };
    
    const config = statusConfig[status];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: QuestionType) => {
    const typeConfig = {
      pre_assessment: { variant: 'info' as const, label: 'Pre-Assessment' },
      post_assessment: { variant: 'success' as const, label: 'Post-Assessment' },
      ongoing: { variant: 'primary' as const, label: 'Ongoing' }
    };
    
    const config = typeConfig[type];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (questionsLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-sehatti-warm-gray-900 flex items-center gap-2">
            <FaQuestionCircle className="text-sehatti-gold-600" />
            Questions Management
          </h2>
          <p className="text-sehatti-warm-gray-600 mt-1">
            Manage assessment questions and multilingual content
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2"
        >
          <FaPlus className="w-4 h-4" />
          Create Question
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as QuestionType | 'all')}
          >
            <option value="all">All Types</option>
            <option value="pre_assessment">Pre-Assessment</option>
            <option value="post_assessment">Post-Assessment</option>
            <option value="ongoing">Ongoing</option>
          </Select>

          <Select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as QuestionStatus | 'all')}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="inactive">Inactive</option>
          </Select>

          <Select
            value={filterDivision}
            onChange={(e) => setFilterDivision(e.target.value)}
          >
            <option value="all">All Divisions</option>
            {divisions.map(division => (
              <option key={division.id} value={division.id}>
                {division.name.en}
              </option>
            ))}
          </Select>
        </div>
      </Card>

      {/* Questions List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredQuestions.map((question) => (
          <Card key={question.id} className="p-6 hover:shadow-lg transition-shadow">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  {getTypeBadge(question.question_type)}
                  {getStatusBadge(question.status)}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openViewModal(question)}
                  >
                    <FaEye className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditModal(question)}
                  >
                    <FaEdit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openDeleteModal(question)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <FaTrash className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Question Text */}
              <div>
                <h3 className="font-semibold text-sehatti-warm-gray-900 mb-2">
                  {question.question_text.en}
                </h3>
                {question.question_text.ar && (
                  <p className="text-sm text-sehatti-warm-gray-600 mb-1" dir="rtl">
                    {question.question_text.ar}
                  </p>
                )}
                {question.question_text.hi && (
                  <p className="text-sm text-sehatti-warm-gray-600">
                    {question.question_text.hi}
                  </p>
                )}
              </div>

              {/* Division & Subdivision */}
              <div className="flex items-center gap-2 text-sm text-sehatti-warm-gray-600">
                <FaLayerGroup className="w-4 h-4" />
                <span>{question.division?.name.en}</span>
                {question.subdivision && (
                  <>
                    <span>→</span>
                    <span>{question.subdivision.name.en}</span>
                  </>
                )}
              </div>

              {/* Options Count */}
              <div className="flex items-center gap-2 text-sm text-sehatti-warm-gray-600">
                <FaTags className="w-4 h-4" />
                <span>{question.options.length} options</span>
              </div>

              {/* Corporate Assignment */}
              {question.corporate_ids.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-sehatti-warm-gray-600">
                  <FaBuilding className="w-4 h-4" />
                  <span>{question.corporate_ids.length} corporate(s) assigned</span>
                </div>
              )}

              {/* Timestamps */}
              <div className="text-xs text-sehatti-warm-gray-500 space-y-1">
                <div>Created: {formatDate(question.created_at)}</div>
                <div>Updated: {formatDate(question.updated_at)}</div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredQuestions.length === 0 && (
        <Card className="p-12 text-center">
          <FaQuestionCircle className="mx-auto h-16 w-16 text-sehatti-warm-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">
            No questions found
          </h3>
          <p className="text-sehatti-warm-gray-600 mb-6">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all' || filterDivision !== 'all' 
              ? 'Try adjusting your filters or search terms.'
              : 'Get started by creating your first question.'
            }
          </p>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <FaPlus className="w-4 h-4 mr-2" />
            Create Question
          </Button>
        </Card>
      )}

      {/* Create Question Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Question"
        size="lg"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Question Type
              </label>
              <Select
                value={formData.question_type}
                onChange={(e) => setFormData(prev => ({ ...prev, question_type: e.target.value as QuestionType }))}
              >
                <option value="ongoing">Ongoing</option>
                <option value="pre_assessment">Pre-Assessment</option>
                <option value="post_assessment">Post-Assessment</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Status
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as QuestionStatus }))}
              >
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
            </div>
          </div>

          {/* Division & Subdivision */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Division Name
              </label>
              <Input
                placeholder="Enter division name (auto-creates if not exists)"
                value={formData.division_name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, division_name: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Subdivision Name
              </label>
              <Input
                placeholder="Enter subdivision name (auto-creates if not exists)"
                value={formData.subdivision_name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, subdivision_name: e.target.value }))}
              />
            </div>
          </div>

          {/* Question Text */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Question Text (English)
              <span className="text-sehatti-gold-600 ml-1">*</span>
            </label>
            <Textarea
              placeholder="Enter your question in English (will be auto-translated to Arabic and Hindi)"
              value={formData.question_text}
              onChange={(e) => setFormData(prev => ({ ...prev, question_text: e.target.value }))}
              rows={3}
            />
            <p className="text-xs text-sehatti-warm-gray-500 mt-1">
              <FaLanguage className="inline w-3 h-3 mr-1" />
              Will be automatically translated to Arabic and Hindi
            </p>
          </div>

          {/* Corporate Assignment */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Assign to Corporates
            </label>
            {corporatesLoading ? (
              <div className="text-sm text-sehatti-warm-gray-500">Loading corporates...</div>
            ) : corporatesError ? (
              <div className="text-sm text-red-600">Failed to load corporates</div>
            ) : (
              <div className="space-y-2 max-h-40 overflow-y-auto border rounded-lg p-3">
                {corporates.length > 0 ? (
                  <>
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={formData.corporate_ids.length === corporates.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              corporate_ids: corporates.map(corp => corp._id || corp.id)
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              corporate_ids: []
                            }));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm font-medium text-sehatti-warm-gray-700">
                        Select All ({corporates.length})
                      </span>
                    </label>
                    <hr className="my-2" />
                    {corporates.map(corporate => (
                      <label key={corporate._id || corporate.id} className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={formData.corporate_ids.includes(corporate._id || corporate.id)}
                          onChange={(e) => {
                            const corporateId = corporate._id || corporate.id;
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                corporate_ids: [...prev.corporate_ids, corporateId]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                corporate_ids: prev.corporate_ids.filter(id => id !== corporateId)
                              }));
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                        <div className="flex-1">
                          <span className="text-sm text-sehatti-warm-gray-700">
                            {corporate.name}
                          </span>
                          {corporate.email && (
                            <div className="text-xs text-sehatti-warm-gray-500">
                              {corporate.email}
                            </div>
                          )}
                        </div>
                        {corporate.status && (
                          <Badge 
                            variant={corporate.status === 'Active' ? 'success' : 'secondary'}
                            size="sm"
                          >
                            {corporate.status}
                          </Badge>
                        )}
                      </label>
                    ))}
                  </>
                ) : (
                  <p className="text-sm text-sehatti-warm-gray-500">
                    No corporates available
                  </p>
                )}
              </div>
            )}
            <p className="text-xs text-sehatti-warm-gray-500 mt-1">
              Select which corporates can access this question. Leave empty to make it available to all.
            </p>
          </div>

          {/* Options */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <label className="block text-sm font-medium text-sehatti-warm-gray-700">
                Answer Options
                <span className="text-sehatti-gold-600 ml-1">*</span>
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOption}
              >
                <FaPlus className="w-3 h-3 mr-1" />
                Add Option
              </Button>
            </div>

            <div className="space-y-3">
              {formData.options.map((option, index) => (
                <div key={index} className="flex gap-3 items-center">
                  <div className="flex-1">
                    <Input
                      placeholder={`Option ${index + 1} text`}
                      value={option.text}
                      onChange={(e) => updateOption(index, 'text', e.target.value)}
                    />
                  </div>
                  <div className="w-20">
                    <Input
                      type="number"
                      placeholder="Value"
                      value={option.value}
                      onChange={(e) => updateOption(index, 'value', parseInt(e.target.value) || 0)}
                    />
                  </div>
                  {formData.options.length > 2 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <FaTrash className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateQuestion}
              disabled={creating || !formData.question_text || formData.options.filter(opt => opt.text.trim()).length < 2}
            >
              {creating ? 'Creating...' : 'Create Question'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Question Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Question"
        size="lg"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Question Type
              </label>
              <Select
                value={formData.question_type}
                onChange={(e) => setFormData(prev => ({ ...prev, question_type: e.target.value as QuestionType }))}
              >
                <option value="ongoing">Ongoing</option>
                <option value="pre_assessment">Pre-Assessment</option>
                <option value="post_assessment">Post-Assessment</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Status
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as QuestionStatus }))}
              >
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
            </div>
          </div>

          {/* Question Text */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Question Text (English)
              <span className="text-sehatti-gold-600 ml-1">*</span>
            </label>
            <Textarea
              placeholder="Enter your question in English"
              value={formData.question_text}
              onChange={(e) => setFormData(prev => ({ ...prev, question_text: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Corporate Assignment */}
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
              Assign to Corporates
            </label>
            {corporatesLoading ? (
              <div className="text-sm text-sehatti-warm-gray-500">Loading corporates...</div>
            ) : corporatesError ? (
              <div className="text-sm text-red-600">Failed to load corporates</div>
            ) : (
              <div className="space-y-2 max-h-40 overflow-y-auto border rounded-lg p-3">
                {corporates.length > 0 ? (
                  <>
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={formData.corporate_ids.length === corporates.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              corporate_ids: corporates.map(corp => corp._id || corp.id)
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              corporate_ids: []
                            }));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm font-medium text-sehatti-warm-gray-700">
                        Select All ({corporates.length})
                      </span>
                    </label>
                    <hr className="my-2" />
                    {corporates.map(corporate => (
                      <label key={corporate._id || corporate.id} className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={formData.corporate_ids.includes(corporate._id || corporate.id)}
                          onChange={(e) => {
                            const corporateId = corporate._id || corporate.id;
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                corporate_ids: [...prev.corporate_ids, corporateId]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                corporate_ids: prev.corporate_ids.filter(id => id !== corporateId)
                              }));
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                        <div className="flex-1">
                          <span className="text-sm text-sehatti-warm-gray-700">
                            {corporate.name}
                          </span>
                          {corporate.email && (
                            <div className="text-xs text-sehatti-warm-gray-500">
                              {corporate.email}
                            </div>
                          )}
                        </div>
                        {corporate.status && (
                          <Badge 
                            variant={corporate.status === 'Active' ? 'success' : 'secondary'}
                            size="sm"
                          >
                            {corporate.status}
                          </Badge>
                        )}
                      </label>
                    ))}
                  </>
                ) : (
                  <p className="text-sm text-sehatti-warm-gray-500">
                    No corporates available
                  </p>
                )}
              </div>
            )}
            <p className="text-xs text-sehatti-warm-gray-500 mt-1">
              Select which corporates can access this question. Leave empty to make it available to all.
            </p>
          </div>

          {/* Options */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <label className="block text-sm font-medium text-sehatti-warm-gray-700">
                Answer Options
                <span className="text-sehatti-gold-600 ml-1">*</span>
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOption}
              >
                <FaPlus className="w-3 h-3 mr-1" />
                Add Option
              </Button>
            </div>

            <div className="space-y-3">
              {formData.options.map((option, index) => (
                <div key={index} className="flex gap-3 items-center">
                  <div className="flex-1">
                    <Input
                      placeholder={`Option ${index + 1} text`}
                      value={option.text}
                      onChange={(e) => updateOption(index, 'text', e.target.value)}
                    />
                  </div>
                  <div className="w-20">
                    <Input
                      type="number"
                      placeholder="Value"
                      value={option.value}
                      onChange={(e) => updateOption(index, 'value', parseInt(e.target.value) || 0)}
                    />
                  </div>
                  {formData.options.length > 2 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <FaTrash className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateQuestion}
              disabled={updating || !formData.question_text || formData.options.filter(opt => opt.text.trim()).length < 2}
            >
              {updating ? 'Updating...' : 'Update Question'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* View Question Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Question Details"
        size="lg"
      >
        {selectedQuestion && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                {getTypeBadge(selectedQuestion.question_type)}
                {getStatusBadge(selectedQuestion.status)}
              </div>
              <div className="text-sm text-sehatti-warm-gray-500">
                ID: {selectedQuestion.id}
              </div>
            </div>

            {/* Question Text */}
            <div>
              <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Question Text
              </h3>
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                    English
                  </div>
                  <p className="text-sehatti-warm-gray-900">
                    {selectedQuestion.question_text.en}
                  </p>
                </div>
                {selectedQuestion.question_text.ar && (
                  <div>
                    <div className="text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                      Arabic
                    </div>
                    <p className="text-sehatti-warm-gray-900" dir="rtl">
                      {selectedQuestion.question_text.ar}
                    </p>
                  </div>
                )}
                {selectedQuestion.question_text.hi && (
                  <div>
                    <div className="text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                      Hindi
                    </div>
                    <p className="text-sehatti-warm-gray-900">
                      {selectedQuestion.question_text.hi}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Options */}
            <div>
              <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Answer Options
              </h3>
              <div className="space-y-3">
                {selectedQuestion.options.map((option, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="text-sm font-medium text-sehatti-warm-gray-700">
                        Option {index + 1}
                      </div>
                      <Badge variant="secondary">Value: {option.value}</Badge>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm text-sehatti-warm-gray-600">EN:</span>
                        <span className="ml-2">{option.text.en}</span>
                      </div>
                      {option.text.ar && (
                        <div>
                          <span className="text-sm text-sehatti-warm-gray-600">AR:</span>
                          <span className="ml-2" dir="rtl">{option.text.ar}</span>
                        </div>
                      )}
                      {option.text.hi && (
                        <div>
                          <span className="text-sm text-sehatti-warm-gray-600">HI:</span>
                          <span className="ml-2">{option.text.hi}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Division & Subdivision */}
            {(selectedQuestion.division || selectedQuestion.subdivision) && (
              <div>
                <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                  Category
                </h3>
                <div className="space-y-2">
                  {selectedQuestion.division && (
                    <div>
                      <span className="text-sm font-medium text-sehatti-warm-gray-700">Division:</span>
                      <span className="ml-2">{selectedQuestion.division.name.en}</span>
                    </div>
                  )}
                  {selectedQuestion.subdivision && (
                    <div>
                      <span className="text-sm font-medium text-sehatti-warm-gray-700">Subdivision:</span>
                      <span className="ml-2">{selectedQuestion.subdivision.name.en}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Corporate IDs */}
            {selectedQuestion.corporate_ids.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                  Assigned Corporates
                </h3>
                <div className="flex flex-wrap gap-2">
                  {selectedQuestion.corporate_ids.map((corpId, index) => (
                    <Badge key={index} variant="outline">
                      {corpId}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Timestamps */}
            <div>
              <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-4">
                Timestamps
              </h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-sehatti-warm-gray-700">Created:</span>
                  <span className="ml-2">{formatDate(selectedQuestion.created_at)}</span>
                </div>
                <div>
                  <span className="font-medium text-sehatti-warm-gray-700">Updated:</span>
                  <span className="ml-2">{formatDate(selectedQuestion.updated_at)}</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsViewModalOpen(false)}
              >
                Close
              </Button>
              <Button
                onClick={() => {
                  setIsViewModalOpen(false);
                  openEditModal(selectedQuestion);
                }}
              >
                Edit Question
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Question"
        size="sm"
      >
        {selectedQuestion && (
          <div className="space-y-4">
            <p className="text-sehatti-warm-gray-700">
              Are you sure you want to delete this question? This action cannot be undone.
            </p>
            
            <div className="bg-sehatti-warm-gray-50 rounded-lg p-4">
              <div className="text-sm font-medium text-sehatti-warm-gray-900 mb-1">
                Question:
              </div>
              <div className="text-sm text-sehatti-warm-gray-700">
                {selectedQuestion.question_text.en}
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsDeleteModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteQuestion}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Question'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default QuestionsManager; 