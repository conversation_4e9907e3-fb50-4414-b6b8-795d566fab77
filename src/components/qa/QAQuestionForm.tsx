import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { FaQuestionCircle, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSitemap, FaBullseye, FaPlus, FaTrash, FaBuilding, FaTimes, FaSearch, FaChevronDown } from "react-icons/fa";
import {
  Button,
  Input,
  Card,
  Badge,
  Select
} from "@/components/ui";
import type { 
  Question, 
  Division,
  Target,
  CreateQuestionRequest, 
  UpdateQuestionRequest,
  QuestionTypeValue,
  QuestionStatusValue
} from "@/types/qa";

// Import corporate API
import { useGetCompaniesQuery } from "@/store/api";

interface QAQuestionFormProps {
  initialData?: Question | null;
  divisions: Division[];
  targets: Target[];
  onSubmit: (data: CreateQuestionRequest | UpdateQuestionRequest) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  onFormSubmit?: (submitFn: () => void) => void;
}

interface QuestionOption {
  text: {
    en: string;
    ar: string;
    hi: string;
  };
  value: number;
}

interface FormData {
  division_id: string;
  subdivision_id: string;
  question_type: QuestionTypeValue | '';
  question_text: {
    en: string;
    ar: string;
    hi: string;
  };
  options: QuestionOption[];
  corporate_ids: string[];
  status: QuestionStatusValue;
}

interface Corporate {
  _id: string;
  id: string;
  name: string;
  email?: string;
  status: string;
}

// Corporate Search Component
const CorporateSearchDropdown: React.FC<{
  selectedCorporateIds: string[];
  onSelectionChange: (corporateIds: string[]) => void;
}> = ({ selectedCorporateIds, onSelectionChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  
  // Fetch corporates from the API
  const { data: corporatesData, isLoading: corporatesLoading } = useGetCompaniesQuery();
  
  // Transform and normalize corporate data
  const corporates: Corporate[] = React.useMemo(() => {
    const rawCorporates = corporatesData?.items || [];
    return Array.isArray(rawCorporates) ? rawCorporates.map((corp: any) => ({
      _id: corp.company_id || corp.id,
      id: corp.company_id || corp.id,
      name: corp.name || 'Unknown Corporate',
      email: corp.email || '',
      status: corp.status || 'ACTIVE'
    })) : [];
  }, [corporatesData]);

  // Filter corporates based on search query
  const filteredCorporates = corporates.filter(corp =>
    corp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (corp.email && corp.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Get selected corporates for display
  const selectedCorporates = corporates.filter(corp => 
    selectedCorporateIds.includes(corp._id || corp.id)
  );

  const handleCorporateToggle = (corporate: Corporate) => {
    const corporateId = corporate._id || corporate.id;
    const isSelected = selectedCorporateIds.includes(corporateId);
    
    if (isSelected) {
      onSelectionChange(selectedCorporateIds.filter(id => id !== corporateId));
    } else {
      onSelectionChange([...selectedCorporateIds, corporateId]);
    }
  };

  const removeCorporate = (corporateId: string) => {
    onSelectionChange(selectedCorporateIds.filter(id => id !== corporateId));
  };

  return (
    <div className="relative">
      {/* Selected Corporates Display */}
      {selectedCorporates.length > 0 && (
        <div className="mb-3">
          <label className="block text-xs font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-2">
            Selected Corporates ({selectedCorporates.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedCorporates.map((corp) => (
              <Badge
                key={corp._id || corp.id}
                variant="outline"
                className="flex items-center gap-2 px-3 py-2"
              >
                <FaBuilding className="w-3 h-3 text-sehatti-gold-600" />
                <span className="font-medium">{corp.name}</span>
                <button
                  type="button"
                  onClick={() => removeCorporate(corp._id || corp.id)}
                  className="ml-1 text-red-500 hover:text-red-700 transition-colors"
                >
                  <FaTimes className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Search Dropdown Button */}
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-sehatti-warm-gray-800 border-2 border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-xl hover:border-sehatti-gold-300 focus:border-sehatti-gold-500 focus:ring-2 focus:ring-sehatti-gold-200 transition-all duration-200"
        >
          <div className="flex items-center gap-2 text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300">
            <FaSearch className="w-4 h-4" />
            <span>
              {selectedCorporates.length > 0 
                ? `${selectedCorporates.length} corporate${selectedCorporates.length === 1 ? '' : 's'} selected`
                : 'Search and select corporates...'
              }
            </span>
          </div>
          <FaChevronDown className={`w-4 h-4 text-sehatti-warm-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {/* Dropdown Panel */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-2 bg-white dark:bg-sehatti-warm-gray-800 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-xl shadow-lg max-h-64 overflow-hidden">
            {/* Search Input */}
            <div className="p-3 border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sehatti-warm-gray-400" />
                <input
                  type="text"
                  placeholder="Search corporates by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-600 rounded-lg focus:ring-2 focus:ring-sehatti-gold-200 focus:border-sehatti-gold-500 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-700 text-sm"
                />
              </div>
            </div>

            {/* Corporate List */}
            <div className="max-h-48 overflow-y-auto">
              {corporatesLoading ? (
                <div className="p-4 text-center">
                  <FaSpinner className="w-5 h-5 animate-spin mx-auto text-sehatti-gold-600" />
                  <p className="text-sm text-sehatti-warm-gray-600 mt-2">Loading corporates...</p>
                </div>
              ) : filteredCorporates.length === 0 ? (
                <div className="p-4 text-center text-sehatti-warm-gray-500">
                  {searchQuery ? `No corporates found matching "${searchQuery}"` : 'No corporates available'}
                </div>
              ) : (
                filteredCorporates.map((corporate) => {
                  const corporateId = corporate._id || corporate.id;
                  const isSelected = selectedCorporateIds.includes(corporateId);
                  
                  return (
                    <button
                      key={corporateId}
                      type="button"
                      onClick={() => handleCorporateToggle(corporate)}
                      className={`w-full px-4 py-3 text-left hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-700 transition-colors border-b border-sehatti-warm-gray-100 dark:border-sehatti-warm-gray-700 last:border-b-0 ${
                        isSelected ? 'bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                            isSelected 
                              ? 'bg-sehatti-gold-500 border-sehatti-gold-500' 
                              : 'border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600'
                          }`}>
                            {isSelected && <FaTimes className="w-2 h-2 text-white" />}
                          </div>
                          <FaBuilding className="w-4 h-4 text-sehatti-gold-600" />
                          <div>
                            <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                              {corporate.name}
                            </p>
                            {corporate.email && (
                              <p className="text-xs text-sehatti-warm-gray-500">
                                {corporate.email}
                              </p>
                            )}
                          </div>
                        </div>
                        <Badge 
                          variant={corporate.status === 'active' ? 'success' : 'outline'}
                          size="sm"
                        >
                          {corporate.status}
                        </Badge>
                      </div>
                    </button>
                  );
                })
              )}
            </div>

            {/* Action Buttons */}
            <div className="p-3 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800">
              <div className="flex justify-between items-center">
                <span className="text-xs text-sehatti-warm-gray-600">
                  {selectedCorporates.length} selected
                </span>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const QAQuestionForm: React.FC<QAQuestionFormProps> = ({
  initialData,
  divisions,
  targets,
  onSubmit,
  onCancel,
  isLoading = false,
  onFormSubmit
}) => {
  const [formData, setFormData] = useState<FormData>({
    division_id: '',
    subdivision_id: '',
    question_type: '',
    question_text: {
      en: '',
      ar: '',
      hi: ''
    },
    options: [
      {
        text: { en: '', ar: '', hi: '' },
        value: 1
      },
      {
        text: { en: '', ar: '', hi: '' },
        value: 2
      }
    ],
    corporate_ids: [],
    status: 'active'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const isEdit = !!initialData;

  // Register submit function with parent
  React.useEffect(() => {
    if (onFormSubmit) {
      onFormSubmit(() => {
        const form = document.querySelector('form[data-qa-question-form]') as HTMLFormElement;
        if (form) {
          form.requestSubmit();
        }
      });
    }
  }, [onFormSubmit]);

  // Filter targets based on selected division
  const filteredTargets = targets.filter(target => 
    !formData.division_id || target.division_id === formData.division_id
  );

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      setFormData({
        division_id: initialData.division_id || '',
        subdivision_id: initialData.subdivision_id || '',
        question_type: initialData.question_type || '',
        question_text: {
          en: initialData.question_text?.en || '',
          ar: initialData.question_text?.ar || '',
          hi: initialData.question_text?.hi || ''
        },
        options: initialData.options?.map(opt => ({
          text: {
            en: opt.text?.en || '',
            ar: opt.text?.ar || '',
            hi: opt.text?.hi || ''
          },
          value: opt.value || 1
        })) || [
          { text: { en: '', ar: '', hi: '' }, value: 1 },
          { text: { en: '', ar: '', hi: '' }, value: 2 }
        ],
        corporate_ids: initialData.corporate_ids || [],
        status: initialData.status || 'active'
      });
    }
  }, [initialData]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.division_id) {
      newErrors.division_id = 'Please select a division';
    }
    
    if (!formData.subdivision_id) {
      newErrors.subdivision_id = 'Please select a target';
    }
    
    if (!formData.question_type) {
      newErrors.question_type = 'Please select a question type';
    }
    
    if (!formData.question_text.en.trim()) {
      newErrors.question_text_en = 'English question text is required';
    }

    // Validate options
    const validOptions = formData.options.filter(opt => opt.text.en.trim());
    if (validOptions.length < 2) {
      newErrors.options = 'At least 2 options with English text are required';
    }

    // Check that all options have English text
    if (formData.options.some(opt => opt.text.en.trim() === '')) {
      newErrors.options = 'All options must have English text';
    }

    // Validate option values are unique and positive
    const optionValues = validOptions.map(opt => opt.value);
    const uniqueValues = new Set(optionValues);
    if (uniqueValues.size !== optionValues.length) {
      newErrors.options = 'Option values must be unique';
    }

    if (optionValues.some(val => val <= 0)) {
      newErrors.options = 'Option values must be positive numbers';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const updateQuestionText = (language: 'en' | 'ar' | 'hi', value: string) => {
    setFormData(prev => ({
      ...prev,
      question_text: { ...prev.question_text, [language]: value }
    }));
    
    if (errors.question_text_en && language === 'en') {
      setErrors(prev => ({ ...prev, question_text_en: '' }));
    }
  };

  const updateOptionText = (index: number, language: 'en' | 'ar' | 'hi', value: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => 
        i === index ? { 
          ...opt, 
          text: { ...opt.text, [language]: value }
        } : opt
      )
    }));
    
    if (errors.options) {
      setErrors(prev => ({ ...prev, options: '' }));
    }
  };

  const updateOptionValue = (index: number, value: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => 
        i === index ? { ...opt, value } : opt
      )
    }));
    
    if (errors.options) {
      setErrors(prev => ({ ...prev, options: '' }));
    }
  };

  const addOption = () => {
    const validValues = formData.options.map(opt => opt.value).filter(val => !isNaN(val) && val > 0);
    const nextValue = validValues.length > 0 ? Math.max(...validValues) + 1 : formData.options.length + 1;
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { 
        text: { en: '', ar: '', hi: '' }, 
        value: nextValue 
      }]
    }));
  };

  const removeOption = (index: number) => {
    if (formData.options.length > 2) {
      setFormData(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    try {
      if (isEdit) {
        // For updates, send full multilingual data
        const updateData: UpdateQuestionRequest = {
          division_id: formData.division_id,
          subdivision_id: formData.subdivision_id,
          question_type: formData.question_type as QuestionTypeValue,
          question_text: formData.question_text,
          options: formData.options.filter(opt => opt.text.en.trim()),
          corporate_ids: formData.corporate_ids,
          status: formData.status
        };

        await onSubmit(updateData);
      } else {
        // For creation, send simple English text (backend will translate)
        const createData: CreateQuestionRequest = {
          division_id: formData.division_id,
          subdivision_id: formData.subdivision_id,
          question_type: formData.question_type as QuestionTypeValue,
          question_text: formData.question_text.en, // Simple string for creation
          options: formData.options
            .filter(opt => opt.text.en.trim())
            .map(opt => ({
              text: opt.text.en, // Simple string for creation
              value: opt.value
            })),
          corporate_ids: formData.corporate_ids,
          status: formData.status
        };

        await onSubmit(createData);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const getTranslatedText = (content: any) => {
    if (typeof content === 'string') return content;
    return content?.en || content?.ar || content?.hi || 'N/A';
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-qa-question-form>

      {/* Division & Target Selection */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaSitemap className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Category Assignment
          </h4>
          <Badge variant="destructive" size="sm">Required</Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              Division *
            </label>
            <Select
              value={formData.division_id}
              onChange={(e) => {
                handleInputChange('division_id', e.target.value);
                // Reset target when division changes
                if (formData.subdivision_id) {
                  handleInputChange('subdivision_id', '');
                }
              }}
              error={errors.division_id}
              required
            >
              <option value="">Choose a division...</option>
              {divisions.map((division) => (
                <option key={division.id} value={division.id}>
                  {getTranslatedText(division.name)}
                </option>
              ))}
            </Select>
            {errors.division_id && (
              <p className="text-sm text-red-500 mt-1">{errors.division_id}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              Target *
            </label>
            <Select
              value={formData.subdivision_id}
              onChange={(e) => handleInputChange('subdivision_id', e.target.value)}
              error={errors.subdivision_id}
              disabled={!formData.division_id}
              required
            >
              <option value="">Choose a target...</option>
              {filteredTargets.map((target) => (
                <option key={target.id} value={target.id}>
                  {getTranslatedText(target.name)}
                </option>
              ))}
            </Select>
            {errors.subdivision_id && (
              <p className="text-sm text-red-500 mt-1">{errors.subdivision_id}</p>
            )}
          </div>
        </div>
      </Card>

      {/* Question Type & Status */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaLanguage className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Question Configuration
          </h4>
          <Badge variant="destructive" size="sm">Required</Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              Question Type *
            </label>
            <Select
              value={formData.question_type}
              onChange={(e) => handleInputChange('question_type', e.target.value)}
              error={errors.question_type}
              required
            >
              <option value="">Choose a type...</option>
              <option value="pre_assessment">Pre Assessment</option>
              <option value="post_assessment">Post Assessment</option>
              <option value="ongoing">Ongoing</option>
            </Select>
            {errors.question_type && (
              <p className="text-sm text-red-500 mt-1">{errors.question_type}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              Status
            </label>
            <Select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
            >
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="inactive">Inactive</option>
            </Select>
          </div>
        </div>
      </Card>

      {/* Question Text */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaQuestionCircle className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Question Text
          </h4>
          <Badge variant="destructive" size="sm">Required</Badge>
        </div>
        
        {/* Simple English Question Text for Create Mode */}
        {!isEdit ? (
          <div>
            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
              Question Text *
            </label>
            <textarea
              value={formData.question_text.en}
              onChange={(e) => updateQuestionText('en', e.target.value)}
              placeholder="Enter your question text"
              rows={3}
              className="w-full rounded-xl border-2 border-sehatti-warm-gray-200 bg-sehatti-warm-gray-50 px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-warm-gray-700 dark:bg-sehatti-warm-gray-800 dark:focus:bg-sehatti-warm-gray-900"
              required
            />
            {errors.question_text_en && (
              <p className="text-sm text-red-500 mt-1">{errors.question_text_en}</p>
            )}
          </div>
        ) : (
          /* Multilingual Fields for Edit Mode */
          <div className="space-y-4">
            {/* English */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                <span className="text-lg">🇺🇸</span>
                English Question *
              </label>
              <textarea
                value={formData.question_text.en}
                onChange={(e) => updateQuestionText('en', e.target.value)}
                placeholder="Enter your question in English"
                rows={3}
                className="w-full rounded-xl border-2 border-sehatti-warm-gray-200 bg-sehatti-warm-gray-50 px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-warm-gray-700 dark:bg-sehatti-warm-gray-800 dark:focus:bg-sehatti-warm-gray-900"
                required
              />
              {errors.question_text_en && (
                <p className="text-sm text-red-500 mt-1">{errors.question_text_en}</p>
              )}
            </div>
            
            {/* Arabic */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                <span className="text-lg">🇸🇦</span>
                Arabic Question
              </label>
              <textarea
                value={formData.question_text.ar}
                onChange={(e) => updateQuestionText('ar', e.target.value)}
                placeholder="السؤال باللغة العربية"
                rows={3}
                dir="rtl"
                className="w-full rounded-xl border-2 border-sehatti-warm-gray-200 bg-sehatti-warm-gray-50 px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-warm-gray-700 dark:bg-sehatti-warm-gray-800 dark:focus:bg-sehatti-warm-gray-900"
              />
            </div>
            
            {/* Hindi */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                <span className="text-lg">🇮🇳</span>
                Hindi Question
              </label>
              <textarea
                value={formData.question_text.hi}
                onChange={(e) => updateQuestionText('hi', e.target.value)}
                placeholder="हिंदी में प्रश्न"
                rows={3}
                className="w-full rounded-xl border-2 border-sehatti-warm-gray-200 bg-sehatti-warm-gray-50 px-4 py-3 text-sm transition-all duration-200 focus:border-sehatti-gold-500 focus:bg-white focus:shadow-[0_0_0_4px_rgba(210,179,122,0.1)] dark:border-sehatti-warm-gray-700 dark:bg-sehatti-warm-gray-800 dark:focus:bg-sehatti-warm-gray-900"
              />
            </div>
          </div>
        )}
      </Card>

      {/* Answer Options - Multilingual */}
      <Card className="p-4">
        {/* Mobile-First Header Layout */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4">
          <div className="flex items-center gap-2 flex-wrap">
            <FaQuestionCircle className="w-4 h-4 text-sehatti-gold-600" />
            <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              Answer Options
            </h4>
            <Badge variant="destructive" size="sm" className="px-2.5 py-1">
              Min 2 Required
            </Badge>
          </div>
          <Button
            type="button"
            variant="outline"
            size="md"
            onClick={addOption}
            className="flex items-center gap-2 w-full sm:w-auto px-4 py-2.5 text-sm font-medium"
          >
            <FaPlus className="w-3.5 h-3.5" />
            Add Option
          </Button>
        </div>
        
        <div className="space-y-4 sm:space-y-6">
          {formData.options.map((option, index) => (
            <div key={index} className="border-2 border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-xl p-3 sm:p-4 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800">
              {/* Mobile-First Option Header */}
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <h5 className="font-medium text-sm sm:text-base text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                  Option {index + 1}
                </h5>
                {formData.options.length > 2 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeOption(index)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 p-2 min-h-[36px] min-w-[36px]"
                  >
                    <FaTrash className="w-3.5 h-3.5" />
                  </Button>
                )}
              </div>
              
              {/* Simple Create Mode Layout */}
              {!isEdit ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {/* Option Text */}
                  <div className="space-y-2">
                    <label className="block text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      Option Text *
                    </label>
                    <Input
                      value={option.text.en}
                      onChange={(e) => updateOptionText(index, 'en', e.target.value)}
                      placeholder={`Option ${index + 1} text`}
                      className="min-h-[44px] sm:min-h-[40px]"
                      required
                    />
                  </div>
                  
                  {/* Value */}
                  <div className="space-y-2">
                    <label className="block text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      Analytics Value *
                    </label>
                    <Input
                      type="number"
                      value={option.value || ''}
                      onChange={(e) => {
                        const val = e.target.value;
                        const numVal = val === '' ? 1 : parseInt(val) || 1;
                        updateOptionValue(index, numVal);
                      }}
                      min={1}
                      className="text-center min-h-[44px] sm:min-h-[40px]"
                      placeholder="1"
                      required
                    />
                  </div>
                </div>
              ) : (
                /* Multilingual Edit Mode Layout */
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {/* English Option */}
                  <div className="space-y-2">
                    <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      <span className="text-base">🇺🇸</span>
                      <span>English *</span>
                    </label>
                    <Input
                      value={option.text.en}
                      onChange={(e) => updateOptionText(index, 'en', e.target.value)}
                      placeholder={`Option ${index + 1} in English`}
                      className="min-h-[44px] sm:min-h-[40px]"
                      required
                    />
                  </div>
                  
                  {/* Arabic Option */}
                  <div className="space-y-2">
                    <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      <span className="text-base">🇸🇦</span>
                      <span>Arabic</span>
                    </label>
                    <Input
                      value={option.text.ar}
                      onChange={(e) => updateOptionText(index, 'ar', e.target.value)}
                      placeholder="خيار باللغة العربية"
                      dir="rtl"
                      className="min-h-[44px] sm:min-h-[40px]"
                    />
                  </div>
                  
                  {/* Hindi Option */}
                  <div className="space-y-2">
                    <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      <span className="text-base">🇮🇳</span>
                      <span>Hindi</span>
                    </label>
                    <Input
                      value={option.text.hi}
                      onChange={(e) => updateOptionText(index, 'hi', e.target.value)}
                      placeholder="विकल्प हिंदी में"
                      className="min-h-[44px] sm:min-h-[40px]"
                    />
                  </div>
                  
                  {/* Value */}
                  <div className="space-y-2">
                    <label className="block text-xs sm:text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      Analytics Value *
                    </label>
                    <Input
                      type="number"
                      value={option.value || ''}
                      onChange={(e) => {
                        const val = e.target.value;
                        const numVal = val === '' ? 1 : parseInt(val) || 1;
                        updateOptionValue(index, numVal);
                      }}
                      min={1}
                      className="text-center min-h-[44px] sm:min-h-[40px]"
                      placeholder="1"
                      required
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {errors.options && (
          <p className="text-sm text-red-500 mt-2">{errors.options}</p>
        )}
        
        {!isEdit && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800/30 rounded-lg">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              <strong>💡 Note:</strong> Arabic and Hindi translations will be generated automatically. You can edit them later.
            </p>
          </div>
        )}
      </Card>

      {/* Corporate Assignment */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FaBuilding className="w-4 h-4 text-sehatti-gold-600" />
          <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            Corporate Assignment
          </h4>
          <Badge variant="outline" size="sm">Optional</Badge>
        </div>
        
        <CorporateSearchDropdown
          selectedCorporateIds={formData.corporate_ids}
          onSelectionChange={(corporateIds) => handleInputChange('corporate_ids', corporateIds)}
        />
        
        <div className="mt-3 p-3 bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 border border-sehatti-gold-200 dark:border-sehatti-gold-800/30 rounded-lg">
          <p className="text-xs text-sehatti-gold-700 dark:text-sehatti-gold-400">
            <strong>Corporate Assignment:</strong> Select specific corporates that should receive this question. 
            Leave empty to make this question available to all corporates.
          </p>
        </div>
      </Card>



    </form>
  );
};

export default QAQuestionForm; 