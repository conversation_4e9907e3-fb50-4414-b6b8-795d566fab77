import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaSave,
  FaTimes,
  FaUpload,
  FaImage,
  FaSpinner,
  FaCheck,
  FaExclamationTriangle,
  FaArrowLeft,
  FaTags,
  FaGlobe,
  FaLanguage,
  FaPlus
} from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { 
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Input,
  Select,
  Badge,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  Alert,
  FileUpload,
  Spinner
} from '@/components/ui';
import { 
  HubVisibility,
  HubStatus,
  Language
} from '@/types/hub';
import type { 
  HubResponse,
  HubUpdateRequest
} from '@/types/hub';
import { hubService } from '@/services/hubService';

interface EditHubProps {
  hubId: string;
  onSuccess?: (hub: HubResponse) => void;
  onCancel?: () => void;
}

interface FormData {
  title: string;
  description: string;
  tags: string[];
  category?: string;
  visibility: HubVisibility;
  status: HubStatus;
  image?: File;
  currentImageUrl?: string;
}

interface ValidationErrors {
  title?: string;
  description?: string;
  tags?: string;
  category?: string;
}

const CATEGORIES = [
  'Technology',
  'Business',
  'Education',
  'Health',
  'Entertainment',
  'News',
  'Sports',
  'Travel',
  'Food',
  'Lifestyle',
  'Science',
  'Arts',
  'Finance',
  'Marketing'
];

const VISIBILITY_OPTIONS = [
  { value: HubVisibility.PUBLIC, label: 'Public', description: 'Visible to everyone' },
  { value: HubVisibility.PRIVATE, label: 'Private', description: 'Only visible to organization members' },
  { value: HubVisibility.RESTRICTED, label: 'Restricted', description: 'Only visible to specific users' }
];

const STATUS_OPTIONS = [
  { value: HubStatus.ACTIVE, label: 'Active', description: 'Hub is live and accessible' },
  { value: HubStatus.INACTIVE, label: 'Inactive', description: 'Hub is hidden but not deleted' },
  { value: HubStatus.DRAFT, label: 'Draft', description: 'Hub is being prepared' },
  { value: HubStatus.ARCHIVED, label: 'Archived', description: 'Hub is archived for reference' }
];

export const EditHub: React.FC<EditHubProps> = ({
  hubId,
  onSuccess,
  onCancel
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const tagInputRef = useRef<HTMLInputElement>(null);
  
  const [hub, setHub] = useState<HubResponse | null>(null);
  const [isLoadingHub, setIsLoadingHub] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    tags: [],
    category: '',
    visibility: HubVisibility.PUBLIC,
    status: HubStatus.ACTIVE,
    image: undefined,
    currentImageUrl: undefined
  });

  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [removeCurrentImage, setRemoveCurrentImage] = useState(false);

  // Load hub data
  const loadHub = useCallback(async () => {
    try {
      setIsLoadingHub(true);
      setLoadError(null);
      const response = await hubService.getHub(hubId);
      const hubData = response.data;
      setHub(hubData);

      // Get English content for form initialization
      const englishContent = hubData.content?.[Language.ENGLISH];
      
      setFormData({
        title: englishContent?.title || '',
        description: englishContent?.description || '',
        tags: hubData.tags || [],
        category: hubData.category,
        visibility: hubData.visibility,
        status: hubData.status,
        image: undefined,
        currentImageUrl: hubData.image_url
      });
    } catch (err: any) {
      setLoadError(err.message || 'Failed to load hub');
      toast.error('Failed to load hub details');
    } finally {
      setIsLoadingHub(false);
    }
  }, [hubId]);

  // Load hub on mount
  useEffect(() => {
    loadHub();
  }, [loadHub]);

  // Form validation
  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    } else if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (formData.tags.length === 0) {
      newErrors.tags = 'At least one tag is required';
    } else if (formData.tags.length > 10) {
      newErrors.tags = 'Maximum 10 tags allowed';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle input changes
  const handleInputChange = useCallback((field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
    // Clear error when user starts typing
    if (errors[field as keyof ValidationErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  // Handle tag operations
  const addTag = useCallback(() => {
    const tag = newTag.trim().toLowerCase();
    
    if (!tag) {
      toast.error('Please enter a tag');
      return;
    }

    if (formData.tags.includes(tag)) {
      toast.error('Tag already exists');
      return;
    }

    if (formData.tags.length >= 10) {
      toast.error('Maximum 10 tags allowed');
      return;
    }

    handleInputChange('tags', [...formData.tags, tag]);
    setNewTag('');
    tagInputRef.current?.focus();
  }, [newTag, formData.tags, handleInputChange]);

  const removeTag = useCallback((tagToRemove: string) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove));
  }, [formData.tags, handleInputChange]);

  const handleTagKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  }, [addTag]);

  // Handle file selection
  const handleFileSelect = useCallback((files: File[]) => {
    const file = files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file');
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size must be less than 5MB');
        return;
      }
      
      handleInputChange('image', file);
      setRemoveCurrentImage(false);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      
      toast.success('New image selected');
    }
  }, [handleInputChange]);

  // Remove image
  const removeImage = useCallback(() => {
    handleInputChange('image', undefined);
    setImagePreview(null);
    setRemoveCurrentImage(true);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleInputChange]);

  // Form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix all validation errors');
      return;
    }

    setIsSubmitting(true);
    const loadingToast = toast.loading('Updating hub...');

    try {
      const updateData: HubUpdateRequest = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        tags: formData.tags,
        category: formData.category,
        visibility: formData.visibility,
        status: formData.status
      };

      // Handle image updates
      if (formData.image) {
        updateData.image = formData.image;
      } else if (removeCurrentImage) {
        updateData.removeImage = true;
      }

      const result = await hubService.updateHub(hubId, updateData);
      toast.success('Hub updated successfully!', { id: loadingToast });

      if (onSuccess) {
        onSuccess(result.data);
      } else {
        navigate('/media-management');
      }
      
    } catch (error: any) {
      console.error('Failed to update hub:', error);
      toast.error(error.message || 'Failed to update hub. Please try again.', { id: loadingToast });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, hubId, removeCurrentImage, onSuccess, navigate]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    if (isDirty) {
      setShowCancelDialog(true);
    } else {
      if (onCancel) {
        onCancel();
      } else {
        navigate('/media-management');
      }
    }
  }, [isDirty, onCancel, navigate]);

  // Confirm cancel
  const confirmCancel = useCallback(() => {
    setShowCancelDialog(false);
    if (onCancel) {
      onCancel();
    } else {
      navigate('/media-management');
    }
  }, [onCancel, navigate]);

  if (isLoadingHub) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card variant="glass" className="shadow-xl">
          <CardContent className="p-12 text-center">
            <Spinner size="lg" />
            <p className="mt-4 text-sehatti-warm-gray-600">Loading hub details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loadError || !hub) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card variant="glass" className="shadow-xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <FaExclamationTriangle className="h-4 w-4" />
              <div>
                <h4 className="font-medium">Error loading hub</h4>
                <p className="text-sm mt-1">{loadError || 'Hub not found'}</p>
                <div className="flex space-x-2 mt-3">
                  <Button 
                    onClick={loadHub} 
                    variant="outline" 
                    size="sm"
                    leftIcon={<FaSpinner />}
                  >
                    Retry
                  </Button>
                  <Button 
                    onClick={handleCancel} 
                    variant="outline" 
                    size="sm"
                    leftIcon={<FaArrowLeft />}
                  >
                    Go Back
                  </Button>
                </div>
              </div>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card variant="glass" className="shadow-xl">
        {/* Header */}
        <CardHeader className="bg-gradient-to-r from-sehatti-gold-50 to-sehatti-gold-100/50 border-b border-sehatti-gold-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 rounded-lg bg-sehatti-gold-200 flex items-center justify-center">
                <FaGlobe className="h-5 w-5 text-sehatti-gold-700" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold text-sehatti-gold-900">
                  Edit Hub
                </CardTitle>
                <p className="text-sm text-sehatti-gold-700">
                  Edit multilingual content hub with language-specific updates
                </p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              onClick={handleCancel}
              className="text-sehatti-gold-700 hover:text-sehatti-gold-900"
            >
              <FaArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-sehatti-warm-gray-900">Basic Information</h3>
              
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Hub Title *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter hub title..."
                  error={errors.title}
                  maxLength={100}
                />
                <p className="text-xs text-sehatti-warm-gray-500 mt-1">
                  {formData.title.length}/100 characters
                </p>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter hub description..."
                  rows={4}
                  maxLength={500}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-sehatti-gold-500 focus:border-sehatti-gold-500 ${
                    errors.description 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-sehatti-warm-gray-300'
                  }`}
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description}</p>
                )}
                <p className="text-xs text-sehatti-warm-gray-500 mt-1">
                  {formData.description.length}/500 characters
                </p>
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Category
                </label>
                <Select
                  value={formData.category || ''}
                  onChange={(e) => handleInputChange('category', e.target.value || undefined)}
                >
                  <option value="">Select a category (optional)</option>
                  {CATEGORIES.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            {/* Status & Visibility */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-sehatti-warm-gray-900">Status & Visibility</h3>
              
              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Status
                </label>
                <div className="space-y-2">
                  {STATUS_OPTIONS.map(option => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value={option.value}
                        checked={formData.status === option.value}
                        onChange={(e) => handleInputChange('status', e.target.value as HubStatus)}
                        className="mt-1 h-4 w-4 text-sehatti-gold-600 focus:ring-sehatti-gold-500 border-sehatti-warm-gray-300"
                      />
                      <div>
                        <div className="text-sm font-medium text-sehatti-warm-gray-900">
                          {option.label}
                        </div>
                        <div className="text-xs text-sehatti-warm-gray-500">
                          {option.description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Visibility */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Visibility
                </label>
                <div className="space-y-2">
                  {VISIBILITY_OPTIONS.map(option => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="visibility"
                        value={option.value}
                        checked={formData.visibility === option.value}
                        onChange={(e) => handleInputChange('visibility', e.target.value as HubVisibility)}
                        className="mt-1 h-4 w-4 text-sehatti-gold-600 focus:ring-sehatti-gold-500 border-sehatti-warm-gray-300"
                      />
                      <div>
                        <div className="text-sm font-medium text-sehatti-warm-gray-900">
                          {option.label}
                        </div>
                        <div className="text-xs text-sehatti-warm-gray-500">
                          {option.description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-sehatti-warm-gray-900">Tags</h3>
              
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Add Tags * (Press Enter to add)
                </label>
                <div className="flex space-x-2">
                  <Input
                    ref={tagInputRef}
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleTagKeyPress}
                    placeholder="Enter a tag..."
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={addTag}
                    variant="outline"
                    disabled={!newTag.trim() || formData.tags.length >= 10}
                  >
                    <FaPlus className="h-4 w-4" />
                  </Button>
                </div>
                {errors.tags && (
                  <p className="text-sm text-red-600 mt-1">{errors.tags}</p>
                )}
              </div>

              {/* Tags Display */}
              {formData.tags.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                    Current Tags ({formData.tags.length}/10)
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center space-x-1 px-3 py-1"
                      >
                        <FaTags className="h-3 w-3" />
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-red-500 hover:text-red-700"
                        >
                          <FaTimes className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Image Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-sehatti-warm-gray-900">Hub Image</h3>
              
              {/* Current Image */}
              {formData.currentImageUrl && !removeCurrentImage && !formData.image && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-sehatti-warm-gray-700">
                      Current Image
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={removeImage}
                      className="text-red-600 hover:text-red-700"
                    >
                      <FaTimes className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                  <img
                    src={formData.currentImageUrl}
                    alt="Current hub"
                    className="h-32 w-full object-cover rounded-lg border border-sehatti-warm-gray-200"
                  />
                </div>
              )}

              {/* New Image Upload */}
              {(!formData.currentImageUrl || removeCurrentImage || formData.image) && (
                <div>
                  {!formData.image ? (
                    <div>
                      <FileUpload
                        onFilesSelected={handleFileSelect}
                        accept="image/*"
                        maxFiles={1}
                        maxSize={5 * 1024 * 1024} // 5MB
                      />
                      <p className="text-xs text-sehatti-warm-gray-500 mt-1">
                        Upload a new image for your hub (max 5MB, JPG/PNG)
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-sehatti-warm-gray-700">
                          New Image Selected
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={removeImage}
                          className="text-red-600 hover:text-red-700"
                        >
                          <FaTimes className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                      {imagePreview && (
                        <img
                          src={imagePreview}
                          alt="New hub preview"
                          className="h-32 w-full object-cover rounded-lg border border-sehatti-warm-gray-200"
                        />
                      )}
                      <p className="text-sm text-sehatti-warm-gray-600">
                        {formData.image.name} ({(formData.image.size / 1024 / 1024).toFixed(2)} MB)
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-sehatti-warm-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting}
                leftIcon={<FaSave />}
                className="min-w-[120px]"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Cancel Confirmation Dialog */}
      <Modal open={showCancelDialog} onClose={() => setShowCancelDialog(false)}>
        <ModalHeader>
          <h3 className="text-lg font-semibold">Discard Changes?</h3>
        </ModalHeader>
        <ModalContent>
          <p className="text-sehatti-warm-gray-600">
            You have unsaved changes. Are you sure you want to discard them and go back?
          </p>
        </ModalContent>
        <ModalFooter className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowCancelDialog(false)}
          >
            Keep Editing
          </Button>
          <Button
            variant="destructive"
            onClick={confirmCancel}
          >
            Discard Changes
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}; 