import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaEdit, 
  FaTrash, 
  FaGlobe, 
  FaEye, 
  FaSpinner,
  FaCheck,
  FaExclamationTriangle,
  FaTimes,
  FaSyncAlt,
  FaChartBar,
  FaCalendar,
  FaUser,
  FaTags,
  FaFolder,
  FaImage,
  FaArrowLeft,
  FaLanguage
} from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { 
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Badge,
  Modal,
  ModalHeader,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>ner,
  <PERSON>ert,
  Tabs,
  Select
} from '@/components/ui';
import { 
  Language, 
  HubStatus,
  HubVisibility
} from '@/types/hub';
import type { 
  HubResponse
} from '@/types/hub';
import { hubService } from '@/services/hubService';

interface HubDetailsProps {
  hubId: string;
  onEdit?: () => void;
  onDelete?: () => void;
  onBack?: () => void;
  showActions?: boolean;
}

const LANGUAGE_FLAGS = {
  [Language.ENGLISH]: '🇺🇸',
  [Language.ARABIC]: '🇸🇦',
  [Language.HINDI]: '🇮🇳'
};

const STATUS_COLORS = {
  [HubStatus.ACTIVE]: 'success',
  [HubStatus.INACTIVE]: 'secondary',
  [HubStatus.DRAFT]: 'warning',
  [HubStatus.ARCHIVED]: 'destructive'
} as const;

const VISIBILITY_LABELS = {
  [HubVisibility.PUBLIC]: 'Public',
  [HubVisibility.PRIVATE]: 'Private',
  [HubVisibility.RESTRICTED]: 'Restricted'
};

export const HubDetails: React.FC<HubDetailsProps> = ({
  hubId,
  onEdit,
  onDelete,
  onBack,
  showActions = true
}) => {
  const navigate = useNavigate();
  
  const [hub, setHub] = useState<HubResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(Language.ENGLISH);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRetranslating, setIsRetranslating] = useState(false);

  // Load hub details
  const loadHub = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await hubService.getHub(hubId);
      setHub(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to load hub details');
      toast.error('Failed to load hub details');
    } finally {
      setIsLoading(false);
    }
  }, [hubId]);

  // Load hub on mount
  useEffect(() => {
    loadHub();
  }, [loadHub]);

  // Handle delete
  const handleDelete = useCallback(async () => {
    setIsDeleting(true);
    const loadingToast = toast.loading('Deleting hub...');
    
    try {
      await hubService.deleteHub(hubId);
      toast.success('Hub deleted successfully', { id: loadingToast });
      setShowDeleteDialog(false);
      
      if (onDelete) {
        onDelete();
      } else {
        navigate('/media-management');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete hub', { id: loadingToast });
    } finally {
      setIsDeleting(false);
    }
  }, [hubId, onDelete, navigate]);

  // Handle retranslate (placeholder for future implementation)
  const handleRetranslate = useCallback(async () => {
    setIsRetranslating(true);
    const loadingToast = toast.loading('Retranslating hub content...');
    
    try {
      // This would call a retranslation API endpoint when available
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      toast.success('Hub content retranslated successfully', { id: loadingToast });
      await loadHub(); // Reload hub data
    } catch (error: any) {
      toast.error('Failed to retranslate hub content', { id: loadingToast });
    } finally {
      setIsRetranslating(false);
    }
  }, [loadHub]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    if (onBack) {
      onBack();
    } else {
      navigate('/media-management');
    }
  }, [onBack, navigate]);

  // Handle edit
  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit();
    } else {
      navigate(`/media-management?edit=${hubId}`);
    }
  }, [onEdit, hubId, navigate]);

  // Get content for selected language
  const getLanguageContent = useCallback((language: Language) => {
    if (!hub?.content) return null;
    return hub.content[language] || null;
  }, [hub]);

  // Get available languages
  const availableLanguages = useMemo(() => {
    if (!hub?.content) return [];
    return Object.keys(hub.content) as Language[];
  }, [hub]);

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <Card variant="glass" className="shadow-xl">
          <CardContent className="p-12 text-center">
            <Spinner size="lg" />
            <p className="mt-4 text-sehatti-warm-gray-600">Loading hub details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !hub) {
    return (
      <div className="max-w-6xl mx-auto">
        <Card variant="glass" className="shadow-xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <FaExclamationTriangle className="h-4 w-4" />
              <div>
                <h4 className="font-medium">Error loading hub</h4>
                <p className="text-sm mt-1">{error || 'Hub not found'}</p>
                <div className="flex space-x-2 mt-3">
                  <Button 
                    onClick={loadHub} 
                    variant="outline" 
                    size="sm"
                    leftIcon={<FaSyncAlt />}
                  >
                    Retry
                  </Button>
                  <Button 
                    onClick={handleBack} 
                    variant="outline" 
                    size="sm"
                    leftIcon={<FaArrowLeft />}
                  >
                    Go Back
                  </Button>
                </div>
              </div>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentContent = getLanguageContent(selectedLanguage);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card variant="glass" className="shadow-xl">
        <CardHeader className="bg-gradient-to-r from-sehatti-gold-50 to-sehatti-gold-100/50 border-b border-sehatti-gold-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={handleBack}
                className="text-sehatti-gold-700 hover:text-sehatti-gold-900"
              >
                <FaArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              
              <div className="flex items-center space-x-3">
                <div className="h-12 w-12 rounded-lg bg-sehatti-gold-200 flex items-center justify-center">
                  <FaGlobe className="h-6 w-6 text-sehatti-gold-700" />
                </div>
                <div>
                  <CardTitle className="text-xl font-semibold text-sehatti-gold-900">
                    Hub Details
                  </CardTitle>
                  <p className="text-sm text-sehatti-gold-700">
                    View and manage multilingual hub content
                  </p>
                </div>
              </div>
            </div>

            {showActions && (
              <div className="flex space-x-3">
                <Button
                  onClick={handleEdit}
                  variant="outline"
                  leftIcon={<FaEdit />}
                >
                  Edit Hub
                </Button>
                
                <Button
                  onClick={handleRetranslate}
                  variant="outline"
                  isLoading={isRetranslating}
                  leftIcon={<FaGlobe />}
                >
                  Retranslate
                </Button>
                
                <Button
                  onClick={() => setShowDeleteDialog(true)}
                  variant="destructive"
                  leftIcon={<FaTrash />}
                >
                  Delete Hub
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Hub Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Language Selector */}
          <Card variant="glass">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <FaLanguage className="h-5 w-5 text-sehatti-gold-600" />
                  <span>Content Language</span>
                </CardTitle>
                <Select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value as Language)}
                >
                  {availableLanguages.map(language => (
                    <option key={language} value={language}>
                      {LANGUAGE_FLAGS[language]} {language}
                    </option>
                  ))}
                </Select>
              </div>
            </CardHeader>
          </Card>

          {/* Content Display */}
          <Card variant="glass">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>{LANGUAGE_FLAGS[selectedLanguage]}</span>
                <span>{selectedLanguage} Content</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {currentContent ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-2">
                      {currentContent.title}
                    </h3>
                    <p className="text-sehatti-warm-gray-600 leading-relaxed">
                      {currentContent.description}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FaLanguage className="h-12 w-12 text-sehatti-warm-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-sehatti-warm-gray-900 mb-2">
                    No content available
                  </h3>
                  <p className="text-sehatti-warm-gray-600">
                    Content for {selectedLanguage} is not available yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Hub Image */}
          {hub.image_url && (
            <Card variant="glass">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FaImage className="h-5 w-5 text-sehatti-gold-600" />
                  <span>Hub Image</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <img
                  src={hub.image_url}
                  alt="Hub"
                  className="w-full h-64 object-cover rounded-lg border border-sehatti-warm-gray-200"
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Visibility */}
          <Card variant="glass">
            <CardHeader>
              <CardTitle>Status & Visibility</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                  Status
                </label>
                <Badge variant={STATUS_COLORS[hub.status] as any}>
                  {hub.status}
                </Badge>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                  Visibility
                </label>
                <Badge variant="outline">
                  {VISIBILITY_LABELS[hub.visibility]}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Hub Information */}
          <Card variant="glass">
            <CardHeader>
              <CardTitle>Hub Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2 text-sm">
                <FaCalendar className="h-4 w-4 text-sehatti-warm-gray-500" />
                <span className="text-sehatti-warm-gray-600">
                  Created: {new Date(hub.created_at).toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center space-x-2 text-sm">
                <FaCalendar className="h-4 w-4 text-sehatti-warm-gray-500" />
                <span className="text-sehatti-warm-gray-600">
                  Updated: {new Date(hub.updated_at).toLocaleDateString()}
                </span>
              </div>
              
              {hub.category && (
                <div className="flex items-center space-x-2 text-sm">
                  <FaFolder className="h-4 w-4 text-sehatti-warm-gray-500" />
                  <span className="text-sehatti-warm-gray-600">
                    Category: {hub.category}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tags */}
          {hub.tags && hub.tags.length > 0 && (
            <Card variant="glass">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FaTags className="h-5 w-5 text-sehatti-gold-600" />
                  <span>Tags</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {hub.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Languages */}
          <Card variant="glass">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FaGlobe className="h-5 w-5 text-sehatti-gold-600" />
                <span>Available Languages</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {availableLanguages.map(language => (
                  <div key={language} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{LANGUAGE_FLAGS[language]}</span>
                      <span className="text-sm font-medium">{language}</span>
                    </div>
                    <Badge 
                      variant={getLanguageContent(language) ? 'success' : 'secondary'}
                      className="text-xs"
                    >
                      {getLanguageContent(language) ? 'Complete' : 'Missing'}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card variant="glass">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FaChartBar className="h-5 w-5 text-sehatti-gold-600" />
                <span>Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-sehatti-warm-gray-600">Content Items:</span>
                <span className="text-sm font-medium">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-sehatti-warm-gray-600">Views:</span>
                <span className="text-sm font-medium">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-sehatti-warm-gray-600">Languages:</span>
                <span className="text-sm font-medium">{availableLanguages.length}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Modal open={showDeleteDialog} onClose={() => setShowDeleteDialog(false)}>
        <ModalHeader>
          <h3 className="text-lg font-semibold text-red-900">Delete Hub</h3>
        </ModalHeader>
        <ModalContent>
          <div className="space-y-3">
            <p className="text-sehatti-warm-gray-600">
              Are you sure you want to delete this hub? This action cannot be undone.
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <FaExclamationTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-red-900">
                    {currentContent?.title || hub.hub_id}
                  </h4>
                  <p className="text-sm text-red-700">
                    All content and translations will be permanently deleted.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </ModalContent>
        <ModalFooter className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowDeleteDialog(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            isLoading={isDeleting}
          >
            Delete Hub
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}; 