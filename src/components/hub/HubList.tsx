import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaSearch, 
  FaPlus, 
  FaEye, 
  FaEdit, 
  FaTrash, 
  FaFilter,
  FaCheckCircle,
  FaTimesCircle,
  FaLanguage,
  FaEllipsisV,
  FaDownload,
  FaUpload,
  FaSync
} from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { 
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Input,
  SearchInput,
  Badge,
  Switch,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Checkbox,
  Select,
  Spinner,
  EmptyState,
  Alert
} from '@/components/ui';
import { 
  HubStatus, 
  Language
} from '@/types/hub';
import type { 
  HubListParams,
  HubListItem
} from '@/types/hub';
import { hubService } from '@/services/hubService';

interface HubListProps {
  onCreateClick?: () => void;
  onEditClick?: (hubId: string) => void;
  onViewClick?: (hubId: string) => void;
  showActions?: boolean;
  showSearch?: boolean;
  showFilters?: boolean;
  defaultParams?: Partial<HubListParams>;
}

interface FilterState {
  status?: HubStatus;
  category?: string;
  created_by?: string;
  language: Language;
}

interface BulkAction {
  type: 'delete' | 'activate' | 'deactivate' | 'archive';
  label: string;
  icon: React.ReactNode;
  confirmText: string;
  requiresConfirmation: boolean;
}

const STATUS_OPTIONS = [
  { value: HubStatus.ACTIVE, label: 'Active', color: 'success' },
  { value: HubStatus.INACTIVE, label: 'Inactive', color: 'secondary' },
  { value: HubStatus.DRAFT, label: 'Draft', color: 'warning' },
  { value: HubStatus.ARCHIVED, label: 'Archived', color: 'destructive' }
] as const;

const LANGUAGE_FLAGS = {
  [Language.ENGLISH]: '🇺🇸',
  [Language.ARABIC]: '🇸🇦',
  [Language.HINDI]: '🇮🇳'
};

const BULK_ACTIONS: BulkAction[] = [
  {
    type: 'activate',
    label: 'Activate Selected',
    icon: <FaCheckCircle className="h-4 w-4" />,
    confirmText: 'Are you sure you want to activate the selected hubs?',
    requiresConfirmation: false
  },
  {
    type: 'deactivate',
    label: 'Deactivate Selected',
    icon: <FaTimesCircle className="h-4 w-4" />,
    confirmText: 'Are you sure you want to deactivate the selected hubs?',
    requiresConfirmation: false
  },
  {
    type: 'archive',
    label: 'Archive Selected',
    icon: <FaDownload className="h-4 w-4" />,
    confirmText: 'Are you sure you want to archive the selected hubs?',
    requiresConfirmation: true
  },
  {
    type: 'delete',
    label: 'Delete Selected',
    icon: <FaTrash className="h-4 w-4" />,
    confirmText: 'Are you sure you want to permanently delete the selected hubs? This action cannot be undone.',
    requiresConfirmation: true
  }
];

export const HubList: React.FC<HubListProps> = ({
  onCreateClick,
  onEditClick,
  onViewClick,
  showActions = true,
  showSearch = true,
  showFilters = true,
  defaultParams = {}
}) => {
  const navigate = useNavigate();
  const loadingRef = useRef(false);
  
  // State management
  const [hubs, setHubs] = useState<HubListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [searchResults, setSearchResults] = useState<HubListItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    language: Language.ENGLISH
  });
  
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [selectedHubs, setSelectedHubs] = useState<string[]>([]);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    type?: 'danger' | 'warning' | 'info';
    isLoading?: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {}
  });

  // Load hubs
  const loadHubs = useCallback(async () => {
    if (loadingRef.current) return; // Prevent multiple simultaneous calls
    
    try {
      loadingRef.current = true;
      setIsLoading(true);
      setError(null);
      const response = await hubService.listHubs({
        ...defaultParams,
        status: filters.status,
        category: filters.category,
        created_by: filters.created_by
      });
      setHubs(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to load hubs');
      toast.error('Failed to load hubs');
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, []); // Empty dependency array to prevent recreation

  // Load hubs on mount only
  useEffect(() => {
    loadHubs();
  }, []);

  // Load hubs when filters change
  useEffect(() => {
    if (filters.status !== undefined || filters.category !== undefined || filters.created_by !== undefined) {
      loadHubs();
    }
  }, [filters.status, filters.category, filters.created_by]);

  // Helper function to get display content for a hub
  const getHubDisplayContent = useCallback((hub: HubListItem, language: Language = filters.language) => {
    const content = hub.content[language] || hub.content[Language.ENGLISH] || Object.values(hub.content)[0];
    return {
      title: content?.title || 'Untitled Hub',
      description: content?.description || ''
    };
  }, [filters.language]);

  // Handle search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setIsSearchMode(false);
      setSearchResults([]);
      return;
    }

    const delayedSearch = setTimeout(() => {
      setIsSearching(true);
      try {
        const filtered = hubs.filter(hub => {
          const content = hub.content[filters.language] || hub.content[Language.ENGLISH] || Object.values(hub.content)[0];
          const title = content?.title || 'Untitled Hub';
          const description = content?.description || '';
          return title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                 description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                 hub.category?.toLowerCase().includes(searchQuery.toLowerCase());
        });
        setSearchResults(filtered);
        setIsSearchMode(true);
      } catch (err: any) {
        toast.error('Search failed');
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, hubs, filters.language]);

  // Handle hub deletion
  const handleDelete = useCallback(async (hubId: string, hubTitle: string) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Hub',
      message: `Are you sure you want to delete "${hubTitle}"? This action cannot be undone.`,
      type: 'danger',
      onConfirm: async () => {
        const loadingToast = toast.loading('Deleting hub...');
        
        try {
          await hubService.deleteHub(hubId);
          toast.success('Hub deleted successfully', { id: loadingToast });
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
          setSelectedHubs(prev => prev.filter(id => id !== hubId));
          loadHubs(); // Refresh the list
        } catch (error: any) {
          toast.error(error.message || 'Failed to delete hub', { id: loadingToast });
        }
      }
    });
  }, []); // Remove loadHubs dependency

  // Handle status toggle
  const handleStatusToggle = useCallback(async (hubId: string, currentStatus: HubStatus) => {
    const newStatus = currentStatus === HubStatus.ACTIVE 
      ? HubStatus.INACTIVE 
      : HubStatus.ACTIVE;
    
    const loadingToast = toast.loading(`${newStatus === HubStatus.ACTIVE ? 'Activating' : 'Deactivating'} hub...`);
    
    try {
      await hubService.updateHub(hubId, { 
        language: filters.language,
        status: newStatus 
      });
      toast.success(`Hub ${newStatus === HubStatus.ACTIVE ? 'activated' : 'deactivated'} successfully`, { id: loadingToast });
      loadHubs(); // Refresh the list
    } catch (error: any) {
      toast.error(error.message || 'Failed to update hub status', { id: loadingToast });
    }
  }, [filters.language]); // Keep only filters.language dependency

  // Handle bulk actions
  const handleBulkAction = useCallback((action: BulkAction) => {
    if (selectedHubs.length === 0) {
      toast.error('Please select at least one hub');
      return;
    }

    const executeAction = async () => {
      const count = selectedHubs.length;
      const loadingToast = toast.loading(`${action.label}...`);
      
      try {
        switch (action.type) {
          case 'delete':
            await Promise.all(selectedHubs.map(id => hubService.deleteHub(id)));
            break;
          case 'activate':
            await Promise.all(selectedHubs.map(id => hubService.updateHub(id, { 
              language: filters.language,
              status: HubStatus.ACTIVE 
            })));
            break;
          case 'deactivate':
            await Promise.all(selectedHubs.map(id => hubService.updateHub(id, { 
              language: filters.language,
              status: HubStatus.INACTIVE 
            })));
            break;
          case 'archive':
            await Promise.all(selectedHubs.map(id => hubService.updateHub(id, { 
              language: filters.language,
              status: HubStatus.ARCHIVED 
            })));
            break;
        }
        
        toast.success(`${action.label} completed successfully for ${count} hub${count > 1 ? 's' : ''}`, { id: loadingToast });
        setSelectedHubs([]);
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        loadHubs(); // Refresh the list
      } catch (error: any) {
        toast.error(`${action.label} failed: ${error.message}`, { id: loadingToast });
      }
    };

    if (action.requiresConfirmation) {
      setConfirmDialog({
        isOpen: true,
        title: action.label,
        message: `${action.confirmText} (${selectedHubs.length} hub${selectedHubs.length > 1 ? 's' : ''})`,
        type: action.type === 'delete' ? 'danger' : 'warning',
        onConfirm: executeAction
      });
    } else {
      executeAction();
    }
  }, [selectedHubs, filters.language]); // Remove loadHubs dependency

  // Get status badge
  const getStatusBadge = (status: HubStatus) => {
    const statusInfo = STATUS_OPTIONS.find(s => s.value === status) || STATUS_OPTIONS[0];
    return (
      <Badge variant={statusInfo.color as any}>
        {status === HubStatus.ACTIVE ? (
          <FaCheckCircle className="mr-1 h-3 w-3" />
        ) : (
          <FaTimesCircle className="mr-1 h-3 w-3" />
        )}
        {statusInfo.label}
      </Badge>
    );
  };

  // Get data to display (search results or regular list)
  const dataToDisplay = isSearchMode ? searchResults : hubs;
  const displayLoading = isSearchMode ? isSearching : isLoading;
  const isAllSelected = selectedHubs.length > 0 && selectedHubs.length === dataToDisplay.length;
  const isSomeSelected = selectedHubs.length > 0 && selectedHubs.length < dataToDisplay.length;

  // Handle select all
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedHubs([]);
    } else {
      setSelectedHubs(dataToDisplay.map(hub => hub.hub_id));
    }
  };

  // Handle individual selection
  const handleSelectHub = (hubId: string) => {
    setSelectedHubs(prev => 
      prev.includes(hubId) 
        ? prev.filter(id => id !== hubId)
        : [...prev, hubId]
    );
  };

  return (
    <div className="space-y-6">
      {/* Modern Header Section */}
      <div className="bg-gradient-to-r from-sehatti-gold-50 to-white rounded-2xl p-6 border border-sehatti-gold-200/50 shadow-sm">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          {/* Title and Stats */}
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-sehatti-gold-100 rounded-lg">
                <FaLanguage className="h-6 w-6 text-sehatti-gold-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-sehatti-warm-gray-900">
                  Content Hubs
                </h2>
                <p className="text-sehatti-warm-gray-600">
                  {isSearchMode 
                    ? `${dataToDisplay.length} results for "${searchQuery}"`
                    : `${dataToDisplay.length} total hubs`}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {onCreateClick && (
              <Button
                onClick={onCreateClick}
                className="bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 hover:from-sehatti-gold-600 hover:to-sehatti-gold-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                leftIcon={<FaPlus />}
              >
                Create New Hub
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        {(showSearch || showFilters) && (
          <div className="mt-6 flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            {showSearch && (
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-sehatti-warm-gray-400" />
                </div>
                <Input
                  type="text"
                  placeholder="Search hubs by title, description, or category..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white border-sehatti-warm-gray-200 focus:border-sehatti-gold-400 focus:ring-sehatti-gold-400/20"
                />
              </div>
            )}

            {/* Filters */}
            {showFilters && (
              <div className="flex items-center gap-3">
                <Select
                  value={filters.status || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as HubStatus || undefined }))}
                  className="min-w-[120px]"
                >
                  <option value="">All Status</option>
                  {STATUS_OPTIONS.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </Select>
                
                <Select
                  value={filters.language}
                  onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value as Language }))}
                  className="min-w-[100px]"
                >
                  {Object.entries(LANGUAGE_FLAGS).map(([lang, flag]) => (
                    <option key={lang} value={lang}>
                      {flag} {lang}
                    </option>
                  ))}
                </Select>
              </div>
            )}
          </div>
        )}

        {/* Bulk Actions */}
        {selectedHubs.length > 0 && (
          <div className="mt-4 p-4 bg-sehatti-gold-100/50 rounded-xl border border-sehatti-gold-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-sehatti-gold-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-medium">{selectedHubs.length}</span>
                  </div>
                  <span className="text-sm font-medium text-sehatti-warm-gray-900">
                    {selectedHubs.length} hub{selectedHubs.length > 1 ? 's' : ''} selected
                  </span>
                </div>
                <button
                  onClick={() => setSelectedHubs([])}
                  className="text-sm text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 underline"
                >
                  Clear selection
                </button>
              </div>
              
              <div className="flex items-center gap-2">
                {BULK_ACTIONS.map((action) => (
                  <Button
                    key={action.type}
                    onClick={() => handleBulkAction(action)}
                    variant={action.type === 'delete' ? 'destructive' : 'outline'}
                    size="sm"
                    leftIcon={action.icon}
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="space-y-4">
        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6">
              <Alert variant="destructive">
                <FaTimesCircle className="h-4 w-4" />
                <div>
                  <h4 className="font-medium">Error loading hubs</h4>
                  <p className="text-sm mt-1">{error}</p>
                  <Button 
                    onClick={loadHubs} 
                    variant="outline" 
                    size="sm" 
                    className="mt-3"
                    leftIcon={<FaSync />}
                  >
                    Retry
                  </Button>
                </div>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {displayLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-sehatti-warm-gray-200 rounded-lg"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-sehatti-warm-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-sehatti-warm-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-sehatti-warm-gray-200 rounded"></div>
                      <div className="h-3 bg-sehatti-warm-gray-200 rounded w-5/6"></div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="h-6 bg-sehatti-warm-gray-200 rounded w-16"></div>
                      <div className="h-8 bg-sehatti-warm-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!displayLoading && !error && dataToDisplay.length === 0 && (
          <Card className="border-dashed border-2 border-sehatti-warm-gray-200">
            <CardContent className="p-12 text-center">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-sehatti-gold-100 rounded-full flex items-center justify-center mx-auto">
                  <FaLanguage className="h-8 w-8 text-sehatti-gold-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-sehatti-warm-gray-900">
                    {isSearchMode ? "No hubs found" : "No hubs yet"}
                  </h3>
                  <p className="text-sehatti-warm-gray-600 mt-1">
                    {isSearchMode 
                      ? "Try adjusting your search criteria or filters"
                      : "Create your first content hub to get started"}
                  </p>
                </div>
                {!isSearchMode && onCreateClick && (
                  <Button 
                    onClick={onCreateClick} 
                    leftIcon={<FaPlus />}
                    className="bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 hover:from-sehatti-gold-600 hover:to-sehatti-gold-700 text-white"
                  >
                    Create Your First Hub
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Hubs Grid */}
        {!displayLoading && !error && dataToDisplay.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dataToDisplay.map((hub) => {
              const { title, description } = getHubDisplayContent(hub);
              const isSelected = selectedHubs.includes(hub.hub_id);
              
              return (
                <Card 
                  key={hub.hub_id}
                  className={`group relative overflow-hidden transition-all duration-200 hover:shadow-lg cursor-pointer ${
                    isSelected 
                      ? 'ring-2 ring-sehatti-gold-400 bg-sehatti-gold-50/50' 
                      : 'hover:shadow-md hover:-translate-y-1'
                  }`}
                  onClick={() => onViewClick?.(hub.hub_id)}
                >
                  {/* Selection Checkbox */}
                  <div 
                    className="absolute top-4 left-4 z-10"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleSelectHub(hub.hub_id)}
                      className="bg-white shadow-sm"
                    />
                  </div>

                  {/* Hub Image/Icon */}
                  <div className="relative h-32 bg-gradient-to-br from-sehatti-gold-100 to-sehatti-gold-200 flex items-center justify-center">
                    {hub.image_url ? (
                      <img 
                        src={hub.image_url} 
                        alt={title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <FaLanguage className="h-12 w-12 text-sehatti-gold-600" />
                    )}
                    
                    {/* Status Badge */}
                    <div className="absolute top-3 right-3">
                      {getStatusBadge(hub.status)}
                    </div>
                  </div>

                  <CardContent className="p-6">
                    {/* Hub Title and Description */}
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold text-lg text-sehatti-warm-gray-900 line-clamp-1">
                          {title}
                        </h3>
                        {description && (
                          <p className="text-sehatti-warm-gray-600 text-sm mt-1 line-clamp-2">
                            {description}
                          </p>
                        )}
                      </div>

                      {/* Category */}
                      {hub.category && (
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {hub.category}
                          </Badge>
                        </div>
                      )}

                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-sehatti-warm-gray-500">
                        <div className="flex items-center gap-4">
                          <span>{hub.content_count || 0} items</span>
                          <span>{new Date(hub.created_at).toLocaleDateString()}</span>
                        </div>
                        
                        {/* Languages */}
                        <div className="flex items-center gap-1">
                          {Object.keys(hub.content || {}).map((lang) => (
                            <span key={lang} className="text-sm" title={lang}>
                              {LANGUAGE_FLAGS[lang as Language] || '🏳️'}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    {showActions && (
                      <div className="mt-4 pt-4 border-t border-sehatti-warm-gray-100">
                        <div className="flex items-center justify-between">
                          {/* Status Toggle */}
                          <div 
                            className="flex items-center gap-2"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Switch
                              checked={hub.status === HubStatus.ACTIVE}
                              onChange={() => handleStatusToggle(hub.hub_id, hub.status)}
                              size="sm"
                            />
                            <span className="text-xs text-sehatti-warm-gray-500">
                              {hub.status === HubStatus.ACTIVE ? 'Active' : 'Inactive'}
                            </span>
                          </div>

                          {/* Action Menu */}
                          <div 
                            className="flex items-center gap-1"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onViewClick?.(hub.hub_id)}
                              className="p-2 h-8 w-8"
                            >
                              <FaEye className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onEditClick?.(hub.hub_id)}
                              className="p-2 h-8 w-8"
                            >
                              <FaEdit className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(hub.hub_id, title)}
                              className="p-2 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <FaTrash className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Confirmation Dialog */}
      <Modal open={confirmDialog.isOpen} onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <ModalHeader>
          <h3 className="text-lg font-semibold">{confirmDialog.title}</h3>
        </ModalHeader>
        <ModalContent>
          <p className="text-sehatti-warm-gray-600">{confirmDialog.message}</p>
        </ModalContent>
        <ModalFooter className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
          >
            Cancel
          </Button>
          <Button
            variant={confirmDialog.type === 'danger' ? 'destructive' : 'default'}
            onClick={confirmDialog.onConfirm}
            isLoading={confirmDialog.isLoading}
          >
            Confirm
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}; 