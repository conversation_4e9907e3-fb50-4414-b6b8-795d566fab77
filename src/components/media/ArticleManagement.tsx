import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { Select, SelectOption } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/Dialog';
import { Trash2, Edit, Eye, Plus, Search, FileText, Calendar, User, Globe } from 'lucide-react';

// Import the articles API
import { 
  useGetArticlesQuery,
  useCreateArticleMutation,
  useUpdateArticleMutation,
  useDeleteArticleMutation,
  usePublishArticleMutation,
  useRetranslateArticleMutation,
  useUploadArticleThumbnailMutation
} from '../../services/articlesApi';

// Import the media API for hubs
import { MediaAPI } from '../../services/mediaApi';

// Language options
const LANGUAGES = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'ar', label: 'العربية', flag: '🇸🇦' },
  { value: 'hi', label: 'हिंदी', flag: '🇮🇳' }
];

// Article categories
const ARTICLE_CATEGORIES = [
  'news',
  'announcement', 
  'guide',
  'tutorial',
  'policy',
  'update',
  'blog',
  'press_release',
  'case_study',
  'other'
];

// Status options
const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'published', label: 'Published', color: 'bg-green-100 text-green-800' },
  { value: 'archived', label: 'Archived', color: 'bg-red-100 text-red-800' }
];

interface ArticleFormData {
  title: string;
  description: string;
  content: string;
  source_division_id: string;
  target_id: string;
  category: string;
  tags: string[];
  author_id: string;
  status: string;
  thumbnail?: File;
}

const ArticleManagement: React.FC = () => {
  // State management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingArticle, setEditingArticle] = useState<any>(null);
  const [viewingArticle, setViewingArticle] = useState<any>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    description: '',
    content: '',
    source_division_id: '',
    target_id: '',
    category: '',
    tags: [],
    author_id: 'current-user',
    status: 'draft',
    thumbnail: undefined
  });

  // RTK Query hooks
  const { data: articlesData, isLoading, error, refetch } = useGetArticlesQuery({});
  const { data: hubsData } = MediaAPI.useGetHubsQuery({});
  const [createArticle] = useCreateArticleMutation();
  const [updateArticle] = useUpdateArticleMutation();
  const [deleteArticle] = useDeleteArticleMutation();
  const [publishArticle] = usePublishArticleMutation();

  // Extract data
  const articles = articlesData?.data || [];
  const hubs = hubsData?.data || [];
  const totalArticles = articlesData?.total || 0;

  // Filter articles based on search
  const filteredArticles = articles.filter((article: any) => {
    const content = article.content?.[selectedLanguage] || article.content?.en || {};
    const title = content.title || '';
    const description = content.description || '';
    const searchLower = searchTerm.toLowerCase();
    
    return title.toLowerCase().includes(searchLower) ||
           description.toLowerCase().includes(searchLower) ||
           (article.category && article.category.toLowerCase().includes(searchLower));
  });

  // Helper functions
  const getHubName = (hubId: string) => {
    const hub = hubs.find((h: any) => h.hub_id === hubId);
    return hub?.content?.[selectedLanguage]?.title || hub?.content?.en?.title || hub?.title || 'Unknown Hub';
  };

  const getArticleTitle = (article: any) => {
    return article.content?.[selectedLanguage]?.title || 
           article.content?.en?.title || 
           'Untitled Article';
  };

  const getArticleDescription = (article: any) => {
    return article.content?.[selectedLanguage]?.description || 
           article.content?.en?.description || 
           'No description available';
  };

  const getStatusColor = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(opt => opt.value === status);
    return statusOption?.color || 'bg-gray-100 text-gray-800';
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      content: '',
      source_division_id: '',
      target_id: '',
      category: '',
      tags: [],
      author_id: 'current-user',
      status: 'draft',
      thumbnail: undefined
    });
  };

  // Handle create article
  const handleCreateArticle = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const createData = {
        title: formData.title,
        description: formData.description,
        content: formData.content,
        source_division_id: formData.source_division_id,
        target_id: formData.target_id,
        category: formData.category,
        tags: formData.tags,
        author_id: formData.author_id,
        status: formData.status,
        thumbnail: formData.thumbnail
      };

      await createArticle(createData).unwrap();
      toast('Article created successfully! 🎉', { icon: '✅' });
      setIsCreateModalOpen(false);
      resetForm();
      refetch();
    } catch (error: any) {
      console.error('Create article error:', error);
      toast(`Failed to create article: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle edit article
  const handleEditArticle = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingArticle) return;

    try {
      const updateData = {
        title: formData.title,
        description: formData.description,
        content: formData.content,
        category: formData.category,
        tags: formData.tags,
        status: formData.status,
        thumbnail: formData.thumbnail,
        language: selectedLanguage
      };

      await updateArticle({ id: editingArticle.article_id, data: updateData }).unwrap();
      toast('Article updated successfully! 🎉', { icon: '✅' });
      setIsEditModalOpen(false);
      setEditingArticle(null);
      resetForm();
      refetch();
    } catch (error: any) {
      console.error('Update article error:', error);
      toast(`Failed to update article: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle delete article
  const handleDeleteArticle = async (articleId: string) => {
    if (!confirm('Are you sure you want to delete this article? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteArticle(articleId).unwrap();
      toast('Article deleted successfully! 🗑️', { icon: '✅' });
      refetch();
    } catch (error: any) {
      console.error('Delete article error:', error);
      toast(`Failed to delete article: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle publish article
  const handlePublishArticle = async (articleId: string) => {
    try {
      await publishArticle(articleId).unwrap();
      toast('Article published successfully! 📢', { icon: '✅' });
      refetch();
    } catch (error: any) {
      console.error('Publish article error:', error);
      toast(`Failed to publish article: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Open edit modal
  const openEditModal = (article: any) => {
    setEditingArticle(article);
    const content = article.content?.[selectedLanguage] || article.content?.en || {};
    setFormData({
      title: content.title || '',
      description: content.description || '',
      content: content.content || '',
      source_division_id: article.source_division_id || '',
      target_id: article.target_id || '',
      category: article.category || '',
      tags: article.tags || [],
      author_id: article.author_id || 'current-user',
      status: article.status || 'draft',
      thumbnail: undefined
    });
    setIsEditModalOpen(true);
  };

  // Open view modal
  const openViewModal = (article: any) => {
    setViewingArticle(article);
    setIsViewModalOpen(true);
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, thumbnail: file }));
    }
  };

  // Handle tags input
  const handleTagsChange = (value: string) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setFormData(prev => ({ ...prev, tags }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sehatti-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading articles: {error.toString()}</p>
        <Button onClick={refetch} className="mt-4">Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-sehatti-gold/10 to-sehatti-gold/5 border-sehatti-gold/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray">Total Articles</p>
                <p className="text-2xl font-bold text-sehatti-gold">{totalArticles}</p>
              </div>
              <FileText className="h-8 w-8 text-sehatti-gold" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100/50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-green-600">
                  {articles.filter((article: any) => article.status === 'published').length}
                </p>
              </div>
              <Globe className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100/50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {articles.filter((article: any) => article.status === 'draft').length}
                </p>
              </div>
              <Edit className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-50 to-blue-100/50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Languages</p>
                <p className="text-2xl font-bold text-blue-600">3</p>
              </div>
              <Globe className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <Select 
            value={selectedLanguage} 
            onChange={(e) => setSelectedLanguage(e.target.value)}
            className="w-48"
          >
            {LANGUAGES.map((lang) => (
              <SelectOption key={lang.value} value={lang.value}>
                {lang.flag} {lang.label}
              </SelectOption>
            ))}
          </Select>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sehatti-gold hover:bg-sehatti-gold/90 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Create Article
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[70vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Article</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateArticle} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Title *</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter article title"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <Select 
                    value={formData.category} 
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  >
                    <SelectOption value="">Select category</SelectOption>
                    {ARTICLE_CATEGORIES.map((category) => (
                      <SelectOption key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                      </SelectOption>
                    ))}
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter article description"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content</label>
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter article content"
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Source Division *</label>
                  <Select 
                    value={formData.source_division_id} 
                    onChange={(e) => setFormData(prev => ({ ...prev, source_division_id: e.target.value }))}
                  >
                    <SelectOption value="">Select source division</SelectOption>
                    {hubs.map((hub: any) => (
                      <SelectOption key={hub.hub_id} value={hub.hub_id}>
                        {getHubName(hub.hub_id)}
                      </SelectOption>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Target Hub *</label>
                  <Select 
                    value={formData.target_id} 
                    onChange={(e) => setFormData(prev => ({ ...prev, target_id: e.target.value }))}
                  >
                    <SelectOption value="">Select target hub</SelectOption>
                    {hubs.map((hub: any) => (
                      <SelectOption key={hub.hub_id} value={hub.hub_id}>
                        {getHubName(hub.hub_id)}
                      </SelectOption>
                    ))}
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Tags</label>
                  <Input
                    value={formData.tags.join(', ')}
                    onChange={(e) => handleTagsChange(e.target.value)}
                    placeholder="Enter tags separated by commas"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Status</label>
                  <Select 
                    value={formData.status} 
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  >
                    {STATUS_OPTIONS.map((option) => (
                      <SelectOption key={option.value} value={option.value}>
                        {option.label}
                      </SelectOption>
                    ))}
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Thumbnail</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sehatti-gold/10 file:text-sehatti-gold hover:file:bg-sehatti-gold/20"
                />
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-sehatti-gold hover:bg-sehatti-gold/90">
                  Create Article
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Articles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Articles ({filteredArticles.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Author</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredArticles.map((article: any) => (
                <TableRow key={article.article_id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{getArticleTitle(article)}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {getArticleDescription(article)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {article.category || 'Uncategorized'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(article.status || 'draft')}>
                      {(article.status || 'draft').charAt(0).toUpperCase() + (article.status || 'draft').slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="w-4 h-4 mr-1" />
                      {article.author_id || 'Unknown'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(article.created_at).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewModal(article)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditModal(article)}
                        className="text-sehatti-gold hover:text-sehatti-gold/80"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      {article.status === 'draft' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePublishArticle(article.article_id)}
                          className="text-green-600 hover:text-green-800"
                        >
                          📢
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteArticle(article.article_id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredArticles.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? 'No articles found matching your search.' : 'No articles available. Create your first article!'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl max-h-[70vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Article</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditArticle} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Title *</label>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter article title"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Category</label>
                <Select 
                  value={formData.category} 
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                >
                  <SelectOption value="">Select category</SelectOption>
                  {ARTICLE_CATEGORIES.map((category) => (
                    <SelectOption key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                    </SelectOption>
                  ))}
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter article description"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Content</label>
              <Textarea
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter article content"
                rows={6}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Tags</label>
                <Input
                  value={formData.tags.join(', ')}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="Enter tags separated by commas"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Status</label>
                <Select 
                  value={formData.status} 
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                >
                  {STATUS_OPTIONS.map((option) => (
                    <SelectOption key={option.value} value={option.value}>
                      {option.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Thumbnail</label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sehatti-gold/10 file:text-sehatti-gold hover:file:bg-sehatti-gold/20"
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-sehatti-gold hover:bg-sehatti-gold/90">
                Update Article
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <span>Article Details</span>
              <div className="flex gap-2">
                {LANGUAGES.map((lang) => (
                  <Button
                    key={lang.value}
                    variant={selectedLanguage === lang.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedLanguage(lang.value)}
                    className="text-xs"
                  >
                    {lang.flag} {lang.label}
                  </Button>
                ))}
              </div>
            </DialogTitle>
          </DialogHeader>
          
          {viewingArticle && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Basic Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Title</label>
                      <p className="text-lg">{getArticleTitle(viewingArticle)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description</label>
                      <p className="text-gray-700">{getArticleDescription(viewingArticle)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Category</label>
                      <Badge variant="outline" className="ml-2">
                        {viewingArticle.category || 'Uncategorized'}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <Badge className={`ml-2 ${getStatusColor(viewingArticle.status || 'draft')}`}>
                        {(viewingArticle.status || 'draft').charAt(0).toUpperCase() + (viewingArticle.status || 'draft').slice(1)}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Statistics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">View Count</span>
                      <span className="font-medium">{viewingArticle.view_count || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Like Count</span>
                      <span className="font-medium">{viewingArticle.like_count || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Available Languages</span>
                      <div className="flex gap-1">
                        {Object.keys(viewingArticle.content || {}).map((lang) => (
                          <span key={lang} className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                            {LANGUAGES.find(l => l.value === lang)?.flag} {lang.toUpperCase()}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created</span>
                      <span className="font-medium">{new Date(viewingArticle.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {viewingArticle.tags && viewingArticle.tags.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {viewingArticle.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-lg font-semibold mb-2">Content</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="whitespace-pre-wrap">
                    {viewingArticle.content?.[selectedLanguage]?.content || 
                     viewingArticle.content?.en?.content || 
                     'No content available'}
                  </p>
                </div>
              </div>

              {viewingArticle.thumbnail_url && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Thumbnail</h3>
                  <img
                    src={viewingArticle.thumbnail_url}
                    alt="Article thumbnail"
                    className="max-w-sm rounded-lg shadow-md"
                  />
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ArticleManagement; 