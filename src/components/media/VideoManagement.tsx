import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { Select, SelectOption } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/Dialog';
import { Trash2, Edit, Eye, Plus, Search, Video, Calendar, User, Play, Pause, Upload } from 'lucide-react';
import { 
  useListContentQuery,
  useGetHubsQuery,
  useCreateMultilingualContentMutation,
  useDeleteContentMutation,
  useUploadFileMutation,
  useUploadThumbnailMutation,
  useGetContentByIdQuery
} from '../../features/API/hubApi';

// Language options
const LANGUAGES = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'ar', label: 'العربية', flag: '🇸🇦' },
  { value: 'hi', label: 'हिंदी', flag: '🇮🇳' }
];

// Gender options for voice
const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' }
];

// Video status options
const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'processing', label: 'Processing', color: 'bg-blue-100 text-blue-800' },
  { value: 'published', label: 'Published', color: 'bg-green-100 text-green-800' },
  { value: 'archived', label: 'Archived', color: 'bg-red-100 text-red-800' }
];

interface VideoFormData {
  title: string;
  description: string;
  content: string;
  hub_id: string;
  language: string;
  gender: string;
  voice_speed: number;
  video_file?: File;
}

const VideoManagement: React.FC = () => {
  // State management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingVideo, setEditingVideo] = useState<any>(null);
  const [viewingVideo, setViewingVideo] = useState<any>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [selectedHub, setSelectedHub] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<VideoFormData>({
    title: '',
    description: '',
    content: '',
    hub_id: '',
    language: 'en',
    gender: 'female',
    voice_speed: 1.0,
    video_file: undefined
  });

  // RTK Query hooks
  const { data: videosData, isLoading, error, refetch } = useListContentQuery({});
  const { data: hubsData } = useGetHubsQuery({});
  const [createContent] = useCreateMultilingualContentMutation();
  const [deleteContent] = useDeleteContentMutation();
  const [uploadFile] = useUploadFileMutation();
  const [uploadThumbnail] = useUploadThumbnailMutation();

  // Extract data
  const videos = videosData?.data || [];
  const hubs = hubsData?.data || [];
  const totalVideos = videosData?.total || 0;

  // Filter videos based on search and hub
  const filteredVideos = videos.filter((video: any) => {
    const title = video.title?.[selectedLanguage] || video.title?.en || '';
    const description = video.description?.[selectedLanguage] || video.description?.en || '';
    const searchLower = searchTerm.toLowerCase();
    
    const matchesSearch = title.toLowerCase().includes(searchLower) ||
                         description.toLowerCase().includes(searchLower);
    const matchesHub = !selectedHub || video.hub_id === selectedHub;
    
    return matchesSearch && matchesHub;
  });

  // Helper functions
  const getHubName = (hubId: string) => {
    const hub = hubs.find((h: any) => h.hub_id === hubId);
    return hub?.content?.[selectedLanguage]?.title || hub?.content?.en?.title || hub?.title || 'Unknown Hub';
  };

  const getStatusColor = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(opt => opt.value === status);
    return statusOption?.color || 'bg-gray-100 text-gray-800';
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      content: '',
      hub_id: '',
      language: 'en',
      gender: 'female',
      voice_speed: 1.0,
      video_file: undefined
    });
  };

  // Handle create video
  const handleCreateVideo = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      let videoFileId = '';
      let thumbnailFileId = '';

      // Upload video file if provided
      if (formData.video_file) {
        const videoResult = await uploadFile({ 
          file: formData.video_file,
          autoCaption: true,
          languages: formData.language 
        }).unwrap();
        videoFileId = videoResult.video_id;
      }

      // Create content with uploaded file IDs
      const createData = {
        hub_id: formData.hub_id,
        title: formData.title,
        description: formData.description,
        source_language: formData.language,
        auto_translate: true,
        generate_captions: true,
        gender: formData.gender,
        tags: '',
        video_file_id: videoFileId,
        thumbnail_file_id: thumbnailFileId
      };

      await createContent(createData).unwrap();
      toast('Video created successfully! 🎉', { icon: '✅' });
      setIsCreateModalOpen(false);
      resetForm();
      refetch();
    } catch (error: any) {
      console.error('Create video error:', error);
      toast(`Failed to create video: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle edit video - Note: No update mutation available in hubApi, using delete/recreate pattern
  const handleEditVideo = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingVideo) return;

    try {
      // For now, just show a message since there's no update endpoint
      toast('Video updates not supported yet. Please delete and recreate.', { icon: '⚠️' });
      setIsEditModalOpen(false);
      setEditingVideo(null);
      resetForm();
    } catch (error: any) {
      console.error('Update video error:', error);
      toast(`Failed to update video: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle delete video
  const handleDeleteVideo = async (videoId: string) => {
    if (!confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteContent(videoId).unwrap();
      toast('Video deleted successfully! 🗑️', { icon: '✅' });
      refetch();
    } catch (error: any) {
      console.error('Delete video error:', error);
      toast(`Failed to delete video: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Open edit modal
  const openEditModal = (video: any) => {
    setEditingVideo(video);
    setFormData({
      title: video.title?.[selectedLanguage] || video.title?.en || '',
      description: video.description?.[selectedLanguage] || video.description?.en || '',
      content: video.content || '',
      hub_id: video.hub_id || '',
      language: selectedLanguage,
      gender: video.gender || 'female',
      voice_speed: video.voice_speed || 1.0,
      video_file: undefined
    });
    setIsEditModalOpen(true);
  };

  // Open view modal
  const openViewModal = (video: any) => {
    setViewingVideo(video);
    setIsViewModalOpen(true);
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, video_file: file }));
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sehatti-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading videos: {error.toString()}</p>
        <Button onClick={refetch} className="mt-4">Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-sehatti-gold/10 to-sehatti-gold/5 border-sehatti-gold/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray">Total Videos</p>
                <p className="text-2xl font-bold text-sehatti-gold">{totalVideos}</p>
              </div>
              <Video className="h-8 w-8 text-sehatti-gold" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100/50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-green-600">
                  {videos.filter((video: any) => video.status === 'published').length}
                </p>
              </div>
              <Play className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-50 to-blue-100/50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Processing</p>
                <p className="text-2xl font-bold text-blue-600">
                  {videos.filter((video: any) => video.status === 'processing').length}
                </p>
              </div>
              <Upload className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100/50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {videos.filter((video: any) => video.status === 'draft').length}
                </p>
              </div>
              <Pause className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search videos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <Select 
            value={selectedLanguage} 
            onChange={(e) => setSelectedLanguage(e.target.value)}
            className="w-48"
          >
            {LANGUAGES.map((lang) => (
              <SelectOption key={lang.value} value={lang.value}>
                {lang.flag} {lang.label}
              </SelectOption>
            ))}
          </Select>

          <Select 
            value={selectedHub} 
            onChange={(e) => setSelectedHub(e.target.value)}
            className="w-48"
          >
            <SelectOption value="">All Hubs</SelectOption>
            {hubs.map((hub: any) => (
              <SelectOption key={hub.hub_id} value={hub.hub_id}>
                {getHubName(hub.hub_id)}
              </SelectOption>
            ))}
          </Select>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sehatti-gold hover:bg-sehatti-gold/90 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Create Video
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[70vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Video</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateVideo} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Title *</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter video title"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Hub *</label>
                  <Select 
                    value={formData.hub_id} 
                    onChange={(e) => setFormData(prev => ({ ...prev, hub_id: e.target.value }))}
                  >
                    <SelectOption value="">Select hub</SelectOption>
                    {hubs.map((hub: any) => (
                      <SelectOption key={hub.hub_id} value={hub.hub_id}>
                        {getHubName(hub.hub_id)}
                      </SelectOption>
                    ))}
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter video description"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content/Script</label>
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter video content or script"
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Language</label>
                  <Select 
                    value={formData.language} 
                    onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}
                  >
                    {LANGUAGES.map((lang) => (
                      <SelectOption key={lang.value} value={lang.value}>
                        {lang.flag} {lang.label}
                      </SelectOption>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Voice Gender</label>
                  <Select 
                    value={formData.gender} 
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
                  >
                    {GENDER_OPTIONS.map((option) => (
                      <SelectOption key={option.value} value={option.value}>
                        {option.label}
                      </SelectOption>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Voice Speed</label>
                  <Input
                    type="number"
                    step="0.1"
                    min="0.5"
                    max="2.0"
                    value={formData.voice_speed}
                    onChange={(e) => setFormData(prev => ({ ...prev, voice_speed: parseFloat(e.target.value) }))}
                    placeholder="1.0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Video File (Optional)</label>
                <input
                  type="file"
                  accept="video/*"
                  onChange={handleFileChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sehatti-gold/10 file:text-sehatti-gold hover:file:bg-sehatti-gold/20"
                />
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-sehatti-gold hover:bg-sehatti-gold/90">
                  Create Video
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Videos Table */}
      <Card>
        <CardHeader>
          <CardTitle>Videos ({filteredVideos.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Hub</TableHead>
                <TableHead>Language</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVideos.map((video: any) => (
                <TableRow key={video.video_id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{video.title}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {video.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getHubName(video.hub_id)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {LANGUAGES.find(l => l.value === video.language)?.flag}
                      <span className="text-sm">{video.language?.toUpperCase()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(video.status || 'draft')}>
                      {(video.status || 'draft').charAt(0).toUpperCase() + (video.status || 'draft').slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {video.duration ? `${Math.round(video.duration)}s` : 'N/A'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(video.created_at).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewModal(video)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditModal(video)}
                        className="text-sehatti-gold hover:text-sehatti-gold/80"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteVideo(video.video_id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredVideos.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || selectedHub ? 'No videos found matching your filters.' : 'No videos available. Create your first video!'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl max-h-[70vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Video</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditVideo} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Title *</label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter video title"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter video description"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Content/Script</label>
              <Textarea
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter video content or script"
                rows={6}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Language</label>
                <Select 
                  value={formData.language} 
                  onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}
                >
                  {LANGUAGES.map((lang) => (
                    <SelectOption key={lang.value} value={lang.value}>
                      {lang.flag} {lang.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Voice Gender</label>
                <Select 
                  value={formData.gender} 
                  onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
                >
                  {GENDER_OPTIONS.map((option) => (
                    <SelectOption key={option.value} value={option.value}>
                      {option.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Voice Speed</label>
                <Input
                  type="number"
                  step="0.1"
                  min="0.5"
                  max="2.0"
                  value={formData.voice_speed}
                  onChange={(e) => setFormData(prev => ({ ...prev, voice_speed: parseFloat(e.target.value) }))}
                  placeholder="1.0"
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-sehatti-gold hover:bg-sehatti-gold/90">
                Update Video
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Video Details</DialogTitle>
          </DialogHeader>
          
          {viewingVideo && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Basic Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Title</label>
                      <p className="text-lg">{viewingVideo.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description</label>
                      <p className="text-gray-700">{viewingVideo.description || 'No description'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Hub</label>
                      <Badge variant="outline" className="ml-2">
                        {getHubName(viewingVideo.hub_id)}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <Badge className={`ml-2 ${getStatusColor(viewingVideo.status || 'draft')}`}>
                        {(viewingVideo.status || 'draft').charAt(0).toUpperCase() + (viewingVideo.status || 'draft').slice(1)}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Technical Details</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Language</span>
                      <div className="flex items-center gap-1">
                        {LANGUAGES.find(l => l.value === viewingVideo.language)?.flag}
                        <span className="font-medium">{viewingVideo.language?.toUpperCase()}</span>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Voice Gender</span>
                      <span className="font-medium">{viewingVideo.gender || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Voice Speed</span>
                      <span className="font-medium">{viewingVideo.voice_speed || 1.0}x</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration</span>
                      <span className="font-medium">{viewingVideo.duration ? `${Math.round(viewingVideo.duration)}s` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created</span>
                      <span className="font-medium">{new Date(viewingVideo.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {viewingVideo.content && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Content/Script</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="whitespace-pre-wrap">{viewingVideo.content}</p>
                  </div>
                </div>
              )}

              {viewingVideo.video_url && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Video Preview</h3>
                  <video
                    controls
                    className="w-full max-w-2xl rounded-lg shadow-md"
                    src={viewingVideo.video_url}
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VideoManagement; 