import React, { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import {
  FaEdit,
  FaImage,
  FaUpload,
  FaTimes,
  FaPlus,
  FaTag,
  FaUser,
  FaLanguage,
  Fa<PERSON><PERSON><PERSON>,
  FaSave
} from 'react-icons/fa'
import { toast } from 'react-hot-toast'

import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import { Textarea } from '../ui/Textarea'
import { Select } from '../ui/Select'
import { Badge } from '../ui/Badge'
import { Progress } from '../ui/Progress'

import type {
  MediaResponse,
  MediaUpdateRequest,
  MediaStatus,
  Gender,
  Language,
  FileUploadResponse
} from '../../types/media'
import { mediaService } from '../../services/mediaService'

// Constants
const STATUS_OPTIONS = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'draft', label: 'Draft' },
  { value: 'processing', label: 'Processing' },
  { value: 'archived', label: 'Archived' }
] as const

const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'mixed', label: 'Mixed/Both' }
] as const

const LANGUAGE_OPTIONS = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'ar', label: 'Arabic', flag: '🇸🇦' },
  { value: 'hi', label: 'Hindi', flag: '🇮🇳' }
] as const

interface EditMediaProps {
  mediaId: string
  onSuccess?: (mediaId: string) => void
  onCancel?: () => void
}

interface FormData {
  title: string
  description: string
  tags: string[]
  gender?: Gender
  status: MediaStatus
}

interface UploadState {
  thumbnail?: File
  gallery: File[]
  thumbnailProgress: number
  galleryProgress: number
  isUploading: boolean
}

export const EditMedia: React.FC<EditMediaProps> = ({
  mediaId,
  onSuccess,
  onCancel
}) => {
  const [media, setMedia] = useState<MediaResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedLanguage, setSelectedLanguage] = useState<Language>('en')

  // Form state
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<FormData>()

  // Upload state
  const [uploadState, setUploadState] = useState<UploadState>({
    gallery: [],
    thumbnailProgress: 0,
    galleryProgress: 0,
    isUploading: false
  })

  const [tagInput, setTagInput] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState<{
    thumbnail?: FileUploadResponse
    gallery: FileUploadResponse[]
  }>({
    gallery: []
  })

  // Watch form values
  const watchedTags = watch('tags') || []

  // Load media data
  useEffect(() => {
    const loadMedia = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await mediaService.getMedia(mediaId, selectedLanguage)
        setMedia(response)
        
        // Populate form with existing data
        setValue('title', response.title)
        setValue('description', response.description)
        setValue('tags', response.tags || [])
        setValue('gender', response.gender || 'mixed')
        setValue('status', response.status)
      } catch (err) {
        console.error('Failed to load media:', err)
        setError(err instanceof Error ? err.message : 'Failed to load media')
      } finally {
        setLoading(false)
      }
    }

    if (mediaId) {
      loadMedia()
    }
  }, [mediaId, selectedLanguage, setValue])

  // File upload handlers
  const handleFileUpload = useCallback(async (file: File, type: 'thumbnail' | 'gallery') => {
    try {
      setUploadState(prev => ({ ...prev, isUploading: true, [`${type}Progress`]: 0 }))

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadState(prev => ({
          ...prev,
          [`${type}Progress`]: Math.min(prev[`${type}Progress` as keyof UploadState] as number + 10, 90)
        }))
      }, 200)

      const response = await mediaService.uploadFile(file, 'media')
      
      clearInterval(progressInterval)
      setUploadState(prev => ({ ...prev, [`${type}Progress`]: 100 }))

      if (type === 'gallery') {
        setUploadedFiles(prev => ({
          ...prev,
          gallery: [...prev.gallery, response]
        }))
        setUploadState(prev => ({
          ...prev,
          gallery: [...prev.gallery, file]
        }))
      } else {
        setUploadedFiles(prev => ({ ...prev, [type]: response }))
        setUploadState(prev => ({ ...prev, [type]: file }))
      }

      toast.success(`${type} uploaded successfully`)
    } catch (error) {
      console.error(`Failed to upload ${type}:`, error)
      toast.error(`Failed to upload ${type}`)
    } finally {
      setUploadState(prev => ({ ...prev, isUploading: false }))
    }
  }, [])

  const handleThumbnailUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileUpload(file, 'thumbnail')
    }
  }

  const handleGalleryUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    files.forEach(file => handleFileUpload(file, 'gallery'))
  }

  // Remove uploaded files
  const removeThumbnail = () => {
    setUploadState(prev => ({ ...prev, thumbnail: undefined }))
    setUploadedFiles(prev => ({ ...prev, thumbnail: undefined }))
  }

  const removeGalleryFile = (index: number) => {
    setUploadState(prev => ({
      ...prev,
      gallery: prev.gallery.filter((_, i) => i !== index)
    }))
    setUploadedFiles(prev => ({
      ...prev,
      gallery: prev.gallery.filter((_, i) => i !== index)
    }))
  }

  // Tag management
  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue('tags', [...watchedTags, tagInput.trim()], { shouldDirty: true })
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove), { shouldDirty: true })
  }

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  // Form submission
  const onSubmit = async (data: FormData) => {
    try {
      const updateData: MediaUpdateRequest = {
        title: data.title,
        description: data.description,
        tags: data.tags,
        gender: data.gender,
        status: data.status,
        thumbnail: uploadState.thumbnail,
        gallery: uploadState.gallery
      }

      const response = await mediaService.updateMedia(mediaId, updateData)
      
      toast.success('Media updated successfully!')
      
      if (onSuccess) {
        onSuccess(response.media_id)
      }
    } catch (error) {
      console.error('Failed to update media:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update media')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex items-center gap-3">
          <FaSpinner className="h-6 w-6 animate-spin text-sehatti-gold-600" />
          <span className="text-sehatti-warm-gray-600">Loading media details...</span>
        </div>
      </div>
    )
  }

  if (error || !media) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FaTimes className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 mb-2">
          Failed to load media
        </h3>
        <p className="text-sehatti-warm-gray-600 mb-4">
          {error || 'Media not found'}
        </p>
        {onCancel && (
          <Button onClick={onCancel} variant="outline">
            Go Back
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-sehatti-gold-50 to-white rounded-2xl p-6 border border-sehatti-gold-200/50 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-sehatti-gold-100 rounded-lg">
              <FaEdit className="h-6 w-6 text-sehatti-gold-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-sehatti-warm-gray-900">
                Edit Media
              </h1>
              <p className="text-sehatti-warm-gray-600">
                Update your media content details
              </p>
            </div>
          </div>

          {/* Language Selector */}
          {media.available_languages && media.available_languages.length > 1 && (
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value as Language)}
              className="px-3 py-2 border border-sehatti-warm-gray-300 rounded-lg text-sm"
            >
              {media.available_languages.map((lang) => (
                <option key={lang} value={lang}>
                  {LANGUAGE_OPTIONS.find(l => l.value === lang)?.flag} {LANGUAGE_OPTIONS.find(l => l.value === lang)?.label}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaEdit className="h-5 w-5 text-sehatti-gold-600" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Title *
              </label>
              <Controller
                name="title"
                control={control}
                rules={{ required: 'Title is required' }}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter media title"
                    error={errors.title?.message}
                  />
                )}
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Description *
              </label>
              <Controller
                name="description"
                control={control}
                rules={{ required: 'Description is required' }}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    placeholder="Describe your media content"
                    rows={4}
                    error={errors.description?.message}
                  />
                )}
              />
            </div>

            {/* Status and Gender */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Status
                </label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select {...field}>
                      {STATUS_OPTIONS.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </Select>
                  )}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  <FaUser className="inline h-4 w-4 mr-1" />
                  Target Gender
                </label>
                <Controller
                  name="gender"
                  control={control}
                  render={({ field }) => (
                    <Select {...field}>
                      {GENDER_OPTIONS.map(gender => (
                        <option key={gender.value} value={gender.value}>
                          {gender.label}
                        </option>
                      ))}
                    </Select>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tags */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaTag className="h-5 w-5 text-sehatti-gold-600" />
              Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleTagKeyPress}
                  placeholder="Add tags to help categorize your content"
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={addTag}
                  variant="outline"
                  disabled={!tagInput.trim()}
                >
                  <FaPlus className="h-4 w-4" />
                </Button>
              </div>

              {watchedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watchedTags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <FaTimes className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Current Media Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Current Media</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Current Thumbnail */}
              <div>
                <h4 className="text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Current Thumbnail
                </h4>
                {media.thumbnail_url ? (
                  <div className="aspect-video bg-sehatti-warm-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={media.thumbnail_url}
                      alt="Current thumbnail"
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="aspect-video bg-sehatti-warm-gray-100 rounded-lg flex items-center justify-center">
                    <FaImage className="h-8 w-8 text-sehatti-warm-gray-400" />
                  </div>
                )}
              </div>

              {/* Current Gallery */}
              {media.gallery_urls && media.gallery_urls.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                    Current Gallery ({media.gallery_urls.length} images)
                  </h4>
                  <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                    {media.gallery_urls.slice(0, 4).map((url, index) => (
                      <div key={index} className="aspect-square rounded-lg overflow-hidden">
                        <img
                          src={url}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                    {media.gallery_urls.length > 4 && (
                      <div className="aspect-square bg-sehatti-warm-gray-100 rounded-lg flex items-center justify-center">
                        <span className="text-sm text-sehatti-warm-gray-600">
                          +{media.gallery_urls.length - 4}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* File Updates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaUpload className="h-5 w-5 text-sehatti-gold-600" />
              Update Files
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* New Thumbnail */}
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Replace Thumbnail
              </label>
              <div className="border-2 border-dashed border-sehatti-warm-gray-300 rounded-lg p-6">
                {uploadState.thumbnail ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <FaImage />
                        <div>
                          <p className="font-medium">{uploadState.thumbnail.name}</p>
                          <p className="text-sm text-sehatti-warm-gray-500">
                            {(uploadState.thumbnail.size / 1024).toFixed(2)} KB
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={removeThumbnail}
                      >
                        <FaTimes />
                      </Button>
                    </div>
                    {uploadState.thumbnailProgress < 100 && (
                      <Progress value={uploadState.thumbnailProgress} />
                    )}
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 bg-sehatti-gold-100 rounded-lg flex items-center justify-center mb-4">
                      <FaImage className="h-6 w-6 text-sehatti-gold-600" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sehatti-warm-gray-600">
                        Upload a new thumbnail image
                      </p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleThumbnailUpload}
                        className="hidden"
                        id="thumbnail-upload"
                      />
                      <label htmlFor="thumbnail-upload">
                        <Button type="button" variant="outline" asChild>
                          <span>Choose Image</span>
                        </Button>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Gallery Images */}
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Add Gallery Images
              </label>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-sehatti-warm-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 bg-sehatti-gold-100 rounded-lg flex items-center justify-center mb-4">
                      <FaImage className="h-6 w-6 text-sehatti-gold-600" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sehatti-warm-gray-600">
                        Upload additional images for gallery
                      </p>
                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleGalleryUpload}
                        className="hidden"
                        id="gallery-upload"
                      />
                      <label htmlFor="gallery-upload">
                        <Button type="button" variant="outline" asChild>
                          <span>Choose Images</span>
                        </Button>
                      </label>
                    </div>
                  </div>
                </div>

                {uploadState.gallery.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {uploadState.gallery.map((file, index) => (
                      <div key={index} className="relative">
                        <div className="aspect-square bg-sehatti-warm-gray-100 rounded-lg flex items-center justify-center">
                          <FaImage className="h-8 w-8 text-sehatti-warm-gray-400" />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeGalleryFile(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"
                        >
                          <FaTimes className="h-3 w-3" />
                        </button>
                        <p className="text-xs text-center mt-1 truncate">
                          {file.name}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || uploadState.isUploading}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            isLoading={isSubmitting || uploadState.isUploading}
            disabled={!isDirty || uploadState.isUploading}
            className="bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 hover:from-sehatti-gold-600 hover:to-sehatti-gold-700 text-white"
            leftIcon={<FaSave />}
          >
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  )
}

export default EditMedia 