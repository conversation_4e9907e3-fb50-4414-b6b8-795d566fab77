/**
 * Create Media Component
 * Handles media content creation with file upload, multi-language support, and AI features
 * Based on the working content-upload.tsx from the old frontend
 */

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { 
  Upload, 
  Video, 
  Image, 
  X, 
  Plus, 
  Globe, 
  Captions, 
  Languages,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import toast from 'react-hot-toast'

import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  Language, 
  Gender, 
  LANGUAGE_LABELS,
  GENDER_LABELS
} from '../../types/media'
import type { 
  MediaContentFormData,
  UploadProgress
} from '../../types/media'
import { mediaService } from '../../services/mediaService'

// Helper function for file size formatting
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

interface CreateMediaProps {
  hubId: string
  onSuccess?: (contentId: string) => void
  onCancel?: () => void
}

export function CreateMedia({ hubId, onSuccess, onCancel }: CreateMediaProps) {
  const [formData, setFormData] = useState<MediaContentFormData>({
    hub_id: hubId,
    title: '',
    description: '',
    default_language: Language.ENGLISH,
    tags: [],
    gender: Gender.ALL,
    auto_translate: true,
    generate_captions: true,
    gallery_files: []
  })

  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTag, setCurrentTag] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Video file dropzone
  const onVideoDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setFormData(prev => ({ ...prev, video_file: acceptedFiles[0] }))
      setErrors(prev => ({ ...prev, video: '' }))
    }
  }, [])

  const { getRootProps: getVideoRootProps, getInputProps: getVideoInputProps, isDragActive: isVideoDragActive } = useDropzone({
    onDrop: onVideoDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    },
    maxFiles: 1,
    maxSize: 2 * 1024 * 1024 * 1024 // 2GB
  })

  // Thumbnail dropzone
  const onThumbnailDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setFormData(prev => ({ ...prev, thumbnail_file: acceptedFiles[0] }))
    }
  }, [])

  const { getRootProps: getThumbnailRootProps, getInputProps: getThumbnailInputProps, isDragActive: isThumbnailDragActive } = useDropzone({
    onDrop: onThumbnailDrop,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.webp']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  // Gallery dropzone
  const onGalleryDrop = useCallback((acceptedFiles: File[]) => {
    setFormData(prev => ({
      ...prev,
      gallery_files: [...prev.gallery_files, ...acceptedFiles]
    }))
  }, [])

  const { getRootProps: getGalleryRootProps, getInputProps: getGalleryInputProps, isDragActive: isGalleryDragActive } = useDropzone({
    onDrop: onGalleryDrop,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.webp']
    },
    maxFiles: 10,
    maxSize: 10 * 1024 * 1024 // 10MB per file
  })

  const handleInputChange = (field: keyof MediaContentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleAddTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }))
      setCurrentTag('')
    }
  }

  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const removeVideoFile = () => {
    setFormData(prev => ({ ...prev, video_file: undefined }))
  }

  const removeThumbnailFile = () => {
    setFormData(prev => ({ ...prev, thumbnail_file: undefined }))
  }

  const removeGalleryFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      gallery_files: prev.gallery_files.filter((_, i) => i !== index)
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    if (!formData.video_file) {
      newErrors.video = 'Video file is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Progressive upload approach - upload files first, then create content
      const uploadPromises: Promise<any>[] = []

      // Upload video file
      if (formData.video_file) {
        const videoUpload = mediaService.uploadFile(formData.video_file, 'videos')
          .then(response => ({ type: 'video', response }))
        uploadPromises.push(videoUpload)
      }

      // Upload thumbnail
      if (formData.thumbnail_file) {
        const thumbnailUpload = mediaService.uploadFile(formData.thumbnail_file, 'thumbnails')
          .then(response => ({ type: 'thumbnail', response }))
        uploadPromises.push(thumbnailUpload)
      }

      // Wait for all uploads to complete
      const uploadResults = await Promise.all(uploadPromises)

      // Extract file IDs
      let video_file_id: string | undefined
      let thumbnail_file_id: string | undefined

      uploadResults.forEach(result => {
        if (result.type === 'video') {
          video_file_id = result.response.file_id
        } else if (result.type === 'thumbnail') {
          thumbnail_file_id = result.response.file_id
        }
      })

      // Create content with file references
      const createRequest = {
        hub_id: formData.hub_id,
        title: formData.title,
        description: formData.description,
        default_language: formData.default_language,
        tags: formData.tags,
        gender: formData.gender,
        auto_translate: formData.auto_translate,
        generate_captions: formData.generate_captions,
        video_file_id,
        thumbnail_file_id
      }

      const content = await mediaService.createContent(createRequest)

      toast.success('Media content created successfully!')
      onSuccess?.(content.content_id)
    } catch (error) {
      console.error('Failed to create content:', error)
      setErrors({ submit: 'Failed to create content. Please try again.' })
      toast.error('Failed to create content')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Upload Media Content</h2>
        <p className="text-gray-300">Create new media content with multi-language support and AI features</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-200 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className={`w-full px-3 py-2 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent ${
                errors.title ? 'border-red-500' : 'border-white/20'
              }`}
              placeholder="Enter content title"
            />
            {errors.title && <p className="mt-1 text-sm text-red-400">{errors.title}</p>}
          </div>

          <div>
            <label htmlFor="language" className="block text-sm font-medium text-gray-200 mb-2">
              Default Language
            </label>
            <select
              id="language"
              value={formData.default_language}
              onChange={(e) => handleInputChange('default_language', e.target.value as Language)}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
            >
              {Object.entries(LANGUAGE_LABELS).map(([value, label]) => (
                <option key={value} value={value} className="bg-gray-800 text-white">{label}</option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-200 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className={`w-full px-3 py-2 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent ${
              errors.description ? 'border-red-500' : 'border-white/20'
            }`}
            placeholder="Enter content description"
          />
          {errors.description && <p className="mt-1 text-sm text-red-400">{errors.description}</p>}
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">Tags</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.tags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="bg-white/10 text-white border-white/20 flex items-center gap-2"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag)}
                  className="p-0.5 rounded-full hover:bg-white/20"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              value={currentTag}
              onChange={(e) => setCurrentTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
              placeholder="Add tag and press Enter"
            />
            <Button
              type="button"
              onClick={handleAddTag}
              size="sm"
              className="bg-gold-600 hover:bg-gold-700"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Gender Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">Target Gender</label>
          <select
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value as Gender)}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            {Object.entries(GENDER_LABELS).map(([value, label]) => (
              <option key={value} value={value} className="bg-gray-800 text-white">{label}</option>
            ))}
          </select>
        </div>

        {/* Video Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">
            Video File *
          </label>
          {formData.video_file ? (
            <div className="border border-green-500/30 rounded-lg p-4 bg-green-500/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Video className="w-5 h-5 text-green-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-green-300">{formData.video_file.name}</p>
                    <p className="text-xs text-green-400">{formatFileSize(formData.video_file.size)}</p>
                  </div>
                </div>
                <Button
                  type="button"
                  onClick={removeVideoFile}
                  size="sm"
                  variant="ghost"
                  className="text-green-400 hover:text-green-300 hover:bg-green-500/20"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div
              {...getVideoRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                isVideoDragActive 
                  ? 'border-gold-400 bg-gold-500/10' 
                  : errors.video 
                    ? 'border-red-400 bg-red-500/10' 
                    : 'border-white/30 hover:border-white/50'
              }`}
            >
              <input {...getVideoInputProps()} />
              <Video className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-300 mb-1">
                {isVideoDragActive ? 'Drop video file here' : 'Drag & drop video file here, or click to select'}
              </p>
              <p className="text-xs text-gray-400">Supports MP4, MOV, AVI, MKV, WebM (max 2GB)</p>
            </div>
          )}
          {errors.video && <p className="mt-1 text-sm text-red-400">{errors.video}</p>}
        </div>

        {/* Thumbnail Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">
            Thumbnail (Optional)
          </label>
          {formData.thumbnail_file ? (
            <div className="border border-green-500/30 rounded-lg p-4 bg-green-500/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Image className="w-5 h-5 text-green-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-green-300">{formData.thumbnail_file.name}</p>
                    <p className="text-xs text-green-400">{formatFileSize(formData.thumbnail_file.size)}</p>
                  </div>
                </div>
                <Button
                  type="button"
                  onClick={removeThumbnailFile}
                  size="sm"
                  variant="ghost"
                  className="text-green-400 hover:text-green-300 hover:bg-green-500/20"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div
              {...getThumbnailRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                isThumbnailDragActive ? 'border-gold-400 bg-gold-500/10' : 'border-white/30 hover:border-white/50'
              }`}
            >
              <input {...getThumbnailInputProps()} />
              <Image className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-300 mb-1">
                {isThumbnailDragActive ? 'Drop thumbnail here' : 'Drag & drop thumbnail here, or click to select'}
              </p>
              <p className="text-xs text-gray-400">Supports JPG, PNG, WebP (max 10MB)</p>
            </div>
          )}
        </div>

        {/* Gallery Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">
            Gallery Images (Optional)
          </label>
          {formData.gallery_files.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {formData.gallery_files.map((file, index) => (
                <div key={index} className="relative border border-white/20 rounded-lg p-2 bg-white/5">
                  <div className="flex items-center">
                    <Image className="w-4 h-4 text-gray-400 mr-2" />
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-300 truncate">{file.name}</p>
                      <p className="text-xs text-gray-400">{formatFileSize(file.size)}</p>
                    </div>
                    <Button
                      type="button"
                      onClick={() => removeGalleryFile(index)}
                      size="sm"
                      variant="ghost"
                      className="text-gray-400 hover:text-gray-300 hover:bg-white/10 p-1"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
          <div
            {...getGalleryRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
              isGalleryDragActive ? 'border-gold-400 bg-gold-500/10' : 'border-white/30 hover:border-white/50'
            }`}
          >
            <input {...getGalleryInputProps()} />
            <Image className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-300 mb-1">
              {isGalleryDragActive ? 'Drop gallery images here' : 'Drag & drop gallery images here, or click to select'}
            </p>
            <p className="text-xs text-gray-400">Supports JPG, PNG, WebP (max 10MB each, up to 10 files)</p>
          </div>
        </div>

        {/* AI Features */}
        <div className="border border-white/20 rounded-lg p-4 bg-white/5">
          <h3 className="text-lg font-medium text-white mb-4">AI Features</h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="auto_translate"
                checked={formData.auto_translate}
                onChange={(e) => handleInputChange('auto_translate', e.target.checked)}
                className="h-4 w-4 text-gold-600 border-gray-300 rounded focus:ring-gold-500"
              />
              <label htmlFor="auto_translate" className="ml-3 flex items-center">
                <Languages className="w-4 h-4 text-gold-400 mr-2" />
                <span className="text-sm text-gray-300">Auto-translate to Arabic and Hindi</span>
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="generate_captions"
                checked={formData.generate_captions}
                onChange={(e) => handleInputChange('generate_captions', e.target.checked)}
                className="h-4 w-4 text-gold-600 border-gray-300 rounded focus:ring-gold-500"
              />
              <label htmlFor="generate_captions" className="ml-3 flex items-center">
                <Captions className="w-4 h-4 text-gold-400 mr-2" />
                <span className="text-sm text-gray-300">Generate captions automatically</span>
              </label>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {errors.submit && (
          <div className="border border-red-500/30 rounded-lg p-4 bg-red-500/10">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
              <p className="text-sm text-red-300">{errors.submit}</p>
            </div>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            onClick={onCancel}
            variant="secondary"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-gold-600 to-gold-500 hover:from-gold-700 hover:to-gold-600 text-white border-0"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating Content...
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Create Content
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
} 