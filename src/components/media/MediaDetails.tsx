/**
 * Media Details Component
 * Displays detailed media content with multi-language support and actions
 * Based on the working ContentDetail.tsx from the old frontend
 */

import React, { useState } from 'react'
import {
  Video,
  Image,
  Globe,
  Captions,
  Download,
  Edit,
  Trash2,
  Play,
  Languages,
  Calendar,
  User,
  Tag,
  Eye
} from 'lucide-react'
import toast from 'react-hot-toast'

import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import {
  MediaResponse,
  Language,
  LANGUAGE_LABELS,
  getContentInLanguage
} from '../../types/media'

interface MediaDetailsProps {
  content: MediaResponse
  showActions?: boolean
  onEdit?: () => void
  onDelete?: () => void
  onClose?: () => void
}

export default function MediaDetails({
  content,
  showActions = true,
  onEdit,
  onDelete,
  onClose
}: MediaDetailsProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(
    content.available_languages.includes('en') ? Language.ENGLISH : 
    content.available_languages[0] as Language
  )

  // Get content in selected language
  const title = getContentInLanguage(content.title, selectedLanguage) as string
  const description = getContentInLanguage(content.description, selectedLanguage) as string
  const tags = getContentInLanguage(content.tags, selectedLanguage) as string[]

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      }).format(date)
    } catch (e) {
      return dateString
    }
  }

  // Helper function to get language flag emoji
  const getLanguageFlag = (lang: string): string => {
    switch (lang) {
      case 'en': return '🇺🇸'
      case 'ar': return '🇸🇦'
      case 'hi': return '🇮🇳'
      default: return '🏳️'
    }
  }

  // Determine media type for UI rendering
  const hasVideo = !!content.video_url
  const hasThumbnail = !!content.thumbnail_url
  const hasSubtitles = content.subtitles_available && content.subtitles_available.length > 0

  const handleDownloadSubtitle = (lang: string) => {
    if (content.subtitle_urls && content.subtitle_urls[lang]) {
      const link = document.createElement('a')
      link.href = content.subtitle_urls[lang]
      link.download = `subtitles_${content.content_id}_${lang}.srt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      toast.success(`Downloading ${LANGUAGE_LABELS[lang as Language]} subtitles`)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white">Media Details</h2>
          <p className="text-gray-300">View detailed information about this media content</p>
        </div>
        {onClose && (
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white hover:bg-white/10"
          >
            <Eye className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Language Selector */}
      {content.available_languages.length > 1 && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-200 mb-2">
            Select Language
          </label>
          <div className="flex flex-wrap gap-2">
            {content.available_languages.map(lang => (
              <Button
                key={lang}
                onClick={() => setSelectedLanguage(lang as Language)}
                size="sm"
                variant={selectedLanguage === lang ? 'default' : 'ghost'}
                className={
                  selectedLanguage === lang
                    ? 'bg-gold-600 hover:bg-gold-700 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }
              >
                <span className="mr-2">{getLanguageFlag(lang)}</span>
                {LANGUAGE_LABELS[lang as Language]}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Media Display Section */}
      <div className="mb-6">
        <div className="relative aspect-video bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded-xl overflow-hidden">
          {hasVideo ? (
            <video 
              src={content.video_url} 
              controls 
              className="w-full h-full object-contain"
              poster={content.thumbnail_url}
            />
          ) : hasThumbnail ? (
            <img 
              src={content.thumbnail_url} 
              alt={title} 
              className="w-full h-full object-contain"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <Video className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                <span className="text-gray-400">No media preview available</span>
              </div>
            </div>
          )}
          
          {/* Media type indicator */}
          <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm flex items-center gap-2">
            {hasVideo ? (
              <>
                <Video className="w-4 h-4 text-blue-400" />
                <span>Video</span>
              </>
            ) : (
              <>
                <Image className="w-4 h-4 text-green-400" />
                <span>Image</span>
              </>
            )}
          </div>
          
          {/* Status Badge */}
          <div className="absolute top-4 left-4">
            <Badge 
              variant={content.status === 'active' ? 'success' : 'secondary'}
              className="bg-black/50 backdrop-blur-sm"
            >
              {content.status}
            </Badge>
          </div>
          
          {/* Subtitles indicator */}
          {hasVideo && hasSubtitles && (
            <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm flex items-center gap-2">
              <Captions className="w-4 h-4 text-yellow-400" />
              <span>{content.subtitles_available.length} Subtitles</span>
            </div>
          )}
        </div>
      </div>
      
      {/* Content Information Section */}
      <div className="space-y-6">
        {/* Title & Description */}
        <div>
          <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
          <p className="text-gray-300 whitespace-pre-wrap">{description}</p>
        </div>
        
        {/* Tags */}
        {tags.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Tag className="w-4 h-4 text-gray-400" />
              <h4 className="text-sm font-medium text-gray-200">Tags</h4>
            </div>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="bg-white/10 text-white border-white/20"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {/* Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <User className="w-4 h-4 text-gray-400" />
              <span className="font-medium">Gender:</span>
              <span>{content.gender}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="font-medium">Created:</span>
              <span>{formatDate(content.created_at)}</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Globe className="w-4 h-4 text-gray-400" />
              <span className="font-medium">Languages:</span>
              <div className="flex items-center gap-1">
                {content.available_languages.map(lang => (
                  <span key={lang} className="text-sm" title={LANGUAGE_LABELS[lang as Language]}>
                    {getLanguageFlag(lang)}
                  </span>
                ))}
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="font-medium">Updated:</span>
              <span>{formatDate(content.updated_at)}</span>
            </div>
          </div>
        </div>
        
        {/* Subtitles Download */}
        {hasVideo && content.subtitle_urls && Object.keys(content.subtitle_urls).length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Captions className="w-4 h-4 text-gray-400" />
              <h4 className="text-sm font-medium text-gray-200">Available Subtitles</h4>
            </div>
            <div className="flex flex-wrap gap-2">
              {Object.entries(content.subtitle_urls).map(([lang, url]) => (
                <Button
                  key={lang}
                  onClick={() => handleDownloadSubtitle(lang)}
                  size="sm"
                  variant="secondary"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <Download className="w-3 h-3 mr-2" />
                  <span className="mr-1">{getLanguageFlag(lang)}</span>
                  {LANGUAGE_LABELS[lang as Language]}
                </Button>
              ))}
            </div>
          </div>
        )}
        
        {/* Action Buttons */}
        {showActions && (
          <div className="flex justify-end gap-3 pt-4 border-t border-white/10">
            {onEdit && (
              <Button
                onClick={onEdit}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                onClick={onDelete}
                variant="destructive"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
} 