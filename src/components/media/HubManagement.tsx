import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { Select, SelectOption } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/Dialog';
import { Trash2, Edit, Eye, Plus, Search, BarChart3, Globe, Calendar, Users, Activity, Video, FileText, Star } from 'lucide-react';
import { MediaAPI } from '../../services/mediaApi';
import { Grid } from '../ui/Grid';
import { EmptyState } from '../ui/EmptyState';
import { FileUpload } from '../ui/FileUpload';
import { Switch } from '../ui/Switch';
import { Label } from '../ui/Label';
import { Separator } from '../ui/Separator';
import { 
  useGetHubsQuery,
  useAddHubMutation,
  useUpdateHubMutation,
  useDeleteHubMutation,
  useListContentQuery
} from '../../features/API/hubApi';

// Language options based on API
const LANGUAGES = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'ar', label: 'العربية', flag: '🇸🇦' },
  { value: 'hi', label: 'हिंदी', flag: '🇮🇳' }
];

// Category options
const CATEGORIES = [
  { value: 'training', label: 'Training' },
  { value: 'wellness', label: 'Wellness' },
  { value: 'compliance', label: 'Compliance' },
  { value: 'onboarding', label: 'Onboarding' },
  { value: 'safety', label: 'Safety' },
  { value: 'leadership', label: 'Leadership' },
  { value: 'general', label: 'General' }
];

// Visibility options based on API
const VISIBILITY_OPTIONS = [
  { value: 'public', label: 'Public', description: 'Visible to everyone' },
  { value: 'private', label: 'Private', description: 'Only visible to authorized users' },
  { value: 'restricted', label: 'Restricted', description: 'Limited access with permissions' }
];

// Status options based on API
const STATUS_OPTIONS = [
  { value: 'active', label: 'Active', color: 'bg-green-100 text-green-800' },
  { value: 'draft', label: 'Draft', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'inactive', label: 'Inactive', color: 'bg-gray-100 text-gray-800' },
  { value: 'archived', label: 'Archived', color: 'bg-red-100 text-red-800' }
];

interface HubFormData {
  title: string;
  description: string;
  category: string;
  tags: string[];
  visibility: 'public' | 'private' | 'restricted';
  auto_translate: boolean;
  image?: File;
}

const HubManagement: React.FC = () => {
  // State management
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedHub, setSelectedHub] = useState<any>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  const [formData, setFormData] = useState<HubFormData>({
    title: '',
    description: '',
    category: '',
    tags: [],
    visibility: 'public',
    auto_translate: true,
    image: undefined
  });

  // RTK Query hooks
  const { data: hubsData, isLoading, error, refetch } = useGetHubsQuery({});
  const [createHub] = useAddHubMutation();
  const [updateHub] = useUpdateHubMutation();
  const [deleteHub] = useDeleteHubMutation();
  const { data: allContentData } = useListContentQuery({});

  // Extract data
  const hubs = hubsData?.data || [];
  const totalHubs = hubsData?.total || 0;

  // Debug logging to understand the data structure
  console.log('🔍 Debug - Hubs data:', { hubsData, hubs, totalHubs });
  if (hubs.length > 0) {
    console.log('🔍 Debug - First hub structure:', hubs[0]);
  }

  // Filter hubs based on search, category, and status
  const filteredHubs = hubs.filter((hub: any) => {
    const title = hub.content?.[selectedLanguage]?.title || hub.content?.en?.title || '';
    const description = hub.content?.[selectedLanguage]?.description || hub.content?.en?.description || '';
    const searchLower = searchTerm.toLowerCase();
    
    const matchesSearch = title.toLowerCase().includes(searchLower) ||
                         description.toLowerCase().includes(searchLower);
    const matchesCategory = !filterCategory || hub.category === filterCategory;
    const matchesStatus = !filterStatus || hub.status === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Helper functions
  const getHubTitle = (hub: any) => {
    return hub.content?.[selectedLanguage]?.title || hub.content?.en?.title || 'Untitled Hub';
  };

  const getHubDescription = (hub: any) => {
    return hub.content?.[selectedLanguage]?.description || hub.content?.en?.description || 'No description';
  };

  const getStatusColor = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(opt => opt.value === status);
    return statusOption?.color || 'bg-gray-100 text-gray-800';
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public': return <Globe className="w-4 h-4" />;
      case 'private': return <Users className="w-4 h-4" />;
      case 'restricted': return <Star className="w-4 h-4" />;
      default: return <Globe className="w-4 h-4" />;
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      tags: [],
      visibility: 'public',
      auto_translate: true,
      image: undefined
    });
  };

  // Handle create hub
  const handleCreateHub = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const createData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        visibility: formData.visibility,
        auto_translate: formData.auto_translate,
        image: formData.image
      };

      await createHub(createData).unwrap();
      toast('Hub created successfully! 🎉', { icon: '✅' });
      setIsCreateModalOpen(false);
      resetForm();
      refetch();
    } catch (error: any) {
      console.error('Create hub error:', error);
      toast(`Failed to create hub: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle edit hub
  const handleEditHub = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedHub) return;

    try {
      const updateData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        visibility: formData.visibility,
        auto_translate: formData.auto_translate,
        image: formData.image
      };

      await updateHub({ 
        id: selectedHub.hub_id, 
        data: updateData,
        language: selectedLanguage 
      }).unwrap();
      
      toast('Hub updated successfully! 🎉', { icon: '✅' });
      setIsEditModalOpen(false);
      setSelectedHub(null);
      resetForm();
      refetch();
    } catch (error: any) {
      console.error('Update hub error:', error);
      toast(`Failed to update hub: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Handle delete hub
  const handleDeleteHub = async (hubId: string) => {
    if (!confirm('Are you sure you want to delete this hub? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteHub(hubId).unwrap();
      toast('Hub deleted successfully! 🗑️', { icon: '✅' });
      refetch();
    } catch (error: any) {
      console.error('Delete hub error:', error);
      toast(`Failed to delete hub: ${error.data?.detail || error.message}`, { icon: '❌' });
    }
  };

  // Open edit modal
  const openEditModal = (hub: any) => {
    setSelectedHub(hub);
    setFormData({
      title: getHubTitle(hub),
      description: getHubDescription(hub),
      category: hub.category || '',
      tags: hub.tags || [],
      visibility: hub.visibility || 'public',
      auto_translate: true,
      image: undefined
    });
    setIsEditModalOpen(true);
  };

  // Open view modal
  const openViewModal = (hub: any) => {
    setSelectedHub(hub);
    setIsViewModalOpen(true);
  };

  // Handle file upload
  const handleFileChange = (file: File) => {
    setFormData(prev => ({ ...prev, image: file }));
  };

  // Handle tags input
  const handleTagsChange = (value: string) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setFormData(prev => ({ ...prev, tags }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading hubs: {error.toString()}</p>
        <Button onClick={() => refetch()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Content Hubs ({totalHubs})
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your content hubs and multilingual content
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Language Selector */}
          <Select 
            value={selectedLanguage} 
            onValueChange={setSelectedLanguage}
            placeholder="Language"
          >
                      {LANGUAGES.map(lang => (
            <SelectOption key={lang.value} value={lang.value}>
              {lang.flag} {lang.label}
            </SelectOption>
          ))}
          </Select>

          {/* Create Hub Button */}
          <Button 
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Hub
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search hubs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
        
        <div className="flex gap-2">
          <Select 
            value={filterCategory} 
            onValueChange={setFilterCategory}
            placeholder="All Categories"
          >
            <SelectOption value="">All Categories</SelectOption>
            {CATEGORIES.map(cat => (
              <SelectOption key={cat.value} value={cat.value}>
                {cat.label}
              </SelectOption>
            ))}
          </Select>

          <Select 
            value={filterStatus} 
            onValueChange={setFilterStatus}
            placeholder="All Status"
          >
            <SelectOption value="">All Status</SelectOption>
            {STATUS_OPTIONS.map(status => (
              <SelectOption key={status.value} value={status.value}>
                {status.label}
              </SelectOption>
            ))}
          </Select>
        </div>
      </div>

      {/* Hubs Grid */}
      {filteredHubs.length === 0 ? (
        <EmptyState
          title="No hubs found"
          description="Create your first content hub to get started"
          action={
            <Button onClick={() => setIsCreateModalOpen(true)} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Hub
            </Button>
          }
        />
      ) : (
        <Grid columns={{ default: 1, md: 2, lg: 3 }} gap="6">
          {filteredHubs.map((hub: any) => (
            <Card key={hub.hub_id} className="group hover:shadow-lg transition-all duration-200 overflow-hidden">
              {/* Hub Image */}
              <div className="relative h-48 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                {hub.image_url ? (
                  <img 
                    src={hub.image_url} 
                    alt={getHubTitle(hub)}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.log('🔍 Image load error for hub:', hub.hub_id, 'URL:', hub.image_url);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Video className="w-12 h-12 text-gray-400" />
                  </div>
                )}
                
                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <Badge className={`${getStatusColor(hub.status)} border-0`}>
                    {hub.status}
                  </Badge>
                </div>

                {/* Visibility Icon */}
                <div className="absolute top-3 left-3 p-2 bg-white/90 dark:bg-gray-800/90 rounded-full">
                  {getVisibilityIcon(hub.visibility)}
                </div>
              </div>

              <CardContent className="p-6">
                {/* Hub Title and Description */}
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-1">
                    {getHubTitle(hub)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
                    {getHubDescription(hub)}
                  </p>
                </div>

                {/* Hub Meta */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(hub.created_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <FileText className="w-4 h-4" />
                    <span>{hub.content_count || 0} items</span>
                  </div>
                </div>

                {/* Category and Tags */}
                <div className="mb-4">
                  {hub.category && (
                    <Badge variant="outline" className="mb-2">
                      {hub.category}
                    </Badge>
                  )}
                  {hub.tags && hub.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {hub.tags.slice(0, 3).map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {hub.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{hub.tags.length - 3} more
                        </Badge>
                      )}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openViewModal(hub)}
                      className="flex items-center gap-1"
                    >
                      <Eye className="w-4 h-4" />
                      View
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openEditModal(hub)}
                      className="flex items-center gap-1"
                    >
                      <Edit className="w-4 h-4" />
                      Edit
                    </Button>
                  </div>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteHub(hub.hub_id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </Grid>
      )}

      {/* Create Hub Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="w-5 h-5" />
              Create New Hub
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleCreateHub} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Hub Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter hub title"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter hub description"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectOption value="">Select Category</SelectOption>
                  {CATEGORIES.map(cat => (
                    <SelectOption key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  value={formData.tags.join(', ')}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="training, wellness, compliance"
                />
                {/* Debug: Show current tags */}
                {formData.tags.length > 0 && (
                  <div className="mt-2 text-sm text-gray-600">
                    Current tags: {formData.tags.map(tag => `"${tag}"`).join(', ')}
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="visibility">Visibility</Label>
                <Select 
                  value={formData.visibility} 
                  onValueChange={(value: 'public' | 'private' | 'restricted') => 
                    setFormData(prev => ({ ...prev, visibility: value }))
                  }
                >
                  {VISIBILITY_OPTIONS.map(option => (
                    <SelectOption key={option.value} value={option.value}>
                      {option.label} - {option.description}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto_translate">Auto-translate content</Label>
                  <p className="text-sm text-gray-500">Automatically translate content to all supported languages</p>
                </div>
                <Switch
                  id="auto_translate"
                  checked={formData.auto_translate}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_translate: checked }))}
                />
              </div>

              <div>
                <Label>Hub Image (Optional)</Label>
                <FileUpload
                  accept="image/*"
                  onFileSelect={handleFileChange}
                  maxSize={5 * 1024 * 1024} // 5MB
                  className="mt-2"
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsCreateModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Create Hub
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Hub Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Edit Hub
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleEditHub} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-title">Hub Title *</Label>
                <Input
                  id="edit-title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter hub title"
                  required
                />
              </div>

              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter hub description"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="edit-category">Category</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectOption value="">Select Category</SelectOption>
                  {CATEGORIES.map(cat => (
                    <SelectOption key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-tags">Tags (comma separated)</Label>
                <Input
                  id="edit-tags"
                  value={formData.tags.join(', ')}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="training, wellness, compliance"
                />
                {/* Debug: Show current tags */}
                {formData.tags.length > 0 && (
                  <div className="mt-2 text-sm text-gray-600">
                    Current tags: {formData.tags.map(tag => `"${tag}"`).join(', ')}
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="edit-visibility">Visibility</Label>
                <Select 
                  value={formData.visibility} 
                  onValueChange={(value: 'public' | 'private' | 'restricted') => 
                    setFormData(prev => ({ ...prev, visibility: value }))
                  }
                >
                  {VISIBILITY_OPTIONS.map(option => (
                    <SelectOption key={option.value} value={option.value}>
                      {option.label} - {option.description}
                    </SelectOption>
                  ))}
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="edit-auto_translate">Auto-translate content</Label>
                  <p className="text-sm text-gray-500">Automatically translate content to all supported languages</p>
                </div>
                <Switch
                  id="edit-auto_translate"
                  checked={formData.auto_translate}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_translate: checked }))}
                />
              </div>

              <div>
                <Label>Hub Image (Optional)</Label>
                <FileUpload
                  accept="image/*"
                  onFileSelect={handleFileChange}
                  maxSize={5 * 1024 * 1024} // 5MB
                  className="mt-2"
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex items-center gap-2">
                <Edit className="w-4 h-4" />
                Update Hub
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Hub Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              {selectedHub && getHubTitle(selectedHub)}
            </DialogTitle>
          </DialogHeader>
          
          {selectedHub && (
            <div className="space-y-6">
              {/* Hub Image */}
              {selectedHub?.image_url && (
                <div className="w-full h-64 rounded-lg overflow-hidden">
                  <img 
                    src={selectedHub.image_url} 
                    alt={getHubTitle(selectedHub)}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.log('🔍 Modal image load error for hub:', selectedHub.hub_id);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* Hub Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Title</Label>
                    <p className="text-lg font-semibold">{getHubTitle(selectedHub)}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Description</Label>
                    <p className="text-gray-700 dark:text-gray-300">{getHubDescription(selectedHub)}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-500">Category</Label>
                    <Badge variant="outline">{selectedHub.category || 'Uncategorized'}</Badge>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-500">Tags</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedHub.tags && selectedHub.tags.length > 0 ? (
                        selectedHub.tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="secondary">
                            {tag}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-gray-500">No tags</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Status</Label>
                    <Badge className={`${getStatusColor(selectedHub.status)} border-0`}>
                      {selectedHub.status}
                    </Badge>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-500">Visibility</Label>
                    <div className="flex items-center gap-2">
                      {getVisibilityIcon(selectedHub.visibility)}
                      <span className="capitalize">{selectedHub.visibility}</span>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-500">Content Count</Label>
                    <p className="text-lg font-semibold">{selectedHub.content_count || 0}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-500">Created At</Label>
                    <p>{new Date(selectedHub.created_at).toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {/* Available Languages */}
              <div>
                <Label className="text-sm font-medium text-gray-500 mb-2 block">Available Languages</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.keys(selectedHub.content || {}).map((lang) => {
                    const language = LANGUAGES.find(l => l.value === lang);
                    return (
                      <Badge key={lang} variant="outline" className="flex items-center gap-1">
                        <span>{language?.flag}</span>
                        <span>{language?.label || lang}</span>
                      </Badge>
                    );
                  })}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => setIsViewModalOpen(false)}
                >
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    setIsViewModalOpen(false);
                    openEditModal(selectedHub);
                  }}
                  className="flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  Edit Hub
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HubManagement; 