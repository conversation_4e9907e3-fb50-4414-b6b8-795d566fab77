/**
 * Media List Component
 * Displays media content with filtering, search, pagination, and management actions
 * Based on the working content-list.tsx from the old frontend
 */

import React, { useState, useEffect, useCallback, useRef } from 'react'
import {
  Search,
  Filter,
  Grid,
  List,
  Play,
  Edit,
  Trash2,
  Download,
  Languages,
  Captions,
  Eye,
  RefreshCw,
  Plus,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  FileText,
  Clock,
  Calendar,
  User,
  Globe,
  Video,
  Image as ImageIcon,
  Share,
  BookOpen,
  Heart,
  ExternalLink
} from 'lucide-react'
import toast from 'react-hot-toast'

import { But<PERSON> } from '../ui/Button'
import { EmptyState } from '../ui/EmptyState'
import { Badge } from '../ui/Badge'
import type {
  MediaResponse,
  MediaListResponse,
  MediaFilters,
  MultilingualText,
  MultilingualTags
} from '../../types/media'
import {
  Language,
  MediaStatus,
  Gender,
  LANGUAGE_LABELS,
  STATUS_LABELS,
  GENDER_LABELS,
  getContentInLanguage
} from '../../types/media'
import { mediaService } from '../../services/mediaService'
import { Input } from '../ui/Input'
import { Select, SelectOption } from '../ui/Select'
import { Card, CardContent } from '../ui/Card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/Dialog'
import { useListContentQuery, useGetHubsQuery } from '../../features/API/hubApi'
import { useGetArticlesQuery } from '../../services/articlesApi'

interface MediaListProps {
  hubId?: string
  onCreateClick?: () => void
  onEditClick?: (mediaId: string) => void
  onViewClick?: (mediaId: string) => void
  showActions?: boolean
  showSearch?: boolean
  showFilters?: boolean
}

export function MediaList({ 
  hubId, 
  onCreateClick, 
  onEditClick, 
  onViewClick, 
  showActions = true, 
  showSearch = true, 
  showFilters = true 
}: MediaListProps) {
  const [content, setContent] = useState<MediaResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const [filters, setFilters] = useState<MediaFilters>({
    hub_id: hubId,
    language: Language.ENGLISH,
    search: ''
  })

  const [pagination, setPagination] = useState({
    limit: 20,
    total: 0,
    hasMore: false,
    lastKey: undefined as string | undefined
  })

  // RTK Query hooks
  const { data: contentData, isLoading: contentLoading, error: contentError } = useListContentQuery({
    hub_id: filters.hub_id || undefined,
    status: filters.status || undefined,
    search: filters.search || undefined
  })
  
  const { data: hubsData } = useGetHubsQuery({})

  // Extract data
  const videoContent = contentData?.data || []
  const articles = contentData?.data || []
  const hubs = hubsData?.data || []

  // Combine all content based on filters
  const getAllContent = () => {
    let allContent: any[] = []

    // Add video content
    if (filters.hub_id && (filters.search === 'all' || filters.search === 'video')) {
      const videoItems = videoContent.map((item: any) => ({
        ...item,
        type: 'video',
        title: item.title?.[filters.language] || item.title?.en || 'Untitled Video',
        description: item.description?.[filters.language] || item.description?.en || '',
        thumbnail: item.thumbnail_url,
        url: item.video_url,
        duration: item.duration,
        created_at: item.created_at,
        status: item.status
      }))
      allContent.push(...videoItems)
    }

    // Add articles
    if (filters.hub_id && (filters.search === 'all' || filters.search === 'article')) {
      const articleItems = articles.map((article: any) => ({
        ...article,
        type: 'article',
        title: article.content?.[filters.language]?.title || article.content?.en?.title || 'Untitled Article',
        description: article.content?.[filters.language]?.description || article.content?.en?.description || '',
        thumbnail: article.thumbnail_url,
        created_at: article.created_at,
        status: article.status
      }))
      allContent.push(...articleItems)
    }

    // Filter by search term
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      allContent = allContent.filter(item => 
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower)
      )
    }

    // Filter by status
    if (filters.status && filters.status !== 'all') {
      allContent = allContent.filter(item => item.status === filters.status)
    }

    // Sort by creation date (newest first)
    return allContent.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  }

  const filteredContent = getAllContent()

  // Helper functions
  const getHubName = (hubId: string) => {
    const hub = hubs.find((h: any) => h.hub_id === hubId)
    return hub?.content?.[filters.language]?.title || hub?.content?.en?.title || 'Unknown Hub'
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-5 h-5" />
      case 'article': return <FileText className="w-5 h-5" />
      case 'image': return <ImageIcon className="w-5 h-5" />
      default: return <FileText className="w-5 h-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'processing': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Fetch content
  const fetchContent = useCallback(async (reset = false) => {
    try {
      setLoading(true)
      setError(null)

      const params = {
        ...filters,
        limit: pagination.limit,
        last_key: reset ? undefined : pagination.lastKey
      }

      const response = await mediaService.listContent(params)

      if (reset) {
        setContent(response.data)
        setPagination(prev => ({
          ...prev,
          total: response.total,
          hasMore: response.has_more,
          lastKey: response.last_key
        }))
      } else {
        setContent(prev => [...prev, ...response.data])
        setPagination(prev => ({
          ...prev,
          total: response.total,
          hasMore: response.has_more,
          lastKey: response.last_key
        }))
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch content')
      toast.error('Failed to load media content')
    } finally {
      setLoading(false)
    }
  }, [filters, pagination.limit, pagination.lastKey])

  // Initial load and filter changes
  useEffect(() => {
    fetchContent(true)
  }, [filters])

  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }))
  }

  const handleFilterChange = (key: keyof MediaFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleDeleteContent = async (contentId: string) => {
    if (!confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
      return
    }

    try {
      await mediaService.deleteContent(contentId)
      setContent(prev => prev.filter(c => c.content_id !== contentId))
      toast.success('Content deleted successfully')
    } catch (err) {
      toast.error('Failed to delete content: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }

  const handleTranslateContent = async (contentId: string) => {
    try {
      await mediaService.translateContent(contentId, {
        target_languages: [Language.ARABIC, Language.HINDI],
        include_captions: true
      })
      toast.success('Translation started. The content will be available in other languages shortly.')
    } catch (err) {
      toast.error('Failed to start translation: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }

  const handleGenerateCaptions = async (contentId: string) => {
    try {
      await mediaService.generateCaptions(contentId, {
        languages: [Language.ENGLISH, Language.ARABIC, Language.HINDI],
        format: 'vtt'
      })
      toast.success('Caption generation started. Captions will be available shortly.')
    } catch (err) {
      toast.error('Failed to start caption generation: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }

  // Helper function to get language flag emoji
  const getLanguageFlag = (lang: string): string => {
    switch (lang) {
      case 'en': return '🇺🇸'
      case 'ar': return '🇸🇦'
      case 'hi': return '🇮🇳'
      default: return '🏳️'
    }
  }

  const ContentCard = ({ content: item }: { content: MediaResponse }) => {
    const title = getContentInLanguage(item.title, filters.language) as string
    const description = getContentInLanguage(item.description, filters.language) as string
    const tags = getContentInLanguage(item.tags, filters.language) as string[]

    return (
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl overflow-hidden hover:bg-white/20 transition-all duration-300 group">
        {/* Thumbnail */}
        <div className="relative aspect-video bg-gradient-to-br from-gray-900/50 to-gray-800/50">
          {item.thumbnail_url ? (
            <img
              src={item.thumbnail_url}
              alt={title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Play className="w-12 h-12 text-gold-400" />
            </div>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <Badge 
              variant={item.status === 'active' ? 'success' : 'secondary'}
              className="bg-black/50 backdrop-blur-sm"
            >
              {item.status}
            </Badge>
          </div>

          {/* Actions Overlay */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-3">
            <Button
              onClick={() => onViewClick?.(item.content_id)}
              size="sm"
              variant="secondary"
              className="bg-white/20 backdrop-blur-sm border-white/30 hover:bg-white/30"
            >
              <Eye className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => onEditClick?.(item.content_id)}
              size="sm"
              variant="secondary"
              className="bg-white/20 backdrop-blur-sm border-white/30 hover:bg-white/30"
            >
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => handleDeleteContent(item.content_id)}
              size="sm"
              variant="destructive"
              className="bg-red-500/20 backdrop-blur-sm border-red-300/30 hover:bg-red-500/30"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Content Info */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-lg font-semibold text-white truncate flex-1">
              {title}
            </h3>
            <div className="flex items-center space-x-1 ml-3">
              {item.available_languages.map(lang => (
                <span key={lang} className="text-sm" title={LANGUAGE_LABELS[lang as Language]}>
                  {getLanguageFlag(lang)}
                </span>
              ))}
            </div>
          </div>

          <p className="text-sm text-gray-300 mb-4 line-clamp-2">
            {description}
          </p>

          {/* Tags */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="secondary" className="bg-white/10 text-white border-white/20">
                  {tag}
                </Badge>
              ))}
              {tags.length > 3 && (
                <Badge variant="secondary" className="bg-white/10 text-white border-white/20">
                  +{tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Metadata */}
          <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
            <span>Gender: {item.gender}</span>
            <span>{new Date(item.created_at).toLocaleDateString()}</span>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-white/10">
            <div className="flex space-x-3">
              {item.subtitles_available.length === 0 && (
                <Button
                  onClick={() => handleGenerateCaptions(item.content_id)}
                  size="sm"
                  variant="ghost"
                  className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                >
                  <Captions className="w-4 h-4" />
                </Button>
              )}
              {item.available_languages.length === 1 && (
                <Button
                  onClick={() => handleTranslateContent(item.content_id)}
                  size="sm"
                  variant="ghost"
                  className="text-green-400 hover:text-green-300 hover:bg-green-500/10"
                >
                  <Languages className="w-4 h-4" />
                </Button>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              {item.subtitles_available.length > 0 && (
                <div className="flex items-center text-xs text-gray-400">
                  <Captions className="w-3 h-3 mr-1" />
                  {item.subtitles_available.length}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const ContentRow = ({ content: item }: { content: MediaResponse }) => {
    const title = getContentInLanguage(item.title, filters.language) as string
    const description = getContentInLanguage(item.description, filters.language) as string

    return (
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-4 hover:bg-white/20 transition-all duration-300">
        <div className="flex items-center space-x-4">
          {/* Thumbnail */}
          <div className="flex-shrink-0 w-20 h-12 bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded overflow-hidden">
            {item.thumbnail_url ? (
              <img
                src={item.thumbnail_url}
                alt={title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Play className="w-4 h-4 text-gold-400" />
              </div>
            )}
          </div>

          {/* Content Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-white truncate">
                  {title}
                </h3>
                <p className="text-sm text-gray-300 truncate">
                  {description}
                </p>
              </div>
              
              <div className="flex items-center space-x-3 ml-4">
                {/* Status */}
                <Badge 
                  variant={item.status === 'active' ? 'success' : 'secondary'}
                  className="bg-white/10"
                >
                  {item.status}
                </Badge>
                
                {/* Languages */}
                <div className="flex items-center space-x-1">
                  {item.available_languages.map(lang => (
                    <span key={lang} className="text-sm" title={LANGUAGE_LABELS[lang as Language]}>
                      {getLanguageFlag(lang)}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Metadata Row */}
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center space-x-4 text-xs text-gray-400">
                <span>Gender: {item.gender}</span>
                <span>{new Date(item.created_at).toLocaleDateString()}</span>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-1">
                <Button
                  onClick={() => onViewClick?.(item.content_id)}
                  size="sm"
                  variant="ghost"
                  className="text-gray-400 hover:text-white hover:bg-white/10"
                >
                  <Eye className="w-4 h-4" />
                </Button>
                <Button
                  onClick={() => onEditClick?.(item.content_id)}
                  size="sm"
                  variant="ghost"
                  className="text-gray-400 hover:text-white hover:bg-white/10"
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  onClick={() => handleDeleteContent(item.content_id)}
                  size="sm"
                  variant="ghost"
                  className="text-gray-400 hover:text-red-400 hover:bg-red-500/10"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Media Content</h1>
          <p className="text-gray-300">Manage your media content with multi-language support</p>
        </div>
        <Button
          onClick={onCreateClick}
          className="bg-gradient-to-r from-gold-600 to-gold-500 hover:from-gold-700 hover:to-gold-600 text-white border-0"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Content
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search content..."
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
            />
          </div>

          {/* Language Filter */}
          <select
            value={filters.language}
            onChange={(e) => handleFilterChange('language', e.target.value as Language)}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            {Object.entries(LANGUAGE_LABELS).map(([value, label]) => (
              <option key={value} value={value} className="bg-gray-800 text-white">{label}</option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={filters.status || ''}
            onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            <option value="" className="bg-gray-800 text-white">All Status</option>
            {Object.entries(STATUS_LABELS).map(([value, label]) => (
              <option key={value} value={value} className="bg-gray-800 text-white">{label}</option>
            ))}
          </select>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setViewMode('grid')}
              size="sm"
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              className={viewMode === 'grid' ? 'bg-gold-600 hover:bg-gold-700' : 'text-gray-400 hover:text-white hover:bg-white/10'}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => setViewMode('list')}
              size="sm"
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              className={viewMode === 'list' ? 'bg-gold-600 hover:bg-gold-700' : 'text-gray-400 hover:text-white hover:bg-white/10'}
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => fetchContent(true)}
              size="sm"
              variant="ghost"
              className="text-gray-400 hover:text-white hover:bg-white/10"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content Grid/List */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <p className="text-red-300">{error}</p>
        </div>
      )}

      {loading && content.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-6 h-6 animate-spin text-gold-500" />
          <span className="ml-2 text-gray-300">Loading content...</span>
        </div>
      ) : content.length === 0 ? (
        <EmptyState
          title="No content found"
          description="Get started by creating your first media content"
          action={{
            label: "Create Content",
            onClick: onCreateClick
          }}
        />
      ) : (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {content.map(item => (
                <ContentCard key={item.content_id} content={item} />
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {content.map(item => (
                <ContentRow key={item.content_id} content={item} />
              ))}
            </div>
          )}

          {/* Load More */}
          {pagination.hasMore && (
            <div className="text-center">
              <Button
                onClick={() => fetchContent(false)}
                disabled={loading}
                variant="secondary"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                {loading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

interface MediaPlayerProps {
  videoUrl: string;
  thumbnailUrl?: string;
  title: string;
  subtitleUrls?: Record<string, string>;
  onClose: () => void;
}

const MediaPlayer: React.FC<MediaPlayerProps> = ({ 
  videoUrl, 
  thumbnailUrl, 
  title, 
  subtitleUrls,
  onClose 
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [selectedSubtitle, setSelectedSubtitle] = useState('off');

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const toggleFullscreen = () => {
    if (videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        videoRef.current.requestFullscreen();
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="relative bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        src={videoUrl}
        poster={thumbnailUrl}
        className="w-full h-full max-h-[70vh] object-contain"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      >
        {subtitleUrls && Object.entries(subtitleUrls).map(([lang, url]) => (
          <track
            key={lang}
            kind="subtitles"
            src={url}
            srcLang={lang}
            label={LANGUAGE_LABELS[lang as Language] || lang}
            default={lang === 'en'}
          />
        ))}
      </video>

      {/* Video Controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        <div className="flex items-center gap-4 text-white">
          <Button
            size="sm"
            variant="ghost"
            onClick={togglePlay}
            className="text-white hover:bg-white/20"
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={toggleMute}
            className="text-white hover:bg-white/20"
          >
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </Button>

          <div className="flex-1 flex items-center gap-2">
            <span className="text-sm">{formatTime(currentTime)}</span>
            <div className="flex-1 bg-white/20 rounded-full h-1 relative">
              <div 
                className="bg-white rounded-full h-1 transition-all duration-100"
                style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
            <span className="text-sm">{formatTime(duration)}</span>
          </div>

          {/* Subtitle Selection */}
          {subtitleUrls && Object.keys(subtitleUrls).length > 0 && (
            <Select 
              value={selectedSubtitle} 
              onValueChange={setSelectedSubtitle}
              className="w-24 text-sm"
            >
              <SelectOption value="off">Off</SelectOption>
              {Object.keys(subtitleUrls).map(lang => (
                <SelectOption key={lang} value={lang}>
                  {LANGUAGE_LABELS[lang as Language] || lang}
                </SelectOption>
              ))}
            </Select>
          )}

          <Button
            size="sm"
            variant="ghost"
            onClick={toggleFullscreen}
            className="text-white hover:bg-white/20"
          >
            <Maximize className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Close Button */}
      <Button
        size="sm"
        variant="ghost"
        onClick={onClose}
        className="absolute top-4 right-4 text-white hover:bg-white/20 z-10"
      >
        ×
      </Button>
    </div>
  );
};

interface ArticleViewerProps {
  article: any;
  language: string;
  onClose: () => void;
}

const ArticleViewer: React.FC<ArticleViewerProps> = ({ article, language, onClose }) => {
  const content = article.content?.[language] || article.content?.en || {};
  const title = content.title || 'Untitled Article';
  const description = content.description || '';
  const body = content.content || content.body || '';
  
  return (
    <div className="max-w-4xl mx-auto">
      {/* Article Header */}
      <div className="mb-8">
        {article.thumbnail_url && (
          <div className="w-full h-64 mb-6 rounded-lg overflow-hidden">
            <img 
              src={article.thumbnail_url} 
              alt={title}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Calendar className="w-4 h-4" />
            <span>{new Date(article.created_at).toLocaleDateString()}</span>
            {article.author && (
              <>
                <span>•</span>
                <User className="w-4 h-4" />
                <span>{article.author}</span>
              </>
            )}
            {article.category && (
              <>
                <span>•</span>
                <Badge variant="outline">{article.category}</Badge>
              </>
            )}
          </div>

          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {title}
          </h1>
          
          {description && (
            <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
              {description}
            </p>
          )}

          {article.tags && article.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag: string, index: number) => (
                <Badge key={index} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Article Content */}
      <div className="prose prose-lg max-w-none dark:prose-invert">
        <div 
          dangerouslySetInnerHTML={{ __html: body }}
          className="text-gray-700 dark:text-gray-300 leading-relaxed"
        />
      </div>

      {/* Article Actions */}
      <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button size="sm" variant="outline" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Like
            </Button>
            <Button size="sm" variant="outline" className="flex items-center gap-2">
              <Share className="w-4 h-4" />
              Share
            </Button>
            <Button size="sm" variant="outline" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Download
            </Button>
          </div>
          
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

 