import React, { useState } from "react";
import { 
  FaBuilding, 
  FaCog, 
  FaEdit, 
  FaTrash, 
  FaPlus, 
  FaEye,
  FaCalendarAlt,
  FaToggleOn,
  FaToggleOff,
  FaUsers,
  FaImage,
  FaSave,
  FaTimes
} from "react-icons/fa";
import { 
  Card,
  Button,
  Input,
  Select,
  Badge,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  GradientText,
  PageLoader,
  Container,
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from "../ui";
import { 
  useGetAllCompanySettingsQuery,
  useCreateCompanySettingsMutation,
  useUpdateCompanySettingsByIdMutation,
  useDeleteCompanySettingsByIdMutation,
  useGetCompanySettingsWithConsultantsQuery,
  type CompanySettings,
  type CompanySettingsCreate,
  type CompanySettingsUpdate
} from "../../store/api/companyApi";
import toast from "react-hot-toast";

interface CompanySettingsManagerProps {
  companyId?: string; // If provided, show settings for specific company
  showCreateButton?: boolean;
  variant?: "full" | "embedded";
}

const CompanySettingsManager: React.FC<CompanySettingsManagerProps> = ({
  companyId,
  showCreateButton = true,
  variant = "full"
}) => {
  // State management
  const [selectedSettings, setSelectedSettings] = useState<CompanySettings | null>(null);
  const [modals, setModals] = useState({
    view: false,
    create: false,
    edit: false,
    delete: false,
  });

  // Form state for create/edit
  const [formData, setFormData] = useState<CompanySettingsCreate>({
    companyId: companyId || '',
    name: '',
    startDate: '',
    endDate: '',
    isShareData: false,
    isEnableSupport: false,
    consultants: [],
  });

  // API hooks
  const { 
    data: allSettings = [], 
    isLoading: settingsLoading, 
    error: settingsError,
    refetch: refetchSettings
  } = useGetAllCompanySettingsQuery();

  const [createSettings, { isLoading: isCreating }] = useCreateCompanySettingsMutation();
  const [updateSettings, { isLoading: isUpdating }] = useUpdateCompanySettingsByIdMutation();
  const [deleteSettings, { isLoading: isDeleting }] = useDeleteCompanySettingsByIdMutation();

  // Filter settings by company if companyId is provided
  const filteredSettings = companyId 
    ? allSettings.filter(setting => setting.companyId === companyId)
    : allSettings;

  // Modal handlers
  const openModal = (modalType: keyof typeof modals, settings?: CompanySettings) => {
    if (settings) {
      setSelectedSettings(settings);
      if (modalType === 'edit') {
        setFormData({
          companyId: settings.companyId,
          name: settings.name,
          logo: settings.logo,
          noc: settings.noc,
          startDate: settings.startDate,
          endDate: settings.endDate,
          isShareData: settings.isShareData,
          isEnableSupport: settings.isEnableSupport,
          preWebinarBanner: settings.preWebinarBanner,
          postWebinarBanner: settings.postWebinarBanner,
          consultants: settings.consultants,
        });
      }
    }
    setModals(prev => ({ ...prev, [modalType]: true }));
  };

  const closeModal = (modalType: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [modalType]: false }));
    setSelectedSettings(null);
    if (modalType === 'create' || modalType === 'edit') {
      setFormData({
        companyId: companyId || '',
        name: '',
        startDate: '',
        endDate: '',
        isShareData: false,
        isEnableSupport: false,
        consultants: [],
      });
    }
  };

  // Form handlers
  const handleFormChange = (field: keyof CompanySettingsCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCreate = async () => {
    try {
      await createSettings(formData).unwrap();
      toast.success('Company settings created successfully');
      closeModal('create');
      refetchSettings();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to create company settings');
      console.error('Create error:', error);
    }
  };

  const handleUpdate = async () => {
    if (!selectedSettings) return;

    try {
      const updateData: CompanySettingsUpdate = { ...formData };
      delete (updateData as any).companyId; // Remove companyId from update data

      await updateSettings({ 
        settingsId: selectedSettings.id, 
        data: updateData 
      }).unwrap();
      
      toast.success('Company settings updated successfully');
      closeModal('edit');
      refetchSettings();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to update company settings');
      console.error('Update error:', error);
    }
  };

  const handleDelete = async () => {
    if (!selectedSettings) return;

    try {
      await deleteSettings(selectedSettings.id).unwrap();
      toast.success('Company settings deleted successfully');
      closeModal('delete');
      refetchSettings();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to delete company settings');
      console.error('Delete error:', error);
    }
  };

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (settings: CompanySettings) => {
    const now = new Date();
    const startDate = new Date(settings.startDate);
    const endDate = new Date(settings.endDate);

    if (now < startDate) {
      return <Badge variant="warning" size="sm">Pending</Badge>;
    } else if (now > endDate) {
      return <Badge variant="destructive" size="sm">Expired</Badge>;
    } else {
      return <Badge variant="success" size="sm">Active</Badge>;
    }
  };

  // Loading state
  if (settingsLoading) {
    return <PageLoader message="Loading company settings..." />;
  }

  // Error state
  if (settingsError) {
    return (
      <Card className="p-8 text-center">
        <div className="text-red-600 mb-4">
          <FaCog className="mx-auto h-16 w-16 mb-4" />
          <h3 className="text-xl font-semibold mb-2">Failed to Load Settings</h3>
          <p className="text-sm">Please try refreshing the page</p>
        </div>
        <Button onClick={() => refetchSettings()} variant="outline">
          Retry
        </Button>
      </Card>
    );
  }

  const renderContent = () => (
    <>
      {/* Header */}
      {variant === "full" && (
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8">
          <div>
            <GradientText
              gradient="gold"
              size="3xl"
              weight="bold"
              className="mb-2"
            >
              Company Settings Management
            </GradientText>
            <p className="text-lg text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Manage company configurations and preferences
            </p>
          </div>
          
          {showCreateButton && (
            <Button
              variant="default"
              size="default"
              onClick={() => openModal('create')}
              leftIcon={<FaPlus className="w-4 h-4" />}
              disabled={isCreating}
              className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700 text-white shadow-lg shadow-sehatti-gold-500/25"
            >
              Create Settings
            </Button>
          )}
        </div>
      )}

      {/* Settings Table */}
      <Card className="overflow-hidden">
        <div className="p-6 border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
          <div className="flex items-center gap-3">
            <FaCog className="w-5 h-5 text-sehatti-gold-600" />
            <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              Company Settings ({filteredSettings.length})
            </h3>
          </div>
        </div>

        {filteredSettings.length === 0 ? (
          <div className="p-8 text-center">
            <FaBuilding className="mx-auto h-16 w-16 text-sehatti-warm-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-2">
              No Company Settings Found
            </h3>
            <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-6">
              {companyId ? 'This company has no settings configured yet.' : 'No company settings have been created yet.'}
            </p>
            {showCreateButton && (
              <Button
                onClick={() => openModal('create')}
                leftIcon={<FaPlus className="w-4 h-4" />}
                className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700 text-white"
              >
                Create First Settings
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Data Sharing</TableHead>
                  <TableHead>Support</TableHead>
                  <TableHead>Consultants</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSettings.map((settings) => (
                  <TableRow key={settings.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-sehatti-gold-100 dark:bg-sehatti-gold-900/20 rounded-lg">
                          <FaBuilding className="w-4 h-4 text-sehatti-gold-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                            {settings.name}
                          </p>
                          <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                            ID: {settings.companyId}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(settings)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="flex items-center gap-1 text-sehatti-warm-gray-600">
                          <FaCalendarAlt className="w-3 h-3" />
                          {formatDate(settings.startDate)}
                        </div>
                        <div className="text-sehatti-warm-gray-500">
                          to {formatDate(settings.endDate)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {settings.isShareData ? (
                        <FaToggleOn className="w-5 h-5 text-green-600" />
                      ) : (
                        <FaToggleOff className="w-5 h-5 text-sehatti-warm-gray-400" />
                      )}
                    </TableCell>
                    <TableCell>
                      {settings.isEnableSupport ? (
                        <FaToggleOn className="w-5 h-5 text-green-600" />
                      ) : (
                        <FaToggleOff className="w-5 h-5 text-sehatti-warm-gray-400" />
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <FaUsers className="w-3 h-3 text-sehatti-warm-gray-500" />
                        <span className="text-sm text-sehatti-warm-gray-600">
                          {settings.consultants.length}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openModal('view', settings)}
                          className="text-sehatti-warm-gray-600 hover:text-sehatti-gold-600"
                        >
                          <FaEye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openModal('edit', settings)}
                          className="text-sehatti-warm-gray-600 hover:text-blue-600"
                        >
                          <FaEdit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openModal('delete', settings)}
                          className="text-sehatti-warm-gray-600 hover:text-red-600"
                        >
                          <FaTrash className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </Card>

      {/* View Modal */}
      <Modal
        open={modals.view}
        onClose={() => closeModal('view')}
        size="lg"
      >
        {selectedSettings && (
          <>
            <ModalHeader>
              <div className="flex items-center gap-4">
                <div className="p-3 bg-sehatti-gold-100 dark:bg-sehatti-gold-900/20 rounded-lg">
                  <FaBuilding className="w-6 h-6 text-sehatti-gold-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    {selectedSettings.name}
                  </h3>
                  {getStatusBadge(selectedSettings)}
                </div>
              </div>
            </ModalHeader>
            <ModalContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      Company ID
                    </label>
                    <p className="text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      {selectedSettings.companyId}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      NOC
                    </label>
                    <p className="text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      {selectedSettings.noc || 'Not specified'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      Start Date
                    </label>
                    <p className="text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      {formatDate(selectedSettings.startDate)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      End Date
                    </label>
                    <p className="text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      {formatDate(selectedSettings.endDate)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      Data Sharing
                    </label>
                    <div className="flex items-center gap-2">
                      {selectedSettings.isShareData ? (
                        <>
                          <FaToggleOn className="w-5 h-5 text-green-600" />
                          <span className="text-green-600">Enabled</span>
                        </>
                      ) : (
                        <>
                          <FaToggleOff className="w-5 h-5 text-sehatti-warm-gray-400" />
                          <span className="text-sehatti-warm-gray-600">Disabled</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-1">
                      Support
                    </label>
                    <div className="flex items-center gap-2">
                      {selectedSettings.isEnableSupport ? (
                        <>
                          <FaToggleOn className="w-5 h-5 text-green-600" />
                          <span className="text-green-600">Enabled</span>
                        </>
                      ) : (
                        <>
                          <FaToggleOff className="w-5 h-5 text-sehatti-warm-gray-400" />
                          <span className="text-sehatti-warm-gray-600">Disabled</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                    Consultants ({selectedSettings.consultants.length})
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {selectedSettings.consultants.length > 0 ? (
                      selectedSettings.consultants.map((consultantId, index) => (
                        <Badge key={index} variant="secondary" size="sm">
                          {consultantId}
                        </Badge>
                      ))
                    ) : (
                      <p className="text-sehatti-warm-gray-500">No consultants assigned</p>
                    )}
                  </div>
                </div>
              </div>
            </ModalContent>
            <ModalFooter>
              <Button
                variant="outline"
                onClick={() => closeModal('view')}
              >
                Close
              </Button>
            </ModalFooter>
          </>
        )}
      </Modal>

      {/* Create/Edit Modal */}
      <Modal
        open={modals.create || modals.edit}
        onClose={() => closeModal(modals.create ? 'create' : 'edit')}
        size="lg"
      >
        <ModalHeader>
          <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
            {modals.create ? 'Create Company Settings' : 'Edit Company Settings'}
          </h3>
        </ModalHeader>
        <ModalContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Company ID *
                </label>
                <Input
                  value={formData.companyId}
                  onChange={(e) => handleFormChange('companyId', e.target.value)}
                  placeholder="Enter company ID"
                  disabled={modals.edit} // Can't change company ID when editing
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Company Name *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  placeholder="Enter company name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  NOC
                </label>
                <Input
                  value={formData.noc || ''}
                  onChange={(e) => handleFormChange('noc', e.target.value)}
                  placeholder="Enter NOC"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Logo URL
                </label>
                <Input
                  value={formData.logo || ''}
                  onChange={(e) => handleFormChange('logo', e.target.value)}
                  placeholder="Enter logo URL"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Start Date *
                </label>
                <Input
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) => handleFormChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  End Date *
                </label>
                <Input
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) => handleFormChange('endDate', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Pre-Webinar Banner URL
                </label>
                <Input
                  value={formData.preWebinarBanner || ''}
                  onChange={(e) => handleFormChange('preWebinarBanner', e.target.value)}
                  placeholder="Enter pre-webinar banner URL"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                  Post-Webinar Banner URL
                </label>
                <Input
                  value={formData.postWebinarBanner || ''}
                  onChange={(e) => handleFormChange('postWebinarBanner', e.target.value)}
                  placeholder="Enter post-webinar banner URL"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={formData.isShareData}
                    onChange={(e) => handleFormChange('isShareData', e.target.checked)}
                    className="w-4 h-4 text-sehatti-gold-600 bg-sehatti-warm-gray-100 border-sehatti-warm-gray-300 rounded focus:ring-sehatti-gold-500"
                  />
                  <span className="text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300">
                    Enable Data Sharing
                  </span>
                </label>
              </div>
              <div>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={formData.isEnableSupport}
                    onChange={(e) => handleFormChange('isEnableSupport', e.target.checked)}
                    className="w-4 h-4 text-sehatti-gold-600 bg-sehatti-warm-gray-100 border-sehatti-warm-gray-300 rounded focus:ring-sehatti-gold-500"
                  />
                  <span className="text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300">
                    Enable Support
                  </span>
                </label>
              </div>
            </div>
          </div>
        </ModalContent>
        <ModalFooter>
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => closeModal(modals.create ? 'create' : 'edit')}
              disabled={isCreating || isUpdating}
            >
              <FaTimes className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={modals.create ? handleCreate : handleUpdate}
              isLoading={isCreating || isUpdating}
              disabled={isCreating || isUpdating || !formData.name || !formData.companyId || !formData.startDate || !formData.endDate}
              className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700 text-white"
            >
              <FaSave className="w-4 h-4 mr-2" />
              {modals.create ? 'Create Settings' : 'Update Settings'}
            </Button>
          </div>
        </ModalFooter>
      </Modal>

      {/* Delete Modal */}
      <Modal
        open={modals.delete}
        onClose={() => closeModal('delete')}
        size="md"
      >
        {selectedSettings && (
          <>
            <ModalHeader>
              <h3 className="text-xl font-semibold text-red-600">
                Delete Company Settings
              </h3>
            </ModalHeader>
            <ModalContent>
              <div className="space-y-4">
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <FaTrash className="w-5 h-5 text-red-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-red-900 dark:text-red-100">
                        Are you sure you want to delete these settings?
                      </h4>
                      <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                        This action cannot be undone. All configuration data for "{selectedSettings.name}" will be permanently deleted.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <FaBuilding className="w-5 h-5 text-sehatti-gold-600" />
                    <div>
                      <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                        {selectedSettings.name}
                      </p>
                      <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                        Company ID: {selectedSettings.companyId}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ModalContent>
            <ModalFooter>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => closeModal('delete')}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleDelete}
                  isLoading={isDeleting}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <FaTrash className="w-4 h-4 mr-2" />
                  Delete Settings
                </Button>
              </div>
            </ModalFooter>
          </>
        )}
      </Modal>
    </>
  );

  return variant === "full" ? (
    <Container size="full" className="py-8">
      {renderContent()}
    </Container>
  ) : (
    renderContent()
  );
};

export default CompanySettingsManager; 