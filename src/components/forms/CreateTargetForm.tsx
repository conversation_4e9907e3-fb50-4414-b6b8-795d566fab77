import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { <PERSON>a<PERSON><PERSON>seye, Fa<PERSON>anguage, FaSitemap } from "react-icons/fa";
import {
  Button,
  Input,
  Select,
  Card,
  Modal,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Spinner
} from "@/components/ui";
import { useCreateQATargetMutation, useGetQADivisionsQuery } from "@/features/API/qaServiceApi";

interface CreateTargetFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  preSelectedDivisionId?: string;
}

interface TargetFormData {
  division_id: string;
  name: string;
  description: string;
}

const CreateTargetForm: React.FC<CreateTargetFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  preSelectedDivisionId
}) => {
  const [formData, setFormData] = useState<TargetFormData>({
    division_id: preSelectedDivisionId || "",
    name: "",
    description: ""
  });

  const [createTarget, { isLoading }] = useCreateQATargetMutation();
  const { data: divisionsData, isLoading: divisionsLoading } = useGetQADivisionsQuery({});

  const divisions = divisionsData?.items || [];

  const handleInputChange = (field: keyof TargetFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.division_id) {
      toast.error("Please select a parent division");
      return;
    }

    if (!formData.name.trim()) {
      toast.error("Target name is required");
      return;
    }

    try {
      const targetData = {
        division_id: formData.division_id,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        status: "active"
      };

      await createTarget(targetData).unwrap();
      
      toast.success("Target created successfully");
      
      // Reset form
      setFormData({
        division_id: preSelectedDivisionId || "",
        name: "",
        description: ""
      });
      
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Create target error:", error);
      toast.error(error?.data?.message || "Failed to create target");
    }
  };

  const handleClose = () => {
    setFormData({
      division_id: preSelectedDivisionId || "",
      name: "",
      description: ""
    });
    onClose();
  };

  const selectedDivision = divisions.find(d => d.id === formData.division_id);

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
            <FaBullseye className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Target
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Add a new target (subdivision) to organize questions within a division
            </p>
          </div>
        </div>
      </ModalHeader>

      <ModalContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Parent Division Selection */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Parent Division *
            </label>
            {divisionsLoading ? (
              <div className="flex items-center gap-2 p-3 border border-gray-300 rounded-lg">
                <Spinner size="sm" />
                <span className="text-gray-500">Loading divisions...</span>
              </div>
            ) : (
              <Select
                value={formData.division_id}
                onChange={(value) => handleInputChange("division_id", value)}
                placeholder="Select a parent division"
                className="w-full"
                required
                disabled={isLoading || !!preSelectedDivisionId}
              >
                {divisions.map((division) => (
                  <option key={division.id} value={division.id}>
                    {division.name.en || division.name}
                  </option>
                ))}
              </Select>
            )}
            
            {selectedDivision && (
              <Card className="p-3 bg-gray-50 border-gray-200">
                <div className="flex items-center gap-2">
                  <FaSitemap className="w-4 h-4 text-gray-600" />
                  <span className="text-sm text-gray-700">
                    Selected: <span className="font-medium">{selectedDivision.name.en || selectedDivision.name}</span>
                  </span>
                </div>
              </Card>
            )}
          </div>

          {/* Target Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Target Name *
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter target name in English"
              className="w-full"
              required
              disabled={isLoading}
            />
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <FaLanguage className="w-3 h-3" />
              <span>
                AI will automatically translate this to Arabic and Hindi
              </span>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Description
              <span className="text-gray-400 font-normal ml-1">(Optional)</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter target description (optional)"
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none transition-all duration-200"
              disabled={isLoading}
            />
          </div>

          {/* Info Card */}
          <Card className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2 flex-shrink-0" />
              <div className="text-sm text-indigo-800">
                <p className="font-medium mb-1">Target Structure</p>
                <p className="text-indigo-700">
                  Targets (subdivisions) provide more granular organization within divisions. Questions and assessments can be assigned to specific targets for better categorization.
                </p>
              </div>
            </div>
          </Card>
        </form>
      </ModalContent>

      <ModalFooter>
        <div className="flex gap-3 justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !formData.division_id || !formData.name.trim()}
            className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Spinner size="sm" />
                <span>Creating...</span>
              </div>
            ) : (
              "Create Target"
            )}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default CreateTargetForm; 