import React, { useState, use<PERSON>emo } from "react";
import { toast } from "react-hot-toast";
import { FaQuestionCircle, FaPlus, FaTrash, FaLanguage } from "react-icons/fa";
import {
  But<PERSON>, Input, Select, Card, Badge, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>box
} from "@/components/ui";
import {
  useCreateQAQuestionMutation, useGetQADivisionsQuery, useGetQATargetsQuery
} from "@/features/API/qaServiceApi";
import { useGetCompaniesWithConsultantsQuery } from "@/store/api";

interface CreateQuestionFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface QuestionOption {
  text: string;
  value: number;
}

interface QuestionFormData {
  division_id: string;
  target_id: string;
  question_text: string;
  question_type: string;
  assessment_type: string;
  options: QuestionOption[];
  corporate_ids: string[];
  status: string;
}

const QUESTION_TYPES = [
  { value: "multiple_choice", label: "Multiple Choice" },
  { value: "single_select", label: "Single Select" },
  { value: "text_input", label: "Text Input" },
  { value: "rating_scale", label: "Rating Scale" }
];

const ASSESSMENT_TYPES = [
  { value: "pre_assessment", label: "Pre Assessment" },
  { value: "post_assessment", label: "Post Assessment" },
  { value: "ongoing", label: "Ongoing" }
];

const CreateQuestionForm: React.FC<CreateQuestionFormProps> = ({
  isOpen, onClose, onSuccess
}) => {
  const [formData, setFormData] = useState<QuestionFormData>({
    division_id: "", target_id: "", question_text: "", question_type: "multiple_choice",
    assessment_type: "pre_assessment", options: [{ text: "", value: 0 }, { text: "", value: 10 }],
    corporate_ids: [], status: "draft"
  });

  const [corporateSearch, setCorporateSearch] = useState("");
  const [createQuestion, { isLoading }] = useCreateQAQuestionMutation();
  const { data: divisionsData } = useGetQADivisionsQuery({});
  const { data: targetsData } = useGetQATargetsQuery({});
  const { data: corporatesData } = useGetCompaniesWithConsultantsQuery();

  const divisions = divisionsData?.items || [];
  const allTargets = targetsData?.items || [];
  const corporates = corporatesData?.data?.data || corporatesData?.data || [];

  const availableTargets = useMemo(() => {
    return allTargets.filter(target => target.division_id === formData.division_id);
  }, [allTargets, formData.division_id]);

  const filteredCorporates = useMemo(() => {
    const unselected = corporates.filter(corp => !formData.corporate_ids.includes(corp._id || corp.id));
    if (!corporateSearch.trim()) return unselected;
    const searchLower = corporateSearch.toLowerCase();
    return unselected.filter(corp => 
      corp.name?.toLowerCase().includes(searchLower) || corp.email?.toLowerCase().includes(searchLower)
    );
  }, [corporates, corporateSearch, formData.corporate_ids]);

  const selectedCorporates = useMemo(() => {
    return corporates.filter(corp => formData.corporate_ids.includes(corp._id || corp.id));
  }, [corporates, formData.corporate_ids]);

  const handleInputChange = (field: keyof QuestionFormData, value: any) => {
    if (field === "division_id") {
      setFormData(prev => ({ ...prev, [field]: value, target_id: "" }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const addOption = () => {
    setFormData(prev => ({
      ...prev, options: [...prev.options, { text: "", value: prev.options.length * 10 }]
    }));
  };

  const removeOption = (index: number) => {
    if (formData.options.length > 1) {
      setFormData(prev => ({ ...prev, options: prev.options.filter((_, i) => i !== index) }));
    }
  };

  const updateOption = (index: number, field: keyof QuestionOption, value: string | number) => {
    setFormData(prev => ({
      ...prev, options: prev.options.map((opt, i) => i === index ? { ...opt, [field]: value } : opt)
    }));
  };

  const toggleCorporate = (corporateId: string) => {
    setFormData(prev => ({
      ...prev,
      corporate_ids: prev.corporate_ids.includes(corporateId)
        ? prev.corporate_ids.filter(id => id !== corporateId)
        : [...prev.corporate_ids, corporateId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.division_id || !formData.target_id || !formData.question_text.trim()) {
      toast.error("Please fill all required fields");
      return;
    }

    if (["multiple_choice", "single_select"].includes(formData.question_type)) {
      if (formData.options.length < 2 || formData.options.some(opt => !opt.text.trim())) {
        toast.error("At least 2 options with text are required");
        return;
      }
    }

    if (formData.corporate_ids.length === 0) {
      toast.error("Please select at least one corporate");
      return;
    }

    try {
      const questionData = {
        division_id: formData.division_id,
        target_id: formData.target_id,
        question_text: formData.question_text.trim(),
        question_type: formData.question_type,
        assessment_type: formData.assessment_type,
        options: formData.options.map((opt, index) => ({
          text: opt.text.trim(), value: opt.value, order: index
        })),
        corporate_ids: formData.corporate_ids,
        status: formData.status
      };

      await createQuestion(questionData).unwrap();
      toast.success("Question created successfully");
      
      setFormData({
        division_id: "", target_id: "", question_text: "", question_type: "multiple_choice",
        assessment_type: "pre_assessment", options: [{ text: "", value: 0 }, { text: "", value: 10 }],
        corporate_ids: [], status: "draft"
      });
      setCorporateSearch("");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Create question error:", error);
      toast.error(error?.data?.message || "Failed to create question");
    }
  };

  const needsOptions = ["multiple_choice", "single_select", "rating_scale"].includes(formData.question_type);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl">
      <ModalHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <FaQuestionCircle className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Create New Question</h2>
            <p className="text-sm text-gray-600 mt-1">Create a new question for assessments</p>
          </div>
        </div>
      </ModalHeader>

      <ModalContent className="max-h-[70vh] overflow-y-auto">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Select
              value={formData.division_id}
              onChange={(value) => handleInputChange("division_id", value)}
              placeholder="Select Division *"
              required
            >
              {divisions.map(div => (
                <option key={div.id} value={div.id}>{div.name.en || div.name}</option>
              ))}
            </Select>

            <Select
              value={formData.target_id}
              onChange={(value) => handleInputChange("target_id", value)}
              placeholder="Select Target *"
              disabled={!formData.division_id}
              required
            >
              {availableTargets.map(target => (
                <option key={target.id} value={target.id}>{target.name.en || target.name}</option>
              ))}
            </Select>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <Select
              value={formData.question_type}
              onChange={(value) => handleInputChange("question_type", value)}
              required
            >
              {QUESTION_TYPES.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </Select>

            <Select
              value={formData.assessment_type}
              onChange={(value) => handleInputChange("assessment_type", value)}
              required
            >
              {ASSESSMENT_TYPES.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </Select>

            <Select
              value={formData.status}
              onChange={(value) => handleInputChange("status", value)}
              required
            >
              <option value="draft">Draft</option>
              <option value="active">Active</option>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Question Text *</label>
            <textarea
              value={formData.question_text}
              onChange={(e) => handleInputChange("question_text", e.target.value)}
              placeholder="Enter your question in English..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              required
            />
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <FaLanguage className="w-3 h-3" />
              <span>AI will automatically translate this to Arabic and Hindi</span>
            </div>
          </div>

          {needsOptions && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Answer Options</h3>
                <Button type="button" onClick={addOption} variant="outline" size="sm">
                  <FaPlus className="w-3 h-3 mr-2" /> Add Option
                </Button>
              </div>

              {formData.options.map((option, index) => (
                <Card key={index} className="p-4">
                  <div className="flex gap-4 items-end">
                    <div className="flex-1">
                      <Input
                        type="text"
                        value={option.text}
                        onChange={(e) => updateOption(index, "text", e.target.value)}
                        placeholder={`Option ${index + 1} text`}
                        required
                      />
                    </div>
                    <div className="w-24">
                      <Input
                        type="number"
                        value={option.value}
                        onChange={(e) => updateOption(index, "value", parseInt(e.target.value) || 0)}
                        placeholder="Value"
                        required
                      />
                    </div>
                    {formData.options.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeOption(index)}
                        variant="outline"
                        size="sm"
                        className="text-red-600"
                      >
                        <FaTrash className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Corporate Assignment *</h3>
              <Badge variant="secondary">{formData.corporate_ids.length} selected</Badge>
            </div>

            {selectedCorporates.length > 0 && (
              <Card className="p-4 bg-blue-50">
                <div className="flex flex-wrap gap-2">
                  {selectedCorporates.map(corp => (
                    <Badge key={corp._id || corp.id} variant="default" className="bg-blue-100 text-blue-800">
                      {corp.name}
                      <button
                        type="button"
                        onClick={() => toggleCorporate(corp._id || corp.id)}
                        className="ml-1 hover:bg-blue-200 rounded-full p-1"
                      >
                        <FaTrash className="w-2 h-2" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </Card>
            )}

            <Input
              type="text"
              value={corporateSearch}
              onChange={(e) => setCorporateSearch(e.target.value)}
              placeholder="Search corporates by name or email..."
            />

            <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
              {filteredCorporates.slice(0, 10).map(corp => (
                <div
                  key={corp._id || corp.id}
                  className="p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3"
                  onClick={() => toggleCorporate(corp._id || corp.id)}
                >
                  <Checkbox
                    checked={formData.corporate_ids.includes(corp._id || corp.id)}
                    onChange={() => toggleCorporate(corp._id || corp.id)}
                  />
                  <div>
                    <div className="font-medium text-gray-900">{corp.name}</div>
                    <div className="text-sm text-gray-600">{corp.email}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </form>
      </ModalContent>

      <ModalFooter>
        <div className="flex gap-3 justify-end">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !formData.division_id || !formData.target_id || !formData.question_text.trim() || formData.corporate_ids.length === 0}
            className="bg-gradient-to-r from-green-500 to-green-600"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Spinner size="sm" />
                <span>Creating...</span>
              </div>
            ) : (
              "Create Question"
            )}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default CreateQuestionForm; 