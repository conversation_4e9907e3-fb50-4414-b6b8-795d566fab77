import React, { useState, useMemo } from "react";
import { toast } from "react-hot-toast";
import { FaC<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaTrash, <PERSON>a<PERSON>heck } from "react-icons/fa";
import {
  Button,
  Input,
  Select,
  Card,
  Badge,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox
} from "@/components/ui";
import {
  useCreateQAAssessmentMutation,
  useGetQADivisionsQuery,
  useGetQATargetsQuery,
  useGetQAQuestionsQuery
} from "@/features/API/qaServiceApi";
import { useGetCompaniesWithConsultantsQuery } from "@/store/api";

interface CreateAssessmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface AssessmentFormData {
  name: string;
  description: string;
  assessment_type: string;
  division_id: string;
  target_id: string;
  question_ids: string[];
  corporate_ids: string[];
  is_active: boolean;
}

const ASSESSMENT_TYPES = [
  { value: "pre_assessment", label: "Pre Assessment" },
  { value: "post_assessment", label: "Post Assessment" },
  { value: "ongoing", label: "Ongoing Assessment" },
  { value: "final_assessment", label: "Final Assessment" }
];

const CreateAssessmentForm: React.FC<CreateAssessmentFormProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<AssessmentFormData>({
    name: "",
    description: "",
    assessment_type: "pre_assessment",
    division_id: "",
    target_id: "",
    question_ids: [],
    corporate_ids: [],
    is_active: true
  });

  const [questionSearch, setQuestionSearch] = useState("");
  const [corporateSearch, setCorporateSearch] = useState("");

  const [createAssessment, { isLoading }] = useCreateQAAssessmentMutation();
  const { data: divisionsData } = useGetQADivisionsQuery({});
  const { data: targetsData } = useGetQATargetsQuery({});
  const { data: questionsData } = useGetQAQuestionsQuery({});
  const { data: corporatesData } = useGetCompaniesWithConsultantsQuery();

  const divisions = divisionsData?.items || [];
  const allTargets = targetsData?.items || [];
  const allQuestions = questionsData?.items || [];
  const corporates = corporatesData?.data?.data || corporatesData?.data || [];

  // Filter targets based on selected division
  const availableTargets = useMemo(() => {
    return allTargets.filter(target => target.division_id === formData.division_id);
  }, [allTargets, formData.division_id]);

  // Filter questions based on selected division and target
  const availableQuestions = useMemo(() => {
    return allQuestions.filter(question => {
      const matchesDivision = !formData.division_id || question.division_id === formData.division_id;
      const matchesTarget = !formData.target_id || question.target_id === formData.target_id;
      const matchesAssessmentType = question.assessment_type === formData.assessment_type;
      return matchesDivision && matchesTarget && matchesAssessmentType;
    });
  }, [allQuestions, formData.division_id, formData.target_id, formData.assessment_type]);

  // Filter questions based on search and exclude selected ones
  const filteredQuestions = useMemo(() => {
    const unselectedQuestions = availableQuestions.filter(q => 
      !formData.question_ids.includes(q.id)
    );
    
    if (!questionSearch.trim()) return unselectedQuestions;
    
    const searchLower = questionSearch.toLowerCase();
    return unselectedQuestions.filter(q => 
      q.question_text?.en?.toLowerCase().includes(searchLower) ||
      q.question_text?.toLowerCase().includes(searchLower)
    );
  }, [availableQuestions, questionSearch, formData.question_ids]);

  // Filter corporates based on search and exclude selected ones
  const filteredCorporates = useMemo(() => {
    const unselectedCorporates = corporates.filter(corp => 
      !formData.corporate_ids.includes(corp._id || corp.id)
    );
    
    if (!corporateSearch.trim()) return unselectedCorporates;
    
    const searchLower = corporateSearch.toLowerCase();
    return unselectedCorporates.filter(corp => 
      corp.name?.toLowerCase().includes(searchLower) ||
      corp.email?.toLowerCase().includes(searchLower)
    );
  }, [corporates, corporateSearch, formData.corporate_ids]);

  const selectedQuestions = useMemo(() => {
    return allQuestions.filter(q => formData.question_ids.includes(q.id));
  }, [allQuestions, formData.question_ids]);

  const selectedCorporates = useMemo(() => {
    return corporates.filter(corp => 
      formData.corporate_ids.includes(corp._id || corp.id)
    );
  }, [corporates, formData.corporate_ids]);

  const handleInputChange = (field: keyof AssessmentFormData, value: any) => {
    if (field === "division_id") {
      setFormData(prev => ({ 
        ...prev, 
        [field]: value, 
        target_id: "",
        question_ids: [] // Reset questions when division changes
      }));
    } else if (field === "target_id" || field === "assessment_type") {
      setFormData(prev => ({ 
        ...prev, 
        [field]: value, 
        question_ids: [] // Reset questions when target or assessment type changes
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const toggleQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      question_ids: prev.question_ids.includes(questionId)
        ? prev.question_ids.filter(id => id !== questionId)
        : [...prev.question_ids, questionId]
    }));
  };

  const toggleCorporate = (corporateId: string) => {
    setFormData(prev => ({
      ...prev,
      corporate_ids: prev.corporate_ids.includes(corporateId)
        ? prev.corporate_ids.filter(id => id !== corporateId)
        : [...prev.corporate_ids, corporateId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.name.trim()) {
      toast.error("Assessment name is required");
      return;
    }

    if (!formData.assessment_type) {
      toast.error("Please select an assessment type");
      return;
    }

    if (!formData.division_id) {
      toast.error("Please select a division");
      return;
    }

    if (formData.question_ids.length === 0) {
      toast.error("Please select at least one question");
      return;
    }

    if (formData.corporate_ids.length === 0) {
      toast.error("Please select at least one corporate");
      return;
    }

    try {
      const assessmentData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        assessment_type: formData.assessment_type,
        division_id: formData.division_id,
        target_id: formData.target_id || undefined,
        question_ids: formData.question_ids,
        corporate_ids: formData.corporate_ids,
        is_active: formData.is_active
      };

      await createAssessment(assessmentData).unwrap();
      
      toast.success("Assessment created successfully");
      
      // Reset form
      setFormData({
        name: "",
        description: "",
        assessment_type: "pre_assessment",
        division_id: "",
        target_id: "",
        question_ids: [],
        corporate_ids: [],
        is_active: true
      });
      
      setQuestionSearch("");
      setCorporateSearch("");
      
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Create assessment error:", error);
      toast.error(error?.data?.message || "Failed to create assessment");
    }
  };

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      assessment_type: "pre_assessment",
      division_id: "",
      target_id: "",
      question_ids: [],
      corporate_ids: [],
      is_active: true
    });
    setQuestionSearch("");
    setCorporateSearch("");
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="5xl">
      <ModalHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <FaClipboardList className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Assessment
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Create a new assessment by selecting questions and assigning to corporates
            </p>
          </div>
        </div>
      </ModalHeader>

      <ModalContent className="max-h-[75vh] overflow-y-auto">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Assessment Name *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter assessment name in English"
                className="w-full"
                required
                disabled={isLoading}
              />
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <FaLanguage className="w-3 h-3" />
                <span>AI will automatically translate this to Arabic and Hindi</span>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Assessment Type *
              </label>
              <Select
                value={formData.assessment_type}
                onChange={(value) => handleInputChange("assessment_type", value)}
                className="w-full"
                required
                disabled={isLoading}
              >
                {ASSESSMENT_TYPES.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Description
              <span className="text-gray-400 font-normal ml-1">(Optional)</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter assessment description (optional)"
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none transition-all duration-200"
              disabled={isLoading}
            />
          </div>

          {/* Division & Target Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Division *
              </label>
              <Select
                value={formData.division_id}
                onChange={(value) => handleInputChange("division_id", value)}
                placeholder="Select a division"
                className="w-full"
                required
                disabled={isLoading}
              >
                {divisions.map((division) => (
                  <option key={division.id} value={division.id}>
                    {division.name.en || division.name}
                  </option>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Target
                <span className="text-gray-400 font-normal ml-1">(Optional)</span>
              </label>
              <Select
                value={formData.target_id}
                onChange={(value) => handleInputChange("target_id", value)}
                placeholder="Select a target (optional)"
                className="w-full"
                disabled={isLoading || !formData.division_id}
              >
                {availableTargets.map((target) => (
                  <option key={target.id} value={target.id}>
                    {target.name.en || target.name}
                  </option>
                ))}
              </Select>
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center gap-3">
            <Checkbox
              checked={formData.is_active}
              onChange={(checked) => handleInputChange("is_active", checked)}
              disabled={isLoading}
            />
            <label className="text-sm font-medium text-gray-700">
              Assessment is active
            </label>
          </div>

          {/* Question Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Select Questions *
              </h3>
              <Badge variant="secondary">
                {formData.question_ids.length} selected
              </Badge>
            </div>

            {/* Selected Questions */}
            {selectedQuestions.length > 0 && (
              <Card className="p-4 bg-green-50 border-green-200">
                <div className="space-y-2">
                  {selectedQuestions.map((question) => (
                    <div
                      key={question.id}
                      className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-200"
                    >
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {question.question_text?.en || question.question_text}
                        </p>
                        <p className="text-xs text-gray-500">
                          Type: {question.question_type?.replace('_', ' ')} | 
                          Assessment: {question.assessment_type?.replace('_', ' ')}
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={() => toggleQuestion(question.id)}
                        className="ml-3 text-red-600 hover:text-red-700"
                        disabled={isLoading}
                      >
                        <FaTrash className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Question Search & Selection */}
            <div className="space-y-3">
              <Input
                type="text"
                value={questionSearch}
                onChange={(e) => setQuestionSearch(e.target.value)}
                placeholder="Search questions by text..."
                className="w-full"
                disabled={isLoading}
              />

              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
                {filteredQuestions.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {filteredQuestions.slice(0, 10).map((question) => (
                      <div
                        key={question.id}
                        className="p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => toggleQuestion(question.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={formData.question_ids.includes(question.id)}
                            onChange={() => toggleQuestion(question.id)}
                            disabled={isLoading}
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {question.question_text?.en || question.question_text}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              Type: {question.question_type?.replace('_', ' ')} | 
                              Assessment: {question.assessment_type?.replace('_', ' ')}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {questionSearch ? "No questions found matching your search" : 
                     !formData.division_id ? "Please select a division first" :
                     "No questions available for the selected criteria"}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Corporate Assignment */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Corporate Assignment *
              </h3>
              <Badge variant="secondary">
                {formData.corporate_ids.length} selected
              </Badge>
            </div>

            {/* Selected Corporates */}
            {selectedCorporates.length > 0 && (
              <Card className="p-4 bg-blue-50 border-blue-200">
                <div className="flex flex-wrap gap-2">
                  {selectedCorporates.map((corporate) => (
                    <Badge
                      key={corporate._id || corporate.id}
                      variant="default"
                      className="flex items-center gap-2 bg-blue-100 text-blue-800"
                    >
                      {corporate.name}
                      <button
                        type="button"
                        onClick={() => toggleCorporate(corporate._id || corporate.id)}
                        className="ml-1 hover:bg-blue-200 rounded-full p-1"
                        disabled={isLoading}
                      >
                        <FaTrash className="w-2 h-2" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </Card>
            )}

            {/* Corporate Search & Selection */}
            <div className="space-y-3">
              <Input
                type="text"
                value={corporateSearch}
                onChange={(e) => setCorporateSearch(e.target.value)}
                placeholder="Search corporates by name or email..."
                className="w-full"
                disabled={isLoading}
              />

              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
                {filteredCorporates.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {filteredCorporates.slice(0, 10).map((corporate) => (
                      <div
                        key={corporate._id || corporate.id}
                        className="p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => toggleCorporate(corporate._id || corporate.id)}
                      >
                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={formData.corporate_ids.includes(corporate._id || corporate.id)}
                            onChange={() => toggleCorporate(corporate._id || corporate.id)}
                            disabled={isLoading}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{corporate.name}</div>
                            <div className="text-sm text-gray-600">{corporate.email}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {corporateSearch ? "No corporates found matching your search" : "No corporates available"}
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </ModalContent>

      <ModalFooter>
        <div className="flex gap-3 justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              isLoading || 
              !formData.name.trim() || 
              !formData.division_id || 
              formData.question_ids.length === 0 || 
              formData.corporate_ids.length === 0
            }
            className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Spinner size="sm" />
                <span>Creating...</span>
              </div>
            ) : (
              "Create Assessment"
            )}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default CreateAssessmentForm; 