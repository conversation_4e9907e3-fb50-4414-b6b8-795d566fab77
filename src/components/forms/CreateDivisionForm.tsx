import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { FaSitemap, FaLanguage } from "react-icons/fa";
import {
  But<PERSON>,
  Input,
  Card,
  GradientT<PERSON>t,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spin<PERSON>
} from "@/components/ui";
import { useCreateQADivisionMutation } from "@/features/API/qaServiceApi";

interface CreateDivisionFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface DivisionFormData {
  name: string;
  description: string;
}

const CreateDivisionForm: React.FC<CreateDivisionFormProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<DivisionFormData>({
    name: "",
    description: ""
  });

  const [createDivision, { isLoading }] = useCreateQADivisionMutation();

  const handleInputChange = (field: keyof DivisionFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.name.trim()) {
      toast.error("Division name is required");
      return;
    }

    try {
      const divisionData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        status: "active"
      };

      await createDivision(divisionData).unwrap();
      
      toast.success("Division created successfully");
      
      // Reset form
      setFormData({
        name: "",
        description: ""
      });
      
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Create division error:", error);
      toast.error(error?.data?.message || "Failed to create division");
    }
  };

  const handleClose = () => {
    setFormData({
      name: "",
      description: ""
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalHeader>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 rounded-lg">
            <FaSitemap className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Division
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Add a new division to organize your questions and assessments
            </p>
          </div>
        </div>
      </ModalHeader>

      <ModalContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Division Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Division Name *
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter division name in English"
              className="w-full"
              required
              disabled={isLoading}
            />
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <FaLanguage className="w-3 h-3" />
              <span>
                AI will automatically translate this to Arabic and Hindi
              </span>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Description
              <span className="text-gray-400 font-normal ml-1">(Optional)</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter division description (optional)"
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 focus:border-transparent resize-none transition-all duration-200"
              disabled={isLoading}
            />
          </div>

          {/* Info Card */}
          <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Division Structure</p>
                <p className="text-blue-700">
                  Divisions help organize your questions and assessments. Each division can contain multiple subdivisions (targets) for more granular organization.
                </p>
              </div>
            </div>
          </Card>
        </form>
      </ModalContent>

      <ModalFooter>
        <div className="flex gap-3 justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !formData.name.trim()}
            className="bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 hover:from-sehatti-gold-600 hover:to-sehatti-gold-700"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Spinner size="sm" />
                <span>Creating...</span>
              </div>
            ) : (
              "Create Division"
            )}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default CreateDivisionForm; 