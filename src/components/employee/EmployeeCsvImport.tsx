import React, { useState } from 'react'
import { 
  FaUpload, 
  FaDownload, 
  FaFileExcel, 
  FaTimes, 
  FaCheckCircle,
  FaExclamationTriangle,
  FaUsers,
  FaSpinner
} from 'react-icons/fa'

// UI Components
import { 
  Modal, 
  ModalHeader, 
  ModalContent, 
  ModalFooter, 
  ModalTitle, 
  ModalDescription,
  ModalClose 
} from '../ui/Modal'
import { Button } from '../ui/Button'
import { FileUpload } from '../ui/FileUpload'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Badge } from '../ui/Badge'
import { Alert } from '../ui/Alert'

// API Hook
import { useUploadEmployeesCsvMutation } from '../../store/api/employeeApi'
import { useAppSelector } from '../../store/hooks'
import type { User } from '@/types/api'

interface EmployeeCsvImportProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface UploadResult {
  success: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
    email?: string
  }>
}

export default function EmployeeCsvImport({ isOpen, onClose, onSuccess }: EmployeeCsvImportProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [validatedData, setValidatedData] = useState<any[] | null>(null)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [step, setStep] = useState<'upload' | 'result'>('upload')
  
  // Get current user for company_id
  const auth = useAppSelector(state => state.auth.user)
  const currentUser = auth as User
  
  // API Mutation
  const [uploadCsv, { isLoading: isUploading }] = useUploadEmployeesCsvMutation()

  // CSV Template data
  const csvTemplate = [
    {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      department: 'Engineering'
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '1234567891',
      department: 'Marketing'
    },
    {
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '1234567892',
      department: 'Sales'
    }
  ]

  const generateCsvTemplate = () => {
    const headers = ['name', 'email', 'phone', 'department']
    const csvContent = [
      headers.join(','),
      ...csvTemplate.map(row => 
        headers.map(header => {
          const value = row[header as keyof typeof row]
          // Force phone numbers to be treated as text by adding a prefix
          if (header === 'phone') {
            return `"'${value}"` // Single quote prefix forces text format in Excel
          }
          return `"${value}"`
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'employee_import_template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // CSV Validation Functions
  const validatePhoneNumber = (phone: string | number): string => {
    if (!phone) return '' // Empty phone is allowed
    
    // Convert to string and clean
    let cleanPhone = String(phone).replace(/[^\d]/g, '') // Remove all non-digits
    
    // Handle common formats
    if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
      cleanPhone = cleanPhone.substring(1) // Remove country code
    }
    
    // Validate length (10 digits for US format)
    if (cleanPhone.length === 10) {
      return cleanPhone
    }
    
    // Return original if can't be cleaned (will be flagged as error)
    return String(phone)
  }

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validateCsvData = (csvData: any[]): { validData: any[], errors: string[] } => {
    const validData: any[] = []
    const errors: string[] = []

    csvData.forEach((row, index) => {
      const rowNumber = index + 1
      const validatedRow: any = {}
      
      // Validate required fields
      if (!row.name || String(row.name).trim() === '') {
        errors.push(`Row ${rowNumber}: Name is required`)
        return
      }
      
      if (!row.email || String(row.email).trim() === '') {
        errors.push(`Row ${rowNumber}: Email is required`)
        return
      }
      
      // Validate email format
      const email = String(row.email).trim()
      if (!validateEmail(email)) {
        errors.push(`Row ${rowNumber}: Invalid email format (${email})`)
        return
      }
      
      // Clean and validate phone number
      const cleanedPhone = validatePhoneNumber(row.phone)
      if (row.phone && cleanedPhone === String(row.phone) && cleanedPhone.length !== 10) {
        errors.push(`Row ${rowNumber}: Invalid phone number format. Use 10 digits (e.g., 1234567890)`)
        return
      }
      
      // Build validated row
      validatedRow.name = String(row.name).trim()
      validatedRow.email = email
      validatedRow.phone = cleanedPhone || ''
      validatedRow.department = row.department ? String(row.department).trim() : ''
      
      validData.push(validatedRow)
    })

    return { validData, errors }
  }

  const parseCsvFile = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string
          const lines = text.split('\n').filter(line => line.trim())
          
          if (lines.length < 2) {
            reject(new Error('CSV file must have at least a header row and one data row'))
            return
          }
          
          const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim().toLowerCase())
          const expectedHeaders = ['name', 'email', 'phone', 'department']
          
          // Validate headers
          const missingHeaders = expectedHeaders.filter(h => !headers.includes(h))
          if (missingHeaders.length > 0) {
            reject(new Error(`Missing required columns: ${missingHeaders.join(', ')}`))
            return
          }
          
          const data = lines.slice(1).map(line => {
            const values = line.split(',').map(v => v.replace(/"/g, '').trim())
            const row: any = {}
            
            headers.forEach((header, index) => {
              let value = values[index] || ''
              // Handle phone numbers with single quote prefix
              if (header === 'phone' && value.startsWith("'")) {
                value = value.substring(1) // Remove the single quote prefix
              }
              // Ensure phone numbers are always strings
              if (header === 'phone' && value) {
                value = String(value)
              }
              row[header] = value
            })
            
            return row
          })
          
          resolve(data)
        } catch (error) {
          reject(new Error('Failed to parse CSV file. Please check the format.'))
        }
      }
      
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  const handleFileSelect = async (file: File | null) => {
    setSelectedFile(file)
    setValidatedData(null)
    setUploadResult(null)
    setStep('upload')
    
    if (file) {
      try {
        // Parse and validate CSV data
        const csvData = await parseCsvFile(file)
        const { validData, errors } = validateCsvData(csvData)
        
        if (errors.length > 0) {
          // Show validation errors
          setUploadResult({
            success: 0,
            failed: errors.length,
            errors: errors.map((error, index) => ({
              row: index + 1,
              field: 'validation',
              message: error
            }))
          })
          setStep('result')
        } else {
          // Store validated data for upload
          setValidatedData(validData)
          console.log(`✅ CSV Validation Passed: ${validData.length} rows ready for upload`)
        }
      } catch (error: any) {
        setUploadResult({
          success: 0,
          failed: 1,
          errors: [{
            row: 0,
            field: 'file',
            message: error.message || 'Failed to process CSV file'
          }]
        })
        setStep('result')
      }
    }
  }

  const createValidatedCsvFile = (data: any[]): File => {
    const headers = ['name', 'email', 'phone', 'department']
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header] || ''
          // Special handling for phone numbers to ensure they're treated as strings
          if (header === 'phone' && value) {
            return `"'${value}"`  // Force string with single quote prefix
          }
          return `"${value}"`
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    return new File([blob], 'validated_employees.csv', { type: 'text/csv' })
  }

  const handleUpload = async () => {
    if (!selectedFile || !validatedData) return

    try {
      // Create a new CSV file from validated data
      const validatedCsvFile = createValidatedCsvFile(validatedData)
      
      const formData = new FormData()
      formData.append('csv_file', validatedCsvFile)

      console.log('📤 Uploading Validated CSV:', {
        fileName: validatedCsvFile.name,
        fileSize: validatedCsvFile.size,
        fileType: validatedCsvFile.type,
        rowCount: validatedData.length,
        formDataKeys: Array.from(formData.keys())
      })

      const response = await uploadCsv(formData).unwrap()
      
      console.log('📤 CSV Upload Response:', response)
      
      // Handle successful upload
      if (response.data) {
        // Map backend response to frontend format
        const mappedResult = {
          success: response.data.created_count || 0,
          failed: response.data.errors ? response.data.errors.length : 0,
          errors: response.data.errors ? response.data.errors.map((errorStr: string, index: number) => ({
            row: index + 1,
            field: 'validation',
            message: errorStr
          })) : []
        }
        
        setUploadResult(mappedResult)
        setStep('result')
        
        // Call success callback if provided
        if (onSuccess && mappedResult.success > 0) {
          onSuccess()
        }
      }
    } catch (error: any) {
      console.error('❌ CSV Upload Error:', error)
      
      // Handle upload error
      setUploadResult({
        success: 0,
        failed: 1,
        errors: [{
          row: 0,
          field: 'upload',
          message: error?.data?.message || 'Upload failed. Please try again.'
        }]
      })
      setStep('result')
    }
  }

  const handleClose = () => {
    setSelectedFile(null)
    setValidatedData(null)
    setUploadResult(null)
    setStep('upload')
    onClose()
  }

  const handleRetry = () => {
    setSelectedFile(null)
    setValidatedData(null)
    setUploadResult(null)
    setStep('upload')
  }

  return (
    <Modal open={isOpen} onClose={handleClose} size="xl">
      <ModalHeader>
        <ModalClose onClick={handleClose}>
          <FaTimes className="w-4 h-4" />
        </ModalClose>
        <ModalTitle className="flex items-center gap-2">
          <FaUsers className="text-sehatti-gold-600" />
          Import Employees via CSV
        </ModalTitle>
        <ModalDescription>
          Upload a CSV file to bulk import employees to your organization
        </ModalDescription>
      </ModalHeader>

      <ModalContent className="space-y-6">
        {step === 'upload' && (
          <>
            {/* Template Download Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <FaDownload className="text-green-600" />
                  Step 1: Download Template
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-sehatti-warm-gray-600">
                  Download our CSV template with the correct format and sample data. 
                  Fill in your employee information following the same structure.
                </p>
                
                <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
                  <FaExclamationTriangle className="w-4 h-4 text-blue-600" />
                  <div className="ml-2">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Important:</strong> Phone numbers should be entered as text (e.g., "1234567890") 
                      without special characters. Use quotes in Excel to ensure proper formatting.
                    </p>
                  </div>
                </Alert>
                
                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                  <h4 className="font-medium text-sm mb-2">Required Fields:</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">name</Badge>
                      <span className="text-red-500">*</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">email</Badge>
                      <span className="text-red-500">*</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">phone</Badge>
                      <span className="text-sehatti-warm-gray-500">optional</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">department</Badge>
                      <span className="text-sehatti-warm-gray-500">optional</span>
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={generateCsvTemplate}
                  className="w-full"
                >
                  <FaFileExcel className="w-4 h-4 mr-2 text-green-600" />
                  Download CSV Template
                </Button>
              </CardContent>
            </Card>

            {/* File Upload Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <FaUpload className="text-blue-600" />
                  Step 2: Upload Filled CSV
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFileSelect={handleFileSelect}
                  selectedFile={selectedFile}
                  acceptedTypes={['.csv', '.xlsx', '.xls']}
                  maxSize={5}
                  label="Employee Data File"
                  description="CSV, Excel (.xlsx, .xls) files up to 5MB"
                />
                
                {selectedFile && (
                  <div className="mt-4 space-y-2">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
                        <FaCheckCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          File selected: {selectedFile.name}
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 dark:text-blue-500 mt-1">
                        Size: {(selectedFile.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                    
                    {validatedData && (
                      <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="flex items-center gap-2 text-green-700 dark:text-green-400">
                          <FaCheckCircle className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            Validation passed: {validatedData.length} employees ready for import
                          </span>
                        </div>
                        <p className="text-xs text-green-600 dark:text-green-500 mt-1">
                          All data has been validated and cleaned. Phone numbers formatted correctly.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Important Notes */}
            <Alert>
              <FaExclamationTriangle className="w-4 h-4" />
              <div className="ml-2">
                <h4 className="font-medium text-sm">Important Notes:</h4>
                <ul className="text-xs mt-1 space-y-1 text-sehatti-warm-gray-600">
                  <li>• Email addresses must be unique and valid</li>
                  <li>• Employees will receive welcome emails with password setup links</li>
                  <li>• Duplicate emails will be skipped</li>
                  <li>• Maximum 100 employees per upload</li>
                </ul>
              </div>
            </Alert>
          </>
        )}

        {step === 'result' && uploadResult && (
          <div className="space-y-4">
            {/* Upload Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  {uploadResult.success > 0 ? (
                    <FaCheckCircle className="text-green-600" />
                  ) : (
                    <FaExclamationTriangle className="text-red-600" />
                  )}
                  Upload Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {uploadResult.success}
                    </div>
                    <div className="text-sm text-green-700 dark:text-green-400">
                      Successfully Imported
                    </div>
                  </div>
                  <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {uploadResult.failed}
                    </div>
                    <div className="text-sm text-red-700 dark:text-red-400">
                      Failed to Import
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Error Details */}
            {uploadResult.errors && uploadResult.errors.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base text-red-600">
                    <FaExclamationTriangle />
                    Import Errors ({uploadResult.errors.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {uploadResult.errors.map((error, index) => (
                      <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 rounded border-l-4 border-red-400">
                        <div className="flex items-start gap-2">
                          <span className="text-xs font-medium text-red-700 dark:text-red-400">
                            Row {error.row}:
                          </span>
                          <div className="flex-1">
                            <span className="text-xs text-red-600 dark:text-red-400">
                              {error.message}
                            </span>
                            {error.email && (
                              <div className="text-xs text-red-500 mt-1">
                                Email: {error.email}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Success Message */}
            {uploadResult.success > 0 && (
              <Alert>
                <FaCheckCircle className="w-4 h-4 text-green-600" />
                <div className="ml-2">
                  <h4 className="font-medium text-sm text-green-700">Import Successful!</h4>
                  <p className="text-xs text-green-600 mt-1">
                    {uploadResult.success} employee(s) have been imported and will receive welcome emails shortly.
                  </p>
                </div>
              </Alert>
            )}
          </div>
        )}
      </ModalContent>

      <ModalFooter>
        <div className="flex items-center justify-between w-full">
          {step === 'upload' ? (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={!selectedFile || !validatedData || isUploading}
                className="min-w-[120px]"
              >
                {isUploading ? (
                  <>
                    <FaSpinner className="animate-spin w-4 h-4 mr-2" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <FaUpload className="w-4 h-4 mr-2" />
                    Import Employees
                  </>
                )}
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleRetry}>
                Import Another File
              </Button>
              <Button onClick={handleClose}>
                Close
              </Button>
            </>
          )}
        </div>
      </ModalFooter>
    </Modal>
  )
} 