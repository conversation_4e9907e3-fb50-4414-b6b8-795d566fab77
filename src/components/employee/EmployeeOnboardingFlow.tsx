import React, { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { toast } from "react-hot-toast"
import { useLanguage, SUPPORTED_LANGUAGES } from "../../utils/languageUtils"
import { onboardingTranslations } from "../../utils/onboardingTranslations"
import {
  FaGlobe,
  FaVideo,
  FaCalendarAlt,
  FaClipboardList,
  FaArrowRight,
  FaArrowLeft,
  FaCheck,
  FaClock,
  FaUsers,
  FaComments
} from "react-icons/fa"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Spinner,
  ProgressBar,
  StepIndicator,
  VideoPlayer,
  StarRating,
  GradientText,
  Textarea,
  RadioGroup,
  type Step
} from "../ui"

// Webinar slot type
interface WebinarSlot {
  id: string
  date: string
  time: string
  title: string
  duration: string
  spots: number
  maxSpots: number
  dateFormatted: string
}

// Generate webinar slots for next 20 days from June 30th, 2025
const generateWebinarSlots = (): WebinarSlot[] => {
  const slots: WebinarSlot[] = []
  const startDate = new Date("2025-06-30")
  const times = ["09:00", "11:00", "14:00", "16:00"]

  for (let day = 0; day < 20; day++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + day)

    times.forEach((time, index) => {
      slots.push({
        id: `${day}-${index}`,
        date: date.toISOString().split("T")[0],
        time,
        title: "Corporate Wellness Introduction",
        duration: "45 mins",
        spots: Math.floor(Math.random() * 15) + 5, // Random spots between 5-20
        maxSpots: 20,
        dateFormatted: date.toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric"
        })
      })
    })
  }

  return slots
}

// Onboarding steps
enum OnboardingStep {
  LANGUAGE_SELECTION = "language",
  INTRO_VIDEO = "video",
  WEBINAR_BOOKING = "webinar",
  WEBINAR_FEEDBACK = "feedback",
  PRE_ASSESSMENT = "assessment",
  COMPLETED = "completed"
}

interface OnboardingProgress {
  currentStep: OnboardingStep
  selectedLanguage: string
  videoWatched: boolean
  videoFullyWatched: boolean
  selectedWebinar: string
  feedbackSubmitted: boolean
  assessmentCompleted: boolean
  completedAt?: string
}

const EmployeeOnboardingFlow: React.FC = () => {
  const navigate = useNavigate()
  const { currentLanguage, changeLanguage, rtlClasses, isRTL } = useLanguage()

  // Get translations for current language
  const t =
    onboardingTranslations[currentLanguage as "EN" | "AR" | "HI"] ||
    onboardingTranslations.EN

  // Load progress from localStorage
  const loadProgress = (): OnboardingProgress => {
    const saved = localStorage.getItem("sehatti-onboarding-progress")
    if (saved) {
      try {
        return JSON.parse(saved)
      } catch (error) {
        console.error("Failed to parse onboarding progress:", error)
      }
    }

    return {
      currentStep: OnboardingStep.LANGUAGE_SELECTION,
      selectedLanguage: currentLanguage.toLowerCase(),
      videoWatched: false,
      videoFullyWatched: false,
      selectedWebinar: "",
      feedbackSubmitted: false,
      assessmentCompleted: false
    }
  }

  const [progress, setProgress] = useState<OnboardingProgress>(loadProgress)
  const [loading, setLoading] = useState(false)
  const [selectedWebinarId, setSelectedWebinarId] = useState<string | null>(
    null
  )
  const [videoCompleted, setVideoCompleted] = useState(false)
  const [videoStarted, setVideoStarted] = useState(false)
  const [feedbackRating, setFeedbackRating] = useState(0)
  const [feedbackText, setFeedbackText] = useState("")
  const [selectedQuestion, setSelectedQuestion] = useState(null)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)

  // Get webinar slots
  const [webinarSlots] = useState(generateWebinarSlots())

  // Mock pre-assessment questions with translations
  const preAssessmentQuestions = [
    {
      id: "1",
      question_text: {
        EN: "How focused were you during work today?",
        AR: "ما مدى تركيزك في العمل اليوم؟",
        HI: "आज काम करते समय आप कितने ध्यान केंद्रित थे?"
      },
      options: [
        {
          text: {
            EN: "Not focused at all",
            AR: "غير مركز إطلاقاً",
            HI: "बिल्कुल भी ध्यान नहीं दिया"
          },
          value: 1
        },
        {
          text: {
            EN: "Slightly focused",
            AR: "مركّز بشكل طفيف",
            HI: "थोड़ा ध्यान केंद्रित"
          },
          value: 2
        },
        {
          text: {
            EN: "Moderately focused",
            AR: "مركّز إلى حد ما",
            HI: "मध्यम रूप से केंद्रित"
          },
          value: 3
        },
        {
          text: {
            EN: "Very focused",
            AR: "مركّز جدًا",
            HI: "बहुत ध्यान केंद्रित"
          },
          value: 4
        }
      ]
    }
  ]

  const questionsLoading = false

  // Initialize video completion state based on saved progress
  useEffect(() => {
    const savedProgress = loadProgress()
    if (savedProgress.videoFullyWatched) {
      setVideoCompleted(true)
      setVideoStarted(true)
    }
  }, [loadProgress])

  // Save progress to localStorage
  const saveProgress = (newProgress: OnboardingProgress) => {
    localStorage.setItem(
      "sehatti-onboarding-progress",
      JSON.stringify(newProgress)
    )
    setProgress(newProgress)
  }

  // Step progression with translations
  const steps: Step[] = [
    {
      id: OnboardingStep.LANGUAGE_SELECTION,
      title: t.steps.language,
      icon: FaGlobe
    },
    { id: OnboardingStep.INTRO_VIDEO, title: t.steps.welcome, icon: FaVideo },
    {
      id: OnboardingStep.WEBINAR_BOOKING,
      title: t.steps.webinar,
      icon: FaCalendarAlt
    },
    {
      id: OnboardingStep.WEBINAR_FEEDBACK,
      title: t.steps.feedback,
      icon: FaComments
    },
    {
      id: OnboardingStep.PRE_ASSESSMENT,
      title: t.steps.assessment,
      icon: FaClipboardList
    }
  ]

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.id === progress.currentStep)
  }

  // Initialize random question for pre-assessment
  useEffect(() => {
    if (
      preAssessmentQuestions &&
      preAssessmentQuestions.length > 0 &&
      !selectedQuestion
    ) {
      setSelectedQuestion(preAssessmentQuestions[0])
    }
  }, [preAssessmentQuestions, selectedQuestion])

  const handleLanguageSelect = (langCode: string) => {
    // Update main app language
    changeLanguage(langCode.toUpperCase() as any)

    const newProgress = {
      ...progress,
      selectedLanguage: langCode,
      currentStep: OnboardingStep.INTRO_VIDEO
    }
    saveProgress(newProgress)
    toast.success(t.toasts.languageSaved)
  }

  const handleVideoComplete = () => {
    const newProgress = {
      ...progress,
      videoWatched: true,
      videoFullyWatched: true,
      currentStep: OnboardingStep.WEBINAR_BOOKING
    }
    saveProgress(newProgress)
    toast.success(t.toasts.videoCompleted)
  }

  // New handlers for video events
  const handleVideoStart = () => {
    setVideoStarted(true)
    toast(t.introVideo.mustWatchComplete, {
      icon: "📹",
      duration: 4000
    })
  }

  const handleVideoEnd = () => {
    setVideoCompleted(true)
    toast.success(t.introVideo.videoCompleted)
  }

  const handleNextAfterVideo = () => {
    if (!videoCompleted) {
      toast.error(t.introVideo.mustCompleteVideo)
      return
    }
    handleVideoComplete()
  }

  const handleWebinarSelect = async (webinarId: string) => {
    setLoading(true)
    setSelectedWebinarId(webinarId)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500))

    const newProgress = {
      ...progress,
      selectedWebinar: webinarId,
      currentStep: OnboardingStep.WEBINAR_FEEDBACK
    }
    saveProgress(newProgress)
    setLoading(false)
    setSelectedWebinarId(null) // Reset selected webinar
    toast.success(t.toasts.webinarBooked)
  }

  const handleFeedbackSubmit = async () => {
    if (feedbackRating === 0) {
      toast.error(t.common.pleaseProvideRating)
      return
    }

    setLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const newProgress = {
        ...progress,
        feedbackSubmitted: true,
        currentStep: OnboardingStep.PRE_ASSESSMENT
      }
      saveProgress(newProgress)
      toast.success(t.toasts.feedbackSubmitted)
    } catch (error) {
      console.error("Feedback submission error:", error)
      toast.error(t.common.error)
    } finally {
      setLoading(false)
    }
  }

  const handleAssessmentSubmit = async () => {
    if (selectedAnswer === null) {
      toast.error(t.common.pleaseSelectAnswer)
      return
    }

    setLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const newProgress = {
      ...progress,
      assessmentCompleted: true,
      currentStep: OnboardingStep.COMPLETED,
      completedAt: new Date().toISOString()
    }
    saveProgress(newProgress)
    setLoading(false)
    toast.success(t.toasts.assessmentCompleted)
  }

  const handleAccessDashboard = () => {
    // Mark onboarding as completed
    const completedProgress = {
      ...progress,
      currentStep: OnboardingStep.COMPLETED,
      completedAt: new Date().toISOString()
    }
    saveProgress(completedProgress)

    // Set completion flag for quick checks
    localStorage.setItem("sehatti-onboarding-completed", "true")

    toast.success(t.toasts.onboardingCompleted)
    navigate("/employee/dashboard")
  }

  const goToPreviousStep = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex > 0) {
      const newProgress = {
        ...progress,
        currentStep: steps[currentIndex - 1].id as OnboardingStep
      }
      saveProgress(newProgress)
    }
  }

  const renderProgressSection = () => {
    const currentIndex = getCurrentStepIndex()
    const progressPercentage = ((currentIndex + 1) / steps.length) * 100

    return (
      <div
        className={`mb-4 sm:mb-6 lg:mb-8 space-y-3 sm:space-y-4 lg:space-y-6 px-2 ${rtlClasses.textAlign}`}
      >
        <ProgressBar
          percentage={progressPercentage}
          title={t.progressTitle}
          currentStep={currentIndex + 1}
          totalSteps={steps.length}
        />

        {/* Mobile: Compact step indicator, Desktop: Full step indicator */}
        <div className="hidden sm:block">
          <StepIndicator
            steps={steps}
            currentStepIndex={currentIndex}
            orientation="horizontal"
            size="md"
          />
        </div>

        {/* Mobile: Simple step counter */}
        <div className="sm:hidden text-center">
          <p className="text-xs text-gray-500">
            Step {currentIndex + 1} of {steps.length}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 to-sehatti-gold-light px-3 py-4 sm:p-6 ${
        isRTL ? "rtl" : "ltr"
      }`}
      dir={isRTL ? "rtl" : "ltr"}
    >
      <div className="max-w-4xl mx-auto">
        {/* Mobile-first header with responsive text sizing */}
        <div
          className={`text-center mb-6 sm:mb-8 pt-4 sm:pt-8 ${
            isRTL ? "text-right" : "text-center"
          }`}
        >
          <GradientText className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">
            {t.welcomeTitle}
          </GradientText>
          <p
            className={`text-sm sm:text-base text-gray-600 px-2 ${
              isRTL ? "text-right" : "text-center"
            }`}
          >
            {t.onboardingSubtitle}
          </p>
        </div>

        {progress.currentStep !== OnboardingStep.COMPLETED &&
          renderProgressSection()}

        <div className="space-y-4 sm:space-y-6">
          {/* Language Selection - Mobile-first design */}
          {progress.currentStep === OnboardingStep.LANGUAGE_SELECTION && (
            <Card className="p-4 sm:p-6 lg:p-8">
              <CardHeader className={`text-center ${rtlClasses.textAlign}`}>
                <FaGlobe className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-gold mx-auto mb-3 sm:mb-4" />
                <CardTitle className="text-xl sm:text-2xl mb-2">
                  {t.languageSelection.title}
                </CardTitle>
                <p className="text-sm sm:text-base text-gray-600 px-2">
                  {t.languageSelection.subtitle}
                </p>
              </CardHeader>
              <CardContent>
                {/* Mobile: Stack vertically, Tablet+: Grid layout */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                  {SUPPORTED_LANGUAGES.map((lang) => {
                    const langTranslation =
                      onboardingTranslations[
                        lang.code.toUpperCase() as "EN" | "AR" | "HI"
                      ]
                    return (
                      <Button
                        key={lang.code}
                        variant="outline"
                        className="h-auto p-4 sm:p-6 flex flex-col items-center space-y-2 sm:space-y-3 hover:border-sehatti-gold hover:bg-sehatti-gold-light active:scale-95 transition-all touch-manipulation"
                        onClick={() => handleLanguageSelect(lang.code)}
                      >
                        <span className="text-2xl sm:text-3xl">
                          {lang.flag}
                        </span>
                        <span className="font-semibold text-sm sm:text-base">
                          {lang.name}
                        </span>
                        <span className="text-xs sm:text-sm text-gray-600 text-center px-1">
                          {langTranslation?.languageSelection.greeting ||
                            t.languageSelection.greeting}
                        </span>
                      </Button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Intro Video - Mobile-optimized */}
          {progress.currentStep === OnboardingStep.INTRO_VIDEO && (
            <Card className="p-4 sm:p-6 lg:p-8">
              <CardHeader className={`text-center ${rtlClasses.textAlign}`}>
                <FaVideo className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-gold mx-auto mb-3 sm:mb-4" />
                <CardTitle className="text-xl sm:text-2xl mb-2">
                  {t.introVideo.title}
                </CardTitle>
                <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2">
                  {t.introVideo.subtitle}
                </p>
              </CardHeader>
              <CardContent>
                <div className="max-w-2xl mx-auto space-y-4 sm:space-y-6">
                  {/* Mobile-responsive video player */}
                  <div className="w-full">
                    <VideoPlayer
                      src="https://blntek-shared-public-assets-869370432397.s3.us-east-1.amazonaws.com/intro+(1080p).mp4"
                      title={t.introVideo.videoTitle}
                      description={t.introVideo.videoDescription}
                      className="w-full rounded-lg"
                      onStart={handleVideoStart}
                      onEnded={handleVideoEnd}
                    />
                  </div>

                  <div
                    className={`text-center space-y-3 sm:space-y-4 ${rtlClasses.textAlign}`}
                  >
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-800 px-2">
                      {t.introVideo.welcomeMessage}
                    </h3>
                    <p className="text-sm sm:text-base text-gray-600 px-2">
                      {t.introVideo.journeyDescription}
                    </p>

                    {/* Video completion status indicator */}
                    {videoStarted && !videoCompleted && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mx-2">
                        <div className="flex items-center justify-center gap-2 text-yellow-800">
                          <FaVideo className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {t.introVideo.mustCompleteVideo}
                          </span>
                        </div>
                      </div>
                    )}

                    {videoCompleted && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mx-2">
                        <div className="flex items-center justify-center gap-2 text-green-800">
                          <FaCheck className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {t.introVideo.videoCompletedProceed}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Mobile-first button layout */}
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center pt-4 px-2">
                      <Button
                        variant="outline"
                        onClick={goToPreviousStep}
                        className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                        size="lg"
                      >
                        {isRTL ? <FaArrowRight /> : <FaArrowLeft />}
                        {t.introVideo.previous}
                      </Button>

                      <Button
                        onClick={handleNextAfterVideo}
                        disabled={!videoCompleted}
                        className={`flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation ${
                          !videoCompleted
                            ? "opacity-50 cursor-not-allowed bg-gray-300 hover:bg-gray-300"
                            : ""
                        }`}
                        size="lg"
                      >
                        {!videoCompleted && <FaVideo className="w-4 h-4" />}
                        {videoCompleted && <FaCheck className="w-4 h-4" />}
                        {t.common.next}
                        {isRTL ? <FaArrowLeft /> : <FaArrowRight />}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Webinar Booking - Mobile-optimized */}
          {progress.currentStep === OnboardingStep.WEBINAR_BOOKING && (
            <Card className="p-4 sm:p-6 lg:p-8">
              <CardHeader className={`text-center ${rtlClasses.textAlign}`}>
                <FaCalendarAlt className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-gold mx-auto mb-3 sm:mb-4" />
                <CardTitle
                  className={`text-xl sm:text-2xl mb-2 ${
                    isRTL ? "text-right" : "text-center"
                  }`}
                >
                  {t.webinarBooking.title}
                </CardTitle>
                <p
                  className={`text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2 ${
                    isRTL ? "text-right" : "text-center"
                  }`}
                >
                  {t.webinarBooking.subtitle}
                </p>
              </CardHeader>
              <CardContent>
                <div className="max-w-4xl mx-auto">
                  {/* Mobile: Single column, Tablet: 2 cols, Desktop: 3 cols */}
                  <div
                    className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 ${
                      isRTL ? "rtl" : "ltr"
                    }`}
                  >
                    {webinarSlots.slice(0, 9).map((slot) => (
                      <Card
                        key={slot.id}
                        className="p-3 sm:p-4 hover:shadow-md transition-shadow border-2 hover:border-sehatti-gold"
                      >
                        <div
                          className={`space-y-2 sm:space-y-3 ${
                            isRTL ? "text-right" : "text-left"
                          }`}
                        >
                          <div
                            className={`flex ${
                              isRTL ? "flex-row-reverse" : "flex-row"
                            } justify-between items-start`}
                          >
                            <div
                              className={`flex-1 min-w-0 ${
                                isRTL ? "text-right" : "text-left"
                              }`}
                            >
                              <h4
                                className={`font-semibold text-gray-800 text-sm sm:text-base truncate`}
                              >
                                {slot.dateFormatted.split(",")[0]}{" "}
                                {/* Show day only on mobile */}
                              </h4>
                              <p
                                className={`text-sehatti-gold font-medium text-sm sm:text-base`}
                              >
                                {slot.time}
                              </p>
                            </div>
                            <Badge
                              variant="secondary"
                              className={`text-xs shrink-0 ${
                                isRTL ? "mr-2" : "ml-2"
                              }`}
                            >
                              {slot.spots}/{slot.maxSpots}
                            </Badge>
                          </div>

                          <div className="space-y-1">
                            <p
                              className={`text-xs sm:text-sm text-gray-600 flex items-center gap-1 sm:gap-2 ${
                                isRTL
                                  ? "flex-row-reverse text-right"
                                  : "text-left"
                              }`}
                            >
                              <FaClock className="w-3 h-3 shrink-0" />
                              <span className="truncate">{slot.duration}</span>
                            </p>
                            <p
                              className={`text-xs sm:text-sm text-gray-600 flex items-center gap-1 sm:gap-2 ${
                                isRTL
                                  ? "flex-row-reverse text-right"
                                  : "text-left"
                              }`}
                            >
                              <FaUsers className="w-3 h-3 shrink-0" />
                              <span>
                                {slot.spots} {t.webinarBooking.spots}
                              </span>
                            </p>
                          </div>

                          <Button
                            size="sm"
                            className="w-full touch-manipulation active:scale-95 transition-transform text-xs sm:text-sm"
                            onClick={() => handleWebinarSelect(slot.id)}
                            disabled={loading || slot.spots === 0}
                          >
                            {loading && selectedWebinarId === slot.id ? (
                              <>
                                <Spinner
                                  variant="white"
                                  size="sm"
                                  className={`${isRTL ? "ml-1" : "mr-1"}`}
                                />
                                <span className="hidden sm:inline">
                                  {t.webinarBooking.booking}
                                </span>
                                <span className="sm:hidden">...</span>
                              </>
                            ) : (
                              <>
                                <span className="hidden sm:inline">
                                  {t.webinarBooking.selectSlot}
                                </span>
                                <span className="sm:hidden">
                                  {t.webinarBooking.select}
                                </span>
                              </>
                            )}
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>

                  {/* Mobile-first navigation */}
                  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center pt-6 sm:pt-8 px-2">
                    <Button
                      variant="outline"
                      onClick={goToPreviousStep}
                      className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                      size="lg"
                    >
                      {isRTL ? <FaArrowRight /> : <FaArrowLeft />}
                      {t.webinarBooking.previous}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Feedback - Mobile-optimized */}
          {progress.currentStep === OnboardingStep.WEBINAR_FEEDBACK && (
            <Card className="p-4 sm:p-6 lg:p-8">
              <CardHeader className={`text-center ${rtlClasses.textAlign}`}>
                <FaComments className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-gold mx-auto mb-3 sm:mb-4" />
                <CardTitle className="text-xl sm:text-2xl mb-2">
                  {t.feedback.title}
                </CardTitle>
                <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2">
                  {t.feedback.subtitle}
                </p>
              </CardHeader>
              <CardContent>
                <div className="max-w-2xl mx-auto space-y-4 sm:space-y-6">
                  <div
                    className={`space-y-3 sm:space-y-4 ${rtlClasses.textAlign}`}
                  >
                    <label className="block text-sm font-medium text-gray-700 px-2">
                      {t.feedback.ratingLabel}
                    </label>
                    <div className="flex justify-center px-2">
                      <StarRating
                        rating={feedbackRating}
                        onRatingChange={setFeedbackRating}
                        size="lg"
                        className="touch-manipulation"
                      />
                    </div>
                  </div>

                  <div className={`space-y-2 px-2 ${rtlClasses.textAlign}`}>
                    <label className="block text-sm font-medium text-gray-700">
                      {t.feedback.commentsLabel}
                    </label>
                    <Textarea
                      value={feedbackText}
                      onChange={(e) => setFeedbackText(e.target.value)}
                      placeholder={t.feedback.commentsPlaceholder}
                      rows={4}
                      className="w-full text-sm sm:text-base"
                    />
                  </div>

                  {/* Mobile-first button layout */}
                  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center pt-4 px-2">
                    <Button
                      variant="outline"
                      onClick={goToPreviousStep}
                      className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                      size="lg"
                    >
                      {isRTL ? <FaArrowRight /> : <FaArrowLeft />}
                      {t.feedback.previous}
                    </Button>

                    <Button
                      onClick={handleFeedbackSubmit}
                      disabled={loading || feedbackRating === 0}
                      className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                      size="lg"
                    >
                      {loading ? (
                        <>
                          <Spinner
                            variant="white"
                            size="sm"
                            className={`${isRTL ? "ml-2" : "mr-2"}`}
                          />
                          <span className="hidden sm:inline">
                            {t.feedback.submitting}
                          </span>
                          <span className="sm:hidden">...</span>
                        </>
                      ) : (
                        t.feedback.submitFeedback
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pre-Assessment - Mobile-optimized */}
          {progress.currentStep === OnboardingStep.PRE_ASSESSMENT && (
            <Card className="p-4 sm:p-6 lg:p-8">
              <CardHeader className={`text-center ${rtlClasses.textAlign}`}>
                <FaClipboardList className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-gold mx-auto mb-3 sm:mb-4" />
                <CardTitle className="text-xl sm:text-2xl mb-2">
                  {t.assessment.title}
                </CardTitle>
                <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2">
                  {t.assessment.subtitle}
                </p>
              </CardHeader>
              <CardContent>
                <div className="max-w-2xl mx-auto space-y-4 sm:space-y-6">
                  {questionsLoading ? (
                    <div className="text-center py-6 sm:py-8">
                      <Spinner
                        variant="gold"
                        size="md"
                        className={`${isRTL ? "ml-2" : "mr-2"}`}
                      />
                      <p className="text-sm sm:text-base mt-2">
                        {t.common.loading}
                      </p>
                    </div>
                  ) : selectedQuestion ? (
                    <div
                      className={`space-y-4 sm:space-y-6 px-2 ${rtlClasses.textAlign}`}
                    >
                      <div className="space-y-3 sm:space-y-4">
                        <label className="block text-sm font-medium text-gray-700">
                          {t.assessment.questionLabel}
                        </label>
                        <h3 className="text-base sm:text-lg font-semibold text-gray-800 leading-relaxed">
                          {selectedQuestion.question_text[currentLanguage] ||
                            selectedQuestion.question_text.EN}
                        </h3>
                      </div>

                      {/* Mobile-optimized radio group */}
                      <div className="space-y-2 sm:space-y-3">
                        <RadioGroup
                          options={selectedQuestion.options.map(
                            (option: any) => ({
                              label:
                                option.text[currentLanguage] || option.text.EN,
                              value: option.value.toString()
                            })
                          )}
                          value={selectedAnswer?.toString() || ""}
                          onChange={(value) =>
                            setSelectedAnswer(parseInt(value as string))
                          }
                          name="assessment-question"
                          orientation="vertical"
                          className="space-y-2 sm:space-y-3"
                        />
                      </div>

                      {/* Mobile-first button layout */}
                      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center pt-4 sm:pt-6">
                        <Button
                          variant="outline"
                          onClick={goToPreviousStep}
                          className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                          size="lg"
                        >
                          {isRTL ? <FaArrowRight /> : <FaArrowLeft />}
                          {t.assessment.previous}
                        </Button>

                        <Button
                          onClick={handleAssessmentSubmit}
                          disabled={loading || selectedAnswer === null}
                          className="flex items-center justify-center gap-2 w-full sm:w-auto touch-manipulation"
                          size="lg"
                        >
                          {loading ? (
                            <>
                              <Spinner
                                variant="white"
                                size="sm"
                                className={`${isRTL ? "ml-2" : "mr-2"}`}
                              />
                              <span className="hidden sm:inline">
                                {t.assessment.submitting}
                              </span>
                              <span className="sm:hidden">...</span>
                            </>
                          ) : (
                            t.assessment.submitAssessment
                          )}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className={`text-center py-6 sm:py-8 px-2 ${rtlClasses.textAlign}`}
                    >
                      <p className="text-sm sm:text-base text-gray-600">
                        {t.common.error}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completion - Mobile-optimized */}
          {progress.currentStep === OnboardingStep.COMPLETED && (
            <Card className="p-4 sm:p-6 lg:p-8 text-center">
              <CardContent>
                <div
                  className={`space-y-4 sm:space-y-6 px-2 ${rtlClasses.textAlign}`}
                >
                  {/* Mobile-responsive success icon */}
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <FaCheck className="w-8 h-8 sm:w-10 sm:h-10 text-green-600" />
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800">
                      {t.completion.title}
                    </h2>
                    <p className="text-sm sm:text-base text-gray-600 max-w-md mx-auto px-2">
                      {t.completion.subtitle}
                    </p>
                  </div>

                  {/* Mobile-first CTA button */}
                  <div className="pt-2 sm:pt-4">
                    <Button
                      size="lg"
                      onClick={handleAccessDashboard}
                      className="flex items-center justify-center gap-2 w-full sm:w-auto mx-auto touch-manipulation text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4"
                    >
                      {t.completion.accessDashboard}
                      {isRTL ? <FaArrowLeft /> : <FaArrowRight />}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmployeeOnboardingFlow
