import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Alert } from '@/components/ui/Alert';
import { GradientText } from '@/components/ui/GradientText';
import { Spinner } from '@/components/ui/Spinner';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faQrcode, 
  faDownload, 
  faRedo, 
  faShare, 
  faPrint, 
  faCopy, 
  faCheck,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { 
  useGetMyQRCodeQuery, 
  useRegenerateEmployeeQRCodeMutation,
  type GenerateQRCodeDto 
} from '@/store/api/employeeApi';
import { toast } from 'sonner';

interface EmployeeQRCodeProps {
  user: {
    id: string;
    name: string;
    email: string;
    company_id: string;
    department?: string;
    position?: string;
    employee_id?: string;
  };
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export function EmployeeQRCode({ user, size = 'md' }: EmployeeQRCodeProps) {
  const [copied, setCopied] = useState(false);
  const [qrOptions, setQrOptions] = useState<GenerateQRCodeDto>({
    size: 256,
    error_correction: 'M',
    include_employee_data: true,
    expires_in_days: 30
  });

  // Fetch QR code data from API
  const { 
    data: qrCodeData, 
    isLoading, 
    error, 
    refetch 
  } = useGetMyQRCodeQuery(qrOptions);

  const [regenerateQRCode, { isLoading: isRegenerating }] = useRegenerateEmployeeQRCodeMutation();

  const sizeClasses = {
    sm: { container: 'max-w-md', qr: 'w-32 h-32', text: 'text-sm' },
    md: { container: 'max-w-lg', qr: 'w-48 h-48', text: 'text-base' },
    lg: { container: 'max-w-xl', qr: 'w-64 h-64', text: 'text-lg' },
    xl: { container: 'max-w-2xl', qr: 'w-80 h-80', text: 'text-xl' }
  };

  const classes = sizeClasses[size];

  const handleDownload = async () => {
    if (!qrCodeData?.qr_code_url) return;
    
    try {
      const response = await fetch(qrCodeData.qr_code_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${user.name.replace(/\s+/g, '_')}_QR_Code.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      toast.success('QR Code downloaded successfully');
    } catch (error) {
      toast.error('Failed to download QR code');
    }
  };

  const handleShare = async () => {
    if (!qrCodeData?.qr_code_url) return;
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: `${user.name} - Employee QR Code`,
          text: `QR Code for ${user.name} (${user.email})`,
          url: qrCodeData.qr_code_url
        });
      } else {
        // Fallback: copy URL to clipboard
        await navigator.clipboard.writeText(qrCodeData.qr_code_url);
        toast.success('QR Code URL copied to clipboard');
      }
    } catch (error) {
      toast.error('Failed to share QR code');
    }
  };

  const handlePrint = () => {
    if (!qrCodeData?.qr_code_url) return;
    
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    printWindow.document.write(`
      <html>
        <head>
          <title>${user.name} - QR Code</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              text-align: center; 
              padding: 20px; 
              background: white;
            }
            .qr-container { 
              border: 2px solid #d2b37a; 
              padding: 20px; 
              margin: 20px auto; 
              max-width: 400px;
              border-radius: 10px;
            }
            img { max-width: 256px; height: auto; }
            .info { margin: 20px 0; line-height: 1.6; }
            .verification { 
              background: #f5f5f5; 
              padding: 10px; 
              border-radius: 5px; 
              font-family: monospace;
              font-weight: bold;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h2>Employee QR Code</h2>
            <img src="${qrCodeData.qr_code_url}" alt="QR Code" />
            <div class="info">
              <strong>${user.name}</strong><br>
              ${user.email}<br>
              ${user.department || 'General'} Department<br>
              ${user.position || 'Employee'}<br>
              ID: ${user.employee_id || user.id}
            </div>
            <div class="verification">
              Verification: ${qrCodeData.verification_code}
            </div>
            <p style="font-size: 12px; color: #666;">
              Generated: ${new Date(qrCodeData.created_at).toLocaleDateString()}<br>
              ${qrCodeData.expires_at ? `Expires: ${new Date(qrCodeData.expires_at).toLocaleDateString()}` : 'No expiration'}
            </p>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const handleCopy = async () => {
    if (!qrCodeData?.verification_code) return;
    
    try {
      await navigator.clipboard.writeText(qrCodeData.verification_code);
      setCopied(true);
      toast.success('Verification code copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy verification code');
    }
  };

  const handleRegenerate = async () => {
    try {
      await regenerateQRCode({ 
        employeeId: user.id, 
        options: qrOptions 
      }).unwrap();
      toast.success('QR Code regenerated successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to regenerate QR code');
    }
  };

  const handleQrOptionsChange = (newOptions: Partial<GenerateQRCodeDto>) => {
    setQrOptions(prev => ({ ...prev, ...newOptions }));
  };

  if (isLoading) {
    return (
      <div className={`mx-auto ${classes.container}`}>
        <Card className="border-2 border-[#d2b37a]">
          <CardContent className="p-8 text-center">
            <Spinner variant="gold" size="lg" className="mx-auto mb-4" />
            <p className="text-gray-600">Generating your QR code...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`mx-auto ${classes.container}`}>
        <Card className="border-2 border-red-300">
          <CardContent className="p-8 text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-4xl mb-4" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">
              Failed to Load QR Code
            </h3>
            <p className="text-red-600 mb-4">
              {error && 'status' in error 
                ? `Error ${error.status}: ${error.data?.message || 'Unable to generate QR code'}`
                : 'Network error occurred'}
            </p>
            <Button 
              onClick={() => refetch()} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              <FontAwesomeIcon icon={faRedo} className="mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!qrCodeData) {
    return (
      <div className={`mx-auto ${classes.container}`}>
        <Card className="border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <p className="text-gray-600">No QR code available</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`mx-auto ${classes.container}`}>
      <Card className="border-2 border-[#d2b37a] shadow-xl">
        <CardHeader className="text-center bg-gradient-to-r from-[#d2b37a]/10 to-[#c19648]/10">
          <CardTitle className="flex items-center justify-center gap-3">
            <FontAwesomeIcon icon={faQrcode} className="text-[#d2b37a]" />
            <GradientText className={classes.text}>
              My QR Code
            </GradientText>
          </CardTitle>
          <CardDescription>
            Use this QR code for on-site events and verification
          </CardDescription>
        </CardHeader>

        <CardContent className="p-6 space-y-6">
          {/* QR Code Display */}
          <div className="text-center">
            <div className={`${classes.qr} mx-auto border-4 border-[#d2b37a]/20 rounded-lg p-4 bg-white shadow-inner`}>
              <img 
                src={qrCodeData.qr_code_url} 
                alt="Employee QR Code"
                className="w-full h-full object-contain"
              />
            </div>
          </div>

          {/* Employee Information */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <h3 className="font-semibold text-gray-800 text-center mb-3">Employee Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div><span className="font-medium">Name:</span> {user.name}</div>
              <div><span className="font-medium">Email:</span> {user.email}</div>
              <div><span className="font-medium">Department:</span> {user.department || 'General'}</div>
              <div><span className="font-medium">Position:</span> {user.position || 'Employee'}</div>
              <div><span className="font-medium">Employee ID:</span> {user.employee_id || user.id}</div>
              <div>
                <span className="font-medium">Status:</span>{' '}
                <Badge variant={qrCodeData.is_active ? "default" : "secondary"}>
                  {qrCodeData.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Verification Code */}
          <div className="bg-[#d2b37a]/10 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-800">Verification Code</h4>
                <code className="text-lg font-mono bg-white px-2 py-1 rounded border">
                  {qrCodeData.verification_code}
                </code>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="ml-2"
              >
                <FontAwesomeIcon 
                  icon={copied ? faCheck : faCopy} 
                  className={copied ? "text-green-600" : "text-gray-600"} 
                />
              </Button>
            </div>
          </div>

          {/* QR Code Metadata */}
          <div className="text-sm text-gray-600 space-y-1">
            <div><span className="font-medium">Generated:</span> {new Date(qrCodeData.created_at).toLocaleDateString()}</div>
            {qrCodeData.expires_at && (
              <div><span className="font-medium">Expires:</span> {new Date(qrCodeData.expires_at).toLocaleDateString()}</div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button 
              variant="outline" 
              onClick={handleDownload}
              className="flex flex-col items-center p-4 h-auto"
            >
              <FontAwesomeIcon icon={faDownload} className="mb-1" />
              <span className="text-xs">Download</span>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleShare}
              className="flex flex-col items-center p-4 h-auto"
            >
              <FontAwesomeIcon icon={faShare} className="mb-1" />
              <span className="text-xs">Share</span>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handlePrint}
              className="flex flex-col items-center p-4 h-auto"
            >
              <FontAwesomeIcon icon={faPrint} className="mb-1" />
              <span className="text-xs">Print</span>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleRegenerate}
              disabled={isRegenerating}
              className="flex flex-col items-center p-4 h-auto"
            >
              {isRegenerating ? (
                <Spinner variant="default" size="sm" className="mb-1" />
              ) : (
                <FontAwesomeIcon icon={faRedo} className="mb-1" />
              )}
              <span className="text-xs">Regenerate</span>
            </Button>
          </div>

                     {/* Usage Instructions */}
           <Alert
             icon={<FontAwesomeIcon icon={faQrcode} className="h-4 w-4" />}
             description={
               <>
                 <strong>How to use:</strong> Show this QR code at event check-ins. 
                 Event organizers can scan it to verify your identity and access permissions. 
                 Keep your verification code secure for manual verification if needed.
               </>
             }
           />
        </CardContent>
      </Card>
    </div>
  );
} 