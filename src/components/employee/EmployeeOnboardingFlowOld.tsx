import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useLanguage, SUPPORTED_LANGUAGES } from '../../utils/languageUtils'
import { onboardingTranslations } from '../../utils/onboardingTranslations'
import { 
  FaGlobe, 
  FaVideo, 
  FaCalendarAlt, 
  FaClipboardList, 
  FaArrowRight, 
  FaArrowLeft,
  FaCheck,
  FaPlay,
  FaClock,
  FaUsers,
  FaStar,
  FaComments,
  FaPause,
  FaForward
} from 'react-icons/fa'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  Button,
  Badge,
  Spinner,
  ProgressBar,
  StepIndicator,
  VideoPlayer,
  StarRating,
  GradientText,
  Textarea,
  RadioGroup,
  type Step
} from '../ui'
// import { useGetQuestionsQuery } from '../../store/api'

// Use the main language system from languageUtils

// Webinar slot type
interface WebinarSlot {
  id: string
  date: string
  time: string
  title: string
  duration: string
  spots: number
  maxSpots: number
  dateFormatted: string
}

// Generate webinar slots for next 20 days from June 30th, 2025
const generateWebinarSlots = (): WebinarSlot[] => {
  const slots: WebinarSlot[] = []
  const startDate = new Date('2025-06-30')
  const times = ['09:00', '11:00', '14:00', '16:00']
  
  for (let day = 0; day < 20; day++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + day)
    
    times.forEach((time, index) => {
      slots.push({
        id: `${day}-${index}`,
        date: date.toISOString().split('T')[0],
        time,
        title: 'Corporate Wellness Introduction',
        duration: '45 mins',
        spots: Math.floor(Math.random() * 15) + 5, // Random spots between 5-20
        maxSpots: 20,
        dateFormatted: date.toLocaleDateString('en-US', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })
      })
    })
  }
  
  return slots
}

// Onboarding steps
enum OnboardingStep {
  LANGUAGE_SELECTION = 'language',
  INTRO_VIDEO = 'video', 
  WEBINAR_BOOKING = 'webinar',
  WEBINAR_FEEDBACK = 'feedback',
  PRE_ASSESSMENT = 'assessment',
  COMPLETED = 'completed'
}

interface OnboardingProgress {
  currentStep: OnboardingStep
  selectedLanguage: string
  videoWatched: boolean
  selectedWebinar: string
  feedbackSubmitted: boolean
  assessmentCompleted: boolean
  completedAt?: string
}

const EmployeeOnboardingFlow: React.FC = () => {
  const navigate = useNavigate()
  const { currentLanguage, changeLanguage, rtlClasses } = useLanguage()
  
  // Get translations for current language
  const t = onboardingTranslations[currentLanguage as 'EN' | 'AR' | 'HI'] || onboardingTranslations.EN
  
  // Load progress from localStorage
  const loadProgress = (): OnboardingProgress => {
    const saved = localStorage.getItem('sehatti-onboarding-progress')
    if (saved) {
      try {
        return JSON.parse(saved)
      } catch (error) {
        console.error('Failed to parse onboarding progress:', error)
      }
    }
    
    return {
      currentStep: OnboardingStep.LANGUAGE_SELECTION,
      selectedLanguage: '',
      videoWatched: false,
      selectedWebinar: '',
      feedbackSubmitted: false,
      assessmentCompleted: false
    }
  }

  const [progress, setProgress] = useState<OnboardingProgress>(loadProgress)
  const [loading, setLoading] = useState(false)
  const [videoPlaying, setVideoPlaying] = useState(false)
  const [videoCurrentTime, setVideoCurrentTime] = useState(0)
  const [videoDuration, setVideoDuration] = useState(0)
  const [feedbackRating, setFeedbackRating] = useState(0)
  const [feedbackText, setFeedbackText] = useState('')
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)

  // Get webinar slots
  const [webinarSlots] = useState(generateWebinarSlots())

  // Mock pre-assessment questions (replace with real API call later)
  const preAssessmentQuestions = [
    {
      id: "1",
      question_text: {
        en: "How focused were you during work today?",
        ar: "ما مدى تركيزك في العمل اليوم؟",
        hi: "आज काम करते समय आप कितने ध्यान केंद्रित थे?"
      },
      options: [
        { text: { en: "Not focused at all", ar: "غير مركز إطلاقاً", hi: "बिल्कुल भी ध्यान नहीं दिया" }, value: 1 },
        { text: { en: "Slightly focused", ar: "مركّز بشكل طفيف", hi: "थोड़ा ध्यान केंद्रित" }, value: 2 },
        { text: { en: "Moderately focused", ar: "مركّز إلى حد ما", hi: "मध्यम रूप से केंद्रित" }, value: 3 },
        { text: { en: "Very focused", ar: "مركّز جدًا", hi: "बहुत ध्यान केंद्रित" }, value: 4 }
      ]
    }
  ]
  const questionsLoading = false

  // Save progress to localStorage
  const saveProgress = (newProgress: OnboardingProgress) => {
    localStorage.setItem('sehatti-onboarding-progress', JSON.stringify(newProgress))
    setProgress(newProgress)
  }

  // Step progression with translations
  const steps: Step[] = [
    { id: OnboardingStep.LANGUAGE_SELECTION, title: t.steps.language, icon: FaGlobe },
    { id: OnboardingStep.INTRO_VIDEO, title: t.steps.welcome, icon: FaVideo },
    { id: OnboardingStep.WEBINAR_BOOKING, title: t.steps.webinar, icon: FaCalendarAlt },
    { id: OnboardingStep.WEBINAR_FEEDBACK, title: t.steps.feedback, icon: FaComments },
    { id: OnboardingStep.PRE_ASSESSMENT, title: t.steps.assessment, icon: FaClipboardList },
  ]

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === progress.currentStep)
  }

  const selectedLang = SUPPORTED_LANGUAGES.find(lang => lang.code === progress.selectedLanguage)

  // Initialize random question for pre-assessment
  useEffect(() => {
    if (preAssessmentQuestions && preAssessmentQuestions.length > 0 && !selectedQuestion) {
      // Just pick the first question for simplicity
      setSelectedQuestion(preAssessmentQuestions[0])
    }
  }, [preAssessmentQuestions, selectedQuestion])

  const handleLanguageSelect = (langCode: string) => {
    // Update main app language
    changeLanguage(langCode.toUpperCase() as any)
    
    const newProgress = {
      ...progress,
      selectedLanguage: langCode,
      currentStep: OnboardingStep.INTRO_VIDEO
    }
    saveProgress(newProgress)
    toast.success(t.toasts.languageSaved)
  }

  const handleVideoComplete = () => {
    const newProgress = {
      ...progress,
      videoWatched: true,
      currentStep: OnboardingStep.WEBINAR_BOOKING
    }
    saveProgress(newProgress)
    setVideoPlaying(false)
    toast.success('Welcome video completed!')
  }

  const handleWebinarSelect = async (webinarId: string) => {
    setLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const newProgress = {
      ...progress,
      selectedWebinar: webinarId,
      currentStep: OnboardingStep.WEBINAR_FEEDBACK
    }
    saveProgress(newProgress)
    setLoading(false)
    toast.success('Webinar slot booked successfully!')
  }

  const handleFeedbackSubmit = async () => {
    if (feedbackRating === 0) {
      toast.error('Please provide a rating')
      return
    }

    setLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const newProgress = {
        ...progress,
        feedbackSubmitted: true,
        currentStep: OnboardingStep.PRE_ASSESSMENT
      }
      saveProgress(newProgress)
      toast.success('Feedback submitted successfully!')
    } catch (error) {
      console.error('Feedback submission error:', error)
      toast.error('Failed to submit feedback. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleAssessmentSubmit = async () => {
    if (selectedAnswer === null) {
      toast.error('Please select an answer')
      return
    }

    setLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newProgress = {
      ...progress,
      assessmentCompleted: true,
      currentStep: OnboardingStep.COMPLETED,
      completedAt: new Date().toISOString()
    }
    saveProgress(newProgress)
    setLoading(false)
    toast.success('Pre-assessment completed!')
  }

  const handleAccessDashboard = () => {
    // Mark onboarding as completed instead of clearing it
    const completedProgress = {
      ...progress,
      currentStep: OnboardingStep.COMPLETED,
      completedAt: new Date().toISOString()
    }
    saveProgress(completedProgress)
    
    // Also set a simpler completion flag for quick checks
    localStorage.setItem('sehatti-onboarding-completed', 'true')
    
    navigate('/employee/dashboard')
  }

  const goToPreviousStep = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex > 0) {
      const newProgress = {
        ...progress,
        currentStep: steps[currentIndex - 1].id as OnboardingStep
      }
      saveProgress(newProgress)
    }
  }

  const renderProgressSection = () => {
    const currentIndex = getCurrentStepIndex()
    const progressPercentage = ((currentIndex + 1) / steps.length) * 100

    return (
      <div className="mb-8 space-y-6">
        <ProgressBar
          percentage={progressPercentage}
          title="Onboarding Progress"
          currentStep={currentIndex + 1}
          totalSteps={steps.length}
        />
        
        <StepIndicator
          steps={steps}
          currentStepIndex={currentIndex}
          orientation="horizontal"
          size="md"
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-sehatti-gold-light p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8 pt-8">
          <GradientText className="text-3xl font-bold mb-2">
            Welcome to Sehatti
          </GradientText>
          <p className="text-gray-600">
            Complete your onboarding to access your wellness dashboard
          </p>
        </div>

        {progress.currentStep !== OnboardingStep.COMPLETED && renderProgressSection()}

        <div className="space-y-6">
          {/* Language Selection */}
          {progress.currentStep === OnboardingStep.LANGUAGE_SELECTION && (
            <Card className="p-8">
              <CardHeader className="text-center">
                <FaGlobe className="w-16 h-16 text-sehatti-gold mx-auto mb-4" />
                <CardTitle className="text-2xl mb-2">Select Your Language</CardTitle>
                <p className="text-gray-600">
                  Choose your preferred language for the best experience
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <Button
                      key={lang.code}
                      variant="outline"
                      className="h-auto p-6 flex flex-col items-center space-y-3 hover:border-sehatti-gold hover:bg-sehatti-gold-light"
                      onClick={() => handleLanguageSelect(lang.code)}
                    >
                      <span className="text-3xl">{lang.flag}</span>
                      <span className="font-semibold">{lang.name}</span>
                      <span className="text-sm text-gray-600 text-center">
                        {onboardingTranslations[lang.code.toUpperCase() as 'EN' | 'AR' | 'HI']?.languageSelection.greeting || t.languageSelection.greeting}
                      </span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Intro Video */}
          {progress.currentStep === OnboardingStep.INTRO_VIDEO && (
            <Card className="p-8">
              <CardHeader className="text-center">
                <FaVideo className="w-16 h-16 text-sehatti-gold mx-auto mb-4" />
                <CardTitle className="text-2xl mb-2">
                  {selectedLang?.greeting || 'Welcome to Sehatti!'}
                </CardTitle>
                <p className="text-gray-600">
                  {selectedLang?.description || 'Your corporate wellness journey begins here'}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <VideoPlayer
                    src="https://blntek-shared-public-assets-869370432397.s3.us-east-1.amazonaws.com/intro+(1080p).mp4"
                    title="Corporate Wellness Introduction"
                    description="Click to play (70MB video - 3min 52sec)"
                    onPlay={() => {
                      console.log('Video started playing')
                      setVideoPlaying(true)
                    }}
                    onPause={() => {
                      console.log('Video paused')
                      setVideoPlaying(false)
                    }}
                    onTimeUpdate={(currentTime) => {
                      setVideoCurrentTime(currentTime)
                      // Remove excessive logging
                      if (Math.floor(currentTime) % 5 === 0) {
                        console.log('Video time:', Math.floor(currentTime))
                      }
                    }}
                    onLoadedMetadata={(duration) => {
                      setVideoDuration(duration)
                      console.log('Video duration:', duration, 'seconds')
                    }}
                    onEnded={() => {
                      console.log('Video ended')
                      handleVideoComplete()
                    }}
                    onError={(error) => {
                      console.error('Video error:', error)
                      toast.error('Failed to load video. Please try again.')
                    }}
                    preload="metadata"
                    playsInline
                  />
                  
                  <div className="text-center">
                    <GradientText className="text-lg font-semibold mb-2">Welcome to Your Wellness Journey</GradientText>
                    <p className="text-gray-600 mb-4">
                      Learn about our comprehensive corporate wellness program
                    </p>
                    
                    {videoDuration > 0 && (
                      <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                        <span>
                          {Math.floor(videoCurrentTime / 60)}:{(videoCurrentTime % 60).toFixed(0).padStart(2, '0')} / 
                          {Math.floor(videoDuration / 60)}:{(videoDuration % 60).toFixed(0).padStart(2, '0')}
                        </span>
                      </div>
                    )}

                    <div className="flex justify-center space-x-4 mt-6">
                      {getCurrentStepIndex() > 0 && (
                        <Button variant="outline" onClick={goToPreviousStep}>
                          <FaArrowLeft className="w-4 h-4 mr-2" />
                          Previous
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Webinar Booking */}
          {progress.currentStep === OnboardingStep.WEBINAR_BOOKING && (
            <Card className="p-8">
              <CardHeader className="text-center">
                <FaCalendarAlt className="w-16 h-16 text-sehatti-gold mx-auto mb-4" />
                <CardTitle className="text-2xl mb-2">Book Your Webinar</CardTitle>
                <p className="text-gray-600">
                  Select a convenient time slot for your personalized training session
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {webinarSlots.slice(0, 12).map((slot) => (
                    <div
                      key={slot.id}
                      className="border rounded-lg p-4 hover:border-sehatti-gold hover:bg-sehatti-gold-light cursor-pointer transition-all"
                      onClick={() => !loading && handleWebinarSelect(slot.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-xs">
                          {slot.duration}
                        </Badge>
                        <div className="flex items-center text-xs text-gray-600">
                          <FaUsers className="w-3 h-3 mr-1" />
                          {slot.spots}/{slot.maxSpots}
                        </div>
                      </div>
                      
                      <h4 className="font-medium text-sm mb-1">{slot.title}</h4>
                      <p className="text-xs text-gray-600 mb-2">
                        {slot.dateFormatted}
                      </p>
                      <div className="flex items-center text-xs text-gray-600">
                        <FaClock className="w-3 h-3 mr-1" />
                        {slot.time}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex justify-center space-x-4 mt-6">
                  <Button variant="outline" onClick={goToPreviousStep}>
                    <FaArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                </div>

                {loading && (
                  <div className="flex justify-center items-center mt-4">
                    <Spinner variant="gold" size="md" className="mr-2" />
                    <span className="text-sehatti-gold-600">Booking your slot...</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Webinar Feedback */}
          {progress.currentStep === OnboardingStep.WEBINAR_FEEDBACK && (
            <Card className="p-8">
              <CardHeader className="text-center">
                <FaComments className="w-16 h-16 text-sehatti-gold mx-auto mb-4" />
                <CardTitle className="text-2xl mb-2">Webinar Feedback</CardTitle>
                <p className="text-gray-600">
                  Help us improve by sharing your experience
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">
                      How would you rate your overall experience?
                    </label>
                    <div className="flex justify-center">
                      <StarRating
                        rating={feedbackRating}
                        onRatingChange={setFeedbackRating}
                        size="lg"
                        showValue={false}
                        color="gold"
                        className="mb-2"
                      />
                    </div>
                    <p className="text-center text-sm text-gray-600 mt-2">
                      {feedbackRating > 0 && `${feedbackRating} star${feedbackRating > 1 ? 's' : ''}`}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Comments (Optional)
                    </label>
                    <Textarea
                      rows={4}
                      placeholder="Share your thoughts about the webinar experience..."
                      value={feedbackText}
                      onChange={(e) => setFeedbackText(e.target.value)}
                    />
                  </div>

                  <div className="flex justify-center space-x-4 mt-6">
                    <Button variant="outline" onClick={goToPreviousStep}>
                      <FaArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </Button>
                    <Button 
                      onClick={handleFeedbackSubmit}
                      disabled={loading || feedbackRating === 0}
                      variant="default"
                      size="default"
                    >
                      {loading ? (
                        <>
                          <Spinner variant="white" size="sm" className="mr-2" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <FaArrowRight className="w-4 h-4 mr-2" />
                          Submit Feedback
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pre-Assessment */}
          {progress.currentStep === OnboardingStep.PRE_ASSESSMENT && (
            <Card className="p-8">
              <CardHeader className="text-center">
                <FaClipboardList className="w-16 h-16 text-sehatti-gold mx-auto mb-4" />
                <CardTitle className="text-2xl mb-2">Pre-Assessment</CardTitle>
                <p className="text-gray-600">
                  Complete this quick assessment to personalize your experience
                </p>
              </CardHeader>
              <CardContent>
                {questionsLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Spinner variant="gold" size="md" className="mr-2" />
                    <span className="text-sehatti-gold-600">Loading assessment questions...</span>
                  </div>
                ) : selectedQuestion ? (
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-6">
                      <RadioGroup
                        label={selectedQuestion.question_text[progress.selectedLanguage] || selectedQuestion.question_text.en}
                        options={selectedQuestion.options.map((option: any) => ({
                          value: option.value,
                          label: option.text[progress.selectedLanguage] || option.text.en
                        }))}
                        value={selectedAnswer}
                        onChange={(value) => setSelectedAnswer(value as number)}
                        size="default"
                        orientation="vertical"
                        required={true}
                      />
                    </div>

                    <div className="flex justify-center space-x-4">
                      <Button variant="outline" onClick={goToPreviousStep}>
                        <FaArrowLeft className="w-4 h-4 mr-2" />
                        Previous
                      </Button>
                      <Button 
                        onClick={handleAssessmentSubmit}
                        disabled={loading || selectedAnswer === null}
                        variant="default"
                        size="default"
                      >
                        {loading ? (
                          <>
                            <Spinner variant="white" size="sm" className="mr-2" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <FaArrowRight className="w-4 h-4 mr-2" />
                            Complete Assessment
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-600">
                      No assessment questions available at the moment.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Completed */}
          {progress.currentStep === OnboardingStep.COMPLETED && (
            <Card className="p-8 text-center">
              <CardContent>
                <div className="space-y-6">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <FaCheck className="w-10 h-10 text-green-600" />
                  </div>
                  
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      {selectedLang?.greeting || 'Welcome to Sehatti!'} 🎉
                    </h2>
                    <p className="text-gray-600">
                      Your onboarding is complete. You're ready to start your wellness journey!
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-left">
                    <div className="p-4 bg-sehatti-gold-light rounded-lg">
                      <FaGlobe className="w-6 h-6 text-sehatti-gold mb-2" />
                      <h4 className="font-medium">Language Set</h4>
                      <p className="text-sm text-gray-600">
                        {selectedLang?.name}
                      </p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <FaVideo className="w-6 h-6 text-blue-600 mb-2" />
                      <h4 className="font-medium">Video Watched</h4>
                      <p className="text-sm text-gray-600">
                        Introduction completed
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <FaCalendarAlt className="w-6 h-6 text-purple-600 mb-2" />
                      <h4 className="font-medium">Webinar Booked</h4>
                      <p className="text-sm text-gray-600">
                        Session scheduled
                      </p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <FaClipboardList className="w-6 h-6 text-green-600 mb-2" />
                      <h4 className="font-medium">Assessment Done</h4>
                      <p className="text-sm text-gray-600">
                        Profile created
                      </p>
                    </div>
                  </div>

                  <Button 
                    onClick={handleAccessDashboard}
                    size="lg"
                    variant="default"
                  >
                    {selectedLang?.accessDashboard || 'Access my dashboard'}
                    <FaArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmployeeOnboardingFlow 