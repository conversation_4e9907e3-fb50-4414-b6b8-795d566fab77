import { useEffect } from "react"
import { useTokenRefresh } from "@/hooks/useTokenRefresh"
import { useAppSelector } from "@/store/store"
import { selectIsAuthenticated } from "@/features/auth/auth-slice"

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const { scheduleTokenRefresh, cancelScheduledRefresh, isTokenValid } =
    useTokenRefresh()

  // Initialize token refresh on app startup
  useEffect(() => {
    if (isAuthenticated && isTokenValid()) {
      scheduleTokenRefresh()
    } else if (isAuthenticated && !isTokenValid()) {
      cancelScheduledRefresh()
    }

    // Cleanup on unmount
    return () => {
      cancelScheduledRefresh()
    }
  }, [
    isAuthenticated,
    scheduleTokenRefresh,
    cancelScheduledRefresh,
    isTokenValid
  ])

  // Handle page visibility changes to refresh token when user returns
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isAuthenticated) {
        // User returned to the tab, check if token needs refresh
        if (!isTokenValid()) {
          scheduleTokenRefresh()
        }
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange)
  }, [isAuthenticated, isTokenValid, scheduleTokenRefresh])

  // Handle app focus events
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated && !isTokenValid()) {
        scheduleTokenRefresh()
      }
    }

    window.addEventListener("focus", handleFocus)
    return () => window.removeEventListener("focus", handleFocus)
  }, [isAuthenticated, isTokenValid, scheduleTokenRefresh])

  return <>{children}</>
}
