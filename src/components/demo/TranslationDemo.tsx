import React from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export const TranslationDemo: React.FC = () => {
  const { t, currentLanguage, changeLanguage, isRTL, rtlClasses } = useTranslation()

  return (
    <div className={`max-w-4xl mx-auto p-6 ${rtlClasses.direction}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <Card>
        <CardHeader>
          <CardTitle className={rtlClasses.textAlign}>
            Translation System Demo - Current Language: {currentLanguage}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Language Switcher */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>Language Switcher:</h3>
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Button 
                onClick={() => changeLanguage('EN')}
                variant={currentLanguage === 'EN' ? 'default' : 'outline'}
                size="sm"
              >
                English
              </Button>
              <Button 
                onClick={() => changeLanguage('AR')}
                variant={currentLanguage === 'AR' ? 'default' : 'outline'}
                size="sm"
              >
                العربية
              </Button>
              <Button 
                onClick={() => changeLanguage('HI')}
                variant={currentLanguage === 'HI' ? 'default' : 'outline'}
                size="sm"
              >
                हिन्दी
              </Button>
            </div>
          </div>

          {/* Common Translations */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>Common Translations:</h3>
            <div className={`grid grid-cols-2 gap-4 ${rtlClasses.textAlign}`}>
              <div>Welcome: {t.common.welcome}</div>
              <div>Language: {t.common.language}</div>
              <div>Score: {t.common.score}</div>
              <div>Questions: {t.common.questions}</div>
              <div>Articles: {t.common.articles}</div>
              <div>Dimensions: {t.common.dimensions}</div>
            </div>
          </div>

          {/* Navigation Translations */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>Navigation:</h3>
            <div className={`grid grid-cols-2 gap-4 ${rtlClasses.textAlign}`}>
              <div>Overview: {t.navigation.overview}</div>
              <div>Check-in: {t.navigation.checkin}</div>
              <div>Dimensions: {t.navigation.dimensions}</div>
              <div>Profile: {t.navigation.profile}</div>
              <div>QR Code: {t.navigation.qrcode}</div>
            </div>
          </div>

          {/* Dashboard Translations */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>Dashboard:</h3>
            <div className={`space-y-2 ${rtlClasses.textAlign}`}>
              <div>Questions Waiting: {t.dashboard.questionsWaiting}</div>
              <div>Journey Progress: {t.dashboard.journeyProgress}</div>
              <div className="text-sm">{t.dashboard.congratulations}</div>
            </div>
          </div>

          {/* Wellness Divisions */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>Wellness Divisions:</h3>
            <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${rtlClasses.textAlign}`}>
              <div>
                <strong>Physical:</strong> {t.wellnessDivisions.physical.name}
                <p className="text-sm text-gray-600">{t.wellnessDivisions.physical.description}</p>
              </div>
              <div>
                <strong>Emotional:</strong> {t.wellnessDivisions.emotional.name}
                <p className="text-sm text-gray-600">{t.wellnessDivisions.emotional.description}</p>
              </div>
              <div>
                <strong>Social:</strong> {t.wellnessDivisions.social.name}
                <p className="text-sm text-gray-600">{t.wellnessDivisions.social.description}</p>
              </div>
              <div>
                <strong>Occupational:</strong> {t.wellnessDivisions.occupational.name}
                <p className="text-sm text-gray-600">{t.wellnessDivisions.occupational.description}</p>
              </div>
            </div>
          </div>

          {/* RTL Information */}
          <div className="space-y-2">
            <h3 className={`font-semibold ${rtlClasses.textAlign}`}>RTL Information:</h3>
            <div className={`space-y-1 text-sm ${rtlClasses.textAlign}`}>
              <div>Is RTL: {isRTL ? 'Yes' : 'No'}</div>
              <div>Direction: {rtlClasses.direction}</div>
              <div>Text Align: {rtlClasses.textAlign}</div>
              <div>Flex Direction: {rtlClasses.flexDirection}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TranslationDemo
