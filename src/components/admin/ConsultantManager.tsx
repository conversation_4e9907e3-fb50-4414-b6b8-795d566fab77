import React, { useState, useCallback } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import { 
  FaUserTie, 
  FaSearch, 
  FaPlus, 
  FaSync,
  FaTrash,
  FaEdit,
  FaEye,
  FaBuilding,
  FaCheck,
  FaTimes,
  FaUserCheck,
  FaUserShield,
  FaLanguage,
  FaGraduationCap,
  FaDollarSign,
  FaStar,
  FaFilter,
  FaChevronDown,
  FaChevronUp
} from 'react-icons/fa'
import { 
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Badge,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  Spinner,
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '../ui'
import {
  useGetConsultantsQuery,
  useCreateConsultantMutation,
  useUpdateConsultantMutation,
  useDeleteConsultantMutation,
  useVerifyConsultantMutation,
  useUnverifyConsultantMutation,
  useActivateConsultantMutation,
  useDeactivateConsultantMutation,
  useAssignConsultantToCompanyMutation,
  useRemoveConsultantFromCompanyByConsultantMutation,
  useGetConsultantCompaniesQuery,
  useGetConsultantStatsQuery,
  useGetAllCompanySettingsQuery
} from '@/store/api'

// Form validation schema
const consultantFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  bio: z.string().min(10, 'Bio must be at least 10 characters'),
  major: z.string().min(2, 'Major/Field of study is required'),
  languages: z.array(z.string()).min(1, 'Please select at least one language'),
  specializations: z.array(z.string()).min(1, 'Please select at least one specialization'),
  yearsOfExperience: z.number().min(0, 'Experience must be non-negative').max(50, 'Experience cannot exceed 50 years'),
  hourlyRate: z.number().optional(),
})

type ConsultantFormValues = z.infer<typeof consultantFormSchema>

interface ConsultantManagerProps {
  onConsultantSelect?: (consultant: any) => void
  showCompanyAssignment?: boolean
  companyId?: string
}

const ConsultantManager: React.FC<ConsultantManagerProps> = ({ 
  onConsultantSelect, 
  showCompanyAssignment = true,
  companyId 
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [specializationFilter, setSpecializationFilter] = useState('')
  const [verificationFilter, setVerificationFilter] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedConsultant, setSelectedConsultant] = useState<any>(null)
  
  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isCompanyAssignModalOpen, setIsCompanyAssignModalOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // API hooks
  const { 
    data: consultantsData, 
    isLoading: isLoadingConsultants, 
    refetch: refetchConsultants 
  } = useGetConsultantsQuery({
    search: searchQuery || undefined,
    specialization: specializationFilter || undefined,
    isVerified: verificationFilter ? verificationFilter === 'verified' : undefined,
    isActive: statusFilter === 'ACTIVE' ? true : statusFilter === 'INACTIVE' ? false : undefined,
  })

  const { data: statsData } = useGetConsultantStatsQuery()
  const { data: companiesData } = useGetAllCompanySettingsQuery()

  // Mutation hooks
  const [createConsultant] = useCreateConsultantMutation()
  const [updateConsultant] = useUpdateConsultantMutation()
  const [deleteConsultant] = useDeleteConsultantMutation()
  const [verifyConsultant] = useVerifyConsultantMutation()
  const [unverifyConsultant] = useUnverifyConsultantMutation()
  const [activateConsultant] = useActivateConsultantMutation()
  const [deactivateConsultant] = useDeactivateConsultantMutation()
  const [assignToCompany] = useAssignConsultantToCompanyMutation()
  const [removeFromCompany] = useRemoveConsultantFromCompanyByConsultantMutation()

  // Form instances
  const createForm = useForm<ConsultantFormValues>({
    resolver: zodResolver(consultantFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      bio: '',
      major: '',
      languages: [],
      specializations: [],
      yearsOfExperience: 0,
      hourlyRate: undefined,
    },
  })

  const editForm = useForm<ConsultantFormValues>({
    resolver: zodResolver(consultantFormSchema),
  })

  // Options
  const languageOptions = [
    { value: 'ENGLISH', label: 'English' },
    { value: 'ARABIC', label: 'Arabic' },
    { value: 'HINDI', label: 'Hindi' },
    { value: 'URDU', label: 'Urdu' },
    { value: 'FRENCH', label: 'French' },
    { value: 'SPANISH', label: 'Spanish' },
  ]

  const specializationOptions = [
    { value: 'MENTAL_HEALTH', label: 'Mental Health' },
    { value: 'NUTRITION', label: 'Nutrition' },
    { value: 'FITNESS', label: 'Fitness' },
    { value: 'STRESS_MANAGEMENT', label: 'Stress Management' },
    { value: 'WELLNESS_COACHING', label: 'Wellness Coaching' },
    { value: 'LIFE_COACHING', label: 'Life Coaching' },
    { value: 'CAREER_COACHING', label: 'Career Coaching' },
    { value: 'MINDFULNESS', label: 'Mindfulness' },
    { value: 'THERAPY', label: 'Therapy' },
    { value: 'COUNSELING', label: 'Counseling' },
  ]

  // CRUD Operations
  const handleCreateConsultant = async (data: ConsultantFormValues) => {
    try {
      setIsSubmitting(true)
      const createPayload = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        bio: data.bio,
        major: data.major,
        specializations: data.specializations,
        experience: data.yearsOfExperience,
        availableHours: {
          start: "09:00",
          end: "17:00",
          timezone: "UTC"
        },
        hourlyRate: data.hourlyRate,
        languages: data.languages,
      }
      
      await createConsultant(createPayload).unwrap()
      toast.success('Consultant created successfully')
      setIsCreateModalOpen(false)
      createForm.reset()
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error creating consultant:', error)
      toast.error(error?.data?.detail || 'Failed to create consultant')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleUpdateConsultant = async (data: ConsultantFormValues) => {
    if (!selectedConsultant) return
    
    try {
      setIsSubmitting(true)
      await updateConsultant({
        id: selectedConsultant.id,
        data: {
          name: data.name,
          email: data.email,
          phone: data.phone,
          bio: data.bio,
          major: data.major,
          specializations: data.specializations,
          experience: data.yearsOfExperience,
          hourlyRate: data.hourlyRate,
          languages: data.languages,
        }
      }).unwrap()
      
      toast.success('Consultant updated successfully')
      setIsEditModalOpen(false)
      editForm.reset()
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error updating consultant:', error)
      toast.error(error?.data?.detail || 'Failed to update consultant')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteConsultant = async () => {
    if (!selectedConsultant) return
    
    try {
      setIsSubmitting(true)
      await deleteConsultant(selectedConsultant.id).unwrap()
      toast.success('Consultant deleted successfully')
      setIsDeleteModalOpen(false)
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error deleting consultant:', error)
      toast.error(error?.data?.detail || 'Failed to delete consultant')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVerifyConsultant = async (consultantId: string, isVerified: boolean) => {
    try {
      if (isVerified) {
        await unverifyConsultant(consultantId).unwrap()
        toast.success('Consultant unverified successfully')
      } else {
        await verifyConsultant(consultantId).unwrap()
        toast.success('Consultant verified successfully')
      }
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error updating consultant verification:', error)
      toast.error(error?.data?.detail || 'Failed to update verification status')
    }
  }

  const handleActivateConsultant = async (consultantId: string, isActive: boolean) => {
    try {
      if (isActive) {
        await deactivateConsultant(consultantId).unwrap()
        toast.success('Consultant deactivated successfully')
      } else {
        await activateConsultant(consultantId).unwrap()
        toast.success('Consultant activated successfully')
      }
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error updating consultant status:', error)
      toast.error(error?.data?.detail || 'Failed to update status')
    }
  }

  const handleAssignToCompany = async (consultantId: string, companyId: string) => {
    try {
      await assignToCompany({ consultantId, companyId }).unwrap()
      toast.success('Consultant assigned to company successfully')
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error assigning consultant to company:', error)
      toast.error(error?.data?.detail || 'Failed to assign consultant to company')
    }
  }

  const handleRemoveFromCompany = async (consultantId: string, companyId: string) => {
    try {
      await removeFromCompany({ consultantId, companyId }).unwrap()
      toast.success('Consultant removed from company successfully')
      await refetchConsultants()
    } catch (error: any) {
      console.error('Error removing consultant from company:', error)  
      toast.error(error?.data?.detail || 'Failed to remove consultant from company')
    }
  }

  // Modal handlers
  const handleEditClick = (consultant: any) => {
    setSelectedConsultant(consultant)
    editForm.reset({
      name: consultant.name,
      email: consultant.email,
      phone: consultant.phone,
      bio: consultant.bio,
      major: consultant.major || '',
      languages: consultant.languages || [],
      specializations: consultant.specializations || [],
      yearsOfExperience: consultant.experience || 0,
      hourlyRate: consultant.hourlyRate,
    })
    setIsEditModalOpen(true)
  }

  const handleViewDetails = (consultant: any) => {
    setSelectedConsultant(consultant)
    setIsViewModalOpen(true)
  }

  const handleDeleteClick = (consultant: any) => {
    setSelectedConsultant(consultant)
    setIsDeleteModalOpen(true)
  }

  const handleCompanyAssignClick = (consultant: any) => {
    setSelectedConsultant(consultant)
    setIsCompanyAssignModalOpen(true)
  }

  // Loading state
  if (isLoadingConsultants) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2">Loading consultants...</span>
      </div>
    )
  }

  const consultants = consultantsData?.data || []

  // Stats calculations
  const stats = {
    total: statsData?.data?.totalConsultants || consultants.length || 0,
    verified: statsData?.data?.verifiedConsultants || consultants.filter(c => c.isVerified).length || 0,
    active: statsData?.data?.activeConsultants || consultants.filter(c => c.isActive).length || 0,
    averageRating: statsData?.data?.averageRating || 0,
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-white">
            Consultant Management
          </h1>
          <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
            Manage consultants and their company assignments
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => refetchConsultants()}
            leftIcon={<FaSync className="w-4 h-4" />}
          >
            Refresh
          </Button>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            leftIcon={<FaPlus className="w-4 h-4" />}
            className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
          >
            Add Consultant
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray-500">Total Consultants</p>
                <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  {stats.total}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <FaUserTie className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray-500">Verified</p>
                <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  {stats.verified}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <FaUserCheck className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray-500">Active</p>
                <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  {stats.active}
                </p>
              </div>
              <div className="p-3 bg-emerald-100 rounded-lg">
                <FaUserShield className="w-6 h-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-sehatti-warm-gray-500">Avg Rating</p>
                <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  {typeof stats.averageRating === 'number' ? stats.averageRating.toFixed(1) : '0.0'}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-lg">
                <FaStar className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search consultants..."
                  leftIcon={<FaSearch className="w-4 h-4 text-sehatti-warm-gray-400" />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                leftIcon={<FaFilter className="w-4 h-4" />}
                rightIcon={showFilters ? <FaChevronUp className="w-4 h-4" /> : <FaChevronDown className="w-4 h-4" />}
              >
                Filters
              </Button>
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 dark:bg-sehatti-warm-gray-800 dark:text-white"
                  >
                    <option value="">All Status</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">Inactive</option>
                    <option value="PENDING">Pending</option>
                    <option value="SUSPENDED">Suspended</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                    Specialization
                  </label>
                  <select
                    value={specializationFilter}
                    onChange={(e) => setSpecializationFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 dark:bg-sehatti-warm-gray-800 dark:text-white"
                  >
                    <option value="">All Specializations</option>
                    {specializationOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                    Verification
                  </label>
                  <select
                    value={verificationFilter}
                    onChange={(e) => setVerificationFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 dark:bg-sehatti-warm-gray-800 dark:text-white"
                  >
                    <option value="">All</option>
                    <option value="verified">Verified</option>
                    <option value="unverified">Unverified</option>
                  </select>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Consultants Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Consultant</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Specialization</TableHead>
                <TableHead>Experience</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Verification</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {consultants.map((consultant: any) => (
                <TableRow key={consultant.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-sehatti-gold-100 dark:bg-sehatti-gold-900/30 flex items-center justify-center">
                        {consultant.avatar ? (
                          <img 
                            src={consultant.avatar} 
                            alt={consultant.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <FaUserTie className="w-5 h-5 text-sehatti-gold-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-sehatti-warm-gray-900 dark:text-white">
                          {consultant.name}
                        </p>
                        <p className="text-sm text-sehatti-warm-gray-500">
                          {consultant.specializations?.[0] || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm text-sehatti-warm-gray-900 dark:text-white">
                        {consultant.email}
                      </p>
                      <p className="text-sm text-sehatti-warm-gray-500">
                        {consultant.phone}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {consultant.specializations?.slice(0, 2).map((spec: string, idx: number) => (
                        <Badge 
                          key={idx} 
                          variant="secondary" 
                          className="text-xs font-medium bg-sehatti-gold-100 text-sehatti-gold-800 border-sehatti-gold-200 hover:bg-sehatti-gold-200"
                        >
                          {spec.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                      ))}
                      {consultant.specializations?.length > 2 && (
                        <Badge 
                          variant="outline" 
                          className="text-xs font-medium bg-sehatti-warm-gray-50 text-sehatti-warm-gray-600 border-sehatti-warm-gray-300"
                        >
                          +{consultant.specializations.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <FaGraduationCap className="w-4 h-4 text-sehatti-warm-gray-400" />
                      <span className="text-sm">{consultant.experience || 0} years</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <button
                      onClick={() => handleActivateConsultant(consultant.id, consultant.isActive)}
                      className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${
                        consultant.isActive
                          ? 'bg-green-100 text-green-700 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {consultant.isActive ? (
                        <>
                          <FaCheck className="w-3 h-3" />
                          Active
                        </>
                      ) : (
                        <>
                          <FaTimes className="w-3 h-3" />
                          Inactive
                        </>
                      )}
                    </button>
                  </TableCell>
                  <TableCell>
                    <button
                      onClick={() => handleVerifyConsultant(consultant.id, consultant.isVerified)}
                      className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${
                        consultant.isVerified
                          ? 'bg-green-100 text-green-700 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {consultant.isVerified ? (
                        <>
                          <FaCheck className="w-3 h-3" />
                          Verified
                        </>
                      ) : (
                        <>
                          <FaTimes className="w-3 h-3" />
                          Unverified
                        </>
                      )}
                    </button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <FaStar className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm">
                        {typeof consultant.rating === 'number' ? consultant.rating.toFixed(1) : '0.0'}
                      </span>
                      <span className="text-xs text-sehatti-warm-gray-500">
                        ({consultant.totalReviews || 0})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(consultant)}
                        className="text-sehatti-warm-gray-600 hover:text-sehatti-gold-600"
                      >
                        <FaEye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditClick(consultant)}
                        className="text-sehatti-warm-gray-600 hover:text-sehatti-gold-600"
                      >
                        <FaEdit className="w-4 h-4" />
                      </Button>
                      {showCompanyAssignment && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCompanyAssignClick(consultant)}
                          className="text-sehatti-warm-gray-600 hover:text-sehatti-gold-600"
                        >
                          <FaBuilding className="w-4 h-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteClick(consultant)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <FaTrash className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create Consultant Modal */}
      <Modal open={isCreateModalOpen} onClose={() => setIsCreateModalOpen(false)} size="lg">
        <form onSubmit={createForm.handleSubmit(handleCreateConsultant)}>
          <ModalHeader>
            <h2 className="text-xl font-bold text-sehatti-warm-gray-900 dark:text-white">
              Add New Consultant
            </h2>
          </ModalHeader>
          <ModalContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Controller
                  name="name"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <Input
                      label="Full Name"
                      placeholder="Enter full name"
                      {...field}
                      error={fieldState.error?.message}
                    />
                  )}
                />
                
                <Controller
                  name="email"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <Input
                      label="Email"
                      type="email"
                      placeholder="Enter email"
                      {...field}
                      error={fieldState.error?.message}
                    />
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Controller
                  name="phone"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <Input
                      label="Phone"
                      placeholder="Enter phone number"
                      {...field}
                      error={fieldState.error?.message}
                    />
                  )}
                />
                
                <Controller
                  name="yearsOfExperience"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <Input
                      label="Years of Experience"
                      type="number"
                      placeholder="Enter years of experience"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      error={fieldState.error?.message}
                    />
                  )}
                />
              </div>

              <Controller
                name="bio"
                control={createForm.control}
                render={({ field, fieldState }) => (
                  <div>
                    <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                      Bio
                    </label>
                    <textarea
                      placeholder="Enter bio"
                      rows={3}
                      className="w-full px-3 py-2 border border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 dark:bg-sehatti-warm-gray-800 dark:text-white"
                      {...field}
                    />
                    {fieldState.error && (
                      <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                    )}
                  </div>
                )}
              />

              <Controller
                name="major"
                control={createForm.control}
                render={({ field, fieldState }) => (
                  <Input
                    label="Major/Field of Study"
                    placeholder="Enter major or field of study"
                    {...field}
                    error={fieldState.error?.message}
                  />
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Controller
                  name="hourlyRate"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <Input
                      label="Hourly Rate (USD)"
                      type="number"
                      placeholder="Enter hourly rate"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      error={fieldState.error?.message}
                    />
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Controller
                  name="languages"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <div>
                      <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                        Languages
                      </label>
                      <div className="space-y-2">
                        {languageOptions.map((option) => (
                          <label key={option.value} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={field.value.includes(option.value)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  field.onChange([...field.value, option.value])
                                } else {
                                  field.onChange(field.value.filter(v => v !== option.value))
                                }
                              }}
                              className="mr-2"
                            />
                            {option.label}
                          </label>
                        ))}
                      </div>
                      {fieldState.error && (
                        <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="specializations"
                  control={createForm.control}
                  render={({ field, fieldState }) => (
                    <div>
                      <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                        Specializations
                      </label>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {specializationOptions.map((option) => (
                          <label key={option.value} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={field.value.includes(option.value)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  field.onChange([...field.value, option.value])
                                } else {
                                  field.onChange(field.value.filter(v => v !== option.value))
                                }
                              }}
                              className="mr-2"
                            />
                            {option.label}
                          </label>
                        ))}
                      </div>
                      {fieldState.error && (
                        <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>
            </div>
          </ModalContent>
          <ModalFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
            >
              {isSubmitting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                'Create Consultant'
              )}
            </Button>
          </ModalFooter>
        </form>
      </Modal>

      {/* Company Assignment Modal */}
      {showCompanyAssignment && (
        <Modal 
          open={isCompanyAssignModalOpen} 
          onClose={() => setIsCompanyAssignModalOpen(false)}
          size="lg"
        >
          <ModalHeader>
            <h2 className="text-xl font-bold text-sehatti-warm-gray-900 dark:text-white">
              Company Assignment
            </h2>
            <p className="text-sm text-sehatti-warm-gray-600">
              Assign consultant to companies
            </p>
          </ModalHeader>
          <ModalContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-4 bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg">
                <div className="w-10 h-10 rounded-full bg-sehatti-gold-100 dark:bg-sehatti-gold-900/30 flex items-center justify-center">
                  <FaUserTie className="w-5 h-5 text-sehatti-gold-600" />
                </div>
                <div>
                  <p className="font-medium text-sehatti-warm-gray-900 dark:text-white">
                    {selectedConsultant?.name}
                  </p>
                  <p className="text-sm text-sehatti-warm-gray-500">
                    {selectedConsultant?.email}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-sehatti-warm-gray-900 dark:text-white mb-3">
                  Available Companies
                </h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {companiesData?.map((company: any) => (
                    <div 
                      key={company.id} 
                      className="flex items-center justify-between p-3 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                          <FaBuilding className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sehatti-warm-gray-900 dark:text-white">
                            {company.name || company.companyId}
                          </p>
                          <p className="text-sm text-sehatti-warm-gray-500">
                            {company.companyId}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAssignToCompany(selectedConsultant?.id, company.companyId)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <FaPlus className="w-3 h-3 mr-1" />
                          Assign
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveFromCompany(selectedConsultant?.id, company.companyId)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <FaTrash className="w-3 h-3 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </ModalContent>
          <ModalFooter>
            <Button
              variant="outline"
              onClick={() => setIsCompanyAssignModalOpen(false)}
            >
              Close
            </Button>
          </ModalFooter>
        </Modal>
      )}

      {/* Other modals would go here (Edit, Delete, View) */}
    </div>
  )
}

export default ConsultantManager 