import React, { useState, useEffect } from 'react'
import { 
  FaBell, 
  FaComments, 
  FaUser, 
  FaCheck, 
  FaTimes, 
  FaClock,
  FaBuilding,
  FaEye,
  FaReply,
  FaCircle,
  FaEllipsisV,
  Fa<PERSON>ilter,
  FaSearch
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { Avatar } from '../ui/Avatar'
import { Input } from '../ui/Input'

import { useAuth } from '@/hooks/useAuth'
import { formatDistanceToNow } from 'date-fns'

interface ChatNotification {
  id: string
  type: 'NEW_CHAT' | 'NEW_MESSAGE' | 'CHAT_REQUEST'
  chatId: string
  fromUser: {
    id: string
    name: string
    avatar?: string
    role: 'EMPLOYEE' | 'HR_ADMIN'
    company: string
    title?: string
  }
  content: string
  timestamp: string
  isRead: boolean
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  metadata?: {
    messageCount?: number
    lastActivity?: string
    isOnline?: boolean
  }
}

interface ActiveChat {
  id: string
  chatId: string
  participant: {
    id: string
    name: string
    avatar?: string
    company: string
    title?: string
    isOnline: boolean
    lastSeen?: string
  }
  lastMessage?: {
    content: string
    timestamp: string
    senderId: string
  }
  unreadCount: number
  lastActivity: string
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  status: 'ACTIVE' | 'PENDING' | 'CLOSED'
}

interface ConsultantNotificationsProps {
  onSelectChat: (chatId: string) => void
  onAcceptChatRequest: (notificationId: string) => void
  onDeclineChatRequest: (notificationId: string) => void
  selectedChatId?: string
  className?: string
}

const ConsultantNotifications: React.FC<ConsultantNotificationsProps> = ({
  onSelectChat,
  onAcceptChatRequest,
  onDeclineChatRequest,
  selectedChatId,
  className = ''
}) => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState<'notifications' | 'chats'>('notifications')
  const [notifications, setNotifications] = useState<ChatNotification[]>([])
  const [activeChats, setActiveChats] = useState<ActiveChat[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'ALL' | 'UNREAD' | 'HIGH_PRIORITY'>('ALL')
  const [loading, setLoading] = useState(false)

  // Mock connection state
  const [isConnected] = useState(true)

  useEffect(() => {
    loadNotifications()
    loadActiveChats()
  }, [])

  const loadNotifications = async () => {
    setLoading(true)
    try {
      // Mock data for now
      const mockNotifications: ChatNotification[] = [
        {
          id: '1',
          type: 'CHAT_REQUEST',
          chatId: 'chat-1',
          fromUser: {
            id: 'emp-1',
            name: 'John Smith',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
            role: 'EMPLOYEE',
            company: 'TechCorp Inc.',
            title: 'Software Engineer'
          },
          content: 'Hi, I would like to discuss stress management techniques for our upcoming project deadline.',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          isRead: false,
          priority: 'HIGH'
        },
        {
          id: '2',
          type: 'NEW_MESSAGE',
          chatId: 'chat-2',
          fromUser: {
            id: 'emp-2',
            name: 'Sarah Ahmed',
            avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face&auto=format',
            role: 'EMPLOYEE',
            company: 'HealthTech Solutions',
            title: 'Product Manager'
          },
          content: 'Thank you for the nutrition advice! Could you recommend some healthy snacks for long work hours?',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          isRead: false,
          priority: 'NORMAL',
          metadata: {
            messageCount: 3,
            isOnline: true
          }
        }
      ]
      
      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadActiveChats = async () => {
    try {
      const mockChats: ActiveChat[] = [
        {
          id: 'chat-2',
          chatId: 'chat-2',
          participant: {
            id: 'emp-2',
            name: 'Sarah Ahmed',
            avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face&auto=format',
            company: 'HealthTech Solutions',
            title: 'Product Manager',
            isOnline: true
          },
          lastMessage: {
            content: 'Thank you for the nutrition advice!',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            senderId: 'emp-2'
          },
          unreadCount: 3,
          lastActivity: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          priority: 'NORMAL',
          status: 'ACTIVE'
        }
      ]
      
      setActiveChats(mockChats)
    } catch (error) {
      console.error('Error loading active chats:', error)
    }
  }

  const handleAcceptRequest = async (notificationId: string) => {
    try {
      await onAcceptChatRequest(notificationId)
      const notification = notifications.find(n => n.id === notificationId)
      if (notification) {
        const newChat: ActiveChat = {
          id: notification.chatId,
          chatId: notification.chatId,
          participant: {
            id: notification.fromUser.id,
            name: notification.fromUser.name,
            avatar: notification.fromUser.avatar,
            company: notification.fromUser.company,
            title: notification.fromUser.title,
            isOnline: notification.metadata?.isOnline || false
          },
          unreadCount: 1,
          lastActivity: notification.timestamp,
          priority: notification.priority,
          status: 'ACTIVE'
        }
        setActiveChats(prev => [newChat, ...prev])
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
      }
    } catch (error) {
      console.error('Error accepting chat request:', error)
    }
  }

  const handleDeclineRequest = async (notificationId: string) => {
    try {
      await onDeclineChatRequest(notificationId)
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
    } catch (error) {
      console.error('Error declining chat request:', error)
    }
  }

  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === notificationId ? { ...n, isRead: true } : n
    ))
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'text-red-600'
      case 'HIGH': return 'text-orange-600'
      case 'NORMAL': return 'text-blue-600'
      case 'LOW': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'NORMAL': return 'bg-blue-100 text-blue-800'
      case 'LOW': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      if (!notification.fromUser.name.toLowerCase().includes(query) &&
          !notification.content.toLowerCase().includes(query) &&
          !notification.fromUser.company.toLowerCase().includes(query)) {
        return false
      }
    }
    
    if (filterStatus === 'UNREAD' && notification.isRead) return false
    if (filterStatus === 'HIGH_PRIORITY' && !['HIGH', 'URGENT'].includes(notification.priority)) return false
    
    return true
  })

  const filteredChats = activeChats.filter(chat => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      if (!chat.participant.name.toLowerCase().includes(query) &&
          !chat.participant.company.toLowerCase().includes(query)) {
        return false
      }
    }
    return true
  })

  const renderNotification = (notification: ChatNotification) => (
    <Card 
      key={notification.id}
      className={`cursor-pointer transition-all hover:shadow-md border-l-4 ${
        notification.isRead 
          ? 'border-l-gray-200 bg-white' 
          : `border-l-sehatti-gold-500 bg-sehatti-gold-50`
      }`}
      onClick={() => markNotificationAsRead(notification.id)}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Avatar
            src={notification.fromUser.avatar}
            alt={notification.fromUser.name}
            size="md"
            className="w-12 h-12"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h4 className="font-semibold text-sehatti-warm-gray-900 flex items-center gap-2">
                  {notification.fromUser.name}
                  <Badge className={`text-xs ${getPriorityBadgeColor(notification.priority)}`}>
                    {notification.priority}
                  </Badge>
                </h4>
                <div className="flex items-center gap-2 text-xs text-sehatti-warm-gray-600">
                  <FaBuilding className="w-3 h-3" />
                  <span>{notification.fromUser.company}</span>
                  {notification.fromUser.title && (
                    <>
                      <span>•</span>
                      <span>{notification.fromUser.title}</span>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-xs text-sehatti-warm-gray-500">
                  {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                </span>
                {!notification.isRead && (
                  <FaCircle className="w-2 h-2 text-sehatti-gold-500" />
                )}
              </div>
            </div>
            
            <p className="text-sm text-sehatti-warm-gray-700 mb-3 line-clamp-2">
              {notification.content}
            </p>
            
            {notification.type === 'CHAT_REQUEST' && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAcceptRequest(notification.id)
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <FaCheck className="w-3 h-3 mr-1" />
                  Accept
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDeclineRequest(notification.id)
                  }}
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <FaTimes className="w-3 h-3 mr-1" />
                  Decline
                </Button>
              </div>
            )}
            
            {notification.type === 'NEW_MESSAGE' && (
              <div className="flex items-center justify-between">
                {notification.metadata?.messageCount && notification.metadata.messageCount > 1 && (
                  <Badge variant="secondary" className="text-xs">
                    {notification.metadata.messageCount} new messages
                  </Badge>
                )}
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onSelectChat(notification.chatId)
                  }}
                  className="bg-sehatti-gold-500 hover:bg-sehatti-gold-600 text-white"
                >
                  <FaReply className="w-3 h-3 mr-1" />
                  Reply
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderActiveChat = (chat: ActiveChat) => (
    <Card 
      key={chat.id}
      className={`cursor-pointer transition-all hover:shadow-md border-2 ${
        selectedChatId === chat.chatId
          ? 'border-sehatti-gold-500 bg-sehatti-gold-50'
          : 'border-sehatti-warm-gray-200 hover:border-sehatti-gold-300'
      }`}
      onClick={() => onSelectChat(chat.chatId)}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="relative">
            <Avatar
              src={chat.participant.avatar}
              alt={chat.participant.name}
              size="md"
              className="w-12 h-12"
            />
            {chat.participant.isOnline && (
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h4 className="font-semibold text-sehatti-warm-gray-900">
                  {chat.participant.name}
                </h4>
                <div className="flex items-center gap-2 text-xs text-sehatti-warm-gray-600">
                  <FaBuilding className="w-3 h-3" />
                  <span>{chat.participant.company}</span>
                  {chat.participant.title && (
                    <>
                      <span>•</span>
                      <span>{chat.participant.title}</span>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {chat.unreadCount > 0 && (
                  <Badge className="bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
                    {chat.unreadCount}
                  </Badge>
                )}
                <span className="text-xs text-sehatti-warm-gray-500">
                  {formatDistanceToNow(new Date(chat.lastActivity), { addSuffix: true })}
                </span>
              </div>
            </div>
            
            {chat.lastMessage && (
              <p className="text-sm text-sehatti-warm-gray-700 truncate">
                {chat.lastMessage.content}
              </p>
            )}
            
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-2">
                <Badge variant={chat.status === 'ACTIVE' ? 'default' : 'secondary'} className="text-xs">
                  {chat.status}
                </Badge>
                <Badge className={`text-xs ${getPriorityBadgeColor(chat.priority)}`}>
                  {chat.priority}
                </Badge>
              </div>
              
              <p className="text-xs text-sehatti-warm-gray-500">
                {chat.participant.isOnline ? (
                  <span className="flex items-center gap-1">
                    <FaCircle className="w-2 h-2 text-green-500" />
                    Online
                  </span>
                ) : (
                  chat.participant.lastSeen && `Last seen ${formatDistanceToNow(new Date(chat.participant.lastSeen), { addSuffix: true })}`
                )}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={`h-full flex flex-col bg-white ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-sehatti-warm-gray-200">
        <h2 className="text-xl font-bold text-sehatti-warm-gray-900 mb-4">
          Chat Management
        </h2>
        
        {/* Tabs */}
        <div className="flex gap-1 mb-4">
          <Button
            variant={activeTab === 'notifications' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('notifications')}
            className="flex items-center gap-2"
          >
            <FaBell className="w-4 h-4" />
            Notifications
            {notifications.filter(n => !n.isRead).length > 0 && (
              <Badge className="bg-red-500 text-white text-xs min-w-[20px] h-5">
                {notifications.filter(n => !n.isRead).length}
              </Badge>
            )}
          </Button>
          <Button
            variant={activeTab === 'chats' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('chats')}
            className="flex items-center gap-2"
          >
            <FaComments className="w-4 h-4" />
            Active Chats
            {activeChats.reduce((sum, chat) => sum + chat.unreadCount, 0) > 0 && (
              <Badge className="bg-red-500 text-white text-xs min-w-[20px] h-5">
                {activeChats.reduce((sum, chat) => sum + chat.unreadCount, 0)}
              </Badge>
            )}
          </Button>
        </div>
        
        {/* Search and Filter */}
        <div className="flex gap-2 mb-4">
          <div className="relative flex-1">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sehatti-warm-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search chats or notifications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select 
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="border border-sehatti-warm-gray-300 rounded px-3 py-2 text-sm"
          >
            <option value="ALL">All</option>
            <option value="UNREAD">Unread</option>
            <option value="HIGH_PRIORITY">High Priority</option>
          </select>
        </div>
      </div>

      {/* Connection Status */}
      {!isConnected && (
        <div className="bg-red-50 border-b border-red-200 px-4 py-2">
          <p className="text-red-700 text-sm">Connection lost. Real-time notifications may not work.</p>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {activeTab === 'notifications' ? (
          <div className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <div className="text-center py-12">
                <FaBell className="w-16 h-16 text-sehatti-warm-gray-300 mx-auto mb-4" />
                <p className="text-sehatti-warm-gray-600">
                  {notifications.length === 0 ? 'No notifications yet' : 'No notifications match your criteria'}
                </p>
              </div>
            ) : (
              filteredNotifications.map(renderNotification)
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredChats.length === 0 ? (
              <div className="text-center py-12">
                <FaComments className="w-16 h-16 text-sehatti-warm-gray-300 mx-auto mb-4" />
                <p className="text-sehatti-warm-gray-600">
                  {activeChats.length === 0 ? 'No active chats yet' : 'No chats match your criteria'}
                </p>
              </div>
            ) : (
              filteredChats.map(renderActiveChat)
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ConsultantNotifications 