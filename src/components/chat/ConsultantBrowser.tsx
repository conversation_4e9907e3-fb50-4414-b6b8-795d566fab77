import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  FaFilter, 
  FaStar, 
  FaGraduationCap, 
  FaClock, 
  FaLanguage,
  FaCheckCircle,
  FaComments,
  FaChevronDown,
  FaSpinner
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { Input } from '../ui/Input'
import { Spinner } from '../ui/Spinner'

interface Consultant {
  id: string
  name: string
  email: string
  phone: string
  bio: string
  major: string
  image?: string
  languages: string[]
  specializations: string[]
  status: string
  isVerified: boolean
  yearsOfExperience: number
  rating: {
    averageRating: number
    totalReviews: number
  }
  availability: {
    isAvailable: boolean
    nextAvailable?: string
  }
}

interface ConsultantBrowserProps {
  onSelectConsultant: (consultant: Consultant) => void
  selectedConsultantId?: string
}

const SPECIALIZATIONS = [
  'HEALTH_WELLNESS',
  'NUTRITION', 
  'FITNESS',
  'MENTAL_HEALTH',
  'CAREER_DEVELOPMENT',
  'LEADERSHIP',
  'STRESS_MANAGEMENT',
  'WORK_LIFE_BALANCE',
  'TEAM_BUILDING',
  'COMMUNICATION',
  'TIME_MANAGEMENT',
  'PRODUCTIVITY',
  'PERSONAL_DEVELOPMENT'
]

const LANGUAGES = ['ENGLISH', 'ARABIC', 'HINDI']

const ConsultantBrowser: React.FC<ConsultantBrowserProps> = ({
  onSelectConsultant,
  selectedConsultantId
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [consultants, setConsultants] = useState<Consultant[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    specializations: [] as string[],
    languages: [] as string[],
    minRating: 0,
    minExperience: 0,
    isAvailable: null as boolean | null
  })
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState<'rating' | 'experience'>('rating')

  useEffect(() => {
    handleSearch()
  }, [filters, sortBy])

  const handleSearch = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      const mockConsultants: Consultant[] = [
        {
          id: '1',
          name: 'Dr. Sarah Johnson',
          email: '<EMAIL>',
          phone: '******-0101',
          bio: 'Certified wellness coach with 8+ years of experience in corporate health programs. Specializing in stress management and work-life balance.',
          major: 'Psychology & Wellness',
          image: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face&auto=format',
          languages: ['ENGLISH', 'ARABIC'],
          specializations: ['STRESS_MANAGEMENT', 'WORK_LIFE_BALANCE', 'MENTAL_HEALTH'],
          status: 'ACTIVE',
          isVerified: true,
          yearsOfExperience: 8,
          rating: {
            averageRating: 4.8,
            totalReviews: 127
          },
          availability: {
            isAvailable: true
          }
        },
        {
          id: '2',
          name: 'Ahmad Al-Hassan',
          email: '<EMAIL>',
          phone: '+************',
          bio: 'Leadership development expert helping corporate teams build effective communication and management skills.',
          major: 'Business Leadership',
          image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          languages: ['ENGLISH', 'ARABIC'],
          specializations: ['LEADERSHIP', 'COMMUNICATION', 'TEAM_BUILDING'],
          status: 'ACTIVE',
          isVerified: true,
          yearsOfExperience: 12,
          rating: {
            averageRating: 4.9,
            totalReviews: 89
          },
          availability: {
            isAvailable: true
          }
        },
        {
          id: '3',
          name: 'Priya Sharma',
          email: '<EMAIL>',
          phone: '+91-555-0303',
          bio: 'Nutritionist and fitness coach specializing in workplace wellness and healthy lifestyle programs for busy professionals.',
          major: 'Nutrition & Fitness',
          image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
          languages: ['ENGLISH', 'HINDI'],
          specializations: ['NUTRITION', 'FITNESS', 'HEALTH_WELLNESS'],
          status: 'ACTIVE',
          isVerified: true,
          yearsOfExperience: 6,
          rating: {
            averageRating: 4.7,
            totalReviews: 156
          },
          availability: {
            isAvailable: false,
            nextAvailable: '2025-01-17T09:00:00Z'
          }
        }
      ]

      // Apply filters
      let filteredConsultants = mockConsultants.filter(consultant => {
        if (searchQuery && !consultant.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
            !consultant.bio.toLowerCase().includes(searchQuery.toLowerCase()) &&
            !consultant.specializations.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()))) {
          return false
        }
        
        if (filters.specializations.length > 0 && 
            !filters.specializations.some(spec => consultant.specializations.includes(spec))) {
          return false
        }
        
        if (filters.languages.length > 0 && 
            !filters.languages.some(lang => consultant.languages.includes(lang))) {
          return false
        }
        
        if (filters.minRating > 0 && consultant.rating.averageRating < filters.minRating) {
          return false
        }
        
        if (filters.minExperience > 0 && consultant.yearsOfExperience < filters.minExperience) {
          return false
        }
        
        if (filters.isAvailable !== null && consultant.availability.isAvailable !== filters.isAvailable) {
          return false
        }
        
        return true
      })

      // Sort consultants
      filteredConsultants.sort((a, b) => {
        switch (sortBy) {
          case 'rating':
            return b.rating.averageRating - a.rating.averageRating
          case 'experience':
            return b.yearsOfExperience - a.yearsOfExperience
          default:
            return 0
        }
      })

      setConsultants(filteredConsultants)
      
    } catch (err) {
      setError('Failed to load consultants')
      console.error('Error searching consultants:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const toggleArrayFilter = (key: 'specializations' | 'languages', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value) 
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }))
  }

  const formatSpecialization = (spec: string) => {
    return spec.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatLanguage = (lang: string) => {
    return lang.charAt(0) + lang.slice(1).toLowerCase()
  }

  const clearFilters = () => {
    setFilters({
      specializations: [],
      languages: [],
      minRating: 0,
      minExperience: 0,
      isAvailable: null
    })
    setSearchQuery('')
  }

  const renderConsultantCard = (consultant: Consultant) => (
    <Card 
      key={consultant.id}
      className={`cursor-pointer transition-all hover:shadow-lg border-2 ${
        selectedConsultantId === consultant.id 
          ? 'border-sehatti-gold-500 bg-sehatti-gold-50' 
          : 'border-sehatti-warm-gray-200 hover:border-sehatti-gold-300'
      }`}
      onClick={() => onSelectConsultant(consultant)}
    >
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          {/* Avatar */}
          <div className="flex-shrink-0">
            {consultant.image ? (
              <img 
                src={consultant.image} 
                alt={consultant.name}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-sehatti-gold-500 flex items-center justify-center">
                <span className="text-white font-bold text-lg">
                  {consultant.name.charAt(0)}
                </span>
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="text-lg font-semibold text-sehatti-warm-gray-900 flex items-center gap-2">
                  {consultant.name}
                  {consultant.isVerified && (
                    <FaCheckCircle className="w-4 h-4 text-green-500" />
                  )}
                </h3>
                <p className="text-sm text-sehatti-warm-gray-600 mb-1">
                  {consultant.major}
                </p>
              </div>
              
              {/* Availability Status */}
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  consultant.availability.isAvailable ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <span className="text-xs text-sehatti-warm-gray-600">
                  {consultant.availability.isAvailable ? 'Available' : 'Busy'}
                </span>
              </div>
            </div>

            {/* Rating & Experience */}
            <div className="flex items-center gap-4 mb-3">
              <div className="flex items-center gap-1">
                <FaStar className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">
                  {consultant.rating.averageRating.toFixed(1)}
                </span>
                <span className="text-xs text-sehatti-warm-gray-600">
                  ({consultant.rating.totalReviews} reviews)
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <FaGraduationCap className="w-4 h-4 text-sehatti-warm-gray-500" />
                <span className="text-sm">
                  {consultant.yearsOfExperience} years exp.
                </span>
              </div>


            </div>

            {/* Bio */}
            <p className="text-sm text-sehatti-warm-gray-700 mb-3 line-clamp-2">
              {consultant.bio}
            </p>

            {/* Specializations */}
            <div className="flex flex-wrap gap-1 mb-3">
              {consultant.specializations.slice(0, 3).map((spec) => (
                <Badge key={spec} variant="secondary" className="text-xs">
                  {formatSpecialization(spec)}
                </Badge>
              ))}
              {consultant.specializations.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{consultant.specializations.length - 3} more
                </Badge>
              )}
            </div>

            {/* Languages */}
            <div className="flex items-center gap-2 mb-3">
              <FaLanguage className="w-4 h-4 text-sehatti-warm-gray-500" />
              <div className="flex gap-1">
                {consultant.languages.slice(0, 3).map((lang, index) => (
                  <span key={lang} className="text-xs text-sehatti-warm-gray-600">
                    {index > 0 && ', '}
                    {formatLanguage(lang)}
                  </span>
                ))}
                {consultant.languages.length > 3 && (
                  <span className="text-xs text-sehatti-warm-gray-600">
                    , +{consultant.languages.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Action Button */}
            <Button 
              size="sm" 
              className="w-full bg-sehatti-gold-500 hover:bg-sehatti-gold-600 text-white"
            >
              <FaComments className="w-4 h-4 mr-2" />
              Start Chat
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-sehatti-warm-gray-200">
        <h2 className="text-xl font-bold text-sehatti-warm-gray-900 mb-4">
          Find a Consultant
        </h2>
        
        {/* Search Bar */}
        <div className="relative mb-4">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sehatti-warm-gray-400 w-4 h-4" />
          <Input
            type="text"
            placeholder="Search by name, specialization, or expertise..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="pl-10 pr-4"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <FaFilter className="w-4 h-4" />
            Filters
            <FaChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>

          <div className="flex items-center gap-2">
            <span className="text-sm text-sehatti-warm-gray-600">Sort by:</span>
            <select 
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'rating' | 'experience')}
              className="text-sm border border-sehatti-warm-gray-300 rounded px-2 py-1"
            >
              <option value="rating">Rating</option>
              <option value="experience">Experience</option>
            </select>
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-sehatti-warm-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Specializations */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Specializations
                </label>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {SPECIALIZATIONS.map((spec) => (
                    <label key={spec} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.specializations.includes(spec)}
                        onChange={() => toggleArrayFilter('specializations', spec)}
                        className="mr-2"
                      />
                      <span className="text-sm">{formatSpecialization(spec)}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Languages */}
              <div>
                <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                  Languages
                </label>
                <div className="space-y-1">
                  {LANGUAGES.map((lang) => (
                    <label key={lang} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.languages.includes(lang)}
                        onChange={() => toggleArrayFilter('languages', lang)}
                        className="mr-2"
                      />
                      <span className="text-sm">{formatLanguage(lang)}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Numeric Filters */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                    Min. Rating: {filters.minRating}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="5"
                    step="0.5"
                    value={filters.minRating}
                    onChange={(e) => handleFilterChange('minRating', parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>



                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                    Min. Experience: {filters.minExperience} years
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="20"
                    step="1"
                    value={filters.minExperience}
                    onChange={(e) => handleFilterChange('minExperience', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                    Availability
                  </label>
                  <select 
                    value={filters.isAvailable === null ? '' : filters.isAvailable.toString()}
                    onChange={(e) => handleFilterChange('isAvailable', 
                      e.target.value === '' ? null : e.target.value === 'true'
                    )}
                    className="w-full border border-sehatti-warm-gray-300 rounded px-2 py-1"
                  >
                    <option value="">Any</option>
                    <option value="true">Available Now</option>
                    <option value="false">Busy</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-4 gap-2">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
              <Button size="sm" onClick={handleSearch}>
                Apply Filters
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Results */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Spinner size="lg" />
            <span className="ml-3 text-sehatti-warm-gray-600">Finding consultants...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">Error loading consultants</p>
            <Button onClick={handleSearch}>Try Again</Button>
          </div>
        ) : consultants.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-sehatti-warm-gray-400 mb-4">
              <FaSearch className="w-12 h-12 mx-auto mb-2" />
            </div>
            <p className="text-sehatti-warm-gray-600 mb-4">
              No consultants found matching your criteria
            </p>
            <Button onClick={clearFilters}>Clear Filters</Button>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-sm text-sehatti-warm-gray-600 mb-4">
              Found {consultants.length} consultant{consultants.length !== 1 ? 's' : ''}
            </p>
            {consultants.map(renderConsultantCard)}
          </div>
        )}
      </div>
    </div>
  )
}

export default ConsultantBrowser 