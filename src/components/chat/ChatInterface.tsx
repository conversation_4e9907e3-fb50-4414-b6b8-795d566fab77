import React, { useState, useEffect, useRef } from 'react'
import { 
  FaPaperPlane, 
  FaPhone, 
  FaVideo, 
  FaEllipsisV, 
  FaSmile, 
  FaPaperclip,
  FaMicrophone,
  FaImage,
  FaFile,
  FaArrowLeft,
  FaCircle,
  FaCheck,
  FaCheckDouble,
  FaComments
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Badge } from '../ui/Badge'
import { Avatar } from '../ui/Avatar'
import { useAuth } from '@/hooks/useAuth'
import { formatDistanceToNow } from 'date-fns'

interface ChatParticipant {
  id: string
  name: string
  avatar?: string
  role: 'CONSULTANT' | 'EMPLOYEE'
  isOnline: boolean
  lastSeen?: string
}

interface ChatMessage {
  id: string
  chatId: string
  senderId: string
  senderName: string
  senderAvatar?: string
  content: string
  type: 'TEXT' | 'IMAGE' | 'FILE' | 'VOICE' | 'SYSTEM'
  timestamp: string
  isRead: boolean
  isDelivered: boolean
  isEdited: boolean
  replyTo?: string
  attachments?: Array<{
    url: string
    name: string
    type: string
    size: number
  }>
}

interface ChatRoom {
  id: string
  chatId: string
  type: 'DIRECT' | 'GROUP' | 'SUPPORT'
  participants: ChatParticipant[]
  title?: string
  description?: string
  lastActivity: string
  unreadCount: number
  messages: ChatMessage[]
}

interface ChatInterfaceProps {
  chatRoom: ChatRoom | null
  onBack?: () => void
  onStartCall?: (type: 'voice' | 'video') => void
  className?: string
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  chatRoom,
  onBack,
  onStartCall,
  className = ''
}) => {
  const { user } = useAuth()
  const [message, setMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const [replyToMessage, setReplyToMessage] = useState<ChatMessage | null>(null)
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messageInputRef = useRef<HTMLInputElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Mock connection state
  const [isConnected, setIsConnected] = useState(true)
  const [messages, setMessages] = useState<ChatMessage[]>(chatRoom?.messages || [])

  // Simulate connection and load messages
  useEffect(() => {
    if (chatRoom) {
      setMessages(chatRoom.messages || [])
      setIsConnected(true)
    }
  }, [chatRoom])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Clear typing timeout
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!message.trim() || !chatRoom || !user) return

    // Create new message
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      chatId: chatRoom.chatId,
      senderId: user.id,
      senderName: user.name,
      senderAvatar: user.image,
      content: message.trim(),
      type: 'TEXT',
      timestamp: new Date().toISOString(),
      isRead: false,
      isDelivered: true,
      isEdited: false
    }

    // Add message to local state
    setMessages(prev => [...prev, newMessage])
    setMessage('')
    setReplyToMessage(null)
    messageInputRef.current?.focus()
    
    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false)
    }

    // Simulate consultant reply after a delay
    setTimeout(() => {
      const consultant = chatRoom.participants.find(p => p.role === 'CONSULTANT')
      if (consultant) {
        const replies = [
          "Thank you for sharing that with me. How are you feeling about this situation?",
          "That's a great question. Let me help you work through this step by step.",
          "I understand your concern. This is quite common, and we can definitely address it together.",
          "Thank you for being open about this. What would you like to focus on first?",
          "That's an important insight. How do you think we can build on this?",
          "I appreciate you bringing this up. Let's explore some strategies that might help.",
          "That sounds challenging. What support do you feel you need right now?",
          "Great progress! How has this been working for you so far?"
        ]
        
        const replyMessage: ChatMessage = {
          id: `msg-${Date.now()}-reply`,
          chatId: chatRoom.chatId,
          senderId: consultant.id,
          senderName: consultant.name,
          senderAvatar: consultant.avatar,
          content: replies[Math.floor(Math.random() * replies.length)],
          type: 'TEXT',
          timestamp: new Date().toISOString(),
          isRead: false,
          isDelivered: true,
          isEdited: false
        }
        
        setMessages(prev => [...prev, replyMessage])
      }
    }, 1500 + Math.random() * 2000) // Random delay between 1.5-3.5 seconds
  }

  const handleTyping = () => {
    if (!chatRoom || !isConnected) return

    if (!isTyping) {
      setIsTyping(true)
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false)
      }
    }, 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleAttachment = (type: 'image' | 'file' | 'voice') => {
    setShowAttachmentMenu(false)
    console.log('Attachment type:', type)
  }

  const getOtherParticipant = (): ChatParticipant | null => {
    if (!chatRoom || !user) return null
    return chatRoom.participants.find(p => p.id !== user.id) || null
  }

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
  }

  const renderMessage = (msg: ChatMessage, index: number) => {
    const isOwnMessage = msg.senderId === user?.id
    const isLastInGroup = index === messages.length - 1 || 
                         messages[index + 1]?.senderId !== msg.senderId

    return (
      <div
        key={msg.id}
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-2`}
      >
        <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
          <div
            className={`
              px-4 py-2 rounded-lg shadow-sm
              ${isOwnMessage 
                ? 'bg-sehatti-gold-500 text-white' 
                : 'bg-white border border-sehatti-warm-gray-200 text-sehatti-warm-gray-900'
              }
            `}
          >
            {msg.replyTo && (
              <div className={`
                text-xs opacity-75 border-l-2 pl-2 mb-1
                ${isOwnMessage ? 'border-white' : 'border-sehatti-gold-500'}
              `}>
                <p>Replying to message...</p>
              </div>
            )}

            <p className="text-sm whitespace-pre-wrap">{msg.content}</p>

            {msg.attachments && msg.attachments.length > 0 && (
              <div className="mt-2 space-y-1">
                {msg.attachments.map((attachment, idx) => (
                  <div key={idx} className="flex items-center gap-2 text-xs">
                    <FaFile className="w-3 h-3" />
                    <span>{attachment.name}</span>
                  </div>
                ))}
              </div>
            )}

            <div className={`
              flex items-center justify-between mt-1 text-xs
              ${isOwnMessage ? 'text-white/70' : 'text-sehatti-warm-gray-500'}
            `}>
              <span>{formatMessageTime(msg.timestamp)}</span>
              {isOwnMessage && (
                <div className="flex items-center gap-1">
                  {msg.isEdited && <span>edited</span>}
                  {msg.isDelivered ? (
                    msg.isRead ? (
                      <FaCheckDouble className="w-3 h-3" />
                    ) : (
                      <FaCheckDouble className="w-3 h-3 opacity-60" />
                    )
                  ) : (
                    <FaCheck className="w-3 h-3" />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {!isOwnMessage && isLastInGroup && (
          <div className="order-1 mr-2">
            <Avatar
              src={msg.senderAvatar}
              alt={msg.senderName}
              size="sm"
              className="w-8 h-8"
            />
          </div>
        )}
      </div>
    )
  }

  if (!chatRoom) {
    return (
      <div className={`flex items-center justify-center h-full bg-sehatti-warm-gray-50 ${className}`}>
        <div className="text-center">
          <FaComments className="w-16 h-16 text-sehatti-warm-gray-300 mx-auto mb-4" />
          <p className="text-sehatti-warm-gray-600">Select a consultant to start chatting</p>
        </div>
      </div>
    )
  }

  const otherParticipant = getOtherParticipant()

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-sehatti-warm-gray-200 bg-white">
        <div className="flex items-center gap-3">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <FaArrowLeft className="w-4 h-4" />
            </Button>
          )}
          
          {otherParticipant && (
            <>
              <div className="relative">
                <Avatar
                  src={otherParticipant.avatar}
                  alt={otherParticipant.name}
                  size="md"
                  className="w-10 h-10"
                />
                {otherParticipant.isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                )}
              </div>
              <div>
                <h3 className="font-semibold text-sehatti-warm-gray-900">
                  {otherParticipant.name}
                </h3>
                <p className="text-xs text-sehatti-warm-gray-600">
                  {otherParticipant.isOnline ? (
                    <span className="flex items-center gap-1">
                      <FaCircle className="w-2 h-2 text-green-500" />
                      Online
                    </span>
                  ) : (
                    otherParticipant.lastSeen && `Last seen ${formatDistanceToNow(new Date(otherParticipant.lastSeen), { addSuffix: true })}`
                  )}
                </p>
              </div>
            </>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onStartCall?.('voice')}
            disabled={!otherParticipant?.isOnline}
          >
            <FaPhone className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onStartCall?.('video')}
            disabled={!otherParticipant?.isOnline}
          >
            <FaVideo className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <FaEllipsisV className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {!isConnected && (
        <div className="bg-red-50 border-b border-red-200 px-4 py-2">
          <p className="text-red-700 text-sm">Connection lost. Messages may not be delivered in real-time.</p>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-1">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-sehatti-warm-gray-600">
              Start your conversation with {otherParticipant?.name}
            </p>
          </div>
        ) : (
          messages.map((msg, index) => renderMessage(msg, index))
        )}
        
        {typingUsers.length > 0 && (
          <div className="flex justify-start mb-2">
            <div className="bg-sehatti-warm-gray-100 px-4 py-2 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-sehatti-warm-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-sehatti-warm-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-sehatti-warm-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {replyToMessage && (
        <div className="border-t border-sehatti-warm-gray-200 px-4 py-2 bg-sehatti-warm-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm text-sehatti-warm-gray-600">
                Replying to {replyToMessage.senderName}
              </p>
              <p className="text-xs text-sehatti-warm-gray-500 truncate">
                {replyToMessage.content}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyToMessage(null)}
            >
              ×
            </Button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="border-t border-sehatti-warm-gray-200 p-4">
        <div className="flex items-end gap-2">
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
            >
              <FaPaperclip className="w-4 h-4" />
            </Button>
            
            {showAttachmentMenu && (
              <div className="absolute bottom-full mb-2 left-0 bg-white border border-sehatti-warm-gray-200 rounded-lg shadow-lg p-2 space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAttachment('image')}
                  className="w-full justify-start"
                >
                  <FaImage className="w-4 h-4 mr-2" />
                  Image
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAttachment('file')}
                  className="w-full justify-start"
                >
                  <FaFile className="w-4 h-4 mr-2" />
                  File
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAttachment('voice')}
                  className="w-full justify-start"
                >
                  <FaMicrophone className="w-4 h-4 mr-2" />
                  Voice Note
                </Button>
              </div>
            )}
          </div>

          <div className="flex-1">
            <Input
              ref={messageInputRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value)
                handleTyping()
              }}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              disabled={!isConnected}
              className="resize-none"
            />
          </div>

          <Button variant="ghost" size="sm">
            <FaSmile className="w-4 h-4" />
          </Button>

          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || !isConnected}
            className="bg-sehatti-gold-500 hover:bg-sehatti-gold-600 text-white"
          >
            <FaPaperPlane className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ChatInterface 