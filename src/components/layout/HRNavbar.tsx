import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  FaHome, 
  FaUsers, 
  FaBell, 
  FaChartLine, 
  FaClipboardList,
  FaCog,
  FaSignOutAlt,
} from 'react-icons/fa'
import { cn } from '@/lib/utils'
import { Badge } from '../ui/Badge'

// HR Navigation Items - Updated to use /hr/* pattern
const hrNavItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/hr/dashboard',
    icon: <FaHome className="w-5 h-5" />,
    shortLabel: 'Dashboard'
  },
  {
    id: 'employees',
    label: 'Employees', 
    href: '/hr/employees',
    icon: <FaUsers className="w-5 h-5" />,
    shortLabel: 'Employees'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/hr/analytics',
    icon: <FaChartLine className="w-5 h-5" />,
    shortLabel: 'Analytics'
  },
  {
    id: 'surveys',
    label: 'Surveys',
    href: '/hr/surveys',
    icon: <FaClipboardList className="w-5 h-5" />,
    shortLabel: 'Surveys'
  },
  {
    id: 'settings',
    label: 'Settings',
    href: '/hr/settings',
    icon: <FaCog className="w-5 h-5" />,
    shortLabel: 'Settings'
  }
]

export interface HRNavbarProps {
  user?: {
    name: string
    email: string
    avatar?: string
    company?: string
  }
  onLogout: () => void
  onNotificationClick?: () => void
  className?: string
  showNotifications?: boolean
  notificationCount?: number
}

export const HRNavbar: React.FC<HRNavbarProps> = ({ 
  user, 
  onLogout, 
  onNotificationClick,
  className,
  showNotifications = true,
  notificationCount = 0 
}) => {
  const location = useLocation()

  const isActiveItem = (item: typeof hrNavItems[0]) => {
    if (item.href === '/hr/dashboard') {
      return location.pathname === '/hr/dashboard' || location.pathname === '/hr'
    }
    return location.pathname.startsWith(item.href)
  }

  return (
    <>
      {/* Desktop Header */}
      <div className="hidden lg:block fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/hr/dashboard" className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-sehatti-gold-500 to-sehatti-gold-600 flex items-center justify-center text-white font-bold text-sm shadow-lg shadow-sehatti-gold-500/30">
                HR
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  Sehatti HR
                </span>
                {user?.company && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {user.company}
                  </span>
                )}
              </div>
            </Link>

            {/* Desktop User Info & Actions */}
            <div className="flex items-center gap-4">
              {/* Notifications */}
              {showNotifications && (
                <button
                  onClick={onNotificationClick}
                  className="relative p-2 text-gray-500 dark:text-gray-400 hover:text-sehatti-gold-600 dark:hover:text-sehatti-gold-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <FaBell className="w-5 h-5" />
                  {notificationCount > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 min-w-[20px] h-5 text-xs flex items-center justify-center rounded-full"
                    >
                      {notificationCount > 99 ? '99+' : notificationCount}
                    </Badge>
                  )}
                </button>
              )}

              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {user?.name || 'HR Manager'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.email || ''}
                  </div>
                </div>
                {user?.avatar ? (
                  <img 
                    src={user.avatar} 
                    alt={user.name}
                    className="w-8 h-8 rounded-full border-2 border-sehatti-gold-500/20"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-sehatti-gold-500 flex items-center justify-center text-white text-sm font-bold">
                    {user?.name?.charAt(0)?.toUpperCase() || 'H'}
                  </div>
                )}
                <button
                  onClick={onLogout}
                  className="p-2 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                  title="Logout"
                >
                  <FaSignOutAlt className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Top Bar */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-center justify-between p-4">
          {/* Logo */}
          <Link to="/hr/dashboard" className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-md bg-gradient-to-br from-sehatti-gold-500 to-sehatti-gold-600 flex items-center justify-center text-white font-bold text-xs">
              HR
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-bold text-sehatti-warm-gray-900 dark:text-white">
                Sehatti HR
              </span>
              {user?.company && (
                <span className="text-[10px] text-gray-500 dark:text-gray-400 leading-none">
                  {user.company}
                </span>
              )}
            </div>
          </Link>

          {/* Mobile Actions */}
          <div className="flex items-center gap-3">
            {/* Notifications */}
            {showNotifications && (
              <button
                onClick={onNotificationClick}
                className="relative p-2 text-gray-500 dark:text-gray-400 hover:text-sehatti-gold-600 dark:hover:text-sehatti-gold-400 transition-colors"
              >
                <FaBell className="w-5 h-5" />
                {notificationCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 min-w-[18px] h-4 text-[10px] flex items-center justify-center rounded-full"
                  >
                    {notificationCount > 99 ? '99+' : notificationCount}
                  </Badge>
                )}
              </button>
            )}

            {/* User Avatar */}
            {user?.avatar ? (
              <img 
                src={user.avatar} 
                alt={user.name}
                className="w-8 h-8 rounded-full border-2 border-sehatti-gold-500/20"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-sehatti-gold-500 flex items-center justify-center text-white text-sm font-bold">
                {user?.name?.charAt(0)?.toUpperCase() || 'H'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation - Key feature like admin dashboard */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg">
        <div className="grid grid-cols-5 gap-1 p-2">
          {hrNavItems.map((item) => (
            <Link
              key={item.id}
              to={item.href}
              className={cn(
                "flex flex-col items-center justify-center p-3 rounded-lg text-xs font-medium transition-all duration-200",
                isActiveItem(item)
                  ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-sehatti-gold-600"
              )}
            >
              <div className="mb-1">
                {item.icon}
              </div>
              <span className="text-[10px] leading-none">
                {item.shortLabel}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}
