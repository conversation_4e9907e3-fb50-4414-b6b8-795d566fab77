import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { 
  FaHome, 
  FaChartLine, 
  FaQuestionCircle, 
  FaVideo, 
  FaComments, 
  FaUser,
  FaBell,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaCog
} from 'react-icons/fa'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'

interface EmployeeLayoutProps {
  children: React.ReactNode
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

// Employee Navigation Items
const employeeNavItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/employee/dashboard',
    icon: <FaHome className="w-5 h-5" />
  },
  {
    id: 'analytics',
    label: 'My Progress', 
    href: '/employee/analytics',
    icon: <FaChartLine className="w-5 h-5" />
  },
  {
    id: 'checkin',
    label: 'Daily Check-in',
    href: '/employee/checkin', 
    icon: <FaQuestionCircle className="w-5 h-5" />
  },
  {
    id: 'media',
    label: 'Media Hub',
    href: '/employee/media',
    icon: <FaVideo className="w-5 h-5" />
  },
  {
    id: 'chat',
    label: 'Consultant Chat',
    href: '/employee/chat',
    icon: <FaComments className="w-5 h-5" />
  },
  {
    id: 'profile',
    label: 'My Profile',
    href: '/employee/profile',
    icon: <FaUser className="w-5 h-5" />
  }
]

export const EmployeeLayout: React.FC<EmployeeLayoutProps> = ({ children, user }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleLogout = () => {
    localStorage.removeItem('sehatti-auth-token')
    navigate('/login')
  }

  const currentPath = location.pathname

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900">
      {/* Top Navigation Bar */}
      <nav className="bg-white dark:bg-sehatti-warm-gray-800 shadow-sm border-b border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-sehatti-gold-500 to-sehatti-gold-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">S</span>
                </div>
                <span className="ml-2 text-xl font-bold text-sehatti-warm-gray-900 dark:text-white">
                  Sehatti
                </span>
                <Badge variant="secondary" className="ml-2 text-xs">
                  Employee
                </Badge>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {employeeNavItems.map((item) => {
                const isActive = currentPath === item.href
                return (
                  <Link
                    key={item.id}
                    to={item.href}
                    className={`
                      flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
                      ${isActive 
                        ? 'bg-sehatti-gold-100 text-sehatti-gold-700 dark:bg-sehatti-gold-900 dark:text-sehatti-gold-300' 
                        : 'text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 hover:bg-sehatti-warm-gray-100 dark:text-sehatti-warm-gray-300 dark:hover:text-white dark:hover:bg-sehatti-warm-gray-700'
                      }
                    `}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              {/* Notifications */}
              <button className="p-2 rounded-md text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 hover:bg-sehatti-warm-gray-100 dark:text-sehatti-warm-gray-300 dark:hover:text-white dark:hover:bg-sehatti-warm-gray-700 relative">
                <FaBell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>

              {/* User Avatar */}
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-sehatti-gold-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-sm">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-sehatti-warm-gray-900 dark:text-white">
                    {user?.name || 'Employee'}
                  </p>
                  <p className="text-xs text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </div>

              {/* Logout Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="hidden sm:flex items-center gap-2"
              >
                <FaSignOutAlt className="w-4 h-4" />
                Logout
              </Button>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 rounded-md text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 hover:bg-sehatti-warm-gray-100"
              >
                {isMobileMenuOpen ? <FaTimes className="w-5 h-5" /> : <FaBars className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white dark:bg-sehatti-warm-gray-800">
              {employeeNavItems.map((item) => {
                const isActive = currentPath === item.href
                return (
                  <Link
                    key={item.id}
                    to={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`
                      flex items-center gap-3 px-3 py-2 rounded-md text-base font-medium transition-colors
                      ${isActive 
                        ? 'bg-sehatti-gold-100 text-sehatti-gold-700 dark:bg-sehatti-gold-900 dark:text-sehatti-gold-300' 
                        : 'text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 hover:bg-sehatti-warm-gray-100 dark:text-sehatti-warm-gray-300 dark:hover:text-white dark:hover:bg-sehatti-warm-gray-700'
                      }
                    `}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                )
              })}
              
              {/* Mobile Logout */}
              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900"
              >
                <FaSignOutAlt className="w-5 h-5" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-sehatti-warm-gray-800 border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
              © 2025 Sehatti. Your wellness journey.
            </p>
            <div className="flex items-center gap-4">
              <Link 
                to="/employee/settings" 
                className="text-sm text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-300 dark:hover:text-white flex items-center gap-1"
              >
                <FaCog className="w-3 h-3" />
                Settings
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default EmployeeLayout 