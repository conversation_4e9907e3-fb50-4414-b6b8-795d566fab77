import React from 'react';
import { useNavigate } from 'react-router-dom';
import { HRNavbar } from './HRNavbar';
import { useSelector } from 'react-redux';
import { useLogoutMutation } from '@/store/api/authApi';
import { toast } from 'react-hot-toast';

interface HRLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const HRLayout: React.FC<HRLayoutProps> = ({ children, className }) => {
  const navigate = useNavigate();
  const [logout] = useLogoutMutation();
  
  // Get user data from Redux store (adjust this path based on your auth slice)
  const auth = useSelector((state: any) => state.auth);
  const user = auth?.user ? {
    name: auth.user.name || 'HR Admin',
    email: auth.user.email || '',
    avatar: auth.user.avatar,
    company: auth.user.company_name || auth.user.companyName || 'Company'
  } : undefined;

  const handleLogout = async () => {
    try {
      await logout().unwrap();
      toast.success('Logged out successfully');
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // Still navigate to login even if logout API fails
      navigate('/login');
    }
  };

  const handleNotificationClick = () => {
    navigate('/hr/notifications');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Enhanced HR Navbar */}
      <HRNavbar
        user={user}
        onLogout={handleLogout}
        showNotifications={true}
        notificationCount={0} // You can connect this to actual notification count
        onNotificationClick={handleNotificationClick}
      />
      
      {/* Main Content Container with proper spacing */}
      <div className="pt-16 pb-20 lg:pb-6">
        <div className={`min-h-[calc(100vh-8rem)] lg:min-h-[calc(100vh-4rem)] ${className || ''}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default HRLayout;
