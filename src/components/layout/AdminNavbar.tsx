import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../../utils/languageUtils';
import { 
  FaUsers, 
  FaCog, 
  FaChartBar, 
  FaSignOutAlt, 
  FaBuilding,
  FaBell,
  FaUserShield,
  FaBookOpen,
  FaRobot,
  FaUserTie,
  FaClipboardList,
  FaQuestionCircle,
  FaChevronDown,
  FaBars,
  FaTimes,
  FaChevronUp,
  FaHome,
  FaVideo
} from 'react-icons/fa';
import { MdOutlineSpaceDashboard } from 'react-icons/md';
import { cn } from '@/lib/utils';
import { Avatar } from '@/components/ui/Avatar';
import { GradientText } from '@/components/ui/GradientText';
import { Badge } from '@/components/ui/Badge';

// Navigation item interface
export interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  submenu?: NavItem[];
  badge?: number;
  permission?: string[];
}

// User interface
export interface NavUser {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

// Navbar props
export interface AdminNavbarProps {
  user?: NavUser;
  navItems?: NavItem[];
  onLogout?: () => void;
  className?: string;
  variant?: 'default' | 'glass' | 'solid';
  showNotifications?: boolean;
  notificationCount?: number;
  onNotificationClick?: () => void;
  logo?: {
    src?: string;
    text?: string;
    href?: string;
  };
}

// Default admin navigation items
const defaultAdminNavItems: NavItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
    href: '/admin/dashboard'
  },
  {
    id: 'corporates',
    label: 'Corporates',
    icon: <FaBuilding className="w-5 h-5" />,
    href: '/admin/corporates'
  },
  {
    id: 'qa-management',
    label: 'QA',
    icon: <FaQuestionCircle className="w-5 h-5" />,
    href: '/admin/qa-management'
  },
  {
    id: 'media-management',
    label: 'Media',
    icon: <FaVideo className="w-5 h-5" />,
    href: '/admin/media-management'
  },

  {
    id: 'automation',
    label: 'Automation',
    icon: <FaRobot className="w-5 h-5" />,
    submenu: [
      { id: 'invitations', label: 'Invitations', icon: <FaUsers className="w-4 h-4" />, href: '/admin/invitations' },
      { id: 'recommendations', label: 'Recommendations', icon: <FaChartBar className="w-4 h-4" />, href: '/admin/recommendations' }
    ]
  },
  {
    id: 'consultants',
    label: 'Consultants',
    icon: <FaUserTie className="w-5 h-5" />,
    href: '/admin/consultants'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <FaChartBar className="w-5 h-5" />,
    href: '/admin/analytics'
  }
];

// Mobile Quick Nav Items are now generated dynamically from navItems prop

// Dropdown menu component - positioned above the navbar
const DropdownMenu: React.FC<{
  items: NavItem[];
  isOpen: boolean;
  onClose: () => void;
}> = ({ items, isOpen, onClose }) => {
  const { isRTL } = useLanguage();
  
  if (!isOpen) return null;

  return (
    <div className={`absolute bottom-full mb-2 w-56 bg-white dark:bg-sehatti-warm-gray-900 rounded-xl shadow-xl border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 py-2 z-50 ${isRTL ? 'right-0' : 'left-0'}`}>
      {items.map((item) => (
        <Link
          key={item.id}
          to={item.href || '#'}
          onClick={onClose}
          className={`flex items-center gap-3 px-4 py-3 text-sm text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          {item.icon}
          <span>{item.label}</span>
          {item.badge && item.badge > 0 && (
            <Badge variant="destructive" size="sm" className={isRTL ? 'mr-auto' : 'ml-auto'}>
              {item.badge > 9 ? '9+' : item.badge}
            </Badge>
          )}
        </Link>
      ))}
    </div>
  );
};

// Main Admin Navbar component - mobile-first floating bottom navbar
export const AdminNavbar: React.FC<AdminNavbarProps> = ({
  user,
  navItems = defaultAdminNavItems,
  onLogout,
  className,
  variant = 'glass',
  showNotifications = true,
  notificationCount = 0,
  onNotificationClick,
  logo = { text: 'Sehatti Admin', href: '/dashboard' }
}) => {
  const location = useLocation();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  
  // Add RTL support and language detection
  const { isRTL, rtlClasses, currentLanguage } = useLanguage();
  
  // Translation helper for navbar-specific text
  const getTranslation = (key: string) => {
    const translations = {
      signOut: {
        EN: 'Sign Out',
        AR: 'تسجيل الخروج',
        HI: 'साइन आउट'
      }
    };
    return translations[key]?.[currentLanguage] || translations[key]?.EN || key;
  };

  // Get mobile-friendly short labels
  const getMobileLabel = (item: NavItem) => {
    const mobileLabels = {
      dashboard: {
        EN: 'Home',
        AR: 'الرئيسية',
        HI: 'होम'
      },
      dailyCheckin: {
        EN: 'Check-in',
        AR: 'التسجيل',
        HI: 'चेक-इन'
      },
      mediaHub: {
        EN: 'Media',
        AR: 'الوسائط',
        HI: 'मीडिया'
      },
      consultantChat: {
        EN: 'Chat',
        AR: 'المحادثة',
        HI: 'चैट'
      },
      myProfile: {
        EN: 'Profile',
        AR: 'الملف',
        HI: 'प्रोफ़ाइल'
      },
      employees: {
        EN: 'Staff',
        AR: 'الموظفون',
        HI: 'कर्मचारी'
      },
      notifications: {
        EN: 'Alerts',
        AR: 'الإشعارات',
        HI: 'सूचनाएं'
      }
    };
    
    // Use mobile label if available, otherwise use full label
    return mobileLabels[item.id]?.[currentLanguage] || item.label;
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      
      // Check if click is outside any dropdown
      const isOutsideDropdown = Object.values(dropdownRefs.current).every(
        ref => !ref?.contains(target)
      );

      if (isOutsideDropdown) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Check if current path matches nav item
  const isActiveItem = (item: NavItem): boolean => {
    if (item.href && location.pathname === item.href) return true;
    if (item.submenu) {
      return item.submenu.some(subItem => subItem.href === location.pathname);
    }
    return false;
  };

  // Get mobile quick nav items dynamically from navItems prop
  const getMobileQuickNavItems = (navItems: NavItem[]) => {
    // Take only first 2 items for mobile quick nav to avoid crowding
    return navItems
      .filter(item => !item.submenu) // Only items without submenus
      .slice(0, 2) // Take only first 2 items for cleaner mobile UI
      .map(item => ({
        id: item.id,
        label: item.label, // Use the already translated label from navItems
        icon: item.id === 'dashboard' ? <FaHome className="w-5 h-5" /> : item.icon,
        href: item.href
      }));
  };

  const mobileQuickNavItems = getMobileQuickNavItems(navItems);

  const navbarVariants = {
    default: "bg-white dark:bg-sehatti-warm-gray-950 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-800",
    glass: "bg-white/95 dark:bg-sehatti-warm-gray-950/95 backdrop-blur-lg border border-sehatti-gold-200/50 dark:border-sehatti-gold-600/30 shadow-xl shadow-sehatti-gold-500/25 dark:shadow-sehatti-gold-400/10",
    solid: "bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-500 text-white border border-sehatti-gold-400 shadow-xl shadow-sehatti-gold-500/30"
  };

  return (
    <>
      {/* Mobile Menu Overlay - Full Screen */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm">
          <div className="absolute bottom-20 left-0 right-0 mx-3">
            <div className={cn(
              "relative bg-white/95 dark:bg-sehatti-warm-gray-950/95 backdrop-blur-lg rounded-2xl border border-sehatti-gold-200/50 dark:border-sehatti-gold-600/30 shadow-xl shadow-sehatti-gold-500/25 dark:shadow-sehatti-gold-400/10 max-h-[70vh] overflow-y-auto",
              "ring-1 ring-sehatti-gold-200/20 dark:ring-sehatti-gold-600/20",
              "before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-sehatti-gold-500/10 before:to-sehatti-gold-400/5 before:blur-sm before:-z-10",
              "after:absolute after:inset-0 after:rounded-2xl after:shadow-2xl after:shadow-sehatti-gold-500/25 after:-z-20"
            )}>
              <div className="p-4 space-y-1">
                {navItems.map((item) => (
                  <div key={item.id}>
                    {item.submenu ? (
                      <div>
                        <button
                          onClick={() => setOpenDropdown(openDropdown === item.id ? null : item.id)}
                          className={cn(
                            "flex items-center justify-between w-full px-4 py-4 text-base font-medium rounded-xl transition-all duration-200 min-h-[3rem]",
                            isActiveItem(item)
                              ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm"
                              : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700"
                          )}
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-6 h-6 flex items-center justify-center">
                              {item.icon}
                            </div>
                            <span>{item.label}</span>
                            {item.badge && item.badge > 0 && (
                              <Badge variant="destructive" size="sm">
                                {item.badge > 9 ? '9+' : item.badge}
                              </Badge>
                            )}
                          </div>
                          <FaChevronUp className={cn(
                            "w-4 h-4 transition-transform duration-200",
                            openDropdown === item.id && "rotate-180"
                          )} />
                        </button>
                        
                        {openDropdown === item.id && (
                          <div className="ml-8 mt-2 space-y-1 animate-in slide-in-from-top-2 duration-200">
                            {item.submenu.map((subItem) => (
                              <Link
                                key={subItem.id}
                                to={subItem.href || '#'}
                                onClick={() => setIsMobileMenuOpen(false)}
                                className={cn(
                                  "flex items-center gap-3 px-4 py-3 text-sm rounded-lg transition-all duration-200 min-h-[2.5rem]",
                                  location.pathname === subItem.href
                                    ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300"
                                    : "text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700"
                                )}
                              >
                                <div className="w-5 h-5 flex items-center justify-center">
                                  {subItem.icon}
                                </div>
                                <span>{subItem.label}</span>
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Link
                        to={item.href || '#'}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={cn(
                          "flex items-center gap-4 px-4 py-4 text-base font-medium rounded-xl transition-all duration-200 min-h-[3rem]",
                          isActiveItem(item)
                            ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm"
                            : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700"
                        )}
                      >
                        <div className="w-6 h-6 flex items-center justify-center">
                          {item.icon}
                        </div>
                        <span>{item.label}</span>
                        {item.badge && item.badge > 0 && (
                          <Badge variant="destructive" size="sm" className="ml-auto">
                            {item.badge > 9 ? '9+' : item.badge}
                          </Badge>
                        )}
                      </Link>
                    )}
                  </div>
                ))}
                
                {/* Mobile Logout Button */}
                <div className="border-t border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 pt-4 mt-4">
                  <button
                    onClick={() => {
                      setIsMobileMenuOpen(false)
                      onLogout?.()
                    }}
                    className="flex items-center gap-4 px-4 py-4 text-base font-medium rounded-xl transition-all duration-200 min-h-[3rem] w-full text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 active:bg-red-100 dark:active:bg-red-900/30"
                  >
                    <div className="w-6 h-6 flex items-center justify-center">
                      <FaSignOutAlt className="w-5 h-5" />
                    </div>
                    <span>{getTranslation('signOut')}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Floating Bottom Navbar */}
      <div className="fixed bottom-0 left-0 right-0 z-50">
        {/* Container wrapper - Mobile margins, Desktop content-aligned */}
        <div className="mx-3 sm:max-w-7xl sm:mx-auto sm:px-4 md:px-6 lg:px-8 mb-4 sm:mb-6 pb-safe">
          <nav className={cn(
            "transition-all duration-300 rounded-2xl ring-1 ring-sehatti-gold-200/20 dark:ring-sehatti-gold-600/20",
            "before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-sehatti-gold-500/10 before:to-sehatti-gold-400/5 before:blur-sm before:-z-10",
            "after:absolute after:inset-0 after:rounded-2xl after:shadow-2xl after:shadow-sehatti-gold-500/25 after:-z-20",
            navbarVariants[variant],
            className
          )}>
            {/* Mobile Bottom Navigation */}
            <div className="md:hidden px-4 py-3">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                {/* Quick Navigation Items - Mobile */}
                <div className={`flex items-center gap-1 flex-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {mobileQuickNavItems.map((item) => (
                    <Link
                      key={item.id}
                      to={item.href || '#'}
                      className={cn(
                        "flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 min-w-[4rem] min-h-[3rem]",
                        isActiveItem(item)
                          ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm"
                          : "text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700"
                      )}
                    >
                      <div className="w-5 h-5 flex items-center justify-center">
                        {item.icon}
                      </div>
                      <span className="leading-tight">{getMobileLabel(item)}</span>
                    </Link>
                  ))}
                </div>

                {/* Action Buttons - Mobile */}
                <div className={`flex items-center gap-2 ${isRTL ? 'mr-2' : 'ml-2'} ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {/* Notifications */}
                  {showNotifications && (
                    <button
                      onClick={onNotificationClick}
                      className="relative p-3 rounded-xl text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700 transition-all duration-200 min-h-[3rem] min-w-[3rem] flex items-center justify-center"
                    >
                      <FaBell className="w-5 h-5" />
                      {notificationCount > 0 && (
                        <Badge
                          variant="destructive"
                          size="sm"
                          className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center text-xs shadow-lg"
                        >
                          {notificationCount > 9 ? '9+' : notificationCount}
                        </Badge>
                      )}
                    </button>
                  )}

                  {/* Menu Toggle */}
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="p-3 rounded-xl text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 active:bg-sehatti-warm-gray-200 dark:active:bg-sehatti-warm-gray-700 transition-all duration-200 min-h-[3rem] min-w-[3rem] flex items-center justify-center"
                  >
                    {isMobileMenuOpen ? (
                      <FaTimes className="w-5 h-5" />
                    ) : (
                      <FaBars className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block px-6 py-4">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                {/* Desktop Navigation - centered */}
                <div className={`flex items-center gap-1 flex-1 justify-center ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {navItems.map((item) => (
                    <div
                      key={item.id}
                      className="relative"
                      ref={el => {
                        dropdownRefs.current[item.id] = el;
                      }}
                    >
                      {item.submenu ? (
                        <button
                          onClick={() => setOpenDropdown(openDropdown === item.id ? null : item.id)}
                          className={cn(
                            "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                            "hover:shadow-md hover:shadow-sehatti-gold-500/20",
                            `${isRTL ? 'flex-row-reverse' : ''}`,
                            isActiveItem(item)
                              ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm shadow-sehatti-gold-500/30"
                              : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600"
                          )}
                        >
                          {item.icon}
                          <span className="hidden lg:block">{item.label}</span>
                          <FaChevronUp className={cn(
                            "w-3 h-3 transition-transform",
                            openDropdown === item.id && "rotate-180"
                          )} />
                          {item.badge && item.badge > 0 && (
                            <Badge variant="destructive" size="sm">
                              {item.badge > 9 ? '9+' : item.badge}
                            </Badge>
                          )}
                        </button>
                      ) : (
                        <Link
                          to={item.href || '#'}
                          className={cn(
                            "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                            "hover:shadow-md hover:shadow-sehatti-gold-500/20",
                            `${isRTL ? 'flex-row-reverse' : ''}`,
                            isActiveItem(item)
                              ? "bg-sehatti-gold-50 text-sehatti-gold-700 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 shadow-sm shadow-sehatti-gold-500/30"
                              : "text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:text-sehatti-gold-600"
                          )}
                        >
                          {item.icon}
                          <span className="hidden lg:block">{item.label}</span>
                          {item.badge && item.badge > 0 && (
                            <Badge variant="destructive" size="sm">
                              {item.badge > 9 ? '9+' : item.badge}
                            </Badge>
                          )}
                        </Link>
                      )}

                      {/* Desktop Dropdown Menu */}
                      {item.submenu && (
                        <DropdownMenu
                          items={item.submenu}
                          isOpen={openDropdown === item.id}
                          onClose={() => setOpenDropdown(null)}
                        />
                      )}
                    </div>
                  ))}
                </div>

                {/* Desktop Right Side Actions */}
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {/* Notifications */}
                  {showNotifications && (
                    <button
                      onClick={onNotificationClick}
                      className="relative p-2 rounded-lg text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:shadow-md hover:shadow-sehatti-gold-500/20 transition-all duration-200"
                    >
                      <FaBell className="w-5 h-5" />
                      {notificationCount > 0 && (
                        <Badge
                          variant="destructive"
                          size="sm"
                          className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center shadow-lg shadow-red-500/30"
                        >
                          {notificationCount > 9 ? '9+' : notificationCount}
                        </Badge>
                      )}
                    </button>
                  )}

                  {/* Sign Out Button */}
                  <button
                    onClick={onLogout}
                    className="flex items-center gap-2 p-2 rounded-lg text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800 hover:text-red-600 hover:shadow-md hover:shadow-red-500/20 transition-all duration-200"
                    title="Sign Out"
                  >
                    <FaSignOutAlt className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </>
  );
}; 