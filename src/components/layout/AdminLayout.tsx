import React from "react"
import { Container } from "@/components/ui/Container"
import { AdminNavbar, type NavItem } from "./AdminNavbar"
import type { User } from "@/types/api"
import { useLanguage } from "../../utils/languageUtils"
import { getNavTranslation } from "../../utils/navigationTranslations"
import {
  FaUsers,
  FaChartBar,
  FaBell,
  FaBuilding,
  FaQuestionCircle,
  FaVideo,
  FaRobot,
  FaUserTie,
  FaClipboardList,
  FaHeartbeat,
  FaComments,
  FaUser
} from "react-icons/fa"
import { MdOutlineSpaceDashboard } from "react-icons/md"

// Navigation translations
interface NavigationTranslations {
  [key: string]: {
    EN: string
    AR: string
    HI: string
  }
}

const NAV_TRANSLATIONS: NavigationTranslations = {
  // Common items
  dashboard: {
    EN: "Dashboard",
    AR: "لوحة التحكم",
    HI: "डैशबोर्ड"
  },
  analytics: {
    EN: "Analytics",
    AR: "التحليلات",
    HI: "एनालिटिक्स"
  },

  // System Admin
  corporates: {
    EN: "Corporates",
    AR: "الشركات",
    HI: "कंपनियां"
  },
  qa: {
    EN: "QA",
    AR: "ضمان الجودة",
    HI: "क्यूए"
  },
  media: {
    EN: "Media",
    AR: "الوسائط",
    HI: "मीडिया"
  },
  automation: {
    EN: "Automation",
    AR: "الأتمتة",
    HI: "ऑटोमेशन"
  },
  consultants: {
    EN: "Consultants",
    AR: "المستشارون",
    HI: "सलाहकार"
  },
  invitations: {
    EN: "Invitations",
    AR: "الدعوات",
    HI: "निमंत्रण"
  },
  recommendations: {
    EN: "Recommendations",
    AR: "التوصيات",
    HI: "सिफारिशें"
  },

  // HR Admin
  employees: {
    EN: "Employees",
    AR: "الموظفون",
    HI: "कर्मचारी"
  },
  notifications: {
    EN: "Notifications",
    AR: "الإشعارات",
    HI: "सूचनाएं"
  },

  // Employee
  dailyCheckin: {
    EN: "Daily Check-in",
    AR: "التسجيل اليومي",
    HI: "दैनिक चेक-इन"
  },
  mediaHub: {
    EN: "Media Hub",
    AR: "مركز الوسائط",
    HI: "मीडिया हब"
  },
  consultantChat: {
    EN: "Consultant Chat",
    AR: "محادثة المستشار",
    HI: "सलाहकार चैट"
  },
  myProfile: {
    EN: "My Profile",
    AR: "ملفي الشخصي",
    HI: "मेरी प्रोफ़ाइल"
  }
}

// Function to get translated navigation items based on user role and current language
const getTranslatedNavItems = (
  role: string,
  currentLanguage: "EN" | "AR" | "HI"
): NavItem[] => {
  const t = (key: string): string => getNavTranslation(key, currentLanguage)

  switch (role) {
    case "system_admin":
    case "system-admin":
    case "SYSTEM_ADMIN":
      return [
        {
          id: "dashboard",
          label: t("dashboard"),
          icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
          href: "/admin/dashboard"
        },
        {
          id: "corporates",
          label: t("corporates"),
          icon: <FaBuilding className="w-5 h-5" />,
          href: "/admin/corporates"
        },
        {
          id: "qa-management",
          label: t("qa"),
          icon: <FaQuestionCircle className="w-5 h-5" />,
          href: "/admin/qa-management"
        },
        {
          id: "media-management",
          label: t("media"),
          icon: <FaVideo className="w-5 h-5" />,
          href: "/admin/media-management"
        },
        {
          id: "automation",
          label: t("automation"),
          icon: <FaRobot className="w-5 h-5" />,
          submenu: [
            {
              id: "invitations",
              label: t("invitations"),
              icon: <FaUsers className="w-4 h-4" />,
              href: "/admin/invitations"
            },
            {
              id: "recommendations",
              label: t("recommendations"),
              icon: <FaChartBar className="w-4 h-4" />,
              href: "/admin/recommendations"
            }
          ]
        },
        {
          id: "consultants",
          label: t("consultants"),
          icon: <FaUserTie className="w-5 h-5" />,
          href: "/admin/consultants"
        },
        {
          id: "analytics",
          label: t("analytics"),
          icon: <FaChartBar className="w-5 h-5" />,
          href: "/admin/analytics"
        }
      ]

    case "hr_admin":
    case "hr-admin":
    case "HR_ADMIN":
      return [
        {
          id: "dashboard",
          label: t("dashboard"),
          icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
          href: "/hr/dashboard"
        },
        {
          id: "employees",
          label: t("employees"),
          icon: <FaUsers className="w-5 h-5" />,
          href: "/hr/employees"
        },
        {
          id: "notifications",
          label: t("notifications"),
          icon: <FaBell className="w-5 h-5" />,
          href: "/hr/notifications"
        },
        {
          id: "invitations",
          label: t("invitations"),
          icon: <FaClipboardList className="w-5 h-5" />,
          href: "/hr/invitations"
        },
        {
          id: "analytics",
          label: t("analytics"),
          icon: <FaChartBar className="w-5 h-5" />,
          href: "/hr/analytics"
        }
      ]

    case "employee":
    case "EMPLOYEE":
      return [
        {
          id: "dashboard",
          label: t("dashboard"),
          icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
          href: "/employee/dashboard"
        },
        {
          id: "checkin",
          label: t("dailyCheckin"),
          icon: <FaHeartbeat className="w-5 h-5" />,
          href: "/employee/checkin"
        },
        {
          id: "media",
          label: t("mediaHub"),
          icon: <FaVideo className="w-5 h-5" />,
          href: "/employee/media"
        },
        {
          id: "chat",
          label: t("consultantChat"),
          icon: <FaComments className="w-5 h-5" />,
          href: "/employee/chat"
        },
        {
          id: "profile",
          label: t("myProfile"),
          icon: <FaUser className="w-5 h-5" />,
          href: "/employee/profile"
        }
      ]

    default:
      return []
  }
}

// Legacy static navigation items for backward compatibility
const systemAdminNavItems: NavItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
    href: "/admin/dashboard"
  },
  {
    id: "corporates",
    label: "Corporates",
    icon: <FaBuilding className="w-5 h-5" />,
    href: "/admin/corporates"
  },
  {
    id: "qa-management",
    label: "QA",
    icon: <FaQuestionCircle className="w-5 h-5" />,
    href: "/admin/qa-management"
  },
  {
    id: "media-management",
    label: "Media",
    icon: <FaVideo className="w-5 h-5" />,
    href: "/admin/media-management"
  },
  {
    id: "automation",
    label: "Automation",
    icon: <FaRobot className="w-5 h-5" />,
    submenu: [
      {
        id: "invitations",
        label: "Invitations",
        icon: <FaUsers className="w-4 h-4" />,
        href: "/admin/invitations"
      },
      {
        id: "recommendations",
        label: "Recommendations",
        icon: <FaChartBar className="w-4 h-4" />,
        href: "/admin/recommendations"
      }
    ]
  },
  {
    id: "consultants",
    label: "Consultants",
    icon: <FaUserTie className="w-5 h-5" />,
    href: "/admin/consultants"
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: <FaChartBar className="w-5 h-5" />,
    href: "/admin/analytics"
  }
]

// HR Admin navigation items
const hrAdminNavItems: NavItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
    href: "/hr/dashboard"
  },
  {
    id: "employees",
    label: "Employees",
    icon: <FaUsers className="w-5 h-5" />,
    href: "/hr/employees"
  },
  {
    id: "notifications",
    label: "Notifications",
    icon: <FaBell className="w-5 h-5" />,
    href: "/hr/notifications"
  },
  {
    id: "invitations",
    label: "Invitations",
    icon: <FaClipboardList className="w-5 h-5" />,
    href: "/hr/invitations"
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: <FaChartBar className="w-5 h-5" />,
    href: "/hr/analytics"
  }
]

// Employee navigation items
const employeeNavItems: NavItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: <MdOutlineSpaceDashboard className="w-5 h-5" />,
    href: "/employee/dashboard"
  },
  {
    id: "checkin",
    label: "Daily Check-in",
    icon: <FaHeartbeat className="w-5 h-5" />,
    href: "/employee/checkin"
  },
  {
    id: "media",
    label: "Media Hub",
    icon: <FaVideo className="w-5 h-5" />,
    href: "/employee/media"
  },
  {
    id: "chat",
    label: "Consultant Chat",
    icon: <FaComments className="w-5 h-5" />,
    href: "/employee/chat"
  },
  {
    id: "profile",
    label: "My Profile",
    icon: <FaUser className="w-5 h-5" />,
    href: "/employee/profile"
  }
]

// Function to get navigation items based on user role (legacy)
const getNavItemsForRole = (role: string): NavItem[] => {
  switch (role) {
    case "system_admin":
    case "system-admin":
    case "SYSTEM_ADMIN":
      return systemAdminNavItems
    case "hr_admin":
    case "hr-admin":
    case "HR_ADMIN":
      return hrAdminNavItems
    case "employee":
    case "EMPLOYEE":
      return employeeNavItems
    default:
      return systemAdminNavItems
  }
}

export interface AdminLayoutProps {
  children: React.ReactNode
  user?: User
  onLogout?: () => void
  variant?: "default" | "glass" | "solid"
  showNotifications?: boolean
  notificationCount?: number
  onNotificationClick?: () => void
  containerSize?: "sm" | "md" | "lg" | "xl" | "full"
  className?: string
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  user,
  onLogout,
  variant = "glass",
  showNotifications = true,
  notificationCount = 0,
  onNotificationClick,
  containerSize = "xl",
  className = ""
}) => {
  const { currentLanguage, isRTL } = useLanguage()

  // Get translated navigation items based on user role and current language
  const navItems = user ? getTranslatedNavItems(user.role, currentLanguage) : []

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex flex-col ${className} ${
        isRTL ? "rtl" : "ltr"
      }`}
    >
      {/* Decorative background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div
          className={`absolute -top-40 w-80 h-80 rounded-full bg-sehatti-gold-200/30 dark:bg-sehatti-gold-900/20 blur-3xl ${
            isRTL ? "-right-40" : "-left-40"
          }`}
        />
        <div
          className={`absolute top-1/4 w-60 h-60 rounded-full bg-sehatti-gold-300/20 dark:bg-sehatti-gold-800/20 blur-3xl ${
            isRTL ? "-left-20" : "-right-20"
          }`}
        />
        <div
          className={`absolute -bottom-40 w-80 h-80 rounded-full bg-sehatti-gold-200/30 dark:bg-sehatti-gold-900/20 blur-3xl ${
            isRTL ? "right-1/3" : "left-1/3"
          }`}
        />
      </div>

      {/* Main Content Area - flex-1 to take available space, pb-24 for navbar space */}
      <div className="flex-1 pb-24 relative z-10">
        <Container size={containerSize} className="pt-6">
          {children}
        </Container>
      </div>

      {/* Floating Bottom Navigation */}
      {user && (
        <AdminNavbar
          user={{
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            role: getRoleDisplayName(user.role)
          }}
          navItems={navItems}
          onLogout={onLogout}
          variant={variant}
          showNotifications={showNotifications}
          notificationCount={notificationCount}
          onNotificationClick={onNotificationClick}
        />
      )}
    </div>
  )
}

// Helper function to get role display name
const getRoleDisplayName = (role: string): string => {
  switch (role) {
    case "system_admin":
    case "system-admin":
    case "SYSTEM_ADMIN":
      return "System Administrator"
    case "hr_admin":
    case "hr-admin":
    case "HR_ADMIN":
      return "HR Administrator"
    case "employee":
    case "EMPLOYEE":
      return "Employee"
    case "consultant":
    case "CONSULTANT":
      return "Consultant"
    default:
      return role
  }
}

export default AdminLayout
