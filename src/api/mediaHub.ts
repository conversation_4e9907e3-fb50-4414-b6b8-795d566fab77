import { mediaServiceApi } from '../store/api/baseApi'
import type { ApiResponse } from '../types/api'

// Media Hub Types
export interface Hub {
  hub_id: string
  content: {
    en?: {
      title: string
      description: string
    }
    ar?: {
      title: string
      description: string
    }
    hi?: {
      title: string
      description: string
    }
  }
  tags: {
    en: string[]
    ar: string[]
    hi: string[]
  }
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ContentItem {
  content_id: string
  hub_id: string
  content_type: 'video' | 'article' | 'audio' | 'document'
  title: {
    en: string
    ar?: string
    hi?: string
  }
  description: {
    en: string
    ar?: string
    hi?: string
  }
  video_url?: string
  thumbnail_url?: string
  file_url?: string
  duration?: number
  tags: {
    en: string[]
    ar: string[]
    hi: string[]
  }
  metadata?: Record<string, any>
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface WellnessArticle {
  id: string
  title: {
    en: string
    ar: string
    hi: string
  }
  description: {
    en: string
    ar: string
    hi: string
  }
  content?: {
    en: string
    ar: string
    hi: string
  }
  category: string
  division: string
  readTime: number
  source: string
  views?: number
  likes?: number
  bookmarked?: boolean
  tags?: string[]
  author?: string
  published_date?: string
  image_url?: string
}

export interface LearningCategory {
  id: string
  name: {
    en: string
    ar: string
    hi: string
  }
  description: {
    en: string
    ar: string
    hi: string
  }
  icon: string
  color: string
  count: number
  is_active: boolean
}

export interface MediaHubFilters {
  category?: string
  division?: string
  language?: 'en' | 'ar' | 'hi'
  search?: string
  sortBy?: 'newest' | 'popular' | 'duration' | 'rating'
  page?: number
  limit?: number
}

export interface MediaHubResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  has_next: boolean
  has_previous: boolean
}

// Media Hub API endpoints
export const mediaHubApi = mediaServiceApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all hubs with optional filters
    getMediaHubs: builder.query<MediaHubResponse<Hub>, MediaHubFilters>({
      query: (filters = {}) => ({
        url: '/hubs',
        method: 'GET',
        params: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          search: filters.search,
          language: filters.language || 'en',
        },
      }),
      providesTags: ['Media'],
    }),

    // Get hub by ID
    getMediaHub: builder.query<ApiResponse<Hub>, string>({
      query: (hubId) => ({
        url: `/hubs/${hubId}`,
        method: 'GET',
      }),
      providesTags: (result, error, hubId) => [{ type: 'Media', id: hubId }],
    }),

    // Get content for a specific hub
    getHubContent: builder.query<MediaHubResponse<ContentItem>, { hubId: string } & MediaHubFilters>({
      query: ({ hubId, ...filters }) => ({
        url: `/hubs/${hubId}/content`,
        method: 'GET',
        params: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          search: filters.search,
          language: filters.language || 'en',
          category: filters.category,
        },
      }),
      providesTags: (result, error, { hubId }) => [{ type: 'Media', id: `hub-${hubId}` }],
    }),

    // Get wellness articles
    getWellnessArticles: builder.query<MediaHubResponse<WellnessArticle>, MediaHubFilters>({
      query: (filters = {}) => ({
        url: '/articles',
        method: 'GET',
        params: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          search: filters.search,
          division: filters.division,
          category: filters.category,
          sort_by: filters.sortBy || 'newest',
          language: filters.language || 'en',
        },
      }),
      providesTags: ['Media'],
    }),

    // Get article by ID
    getWellnessArticle: builder.query<ApiResponse<WellnessArticle>, string>({
      query: (articleId) => ({
        url: `/articles/${articleId}`,
        method: 'GET',
      }),
      providesTags: (result, error, articleId) => [{ type: 'Media', id: articleId }],
    }),

    // Get learning categories
    getLearningCategories: builder.query<MediaHubResponse<LearningCategory>, { language?: 'en' | 'ar' | 'hi' }>({
      query: ({ language = 'en' } = {}) => ({
        url: '/categories',
        method: 'GET',
        params: { language },
      }),
      providesTags: ['Media'],
    }),

    // Track article view
    trackArticleView: builder.mutation<ApiResponse<void>, { articleId: string; duration?: number }>({
      query: ({ articleId, duration }) => ({
        url: `/articles/${articleId}/view`,
        method: 'POST',
        body: { duration },
      }),
      invalidatesTags: (result, error, { articleId }) => [{ type: 'Media', id: articleId }],
    }),

    // Like/Unlike article
    toggleArticleLike: builder.mutation<ApiResponse<{ liked: boolean }>, string>({
      query: (articleId) => ({
        url: `/articles/${articleId}/like`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, articleId) => [{ type: 'Media', id: articleId }],
    }),

    // Bookmark/Unbookmark article
    toggleArticleBookmark: builder.mutation<ApiResponse<{ bookmarked: boolean }>, string>({
      query: (articleId) => ({
        url: `/articles/${articleId}/bookmark`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, articleId) => [{ type: 'Media', id: articleId }],
    }),

    // Get user's bookmarked articles
    getBookmarkedArticles: builder.query<MediaHubResponse<WellnessArticle>, MediaHubFilters>({
      query: (filters = {}) => ({
        url: '/articles/bookmarked',
        method: 'GET',
        params: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          language: filters.language || 'en',
        },
      }),
      providesTags: ['Media'],
    }),

    // Track video view
    trackVideoView: builder.mutation<ApiResponse<void>, { contentId: string; duration?: number; completed?: boolean }>({
      query: ({ contentId, duration, completed }) => ({
        url: `/content/${contentId}/view`,
        method: 'POST',
        body: { duration, completed },
      }),
      invalidatesTags: (result, error, { contentId }) => [{ type: 'Media', id: contentId }],
    }),
  }),
})

// Export hooks
export const {
  useGetMediaHubsQuery,
  useGetMediaHubQuery,
  useGetHubContentQuery,
  useGetWellnessArticlesQuery,
  useGetWellnessArticleQuery,
  useGetLearningCategoriesQuery,
  useTrackArticleViewMutation,
  useToggleArticleLikeMutation,
  useToggleArticleBookmarkMutation,
  useGetBookmarkedArticlesQuery,
  useTrackVideoViewMutation,
} = mediaHubApi
