import { useState } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { useLoginMutation } from "@/store/api";
import { useAppDispatch } from "../store/store";
import { loginSuccess } from "../features/auth/auth-slice";
import { setTokens } from "../lib/axios";
import type { LoginRequest } from "../types/api";

interface LoginData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const useLoginFunc = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [loginData, setLoginData] = useState<LoginData>({
    email: '',
    password: '',
    rememberMe: false,
  });

  const [login, { isLoading, error }] = useLoginMutation();

  const handleInputChange = (field: keyof LoginData, value: string | boolean) => {
    setLoginData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isLoading) {
      console.log('Login already in progress, preventing duplicate submission');
      return;
    }

    // Validation
    if (!loginData.email || !loginData.password) {
      toast.error('Please fill in all fields');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(loginData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      const loginRequest: LoginRequest = {
        email: loginData.email.toLowerCase().trim(),
        password: loginData.password,
      };

      const result = await login(loginRequest).unwrap();
      
      // Store tokens using enhanced token management with remember me preference
      setTokens(result.token, undefined, loginData.rememberMe);
      
      // Dispatch success action to update Redux state with v2 response structure
      dispatch(loginSuccess({
        user: result.user,
        token: result.token,
        isEnableSupport: result.isEnableSupport,
      }));

      toast.success('Login successful!');
      
      // Navigate to dashboard or intended route
      const redirectTo = new URLSearchParams(window.location.search).get('redirect') || '/dashboard';
      navigate(redirectTo, { replace: true });
      
    } catch (err: any) {
      console.error('Login error:', err);
      
      // Handle specific error cases
      if (err?.status === 401) {
        toast.error('Invalid email or password');
      } else if (err?.status === 403) {
        toast.error('Account is not active. Please contact support.');
      } else if (err?.status === 429) {
        toast.error('Too many login attempts. Please try again later.');
      } else {
        toast.error(err?.data?.message || 'Login failed. Please try again.');
      }
    }
  };

  return {
    loginData,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
  };
}; 