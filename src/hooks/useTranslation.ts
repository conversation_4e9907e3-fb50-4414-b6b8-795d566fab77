import { useTranslation as useI18nextTranslation } from 'react-i18next'
import { useCallback, useMemo } from 'react'

// Language mapping between our custom codes and i18next codes
const LANGUAGE_MAP = {
  'EN': 'en',
  'AR': 'ar', 
  'HI': 'hi'
} as const

const REVERSE_LANGUAGE_MAP = {
  'en': 'EN',
  'ar': 'AR',
  'hi': 'HI'
} as const

export type SupportedLanguage = 'EN' | 'AR' | 'HI'
export type I18nextLanguage = 'en' | 'ar' | 'hi'

// Language configuration interface
export interface LanguageConfig {
  code: SupportedLanguage
  i18nCode: I18nextLanguage
  name: string
  nativeName: string
  flag: string
  isRTL: boolean
  direction: 'ltr' | 'rtl'
}

// Supported languages configuration
export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  {
    code: 'EN',
    i18nCode: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    isRTL: false,
    direction: 'ltr'
  },
  {
    code: 'AR',
    i18nCode: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    isRTL: true,
    direction: 'rtl'
  },
  {
    code: 'HI',
    i18nCode: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    isRTL: false,
    direction: 'ltr'
  }
]

// Translation interface for type safety
export interface TranslationKeys {
  common: {
    welcome: string
    refresh: string
    language: string
    score: string
    questions: string
    articles: string
    dimensions: string
    minutes: string
    readMore: string
    of: string
    great: string
    okay: string
    notGood: string
  }
  navigation: {
    overview: string
    checkin: string
    dimensions: string
    profile: string
    qrcode: string
    wellnessOverview: string
    dailyCheckin: string
    wellnessDimensions: string
    profileSettings: string
    myQRCode: string
    mediaHub: string
    consultantChat: string
    myProgress: string
  }
  dashboard: {
    questionsWaiting: string
    overallScore: string
    journeyProgress: string
    congratulations: string
  }
  assessment: {
    webinarIntro: string
    webinarBooking: string
    preAssessment: string
    contentAccess: string
    scientificAssessment: string
    questionOf: string
    fivePointScale: string
    responseRecorded: string
    completeCheckin: string
    submitResponses: string
  }
  checkin: {
    todaysCheckin: string
    howDoYouFeel: string
  }
  content: {
    exploreContent: string
    relatedContent: string
    sevenDimensions: string
  }
  chat: {
    connectConsultants: string
    personalizedGuidance: string
    startConsultation: string
  }
  wellnessDivisions: {
    physical: {
      name: string
      description: string
    }
    emotional: {
      name: string
      description: string
    }
    social: {
      name: string
      description: string
    }
    occupational: {
      name: string
      description: string
    }
    intellectual: {
      name: string
      description: string
    }
    environmental: {
      name: string
      description: string
    }
    financial: {
      name: string
      description: string
    }
  }
  mediaHub: {
    title: string
    subtitle: string
    tabs: {
      media: string
      articles: string
    }
    search: {
      placeholder: string
      placeholderArticles: string
      placeholderHubs: string
      noResults: string
      tryDifferentTerms: string
    }
    filters: {
      allCategories: string
      allDivisions: string
      sortBy: string
      newest: string
      popular: string
      shortestRead: string
      highestRated: string
      clearFilters: string
    }
    categories: {
      fundamentals: string
      workplace: string
      mindfulness: string
      physical: string
      social: string
      environment: string
      financial: string
    }
    stats: {
      showing: string
      of: string
      articles: string
      videos: string
      bookmarked: string
      liked: string
      views: string
      likes: string
    }
    actions: {
      readArticle: string
      watchVideo: string
      share: string
      bookmark: string
      like: string
      download: string
      copyLink: string
      copied: string
      helpful: string
    }
    sharing: {
      title: string
      shareVia: string
      twitter: string
      linkedin: string
      whatsapp: string
      copyLink: string
    }
    viewModes: {
      grid: string
      list: string
    }
    loading: {
      loadingContent: string
      loadingArticles: string
      loadingVideos: string
    }
    errors: {
      failedToLoad: string
      tryAgain: string
      networkError: string
    }
  }
}

// Custom hook that extends react-i18next with our specific needs
export const useTranslation = () => {
  const { t: i18nT, i18n } = useI18nextTranslation()

  // Get current language in our format
  const currentLanguage = useMemo((): SupportedLanguage => {
    return REVERSE_LANGUAGE_MAP[i18n.language as I18nextLanguage] || 'EN'
  }, [i18n.language])

  // Get language configuration
  const languageConfig = useMemo((): LanguageConfig => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === currentLanguage) || SUPPORTED_LANGUAGES[0]
  }, [currentLanguage])

  // Change language function
  const changeLanguage = useCallback((language: SupportedLanguage) => {
    const i18nCode = LANGUAGE_MAP[language]
    if (i18nCode) {
      i18n.changeLanguage(i18nCode)
    }
  }, [i18n])

  // Type-safe translation function
  const t = useMemo((): TranslationKeys => {
    return {
      common: {
        welcome: i18nT('common.welcome'),
        refresh: i18nT('common.refresh'),
        language: i18nT('common.language'),
        score: i18nT('common.score'),
        questions: i18nT('common.questions'),
        articles: i18nT('common.articles'),
        dimensions: i18nT('common.dimensions'),
        minutes: i18nT('common.minutes'),
        readMore: i18nT('common.readMore'),
        of: i18nT('common.of'),
        great: i18nT('common.great'),
        okay: i18nT('common.okay'),
        notGood: i18nT('common.notGood')
      },
      navigation: {
        overview: i18nT('navigation.overview'),
        checkin: i18nT('navigation.checkin'),
        dimensions: i18nT('navigation.dimensions'),
        profile: i18nT('navigation.profile'),
        qrcode: i18nT('navigation.qrcode'),
        wellnessOverview: i18nT('navigation.wellnessOverview'),
        dailyCheckin: i18nT('navigation.dailyCheckin'),
        wellnessDimensions: i18nT('navigation.wellnessDimensions'),
        profileSettings: i18nT('navigation.profileSettings'),
        myQRCode: i18nT('navigation.myQRCode'),
        mediaHub: i18nT('navigation.mediaHub'),
        consultantChat: i18nT('navigation.consultantChat'),
        myProgress: i18nT('navigation.myProgress')
      },
      dashboard: {
        questionsWaiting: i18nT('dashboard.questionsWaiting'),
        overallScore: i18nT('dashboard.overallScore'),
        journeyProgress: i18nT('dashboard.journeyProgress'),
        congratulations: i18nT('dashboard.congratulations')
      },
      assessment: {
        webinarIntro: i18nT('assessment.webinarIntro'),
        webinarBooking: i18nT('assessment.webinarBooking'),
        preAssessment: i18nT('assessment.preAssessment'),
        contentAccess: i18nT('assessment.contentAccess'),
        scientificAssessment: i18nT('assessment.scientificAssessment'),
        questionOf: i18nT('assessment.questionOf'),
        fivePointScale: i18nT('assessment.fivePointScale'),
        responseRecorded: i18nT('assessment.responseRecorded'),
        completeCheckin: i18nT('assessment.completeCheckin'),
        submitResponses: i18nT('assessment.submitResponses')
      },
      checkin: {
        todaysCheckin: i18nT('checkin.todaysCheckin'),
        howDoYouFeel: i18nT('checkin.howDoYouFeel')
      },
      content: {
        exploreContent: i18nT('content.exploreContent'),
        relatedContent: i18nT('content.relatedContent'),
        sevenDimensions: i18nT('content.sevenDimensions')
      },
      chat: {
        connectConsultants: i18nT('chat.connectConsultants'),
        personalizedGuidance: i18nT('chat.personalizedGuidance'),
        startConsultation: i18nT('chat.startConsultation')
      },
      wellnessDivisions: {
        physical: {
          name: i18nT('wellnessDivisions.physical.name'),
          description: i18nT('wellnessDivisions.physical.description')
        },
        emotional: {
          name: i18nT('wellnessDivisions.emotional.name'),
          description: i18nT('wellnessDivisions.emotional.description')
        },
        social: {
          name: i18nT('wellnessDivisions.social.name'),
          description: i18nT('wellnessDivisions.social.description')
        },
        occupational: {
          name: i18nT('wellnessDivisions.occupational.name'),
          description: i18nT('wellnessDivisions.occupational.description')
        },
        intellectual: {
          name: i18nT('wellnessDivisions.intellectual.name'),
          description: i18nT('wellnessDivisions.intellectual.description')
        },
        environmental: {
          name: i18nT('wellnessDivisions.environmental.name'),
          description: i18nT('wellnessDivisions.environmental.description')
        },
        financial: {
          name: i18nT('wellnessDivisions.financial.name'),
          description: i18nT('wellnessDivisions.financial.description')
        }
      },
      mediaHub: {
        title: i18nT('mediaHub.title'),
        subtitle: i18nT('mediaHub.subtitle'),
        tabs: {
          media: i18nT('mediaHub.tabs.media'),
          articles: i18nT('mediaHub.tabs.articles')
        },
        search: {
          placeholder: i18nT('mediaHub.search.placeholder'),
          placeholderArticles: i18nT('mediaHub.search.placeholderArticles'),
          placeholderHubs: i18nT('mediaHub.search.placeholderHubs'),
          noResults: i18nT('mediaHub.search.noResults'),
          tryDifferentTerms: i18nT('mediaHub.search.tryDifferentTerms')
        },
        filters: {
          allCategories: i18nT('mediaHub.filters.allCategories'),
          allDivisions: i18nT('mediaHub.filters.allDivisions'),
          sortBy: i18nT('mediaHub.filters.sortBy'),
          newest: i18nT('mediaHub.filters.newest'),
          popular: i18nT('mediaHub.filters.popular'),
          shortestRead: i18nT('mediaHub.filters.shortestRead'),
          highestRated: i18nT('mediaHub.filters.highestRated'),
          clearFilters: i18nT('mediaHub.filters.clearFilters')
        },
        categories: {
          fundamentals: i18nT('mediaHub.categories.fundamentals'),
          workplace: i18nT('mediaHub.categories.workplace'),
          mindfulness: i18nT('mediaHub.categories.mindfulness'),
          physical: i18nT('mediaHub.categories.physical'),
          social: i18nT('mediaHub.categories.social'),
          environment: i18nT('mediaHub.categories.environment'),
          financial: i18nT('mediaHub.categories.financial')
        },
        stats: {
          showing: i18nT('mediaHub.stats.showing'),
          of: i18nT('mediaHub.stats.of'),
          articles: i18nT('mediaHub.stats.articles'),
          videos: i18nT('mediaHub.stats.videos'),
          bookmarked: i18nT('mediaHub.stats.bookmarked'),
          liked: i18nT('mediaHub.stats.liked'),
          views: i18nT('mediaHub.stats.views'),
          likes: i18nT('mediaHub.stats.likes')
        },
        actions: {
          readArticle: i18nT('mediaHub.actions.readArticle'),
          watchVideo: i18nT('mediaHub.actions.watchVideo'),
          share: i18nT('mediaHub.actions.share'),
          bookmark: i18nT('mediaHub.actions.bookmark'),
          like: i18nT('mediaHub.actions.like'),
          download: i18nT('mediaHub.actions.download'),
          copyLink: i18nT('mediaHub.actions.copyLink'),
          copied: i18nT('mediaHub.actions.copied'),
          helpful: i18nT('mediaHub.actions.helpful')
        },
        sharing: {
          title: i18nT('mediaHub.sharing.title'),
          shareVia: i18nT('mediaHub.sharing.shareVia'),
          twitter: i18nT('mediaHub.sharing.twitter'),
          linkedin: i18nT('mediaHub.sharing.linkedin'),
          whatsapp: i18nT('mediaHub.sharing.whatsapp'),
          copyLink: i18nT('mediaHub.sharing.copyLink')
        },
        viewModes: {
          grid: i18nT('mediaHub.viewModes.grid'),
          list: i18nT('mediaHub.viewModes.list')
        },
        loading: {
          loadingContent: i18nT('mediaHub.loading.loadingContent'),
          loadingArticles: i18nT('mediaHub.loading.loadingArticles'),
          loadingVideos: i18nT('mediaHub.loading.loadingVideos')
        },
        errors: {
          failedToLoad: i18nT('mediaHub.errors.failedToLoad'),
          tryAgain: i18nT('mediaHub.errors.tryAgain'),
          networkError: i18nT('mediaHub.errors.networkError')
        }
      }
    }
  }, [i18nT])

  // RTL helper functions
  const isRTL = languageConfig.isRTL
  const rtlClasses = useMemo(() => ({
    direction: isRTL ? 'rtl' : 'ltr',
    textAlign: isRTL ? 'text-right' : 'text-left',
    flexDirection: isRTL ? 'flex-row-reverse' : 'flex-row',
    spaceDirection: isRTL ? 'space-x-reverse' : ''
  }), [isRTL])

  return {
    t,
    currentLanguage,
    changeLanguage,
    isRTL,
    languageConfig,
    rtlClasses,
    i18n
  }
}
