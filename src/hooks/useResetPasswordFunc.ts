import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useResetPasswordMutation } from '@/store/api';

interface ResetPasswordData {
  password: string;
  confirmPassword: string;
}

export const useResetPasswordFunc = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token') || '';

  const [resetData, setResetData] = useState<ResetPasswordData>({
    password: '',
    confirmPassword: '',
  });
  const [tokenValid, setTokenValid] = useState<boolean | null>(true); // Assume token is valid initially
  const [showSuccess, setShowSuccess] = useState(false);

  const [resetPassword, { isLoading: isResetting }] = useResetPasswordMutation();

  // Debug loading state changes
  useEffect(() => {
    console.log('Reset password loading state changed:', isResetting);
  }, [isResetting]);

  // Check if token exists in URL
  useEffect(() => {
    if (!token) {
      setTokenValid(false);
      toast.error('Invalid or missing reset token');
    } else {
      setTokenValid(true);
    }
  }, [token]);

  const handleInputChange = (field: keyof ResetPasswordData, value: string) => {
    setResetData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    console.log('Form submit triggered!');
    e.preventDefault();

    console.log('Form data:', resetData);
    console.log('Token:', token);

    // Validation
    if (!resetData.password || !resetData.confirmPassword) {
      console.log('Validation failed: Missing password fields');
      toast.error('Please fill in all fields');
      return;
    }

    if (resetData.password !== resetData.confirmPassword) {
      console.log('Validation failed: Passwords do not match');
      toast.error('Passwords do not match');
      return;
    }

    if (resetData.password.length < 8) {
      console.log('Validation failed: Password too short');
      toast.error('Password must be at least 8 characters long');
      return;
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
    if (!passwordRegex.test(resetData.password)) {
      console.log('Validation failed: Password does not meet requirements');
      toast.error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      return;
    }

    console.log('All validations passed, proceeding with API call...');

    try {
      const request = {
        token,
        password: resetData.password,
      };

      console.log('Submitting reset password request...', request);
      console.log('Loading state before request:', isResetting);

      // Add minimum loading duration to make loading visible (remove in production)
      const [result] = await Promise.all([
        resetPassword(request).unwrap(),
        new Promise(resolve => setTimeout(resolve, 1000)) // Minimum 1 second loading
      ]);
      
      console.log('Reset password success:', result);
      console.log('Loading state after success:', isResetting);
      toast.success(result.message || 'Password reset successfully!');
      setShowSuccess(true);
      
      // Navigate to login after a short delay
      setTimeout(() => {
        navigate('/', { 
          state: { message: 'Password reset successfully. Please login with your new password.' }
        });
      }, 2000);
      
    } catch (err: any) {
      console.error('Reset password error:', err);
      console.error('Error details:', err?.data);
      console.error('Error status:', err?.status);
      
      // Handle specific error cases
      if (err?.status === 404 || err?.status === 401) {
        toast.error('Invalid or expired reset token. Please request a new password reset.');
        setTimeout(() => navigate('/forgot-password'), 2000);
      } else if (err?.status === 422) {
        // Handle validation errors from backend
        const errors = err?.data?.errors;
        if (errors && Array.isArray(errors)) {
          errors.forEach((error: any) => {
            toast.error(error.message || 'Validation error');
          });
        } else {
          toast.error(err?.data?.message || 'Password reset failed. Please check your password requirements.');
        }
      } else if (err?.status === 429) {
        toast.error('Too many attempts. Please try again later.');
      } else {
        toast.error(err?.data?.message || 'Password reset failed. Please try again.');
      }
    }
  };

  const navigateToLogin = () => {
    navigate('/');
  };

  const navigateToForgotPassword = () => {
    navigate('/forgot-password');
  };

  return {
    resetData,
    tokenValid,
    showSuccess,
    isLoading: isResetting,
    handleInputChange,
    handleSubmit,
    navigateToLogin,
    navigateToForgotPassword,
  };
}; 