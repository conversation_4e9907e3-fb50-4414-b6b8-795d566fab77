import { useAppSelector } from '@/store/store'
import { selectAuth, selectUser, selectIsAuthenticated } from '@/features/auth/auth-slice'

export function useAuth() {
  const auth = useAppSelector(selectAuth)
  const user = useAppSelector(selectUser)
  const isAuthenticated = useAppSelector(selectIsAuthenticated)

  return {
    user,
    isAuthenticated,
    loading: auth.loading,
    error: auth.error,
    token: auth.token
  }
} 