import { useCallback, useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { selectAuth, logout } from '@/features/auth/auth-slice';
import { getAuthToken, getRefreshToken, setTokens, clearTokens, isTokenExpired } from '@/lib/axios';

interface UseTokenRefreshReturn {
  refreshToken: () => Promise<boolean>;
  isTokenValid: () => boolean;
  scheduleTokenRefresh: () => void;
  cancelScheduledRefresh: () => void;
}

export const useTokenRefresh = (): UseTokenRefreshReturn => {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(selectAuth);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Listen for auth logout events
  useEffect(() => {
    const handleLogout = () => {
      dispatch(logout());
      cancelScheduledRefresh();
    };

    window.addEventListener('auth:logout', handleLogout);
    return () => window.removeEventListener('auth:logout', handleLogout);
  }, [dispatch]);

  // Check if current token is valid
  const isTokenValid = useCallback((): boolean => {
    const token = getAuthToken();
    return token ? !isTokenExpired(token) : false;
  }, []);

  // Manual token refresh function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    if (isRefreshingRef.current) {
      return false;
    }

    const refreshToken = getRefreshToken();
    if (!refreshToken || !isAuthenticated) {
      return false;
    }

    isRefreshingRef.current = true;

    try {
      const response = await fetch(`${process.env.VITE_API_URL || ''}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${refreshToken}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      const { token: newToken, refreshToken: newRefreshToken } = data.data;

      // Update tokens
      setTokens(newToken, newRefreshToken);
      
      // Schedule next refresh
      scheduleTokenRefresh();
      
      return true;
    } catch (error) {
      console.error('Token refresh error:', error);
      
      // Clear tokens and logout
      clearTokens();
      dispatch(logout());
      
      return false;
    } finally {
      isRefreshingRef.current = false;
    }
  }, [isAuthenticated, dispatch]);

  // Schedule automatic token refresh
  const scheduleTokenRefresh = useCallback(() => {
    // Clear existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    const token = getAuthToken();
    if (!token || !isAuthenticated) {
      return;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();
      const timeUntilExpiry = expirationTime - currentTime;
      
      // Refresh token 5 minutes before expiry (300,000ms)
      const refreshTime = Math.max(timeUntilExpiry - 300000, 60000); // At least 1 minute

      if (refreshTime > 0) {
        refreshTimeoutRef.current = setTimeout(() => {
          refreshToken();
        }, refreshTime);
      }
    } catch (error) {
      console.error('Error scheduling token refresh:', error);
    }
  }, [isAuthenticated, refreshToken]);

  // Cancel scheduled refresh
  const cancelScheduledRefresh = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }
  }, []);

  // Auto-schedule refresh when token or auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      scheduleTokenRefresh();
    } else {
      cancelScheduledRefresh();
    }
  }, [isAuthenticated, scheduleTokenRefresh, cancelScheduledRefresh]);

  return {
    refreshToken,
    isTokenValid,
    scheduleTokenRefresh,
    cancelScheduledRefresh,
  };
}; 