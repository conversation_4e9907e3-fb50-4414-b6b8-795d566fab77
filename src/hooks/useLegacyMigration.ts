import { useCallback } from 'react';
import { useSyncCorporateToQAMutation } from '../features/API/qaServiceApi';
import type { Corporate } from '../types/qa';

export interface UseLegacyMigrationReturn {
  migrateCorporateData: (corporate: Corporate) => Promise<void>;
  isMigrating: boolean;
}

export const useLegacyMigration = (): UseLegacyMigrationReturn => {
  const [syncCorporateToQA, { isLoading: isMigrating }] = useSyncCorporateToQAMutation();

  const migrateCorporateData = useCallback(async (corporate: Corporate): Promise<void> => {
    try {
      await syncCorporateToQA({ corporateId: corporate.id }).unwrap();
    } catch (error) {
      console.error('Failed to migrate corporate data:', error);
      throw error;
    }
  }, [syncCorporateToQA]);

  return {
    migrateCorporateData,
    isMigrating,
  };
}; 