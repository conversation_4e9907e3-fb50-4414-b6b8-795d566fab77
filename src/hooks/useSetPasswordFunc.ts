import { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useSetPasswordMutation, useVerifyTokenMutation } from '../store/api/authApi';
import type { SetPasswordRequest, VerifyTokenRequest } from '../types/api';

interface SetPasswordData {
  password: string;
  confirmPassword: string;
}

export const useSetPasswordFunc = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token') || '';
  const role = (searchParams.get('role') as 'employee' | 'consultant') || 'employee';
  const hasVerified = useRef(false);

  const [passwordData, setPasswordData] = useState<SetPasswordData>({
    password: '',
    confirmPassword: '',
  });
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const [setPassword, { isLoading: isSetting }] = useSetPasswordMutation();
  const [verifyToken, { isLoading: isVerifying }] = useVerifyTokenMutation();

  // Verify token on component mount
  useEffect(() => {
    if (hasVerified.current) return;
    
    const verifySetPasswordToken = async () => {
      hasVerified.current = true;
      
      if (!token) {
        setTokenValid(false);
        return;
      }

      // Development mode bypass - remove this in production
      if (process.env.NODE_ENV === 'development' && token === 'dev-token') {
        setTokenValid(true);
        return;
      }

      try {
        const request: VerifyTokenRequest = { token };
        const result = await verifyToken(request).unwrap();
        setTokenValid(true);
        
        // Role validation removed - accept any valid token regardless of role
      } catch (err: any) {
        console.error('Token verification error:', err);
        setTokenValid(false);
        
        if (err?.status === 404 || err?.status === 401) {
          toast.error('Invalid or expired invitation token');
        } else {
          toast.error('Failed to verify invitation token');
        }
      }
    };

    verifySetPasswordToken();
  }, [token, role, verifyToken]);

  const handleInputChange = (field: keyof SetPasswordData, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const checkPasswordMatch = () => {
    if (passwordData.password && passwordData.confirmPassword) {
      return passwordData.password === passwordData.confirmPassword;
    }
    return true; // Don't show error if fields are empty
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validation
    if (!passwordData.password || !passwordData.confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (passwordData.password !== passwordData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (passwordData.password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
    if (!passwordRegex.test(passwordData.password)) {
      toast.error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      return;
    }

    try {
      const request: SetPasswordRequest & { role: 'employee' | 'consultant' } = {
        token,
        password: passwordData.password,
        confirm_password: passwordData.confirmPassword, // Use snake_case for backend
        role,
      };

      const result = await setPassword(request).unwrap();
      
      toast.success('Password set successfully!');
      setShowSuccess(true);
      
      // Navigate to login after a short delay
      setTimeout(() => {
                 navigate('/', { 
           state: { 
             message: `Welcome! Your ${role} account has been activated. Please login with your credentials.`
           }
         });
      }, 2000);
      
    } catch (err: any) {
      console.error('Set password error:', err);
      
      // Handle specific error cases
      if (err?.status === 404 || err?.status === 401) {
        toast.error('Invalid or expired invitation token. Please contact your administrator.');
      } else if (err?.status === 409) {
        toast.error('This invitation has already been used. Please contact your administrator if you need assistance.');
      } else if (err?.status === 422) {
        // Handle validation errors from backend
        const errors = err?.data?.errors;
        if (errors && Array.isArray(errors)) {
          errors.forEach((error: any) => {
            toast.error(error.message || 'Validation error');
          });
        } else {
          toast.error(err?.data?.message || 'Password setup failed. Please check your password requirements.');
        }
      } else if (err?.status === 429) {
        toast.error('Too many attempts. Please try again later.');
      } else {
        toast.error(err?.data?.message || 'Password setup failed. Please try again.');
      }
    }
  };

  const navigateToLogin = () => {
    navigate('/');
  };

  return {
    passwordData,
    tokenValid,
    showSuccess,
    role,
    isLoading: isSetting || isVerifying,
    checkPasswordMatch,
    handleInputChange,
    handleSubmit,
    navigateToLogin,
  };
}; 