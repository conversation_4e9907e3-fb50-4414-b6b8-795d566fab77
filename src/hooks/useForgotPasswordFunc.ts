import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { useForgotPasswordMutation } from '@/store/api';

export const useForgotPasswordFunc = () => {
  const [email, setEmail] = useState('');
  const [emailSent, setEmailSent] = useState(false);

  const [forgotPassword, { isLoading, error }] = useForgotPasswordMutation();

  // Debug loading state changes
  useEffect(() => {
    console.log('Loading state changed:', isLoading);
  }, [isLoading]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      const request = {
        email: email.toLowerCase().trim(),
      };

      console.log('Submitting forgot password request...', request);
      console.log('Loading state before request:', isLoading);

      const result = await forgotPassword(request).unwrap();
      
      console.log('Forgot password success:', result);
      toast.success(result.message || 'Password reset instructions sent to your email');
      setEmailSent(true);
      
      // Clear the email field after successful submission
      setEmail('');
      
    } catch (err: any) {
      console.error('Forgot password error:', err);
      console.log('Loading state after error:', isLoading);
      // Error will be handled by the Alert component in the UI
    }
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    // Reset email sent status when user starts typing again
    if (emailSent) {
      setEmailSent(false);
    }
  };

  return {
    email,
    emailSent,
    isLoading,
    error,
    handleEmailChange,
    handleSubmit,
  };
}; 