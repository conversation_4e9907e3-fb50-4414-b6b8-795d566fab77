import { useCallback } from 'react';
import type { TranslatedContent } from '../types/qa';

export interface UseLanguageHelperReturn {
  getTranslatedText: (content: TranslatedContent | string | undefined, fallbackLang?: string) => string;
  getCurrentLanguage: () => string;
  getSupportedLanguages: () => string[];
  formatTranslatedContent: (text: string, language?: string) => TranslatedContent;
}

export const useLanguageHelper = (): UseLanguageHelperReturn => {
  // Get current language (fallback to 'en' if not available)
  const getCurrentLanguage = useCallback((): string => {
    // Try to get from localStorage, URL params, or browser locale
    const savedLang = localStorage.getItem('language') || 
                     localStorage.getItem('i18nextLng') ||
                     navigator.language.split('-')[0];
    
    return ['en', 'ar', 'hi'].includes(savedLang) ? savedLang : 'en';
  }, []);

  // Get list of supported languages
  const getSupportedLanguages = useCallback((): string[] => {
    return ['en', 'ar', 'hi'];
  }, []);

  // Extract translated text based on current language with fallbacks
  const getTranslatedText = useCallback((
    content: TranslatedContent | string | undefined, 
    fallbackLang: string = 'en'
  ): string => {
    if (!content) return '';
    
    // If content is already a string, return it
    if (typeof content === 'string') return content;
    
    const currentLang = getCurrentLanguage();
    
    // Try current language first
    if (content[currentLang] && content[currentLang].trim()) {
      return content[currentLang];
    }
    
    // Try fallback language
    if (content[fallbackLang] && content[fallbackLang].trim()) {
      return content[fallbackLang];
    }
    
    // Try English as ultimate fallback
    if (content.en && content.en.trim()) {
      return content.en;
    }
    
    // Try any available language
    const availableKeys = Object.keys(content);
    for (const key of availableKeys) {
      if (content[key] && content[key].trim()) {
        return content[key];
      }
    }
    
    return '';
  }, [getCurrentLanguage]);

  // Format a single text string into TranslatedContent structure
  const formatTranslatedContent = useCallback((
    text: string, 
    language?: string
  ): TranslatedContent => {
    const lang = language || getCurrentLanguage();
    
    return {
      en: lang === 'en' ? text : '',
      ar: lang === 'ar' ? text : '',
      hi: lang === 'hi' ? text : '',
      [lang]: text
    };
  }, [getCurrentLanguage]);

  return {
    getTranslatedText,
    getCurrentLanguage,
    getSupportedLanguages,
    formatTranslatedContent,
  };
}; 