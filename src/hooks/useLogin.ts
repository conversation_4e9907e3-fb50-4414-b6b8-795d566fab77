import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import Cookies from 'js-cookie'
import { useLoginMutation } from '@/store/api/authApi'
import { useAppDispatch } from '@/hooks/redux'
import { loginSuccess } from '@/store/slices/authSlice'

export interface LoginFormData {
  email: string
  password: string
  rememberMe: boolean
}

export interface UseLoginReturn {
  email: string
  password: string
  isRemember: boolean
  isLoading: boolean
  pendingModal: boolean
  setEmail: (email: string) => void
  setPassword: (password: string) => void
  setIsRemember: (remember: boolean) => void
  setPendingModal: (show: boolean) => void
  handleLogin: (e: React.FormEvent<HTMLFormElement>) => Promise<void>
}

export const useLogin = (): UseLoginReturn => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isRemember, setIsRemember] = useState(false)
  const [pendingModal, setPendingModal] = useState(false)

  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [login, { isLoading }] = useLoginMutation()

  // Load saved credentials from cookies
  useEffect(() => {
    const savedAuth = Cookies.get('auth')
    if (savedAuth) {
      try {
        const { email: savedEmail, password: savedPassword } = JSON.parse(savedAuth)
        setEmail(savedEmail)
        setPassword(savedPassword)
        setIsRemember(true)
      } catch (error) {
        console.error('Error parsing saved auth:', error)
      }
    }
  }, [])

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email || !password) {
      toast.error('Please fill in all fields')
      return
    }

    // Save credentials if remember me is checked
    if (isRemember) {
      Cookies.set('auth', JSON.stringify({ 
        email, 
        password 
      }), { 
        expires: 1, // 1 day
        secure: true,
        sameSite: 'strict'
      })
    } else {
      Cookies.remove('auth')
    }

    try {
      const result = await login({
        email,
        password,
      }).unwrap()

      dispatch(loginSuccess({
        user: result.user,
        token: result.token,
        isEnableSupport: result.isEnableSupport,
      }))

      toast.success('Login successful!')
      navigate('/dashboard')
    } catch (error: any) {
      console.error('Login error:', error)
      
      // Handle specific error types
      if (error?.status === 'FETCH_ERROR') {
        if (error?.error?.toString().includes('ERR_NAME_NOT_RESOLVED')) {
          toast.error('Cannot connect to API server. DNS resolution failed.')
        } else if (error?.error?.toString().includes('CORS')) {
          toast.error('Connection blocked by CORS policy. Please check server configuration.')
        } else {
          toast.error('Network error. Please check your connection.')
        }
      }
      // Check for MongoDB and authentication issues
      else if (error?.data?.statusCode === 500 && 
               (error?.data?.message?.includes('MongoDB') || 
                error?.data?.message?.includes('database') ||
                error?.data?.message?.includes('connection'))) {
        toast.error('Database connection issue. Please try again in a few moments.')
        console.error('Database connection error:', error.data)
      } 
      // Handle specific auth errors
      else if (error?.data?.statusCode === 401 || error?.data?.message?.includes('credentials')) {
        toast.error('Invalid username or password. Please check your credentials.')
      }
      // User pending approval
      else if (error?.data?.message === "User is not able to login.") {
        setPendingModal(true)
      } else {
        // Safe error handling
        const errorMessage = error?.data?.message || "Login failed. Please check your credentials."
        toast.error(errorMessage)
      }
    }
  }

  return {
    email,
    password,
    isRemember,
    isLoading,
    pendingModal,
    setEmail,
    setPassword,
    setIsRemember,
    setPendingModal,
    handleLogin,
  }
} 