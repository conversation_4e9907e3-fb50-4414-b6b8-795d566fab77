import { useState } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { useRegisterMutation } from "../store/api/authApi";
import { useAppDispatch } from "../store/hooks";
import { loginSuccess } from "../store/slices/authSlice";
import type { RegisterRequest } from "../types/api";

interface RegisterData {
  email: string;
  corporateName: string;
  phone: string;
  password: string;
  confirmPassword: string;
}



export const useRegisterFunc = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [registerData, setRegisterData] = useState<RegisterData>({
    email: "",
    corporateName: "",
    phone: "",
    password: "",
    confirmPassword: "",
  });
  const [shareDataWithUs, setShareDataWithUs] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const [register, { isLoading, error }] = useRegisterMutation();

  const handleInputChange = (field: keyof RegisterData, value: string) => {
    setRegisterData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validation
    if (!registerData.email || !registerData.corporateName || !registerData.phone || !registerData.password || !registerData.confirmPassword) {
      toast.error("Please fill all fields");
      return;
    }

    if (registerData.password !== registerData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (registerData.password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(registerData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    // Phone validation (basic)
    if (registerData.phone.length < 10) {
      toast.error("Please enter a valid phone number");
      return;
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
    if (!passwordRegex.test(registerData.password)) {
      toast.error("Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character");
      return;
    }

    try {
      const registerRequest: RegisterRequest = {
        name: `HR Admin for ${registerData.corporateName.trim()}`, // HR admin personal name
        company_name: registerData.corporateName.trim(),           // Company name
        email: registerData.email.toLowerCase().trim(),
        phone: registerData.phone.trim(),
        password: registerData.password,
        confirmPassword: registerData.confirmPassword,
        is_share_data: shareDataWithUs,                           // Fixed field name
      };

      const result = await register(registerRequest).unwrap();
      
      console.log('✅ Registration successful:', result);
      
      // Use the message from backend response
      toast.success(result.message || "Registration successful!");
      
      // Show success modal
      setShowSuccessModal(true);
      
      // Navigate to login page after a short delay
      setTimeout(() => {
        setShowSuccessModal(false);
        navigate("/login");
      }, 3000);
      
    } catch (err: any) {
      console.error("Registration error:", err);
      
      // Handle specific error cases
      if (err?.status === 409) {
        toast.error("An account with this email already exists");
      } else if (err?.status === 422) {
        // Handle validation errors from backend
        const errors = err?.data?.errors;
        if (errors && Array.isArray(errors)) {
          errors.forEach((error: any) => {
            toast.error(error.message || "Validation error");
          });
        } else {
          toast.error(err?.data?.message || "Registration failed. Please check your information.");
        }
      } else if (err?.status === 429) {
        toast.error("Too many registration attempts. Please try again later.");
      } else {
        toast.error(err?.data?.message || "Registration failed. Please try again.");
      }
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    navigate("/");
  };

  return {
    registerData,
    shareDataWithUs,
    showSuccessModal,
    handleInputChange,
    setShareDataWithUs,
    handleSubmit,
    handleModalClose,
    isLoading,
    error,
  };
}; 