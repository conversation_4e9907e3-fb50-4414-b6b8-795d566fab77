import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Hub, ContentItem, WellnessArticle, LearningCategory, MediaHubFilters } from '../api/mediaHub'

export interface MediaHubState {
  // UI State
  selectedTab: 'media' | 'articles'
  selectedHub: Hub | null
  selectedVideo: ContentItem | null
  selectedArticle: WellnessArticle | null
  isVideoModalOpen: boolean
  isShareModalOpen: boolean
  viewMode: 'grid' | 'list'
  
  // Filters and Search
  searchTerm: string
  selectedCategory: string
  selectedDivision: string
  sortBy: 'newest' | 'popular' | 'duration' | 'rating'
  currentLanguage: 'en' | 'ar' | 'hi'
  
  // Data State
  hubs: Hub[]
  content: ContentItem[]
  articles: WellnessArticle[]
  categories: LearningCategory[]
  
  // User Interactions
  bookmarkedArticles: Set<string>
  likedArticles: Set<string>
  viewedArticles: Set<string>
  viewedVideos: Set<string>
  
  // Loading States
  loading: boolean
  contentLoading: boolean
  error: string | null
  
  // Pagination
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  
  // Copy success state
  copySuccess: boolean
}

export interface MediaHubActions {
  // UI Actions
  setSelectedTab: (tab: 'media' | 'articles') => void
  setSelectedHub: (hub: Hub | null) => void
  setSelectedVideo: (video: ContentItem | null) => void
  setSelectedArticle: (article: WellnessArticle | null) => void
  setVideoModalOpen: (open: boolean) => void
  setShareModalOpen: (open: boolean) => void
  setViewMode: (mode: 'grid' | 'list') => void
  
  // Filter Actions
  setSearchTerm: (term: string) => void
  setSelectedCategory: (category: string) => void
  setSelectedDivision: (division: string) => void
  setSortBy: (sortBy: 'newest' | 'popular' | 'duration' | 'rating') => void
  setCurrentLanguage: (language: 'en' | 'ar' | 'hi') => void
  clearFilters: () => void
  
  // Data Actions
  setHubs: (hubs: Hub[]) => void
  setContent: (content: ContentItem[]) => void
  setArticles: (articles: WellnessArticle[]) => void
  setCategories: (categories: LearningCategory[]) => void
  
  // User Interaction Actions
  toggleBookmark: (articleId: string) => void
  toggleLike: (articleId: string) => void
  markAsViewed: (id: string, type: 'article' | 'video') => void
  
  // Loading Actions
  setLoading: (loading: boolean) => void
  setContentLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Pagination Actions
  setCurrentPage: (page: number) => void
  setPagination: (data: { totalPages: number; hasNextPage: boolean }) => void
  
  // Utility Actions
  setCopySuccess: (success: boolean) => void
  resetState: () => void
  
  // Computed getters
  getFilteredArticles: () => WellnessArticle[]
  getFilteredContent: () => ContentItem[]
  getCurrentFilters: () => MediaHubFilters
}

const initialState: MediaHubState = {
  // UI State
  selectedTab: 'media',
  selectedHub: null,
  selectedVideo: null,
  selectedArticle: null,
  isVideoModalOpen: false,
  isShareModalOpen: false,
  viewMode: 'grid',
  
  // Filters and Search
  searchTerm: '',
  selectedCategory: 'all',
  selectedDivision: 'all',
  sortBy: 'newest',
  currentLanguage: 'en',
  
  // Data State
  hubs: [],
  content: [],
  articles: [],
  categories: [],
  
  // User Interactions
  bookmarkedArticles: new Set(),
  likedArticles: new Set(),
  viewedArticles: new Set(),
  viewedVideos: new Set(),
  
  // Loading States
  loading: false,
  contentLoading: false,
  error: null,
  
  // Pagination
  currentPage: 1,
  totalPages: 1,
  hasNextPage: false,
  
  // Copy success state
  copySuccess: false,
}

export const useMediaHubStore = create<MediaHubState & MediaHubActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // UI Actions
        setSelectedTab: (tab) => set({ selectedTab: tab }),
        setSelectedHub: (hub) => set({ selectedHub: hub }),
        setSelectedVideo: (video) => set({ selectedVideo: video }),
        setSelectedArticle: (article) => set({ selectedArticle: article }),
        setVideoModalOpen: (open) => set({ isVideoModalOpen: open }),
        setShareModalOpen: (open) => set({ isShareModalOpen: open }),
        setViewMode: (mode) => set({ viewMode: mode }),
        
        // Filter Actions
        setSearchTerm: (term) => set({ searchTerm: term, currentPage: 1 }),
        setSelectedCategory: (category) => set({ selectedCategory: category, currentPage: 1 }),
        setSelectedDivision: (division) => set({ selectedDivision: division, currentPage: 1 }),
        setSortBy: (sortBy) => set({ sortBy, currentPage: 1 }),
        setCurrentLanguage: (language) => set({ currentLanguage: language }),
        clearFilters: () => set({
          searchTerm: '',
          selectedCategory: 'all',
          selectedDivision: 'all',
          sortBy: 'newest',
          currentPage: 1,
        }),
        
        // Data Actions
        setHubs: (hubs) => set({ hubs }),
        setContent: (content) => set({ content }),
        setArticles: (articles) => set({ articles }),
        setCategories: (categories) => set({ categories }),
        
        // User Interaction Actions
        toggleBookmark: (articleId) => set((state) => {
          const newBookmarks = new Set(state.bookmarkedArticles)
          if (newBookmarks.has(articleId)) {
            newBookmarks.delete(articleId)
          } else {
            newBookmarks.add(articleId)
          }
          return { bookmarkedArticles: newBookmarks }
        }),
        
        toggleLike: (articleId) => set((state) => {
          const newLikes = new Set(state.likedArticles)
          if (newLikes.has(articleId)) {
            newLikes.delete(articleId)
          } else {
            newLikes.add(articleId)
          }
          return { likedArticles: newLikes }
        }),
        
        markAsViewed: (id, type) => set((state) => {
          if (type === 'article') {
            const newViewed = new Set(state.viewedArticles)
            newViewed.add(id)
            return { viewedArticles: newViewed }
          } else {
            const newViewed = new Set(state.viewedVideos)
            newViewed.add(id)
            return { viewedVideos: newViewed }
          }
        }),
        
        // Loading Actions
        setLoading: (loading) => set({ loading }),
        setContentLoading: (loading) => set({ contentLoading: loading }),
        setError: (error) => set({ error }),
        
        // Pagination Actions
        setCurrentPage: (page) => set({ currentPage: page }),
        setPagination: (data) => set({ 
          totalPages: data.totalPages, 
          hasNextPage: data.hasNextPage 
        }),
        
        // Utility Actions
        setCopySuccess: (success) => {
          set({ copySuccess: success })
          if (success) {
            setTimeout(() => set({ copySuccess: false }), 2000)
          }
        },
        
        resetState: () => set(initialState),
        
        // Computed getters
        getFilteredArticles: () => {
          const state = get()
          let filtered = [...(state.articles || [])]

          // Apply search filter
          if (state.searchTerm && filtered.length > 0) {
            const searchLower = state.searchTerm.toLowerCase()
            filtered = filtered.filter(article => {
              // Handle both new and old article formats
              const title = article.title?.[state.currentLanguage] ||
                           article.title?.en ||
                           (article as any).titleEN ||
                           ''
              const description = article.description?.[state.currentLanguage] ||
                                 article.description?.en ||
                                 (article as any).descriptionEN ||
                                 ''
              return title.toLowerCase().includes(searchLower) ||
                     description.toLowerCase().includes(searchLower)
            })
          }

          // Apply division filter
          if (state.selectedDivision !== 'all' && filtered.length > 0) {
            filtered = filtered.filter(article => article.category === state.selectedDivision)
          }

          // Apply sorting
          if (filtered.length > 0) {
            filtered.sort((a, b) => {
              switch (state.sortBy) {
                case 'popular':
                  return (b.views || 0) - (a.views || 0)
                case 'duration':
                  return (a.readTime || 0) - (b.readTime || 0)
                case 'rating':
                  return (b.likes || 0) - (a.likes || 0)
                case 'newest':
                default:
                  const aDate = new Date(b.published_date || b.created_at || b.id).getTime()
                  const bDate = new Date(a.published_date || a.created_at || a.id).getTime()
                  return aDate - bDate
              }
            })
          }

          return filtered
        },
        
        getFilteredContent: () => {
          const state = get()
          let filtered = [...(state.content || [])]

          // Apply search filter
          if (state.searchTerm && filtered.length > 0) {
            const searchLower = state.searchTerm.toLowerCase()
            filtered = filtered.filter(item => {
              const title = item.title?.[state.currentLanguage] || item.title?.en || ''
              const description = item.description?.[state.currentLanguage] || item.description?.en || ''
              return title.toLowerCase().includes(searchLower) ||
                     description.toLowerCase().includes(searchLower)
            })
          }

          // Apply category filter
          if (state.selectedCategory !== 'all' && filtered.length > 0) {
            filtered = filtered.filter(item => {
              const tags = item.tags?.[state.currentLanguage] || item.tags?.en || []
              return tags.includes(state.selectedCategory)
            })
          }

          return filtered
        },
        
        getCurrentFilters: () => {
          const state = get()
          return {
            search: state.searchTerm || undefined,
            category: state.selectedCategory !== 'all' ? state.selectedCategory : undefined,
            division: state.selectedDivision !== 'all' ? state.selectedDivision : undefined,
            sortBy: state.sortBy,
            language: state.currentLanguage,
            page: state.currentPage,
            limit: 20,
          }
        },
      }),
      {
        name: 'media-hub-storage',
        partialize: (state) => ({
          // Only persist user preferences and interactions
          viewMode: state.viewMode,
          currentLanguage: state.currentLanguage,
          bookmarkedArticles: Array.from(state.bookmarkedArticles),
          likedArticles: Array.from(state.likedArticles),
          viewedArticles: Array.from(state.viewedArticles),
          viewedVideos: Array.from(state.viewedVideos),
        }),
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Convert arrays back to Sets
            state.bookmarkedArticles = new Set(state.bookmarkedArticles as any)
            state.likedArticles = new Set(state.likedArticles as any)
            state.viewedArticles = new Set(state.viewedArticles as any)
            state.viewedVideos = new Set(state.viewedVideos as any)
          }
        },
      }
    ),
    {
      name: 'media-hub-store',
    }
  )
)
