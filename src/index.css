@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 0.625rem;
    
    /* Base colors */
    --background: 40 33% 98%;
    --foreground: 20 10% 10%;
    
    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 20 10% 10%;
    
    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 20 10% 10%;
    
    /* Primary colors - Gold */
    --primary: 40 50% 65%;  /* D2B37A */
    --primary-foreground: 0 0% 100%;
    
    /* Secondary colors - Darker Gold */
    --secondary: 39 54% 52%; /* C19648 */
    --secondary-foreground: 0 0% 100%;
    
    /* Muted colors */
    --muted: 40 30% 96%;
    --muted-foreground: 20 10% 40%;
    
    /* Accent colors */
    --accent: 40 40% 90%;
    --accent-foreground: 39 54% 52%;
    
    /* Destructive colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    /* Border, input, and ring colors */
    --border: 40 30% 85%;
    --input: 40 30% 85%;
    --ring: 40 50% 65%;
    
    /* Custom Sehatti colors */
    --sehatti-gold: 40 50% 60%;
    --sehatti-gold-dark: 40 45% 55%;
    --sehatti-gold-light: 40 55% 90%;
    --sehatti-warm-gray: 40 10% 95%;
    --sehatti-text-primary: 30 10% 20%;
    --sehatti-text-secondary: 40 15% 50%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 20 10% 10%;
    --foreground: 40 30% 96%;
    
    --card: 20 15% 15%;
    --card-foreground: 40 30% 96%;
    
    --popover: 20 15% 15%;
    --popover-foreground: 40 30% 96%;
    
    --primary: 40 50% 65%;
    --primary-foreground: 20 10% 10%;
    
    --secondary: 39 54% 52%;
    --secondary-foreground: 0 0% 100%;
    
    --muted: 20 15% 20%;
    --muted-foreground: 40 30% 70%;
    
    --accent: 20 15% 20%;
    --accent-foreground: 40 50% 65%;
    
    --destructive: 0 62% 45%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 40 30% 40%;
    --input: 40 30% 40%;
    --ring: 40 50% 65%;
    
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-sehatti-warm-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-sehatti-gold-400 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-sehatti-gold-600;
  }
  
  /* Focus outline */
  :focus-visible {
    @apply outline-none ring-2 ring-sehatti-gold-500 ring-offset-2 ring-offset-white;
  }
  
  .dark :focus-visible {
    @apply ring-offset-sehatti-warm-gray-950;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .sehatti-gradient {
    background: linear-gradient(135deg, hsl(var(--sehatti-gold)) 0%, hsl(var(--sehatti-gold-dark)) 100%);
  }
  
  .sehatti-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--sehatti-gold-light));
  }
  
  /* Glass effect */
  .glass {
    @apply backdrop-blur-md bg-white/70 dark:bg-sehatti-warm-gray-900/70 border border-white/20 dark:border-sehatti-warm-gray-800/30;
  }
  
  /* Gold gradient text */
  .gold-gradient-text {
    @apply bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400 bg-clip-text text-transparent;
  }
  
  /* Gold gradient background */
  .gold-gradient-bg {
    @apply bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400;
  }
  
  /* Gold glow effect */
  .gold-glow {
    @apply shadow-gold-glow;
  }
  
  /* Shimmer effect */
  .shimmer {
    @apply bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:200%_100%] animate-shimmer;
  }

  /* Hide scrollbars while maintaining functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;     /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;             /* Safari and Chrome */
  }
  
  /* Touch optimization */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Safe area padding for mobile devices */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .pt-safe {
    padding-top: env(safe-area-inset-top);
  }
  
  /* Mobile-specific spacing */
  .mobile-padding {
    @apply px-3 py-4;
  }
  
  @screen sm {
    .mobile-padding {
      @apply px-4 py-6;
    }
  }
  
  @screen lg {
    .mobile-padding {
      @apply px-6 py-8;
    }
  }
  
  /* Improved touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Mobile-friendly text sizes */
  .mobile-text {
    @apply text-sm;
  }
  
  @screen sm {
    .mobile-text {
      @apply text-base;
    }
  }
  
  /* Mobile card optimizations */
  .mobile-card {
    @apply p-4;
  }
  
  @screen sm {
    .mobile-card {
      @apply p-5;
    }
  }
  
  @screen lg {
    .mobile-card {
      @apply p-6;
    }
  }
  
  /* Responsive grid gaps */
  .mobile-gap {
    @apply gap-3;
  }
  
  @screen sm {
    .mobile-gap {
      @apply gap-4;
    }
  }
  
  @screen lg {
    .mobile-gap {
      @apply gap-6;
    }
  }
  
  /* Mobile-first button sizing */
  .mobile-button {
    @apply min-h-[3rem] text-sm font-medium;
  }
  
  @screen sm {
    .mobile-button {
      @apply text-base;
    }
  }
  
  /* Smooth scrolling for mobile */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Mobile-optimized badge sizing */
  .mobile-badge {
    @apply text-xs px-2 py-1;
  }
  
  @screen sm {
    .mobile-badge {
      @apply px-3 py-1;
    }
  }
}

/* Component styles */
@layer components {
  /* Card styles */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-muted-foreground;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Form styles */
  .form-label {
    @apply block text-sm font-medium text-foreground mb-2;
  }
  
  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-default {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90;
  }
  
  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }
  
  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .badge-default {
    @apply bg-primary text-primary-foreground hover:bg-primary/80;
  }
  
  .badge-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .badge-outline {
    @apply text-foreground;
  }
  
  .badge-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* RTL Support for Arabic language */
.rtl, [dir="rtl"] {
  direction: rtl !important;
}

.ltr, [dir="ltr"] {
  direction: ltr !important;
}

/* Force RTL on all child elements */
[dir="rtl"] * {
  direction: inherit;
}

/* Ensure text alignment follows direction */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* RTL-specific adjustments */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse;
}

/* Arabic font improvements */
[dir="rtl"] {
  font-family: 'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', system-ui, -apple-system, sans-serif;
}

/* Hindi font improvements */
.hindi-text {
  font-family: 'Noto Sans Devanagari', 'Mangal', 'Kruti Dev', system-ui, -apple-system, sans-serif;
}

/* Ensure proper text alignment for RTL */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL-specific margin and padding adjustments */
[dir="rtl"] .mr-1 {
  margin-right: 0;
  margin-left: 0.25rem;
}

[dir="rtl"] .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL-specific padding adjustments */
[dir="rtl"] .pr-1 {
  padding-right: 0;
  padding-left: 0.25rem;
}

[dir="rtl"] .pl-1 {
  padding-left: 0;
  padding-right: 0.25rem;
}

[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}

[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}

[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

/* RTL-specific border radius adjustments */
[dir="rtl"] .rounded-l {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .rounded-r {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* RTL-specific transform adjustments for icons */
[dir="rtl"] .transform-rtl {
  transform: scaleX(-1);
}

/* Ensure proper line height for Arabic text */
[dir="rtl"] {
  line-height: 1.8;
}

/* Improve readability for Arabic text */
[dir="rtl"] p, [dir="rtl"] span, [dir="rtl"] div {
  text-align: right;
  letter-spacing: 0.02em;
}

/* Hindi text improvements */
.hindi-text p, .hindi-text span, .hindi-text div {
  line-height: 1.7;
  letter-spacing: 0.01em;
}

/* Language-specific adjustments */
.lang-ar {
  font-size: 1.05em;
  line-height: 1.8;
}

.lang-hi {
  font-size: 1.02em;
  line-height: 1.7;
}

/* Ensure proper button alignment in RTL */
[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}

/* RTL-specific table adjustments */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th, [dir="rtl"] td {
  text-align: right;
}

/* RTL-specific form adjustments */
[dir="rtl"] .form-group {
  text-align: right;
}

[dir="rtl"] input, [dir="rtl"] textarea, [dir="rtl"] select {
  text-align: right;
}

/* Ensure proper badge alignment in RTL */
[dir="rtl"] .badge {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* RTL-specific navigation adjustments */
[dir="rtl"] .nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

[dir="rtl"] .nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0.25rem;
}

/* Smooth transitions for language switching */
.language-transition {
  transition: all 0.3s ease-in-out;
}

/* Ensure proper icon alignment in RTL */
[dir="rtl"] .icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* RTL-specific dropdown adjustments */
[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

/* Ensure proper grid alignment in RTL */
[dir="rtl"] .grid-rtl {
  direction: rtl;
}

/* RTL-specific card adjustments */
[dir="rtl"] .card {
  text-align: right;
}

[dir="rtl"] .card-header {
  text-align: right;
}

[dir="rtl"] .card-body {
  text-align: right;
}

/* Ensure proper progress bar direction in RTL */
[dir="rtl"] .progress {
  direction: ltr;
}

[dir="rtl"] .progress-bar {
  direction: ltr;
}

/* RTL-specific modal adjustments */
[dir="rtl"] .modal-header {
  text-align: right;
}

[dir="rtl"] .modal-body {
  text-align: right;
}

[dir="rtl"] .modal-footer {
  justify-content: flex-start;
}

/* Ensure proper tooltip positioning in RTL */
[dir="rtl"] .tooltip {
  direction: ltr;
}

/* RTL-specific alert adjustments */
[dir="rtl"] .alert {
  text-align: right;
}

/* Ensure proper checkbox and radio alignment in RTL */
[dir="rtl"] .form-check {
  text-align: right;
}

[dir="rtl"] .form-check-input {
  margin-left: 0;
  margin-right: -1.25rem;
}

[dir="rtl"] .form-check-label {
  padding-left: 0;
  padding-right: 1.25rem;
}

/* RTL-specific breadcrumb adjustments */
[dir="rtl"] .breadcrumb {
  direction: rtl;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  content: "\\";
  transform: scaleX(-1);
}

/* Ensure proper scrollbar direction in RTL */
[dir="rtl"] ::-webkit-scrollbar {
  width: 8px;
}

[dir="rtl"] ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

[dir="rtl"] ::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

[dir="rtl"] ::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Additional RTL utilities for better language switching */
.rtl-flip {
  transform: scaleX(-1);
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(1);
}

/* Force immediate direction changes */
.force-rtl {
  direction: rtl !important;
  text-align: right !important;
}

.force-ltr {
  direction: ltr !important;
  text-align: left !important;
}

/* Enhanced flex direction utilities for RTL */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

/* Grid and layout utilities for better RTL support */
[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

[dir="rtl"] .items-start {
  align-items: flex-end;
}

[dir="rtl"] .items-end {
  align-items: flex-start;
}

/* Text alignment utilities */
[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

/* Ensure proper float behavior in RTL */
[dir="rtl"] .float-left {
  float: right;
}

[dir="rtl"] .float-right {
  float: left;
}

/* Language transition effects */
.language-switching {
  transition: direction 0.3s ease, text-align 0.3s ease;
}
