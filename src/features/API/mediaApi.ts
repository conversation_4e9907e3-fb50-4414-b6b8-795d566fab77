import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getMediaServiceUrl } from '../../config/api';

// Types for media service
export interface MediaContent {
  id: string;
  title: string;
  description?: string;
  content_type: 'video' | 'audio' | 'document' | 'image' | 'article';
  file_url?: string;
  thumbnail_url?: string;
  duration_seconds?: number;
  file_size_bytes?: number;
  language: 'en' | 'ar' | 'hi';
  tags: string[];
  category: string;
  corporate_id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  view_count: number;
  like_count: number;
  metadata?: Record<string, any>;
}

// Media service response types (matching backend structure)
export interface MediaServiceContentItem {
  content_id: string;
  hub_id: string;
  title: Record<string, string>; // {"en": "title", "ar": "عنوان", "hi": "शीर्षक"}
  description: Record<string, string>; // {"en": "desc", "ar": "وصف", "hi": "विवरण"}
  tags: Record<string, string[]>; // {"en": ["tag1"], "ar": ["علامة1"], "hi": ["टैग1"]}
  video_id: string;
  thumbnail_id: string;
  video_url: string;
  thumbnail_url: string;
  subtitle_urls: Record<string, string>; // {"en": "url", "ar": "url", "hi": "url"}
  subtitles_available: string[];
  available_languages: string[];
  gender: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface MediaFilters {
  corporate_id?: string;
  is_active?: boolean;
  limit?: number;
  search?: string;
  language?: string;
  content_type?: string;
  category?: string;
  offset?: string;
  last_key?: string;
}

export interface MediaServiceResponse {
  data: MediaContent[];
  total?: number;
  last_key?: string;
  has_more?: boolean;
}

export interface MediaUploadRequest {
  title: string;
  description?: string;
  content_type: 'video' | 'audio' | 'document' | 'image' | 'article';
  language: 'en' | 'ar' | 'hi';
  tags: string[];
  category: string;
  corporate_id: string;
  file?: File;
  content_text?: string; // For articles
}

export interface MediaAnalytics {
  media_id: string;
  employee_id: string;
  action: 'view' | 'like' | 'share' | 'download';
  timestamp: string;
  duration_watched?: number;
  metadata?: Record<string, any>;
}

export interface StreamingUrlResponse {
  url: string;
  expires_at: string;
  content_type: string;
}

export interface UploadResponse {
  media_id: string;
  file_url: string;
  thumbnail_url?: string;
  status: 'uploaded' | 'processing' | 'ready' | 'error';
}

const mediaServiceAPI = createApi({
  reducerPath: 'mediaServiceAPI',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getMediaServiceUrl()}/api/media/v1`,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem('token');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['MediaContent', 'MediaAnalytics'],
  endpoints: (builder) => ({
    // Get media content with filters
    getMediaContent: builder.query<MediaServiceResponse, MediaFilters>({
      queryFn: async (filters, { getState }, extraOptions, baseQuery) => {
        try {
          // Build parameters for the media service
          const params: any = {};
          
          // Content is for all users, no corporate_id/hub_id needed
          // Only pass relevant parameters that the backend expects
          if (filters.limit) params.limit = filters.limit;
          if (filters.search) params.search = filters.search;
          
          // Map is_active to status if provided
          if (filters.is_active !== undefined) {
            params.status = filters.is_active ? 'active' : 'inactive';
          }
          
          // For pagination, use last_key instead of offset
          if (filters.offset) params.last_key = filters.offset;
          
          const result = await baseQuery({
            url: '/media/content',
            method: 'GET',
            params,
          });
          
          if (result.data) {
            // Handle different response formats from the media service
            const responseData = result.data as any;
            
            // If the response has a 'data' property, use it
            if (responseData.data && Array.isArray(responseData.data)) {
              return { 
                data: {
                  data: responseData.data,
                  total: responseData.total || responseData.data.length,
                  last_key: responseData.last_key,
                  has_more: responseData.has_more || false
                }
              };
            }
            
            // If the response is directly an array
            if (Array.isArray(responseData)) {
              return { 
                data: {
                  data: responseData,
                  total: responseData.length,
                  has_more: false
                }
              };
            }
            
            // If the response has content or items property
            if (responseData.content && Array.isArray(responseData.content)) {
              return { 
                data: {
                  data: responseData.content,
                  total: responseData.total || responseData.content.length,
                  last_key: responseData.last_key,
                  has_more: responseData.has_more || false
                }
              };
            }
            
            // Fallback: return empty array
            return { 
              data: {
                data: [],
                total: 0,
                has_more: false
              }
            };
          }
          
          return { 
            data: {
              data: [],
              total: 0,
              has_more: false
            }
          };
        } catch (error) {
          console.warn('Media service not available, using mock data');
          // Return mock data when service is unavailable
          return {
            data: {
              data: [
                {
                  id: 'mock-1',
                  title: 'Professional Development Video',
                  description: 'Learn essential skills for career growth',
                  content_type: 'video' as const,
                  file_url: '/mock-video-1.mp4',
                  thumbnail_url: '/mock-thumb-1.jpg',
                  duration_seconds: 1800,
                  language: 'en' as const,
                  tags: ['professional', 'development'],
                  category: 'training',
                  corporate_id: filters.corporate_id || 'mock-corp',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  is_active: true,
                  view_count: 150,
                  like_count: 25
                },
                {
                  id: 'mock-2',
                  title: 'Leadership Training',
                  description: 'Develop your leadership capabilities',
                  content_type: 'video' as const,
                  file_url: '/mock-video-2.mp4',
                  thumbnail_url: '/mock-thumb-2.jpg',
                  duration_seconds: 2400,
                  language: 'en' as const,
                  tags: ['leadership', 'management'],
                  category: 'training',
                  corporate_id: filters.corporate_id || 'mock-corp',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  is_active: true,
                  view_count: 200,
                  like_count: 40
                }
              ],
              total: 2,
              has_more: false
            }
          };
        }
      },
      providesTags: ['MediaContent'],
    }),

    // Get media content by ID
    getMediaById: builder.query<MediaContent, string>({
      query: (id) => ({
        url: `/media/${id}`,
        method: 'GET',
      }),
      providesTags: ['MediaContent'],
    }),

    // Get content for specific employee (personalized)
    getEmployeeContent: builder.query<{ data: MediaContent[]; total: number }, {
      employee_id: string;
      corporate_id: string;
      language?: string;
      limit?: number;
    }>({
      query: (params) => ({
        url: '/media/employee-content',
        method: 'GET',
        params,
      }),
      providesTags: ['MediaContent'],
    }),

    // Upload new media content
    uploadMedia: builder.mutation<UploadResponse, FormData>({
      query: (formData) => ({
        url: '/media/upload',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['MediaContent'],
    }),

    // Update media content
    updateMedia: builder.mutation<MediaContent, { id: string; data: Partial<MediaContent> }>({
      query: ({ id, data }) => ({
        url: `/media/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['MediaContent'],
    }),

    // Delete media content
    deleteMedia: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/media/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['MediaContent'],
    }),

    // Record media analytics (views, likes, etc.)
    recordMediaAnalytics: builder.mutation<{ success: boolean }, MediaAnalytics>({
      query: (analytics) => ({
        url: '/media/analytics',
        method: 'POST',
        body: analytics,
      }),
      invalidatesTags: ['MediaAnalytics'],
    }),

    // Get media analytics
    getMediaAnalytics: builder.query<{ data: MediaAnalytics[]; total: number }, {
      media_id?: string;
      employee_id?: string;
      corporate_id?: string;
      start_date?: string;
      end_date?: string;
      limit?: number;
    }>({
      query: (params) => ({
        url: '/media/analytics',
        method: 'GET',
        params,
      }),
      providesTags: ['MediaAnalytics'],
    }),

    // Get streaming URL for protected content
    getStreamingUrl: builder.query<StreamingUrlResponse, { media_id: string; quality?: string }>({
      query: ({ media_id, quality }) => ({
        url: `/media/${media_id}/stream`,
        method: 'GET',
        params: quality ? { quality } : {},
      }),
    }),

    // Search media content
    searchMediaContent: builder.query<{ data: MediaContent[]; total: number }, {
      query: string;
      corporate_id?: string;
      language?: string;
      content_type?: string;
      limit?: number;
    }>({
      query: (params) => ({
        url: '/media/search',
        method: 'GET',
        params,
      }),
      providesTags: ['MediaContent'],
    }),

    // Get popular content
    getPopularContent: builder.query<{ data: MediaContent[]; total: number }, {
      corporate_id?: string;
      language?: string;
      time_period?: 'day' | 'week' | 'month';
      limit?: number;
    }>({
      query: (params) => ({
        url: '/media/popular',
        method: 'GET',
        params,
      }),
      providesTags: ['MediaContent'],
    }),

    // Get recommendations for employee
    getRecommendedContent: builder.query<{ data: MediaContent[]; total: number }, {
      employee_id: string;
      corporate_id: string;
      language?: string;
      limit?: number;
    }>({
      query: (params) => ({
        url: '/media/recommendations',
        method: 'GET',
        params,
      }),
      providesTags: ['MediaContent'],
    }),

    // Health check
    mediaHealthCheck: builder.query<{ status: string; timestamp: string }, void>({
      query: () => '/api/media/health',
    }),
  }),
});

export const {
  useGetMediaContentQuery,
  useGetMediaByIdQuery,
  useGetEmployeeContentQuery,
  useUploadMediaMutation,
  useUpdateMediaMutation,
  useDeleteMediaMutation,
  useRecordMediaAnalyticsMutation,
  useGetMediaAnalyticsQuery,
  useGetStreamingUrlQuery,
  useLazyGetStreamingUrlQuery,
  useSearchMediaContentQuery,
  useGetPopularContentQuery,
  useGetRecommendedContentQuery,
  useMediaHealthCheckQuery,
} = mediaServiceAPI;

export default mediaServiceAPI; 