import { baseApi } from "../../services/api";

const ArticleApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getArticles: builder.query({
      query: (params) => ({
        url: "/articles",
        method: "GET",
        params: {
          limit: params?.limit || 20,
          last_key: params?.last_key,
          category: params?.category,
          status: params?.status,
          author_id: params?.author_id,
          target_id: params?.target_id,
        },
      }),
      providesTags: ["Article"],
    }),

    getSingleArticle: builder.query({
      query: (id) => ({
        url: `/articles/${id}`,
        method: "GET",
        params: {
          language: "en",
        },
      }),
      providesTags: ["Article"],
    }),

    createArticle: builder.mutation({
      query: (data) => {
        const formData = new FormData();
        
        formData.append("title", data.title);
        formData.append("description", data.description);
        formData.append("source_division_id", data.source_division_id);
        formData.append("target_id", data.target_id);
        
        if (data.category) formData.append("category", data.category);
        if (data.tags && Array.isArray(data.tags)) {
          formData.append("tags", data.tags.join(","));
        }
        if (data.author_id) formData.append("author_id", data.author_id);
        if (data.status) formData.append("status", data.status);
        
        if (data.thumbnail_file_id) {
          formData.append("thumbnail_file_id", data.thumbnail_file_id);
        } else if (data.thumbnail) {
          formData.append("thumbnail", data.thumbnail);
        }

        return {
          url: "/articles",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Article"],
    }),

    updateArticle: builder.mutation({
      query: ({ id, formData }) => ({
        url: `/articles/${id}`,
        method: "PATCH",
        body: formData,
      }),
      invalidatesTags: ["Article"],
    }),

    getTotalUsefulArticles: builder.query({
      query: () => ({
        url: "/likes/article-count",
        method: "GET",
      }),
      providesTags: ["adminDashboard"],
    }),

    deleteArticle: builder.mutation({
      query: (id) => ({
        url: `/articles/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Article"],
    }),
    
    getSurveySummary: builder.query({
      query: ({ userId }) => ({
        url: `/hr-admins/${userId}/survey-summary`,
        method: "GET",
      }),
      providesTags: ["SurveySummary"],
    }),
    
    getArticleForAdmin: builder.query({
      query: (params) => ({
        url: "/articles",
        method: "GET",
        params: {
          limit: params?.limit || 20,
          last_key: params?.last_key,
          category: params?.category,
          status: params?.status,
          author_id: params?.author_id,
          target_id: params?.target_id,
        },
      }),
      providesTags: ["Article"],
    }),

    uploadArticleThumbnail: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append("file", file);
        
        return {
          url: "/articles/upload-thumbnail",
          method: "POST",
          body: formData,
        };
      },
    }),

    publishArticle: builder.mutation({
      query: (id) => ({
        url: `/articles/${id}/publish`,
        method: "POST",
      }),
      invalidatesTags: ["Article"],
    }),

    retranslateArticle: builder.mutation({
      query: ({ id, source_language = "en", target_languages = "ar,hi" }) => {
        const formData = new FormData();
        formData.append("source_language", source_language);
        formData.append("target_languages", target_languages);
        
        return {
          url: `/articles/${id}/retranslate`,
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Article"],
    }),
  }),
});

export const {
  useGetArticlesQuery,
  useLazyGetArticlesQuery,
  useGetSingleArticleQuery,
  useCreateArticleMutation,
  useUpdateArticleMutation,
  useDeleteArticleMutation,
  useGetTotalUsefulArticlesQuery,
  useGetSurveySummaryQuery,
  useGetArticleForAdminQuery,
  useUploadArticleThumbnailMutation,
  usePublishArticleMutation,
  useRetranslateArticleMutation,
} = ArticleApi;
