import { mediaServiceApi } from "../../store/api/baseApi";

// Types for the new multilingual content API
export interface ContentListItem {
  content_id: string;
  hub_id: string;
  title: Record<string, string>; // {"en": "title", "ar": "عنوان", "hi": "शीर्षक"}
  description: Record<string, string>; // {"en": "desc", "ar": "وصف", "hi": "विवरण"}
  tags: Record<string, string[]>; // {"en": ["tag1"], "ar": ["علامة1"], "hi": ["टैग1"]}
  video_id: string;
  thumbnail_id: string;
  video_url: string;
  thumbnail_url: string;
  subtitle_urls: Record<string, string>; // {"en": "url", "ar": "url", "hi": "url"}
  subtitles_available: string[]; // List of available subtitle languages
  available_languages: string[]; // List of available content languages
  gender: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ContentListResponse {
  data: ContentListItem[];
  total: number;
  limit: number;
  last_key?: string;
  has_more: boolean;
}

// Create Hub API using the media service API
export const hubApi = mediaServiceApi.injectEndpoints({
  endpoints: (builder) => ({
    addHub: builder.mutation({
      query: (data) => {
        // Create FormData for multipart upload
        const formData = new FormData();
        formData.append('title', data.title);
        if (data.description) formData.append('description', data.description);
        if (data.category) formData.append('category', data.category);
        if (data.tags && data.tags.length > 0) formData.append('tags', data.tags.join(','));
        if (data.visibility) formData.append('visibility', data.visibility);
        if (data.created_by) formData.append('created_by', data.created_by);
        if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString());
        if (data.image) formData.append('image', data.image);

        return {
          url: "/v1/hubs",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Hub"],
    }),

    // Upload File
    uploadFile: builder.mutation({
      query: ({ file, folder = "media", autoCaption = true, languages = "en-US" }) => {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("auto_caption", String(autoCaption));
        formData.append("languages", languages);
        
        return {
          url: "/v1/videos/upload",
          method: "POST",
          body: formData
        };
      }
    }),

    // Upload Thumbnail
    uploadThumbnail: builder.mutation({
      query: ({ file }) => {
        const formData = new FormData();
        formData.append("file", file);
        
        return {
          url: "/v1/videos/thumbnail/upload",
          method: "POST",
          body: formData
        };
      }
    }),

    // Create multilingual content
    createMultilingualContent: builder.mutation({
      query: (data) => {
        const formData = new FormData();
        formData.append('hub_id', data.hub_id);
        formData.append('title', data.title);
        formData.append('description', data.description);
        if (data.source_language) formData.append('source_language', data.source_language);
        if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString());
        if (data.generate_captions !== undefined) formData.append('generate_captions', data.generate_captions.toString());
        if (data.gender) formData.append('gender', data.gender);
        if (data.tags) formData.append('tags', data.tags);
        formData.append('video_file_id', data.video_file_id);
        formData.append('thumbnail_file_id', data.thumbnail_file_id);

        return {
          url: "/v1/media/content",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Media", "HubContent"],
    }),

    // List multilingual content
    listContent: builder.query<ContentListResponse, {
      hub_id?: string;
      status?: string;
      limit?: number;
      last_key?: string;
      search?: string;
    }>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.hub_id) searchParams.append('hub_id', params.hub_id);
        if (params.status) searchParams.append('status', params.status);
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.last_key) searchParams.append('last_key', params.last_key);
        if (params.search) searchParams.append('search', params.search);

        return {
          url: `/v1/media/content?${searchParams.toString()}`,
          method: "GET"
        };
      },
      providesTags: ["Media", "HubContent"],
    }),

    // Get content by ID
    getContentById: builder.query<ContentListItem, string>({
      query: (contentId: string) => ({
        url: `/v1/media/content/${contentId}`,
        method: "GET"
      }),
      providesTags: ["Media", "HubContent"],
    }),

    // Delete content
    deleteContent: builder.mutation({
      query: (contentId: string) => ({
        url: `/v1/media/content/${contentId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Media", "HubContent"],
    }),

    getHubById: builder.query({
      query: (id) => `/v1/hubs/${id}?include_content_count=true`,
      providesTags: ["Hub"],
    }),

    getHubs: builder.query({
      query: (params) => {
        // If params is null, empty, or undefined, don't add query parameters
        if (params === null || !params || Object.keys(params).length === 0) {
          return {
            url: "/v1/hubs",
            method: "GET"
          };
        }
        
        // Otherwise include the params
        return {
          url: "/v1/hubs",
          method: "GET",
          params
        };
      },
      providesTags: ["Hub"],
    }),

    // Delete Hub
    deleteHub: builder.mutation({
      query: (id) => ({
        url: `/v1/hubs/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Hub"],
    }),

    // Update Hub
    updateHub: builder.mutation({
      query: ({ id, data, language = 'en' }) => {
        // Create FormData for multipart upload
        const formData = new FormData();
        // REQUIRED: language parameter for PATCH operations
        formData.append('language', language);
        if (data.title) formData.append('title', data.title);
        if (data.description !== undefined) formData.append('description', data.description);
        if (data.category) formData.append('category', data.category);
        if (data.tags && data.tags.length > 0) formData.append('tags', data.tags.join(','));
        if (data.visibility) formData.append('visibility', data.visibility);
        if (data.status) formData.append('hub_status', data.status);
        if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString());
        if (data.image) formData.append('image', data.image);

        return {
          url: `/v1/hubs/${id}`,
          method: "PATCH",
          body: formData,
        };
      },
      invalidatesTags: ["Hub"],
    }),

    // Get hub in specific language
    getHubLocalized: builder.query({
      query: ({ hubId, language, includeContentCount = true }) => 
        `/v1/hubs/${hubId}/localized/${language}?include_content_count=${includeContentCount}`,
      providesTags: ["Hub"],
    }),

    // Trigger manual translation
    translateHub: builder.mutation({
      query: ({ hubId, sourceLanguage = 'en', targetLanguages = ['ar', 'hi'] }) => {
        const formData = new FormData();
        formData.append('source_language', sourceLanguage);
        formData.append('target_languages', targetLanguages.join(','));

        return {
          url: `/v1/hubs/${hubId}/translate`,
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Hub"],
    }),

    // Get hub statistics
    getHubStats: builder.query({
      query: (hubId) => `/v1/hubs/${hubId}/stats`,
      providesTags: ["Hub"],
    }),

    // Change hub status
    changeHubStatus: builder.mutation({
      query: ({ hubId, newStatus, reason }) => {
        const formData = new FormData();
        formData.append('new_status', newStatus);
        if (reason) formData.append('reason', reason);

        return {
          url: `/v1/hubs/${hubId}/status`,
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Hub"],
    }),
  }),
});

export const {
  useAddHubMutation,
  useGetHubsQuery,
  useDeleteHubMutation,
  useUpdateHubMutation,
  useGetHubByIdQuery,
  useGetHubLocalizedQuery,
  useTranslateHubMutation,
  useGetHubStatsQuery,
  useChangeHubStatusMutation,
  useUploadFileMutation,
  useUploadThumbnailMutation,
  useCreateMultilingualContentMutation,
  useListContentQuery,
  useDeleteContentMutation,
  useGetContentByIdQuery,
} = hubApi;
