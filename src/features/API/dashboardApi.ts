import { API } from './API'

// Mock data for system admin dashboard (temporary solution to unblock frontend)
const MOCK_SYSTEM_ADMIN_DATA = [
  {
    status: "ACTIVE",
    count: 5,
    type: "companies",
    label: "Active Companies"
  },
  {
    status: "PENDING", 
    count: 2,
    type: "companies",
    label: "Pending Companies"
  },
  {
    status: "ACTIVE",
    count: 12,
    type: "hr_admins", 
    label: "HR Administrators"
  },
  {
    status: "PENDING",
    count: 3,
    type: "hr_admins",
    label: "Pending HR Admins"
  }
];

// Legacy Backend Dashboard API
export const dashboardApi = API.injectEndpoints({
  endpoints: (builder) => ({
    // System Admin Dashboard - with mock data fallback
    getSystemAdminDashboard: builder.query<any[], void>({
      query: () => '/dashboard/system-admin',
      providesTags: ['Dashboard'],
      // Transform response to handle timeout/error with mock data
      transformResponse: (response: any) => {
        console.log('✅ Dashboard API: Real data received', response);
        return response;
      },
      transformErrorResponse: (error: any) => {
        console.warn('⚠️ Dashboard API: Using mock data due to error:', error);
        // Return mock data instead of error
        return MOCK_SYSTEM_ADMIN_DATA;
      },
      // Override query function to implement timeout with fallback
      queryFn: async (arg, api, extraOptions, baseQuery) => {
        console.log('🔄 Dashboard API: Attempting to fetch real data...');
        
        // Create a promise that resolves with mock data after 5 seconds
        const mockDataPromise = new Promise((resolve) => {
          setTimeout(() => {
            console.log('⏰ Dashboard API: Timeout reached, using mock data');
            resolve({ data: MOCK_SYSTEM_ADMIN_DATA });
          }, 5000); // 5 second timeout
        });
        
        // Create the actual API call promise
        const apiCallPromise = baseQuery('/dashboard/system-admin');
        
        try {
          // Race between API call and timeout
          const result = await Promise.race([apiCallPromise, mockDataPromise]);
          return result;
        } catch (error) {
          console.warn('❌ Dashboard API: Error occurred, using mock data:', error);
          return { data: MOCK_SYSTEM_ADMIN_DATA };
        }
      },
    }),
    
    // HR Admin Dashboard
    getHrAdminDashboard: builder.query<any[], { companyId: string }>({
      query: ({ companyId }) => `/dashboard/hr-admin/${companyId}`,
      providesTags: ['Dashboard'],
    }),
    
    // HR Admin Question Count
    getHrAdminQuestionCount: builder.query<any[], { companyId: string }>({
      query: ({ companyId }) => `/dashboard/hr-admin/${companyId}/question-count`,
      providesTags: ['Dashboard'],
    }),
    
    // Company Settings (single company)
    getCompanySettings: builder.query<any, { companyId: string }>({
      query: ({ companyId }) => `/company-settings/${companyId}`,
      providesTags: ['Company'],
    }),
    
    // Company with Consultants (single company)
    getCompanyWithConsultants: builder.query<any, { companyId: string }>({
      query: ({ companyId }) => `/company-settings/${companyId}/consultants`,
      providesTags: ['Company'],
    }),
    
    // All Companies with Consultants (for dashboard) - using system-admin endpoint
    getCompaniesWithConsultants: builder.query<any[], void>({
      query: () => '/system-admins/companies',
      providesTags: ['Company'],
    }),
  }),
})

export const {
  useGetSystemAdminDashboardQuery,
  useGetHrAdminDashboardQuery,
  useGetHrAdminQuestionCountQuery,
  useGetCompanySettingsQuery,
  useGetCompanyWithConsultantsQuery,
  useGetCompaniesWithConsultantsQuery,
} = dashboardApi 