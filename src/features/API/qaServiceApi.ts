import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { getQaServiceUrl } from "../../config/api";
import type {
  Division,
  Target,
  Question,
  Corporate,
  PaginatedResponse,
  LegacyCorporateResponse,
  DivisionQueryParams,
  TargetQueryParams,
  QuestionQueryParams,
  CreateDivisionRequest,
  UpdateDivisionRequest,
  CreateTargetRequest,
  UpdateTargetRequest,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionTypeValue,
  QuestionStatusValue,
} from "../../types/qa";

// Mock data for QA service (temporary solution to unblock frontend)
const MOCK_DIVISIONS_DATA = [
  {
    id: "div_1",
    name: "Human Resources",
    description: "HR related assessments and questions",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "div_2", 
    name: "Finance & Accounting",
    description: "Financial compliance and accounting assessments",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "div_3",
    name: "Operations",
    description: "Operational efficiency and process assessments", 
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

const MOCK_QUESTIONS_DATA = [
  {
    id: "q_1",
    text: "What is your company's employee satisfaction rate?",
    type: "multiple_choice",
    status: "ACTIVE",
    division_id: "div_1",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "q_2",
    text: "How often do you conduct financial audits?",
    type: "multiple_choice", 
    status: "ACTIVE",
    division_id: "div_2",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "q_3",
    text: "What is your average project completion time?",
    type: "text",
    status: "ACTIVE", 
    division_id: "div_3",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// QA Service API - Aligned with sehatti-qa-service backend
export const qaServiceApi = createApi({
  reducerPath: "qaServiceApi",
  baseQuery: fetchBaseQuery({
    baseUrl: getQaServiceUrl() + "/api/qa/v1",
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("hr-auth-token");
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      headers.set("X-Requested-With", "XMLHttpRequest");
      return headers;
    },
    credentials: 'omit',
  }),
  tagTypes: ['Division', 'Target', 'Question', 'Corporate'],
  endpoints: (builder) => ({
    // Legacy Corporate Management (external system)
    getLegacyCorporates: builder.query<LegacyCorporateResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 100 }) => `/legacy/corporates?page=${page}&limit=${limit}`,
      providesTags: ['Corporate'],
    }),

    syncCorporateToQA: builder.mutation<any, { corporateId: string }>({
      query: ({ corporateId }) => ({
        url: `/legacy/corporates/${corporateId}/sync`,
        method: 'POST',
      }),
      invalidatesTags: ['Corporate'],
    }),

    deleteLegacyCorporate: builder.mutation<any, string>({
      query: (corporateId) => ({
        url: `/legacy/corporates/${corporateId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Corporate'],
    }),

    // Divisions CRUD Operations - with mock data fallback
    getDivisions: builder.query<Division[], DivisionQueryParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        Object.entries(params || {}).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        return `/divisions?${searchParams.toString()}`;
      },
      transformResponse: (response: Division[]) => response,
      providesTags: ['Division'],
      // Add timeout with mock data fallback
      queryFn: async (arg, api, extraOptions, baseQuery) => {
        console.log('🔄 QA Service: Attempting to fetch divisions...');
        
        const mockDataPromise = new Promise((resolve) => {
          setTimeout(() => {
            console.log('⏰ QA Service: Timeout reached, using mock divisions data');
            resolve({ data: MOCK_DIVISIONS_DATA });
          }, 5000);
        });
        
        const params = arg || {};
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        
        const apiCallPromise = baseQuery(`/divisions?${searchParams.toString()}`);
        
        try {
          const result = await Promise.race([apiCallPromise, mockDataPromise]);
          return result;
        } catch (error) {
          console.warn('❌ QA Service: Error occurred, using mock divisions data:', error);
          return { data: MOCK_DIVISIONS_DATA };
        }
      },
    }),

    getDivision: builder.query<Division, string>({
      query: (id) => `/divisions/${id}`,
      providesTags: ['Division'],
    }),

    createDivision: builder.mutation<Division, CreateDivisionRequest>({
      query: (body) => ({
        url: '/divisions',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Division'],
    }),

    updateDivision: builder.mutation<Division, { id: string; data: UpdateDivisionRequest }>({
      query: ({ id, data }) => ({
        url: `/divisions/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Division'],
    }),

    deleteDivision: builder.mutation<any, { id: string; hard_delete?: boolean }>({
      query: ({ id, hard_delete = false }) => ({
        url: `/divisions/${id}?hard_delete=${hard_delete}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Division', 'Target', 'Question'],
    }),

    // SubDivisions (Targets) CRUD Operations
    getTargets: builder.query<Target[], TargetQueryParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        Object.entries(params || {}).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        return `/subdivisions?${searchParams.toString()}`;
      },
      transformResponse: (response: Target[]) => response,
      providesTags: ['Target'],
    }),

    getTarget: builder.query<Target, string>({
      query: (id) => `/subdivisions/${id}`,
      providesTags: ['Target'],
    }),

    createTarget: builder.mutation<Target, CreateTargetRequest>({
      query: (body) => ({
        url: '/subdivisions',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Target'],
    }),

    updateTarget: builder.mutation<Target, { id: string; data: UpdateTargetRequest }>({
      query: ({ id, data }) => ({
        url: `/subdivisions/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Target'],
    }),

    deleteTarget: builder.mutation<any, { id: string; hard_delete?: boolean }>({
      query: ({ id, hard_delete = false }) => ({
        url: `/subdivisions/${id}?hard_delete=${hard_delete}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Target', 'Question'],
    }),

    // Questions CRUD Operations - with mock data fallback
    getQuestions: builder.query<Question[], QuestionQueryParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        Object.entries(params || {}).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        return `/questions?${searchParams.toString()}`;
      },
      transformResponse: (response: Question[]) => response,
      providesTags: ['Question'],
      // Add timeout with mock data fallback
      queryFn: async (arg, api, extraOptions, baseQuery) => {
        console.log('🔄 QA Service: Attempting to fetch questions...');
        
        const mockDataPromise = new Promise((resolve) => {
          setTimeout(() => {
            console.log('⏰ QA Service: Timeout reached, using mock questions data');
            resolve({ data: MOCK_QUESTIONS_DATA });
          }, 5000);
        });
        
        const params = arg || {};
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        
        const apiCallPromise = baseQuery(`/questions?${searchParams.toString()}`);
        
        try {
          const result = await Promise.race([apiCallPromise, mockDataPromise]);
          return result;
        } catch (error) {
          console.warn('❌ QA Service: Error occurred, using mock questions data:', error);
          return { data: MOCK_QUESTIONS_DATA };
        }
      },
    }),

    getQuestion: builder.query<Question, string>({
      query: (id) => `/questions/${id}`,
      providesTags: ['Question'],
    }),

    createQuestion: builder.mutation<Question, CreateQuestionRequest>({
      query: (body) => ({
        url: '/questions',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Question'],
    }),

    updateQuestion: builder.mutation<Question, { id: string; data: UpdateQuestionRequest }>({
      query: ({ id, data }) => ({
        url: `/questions/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Question'],
    }),

    deleteQuestion: builder.mutation<any, { id: string; hard_delete?: boolean }>({
      query: ({ id, hard_delete = false }) => ({
        url: `/questions/${id}?hard_delete=${hard_delete}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Question'],
    }),

    // Question utility endpoints
    getQuestionTypes: builder.query<string[], void>({
      query: () => '/questions/types',
    }),

    getQuestionStatuses: builder.query<string[], void>({
      query: () => '/questions/statuses',
    }),

    fixMissingTranslations: builder.mutation<any, void>({
      query: () => ({
        url: '/questions/fix-translations',
        method: 'POST',
      }),
      invalidatesTags: ['Question'],
    }),
  }),
});

export const {
  // Legacy Corporate Management
  useGetLegacyCorporatesQuery,
  useSyncCorporateToQAMutation,
  useDeleteLegacyCorporateMutation,
  
  // Divisions
  useGetDivisionsQuery,
  useGetDivisionQuery,
  useCreateDivisionMutation,
  useUpdateDivisionMutation,
  useDeleteDivisionMutation,
  
  // Targets (SubDivisions)
  useGetTargetsQuery,
  useGetTargetQuery,
  useCreateTargetMutation,
  useUpdateTargetMutation,
  useDeleteTargetMutation,
  
  // Questions
  useGetQuestionsQuery,
  useGetQuestionQuery,
  useCreateQuestionMutation,
  useUpdateQuestionMutation,
  useDeleteQuestionMutation,
  
  // Question utilities
  useGetQuestionTypesQuery,
  useGetQuestionStatusesQuery,
  useFixMissingTranslationsMutation,
} = qaServiceApi; 