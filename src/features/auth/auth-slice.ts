// authentication state

import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../../store/store";

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string | null;
  department?: string | null;
  major?: string | null;
  occupation?: string | null;
  bio?: string | null;
  avatar?: string | null;
  role: 'SYSTEM_ADMIN' | 'HR_ADMIN' | 'EMPLOYEE' | 'CONSULTANT';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'BLOCKED';
  companyId?: string | null;
  languages: string[];
  isDeleted: boolean;
  isLeader: boolean;
  createdAt?: number | null;
  updatedAt?: number | null;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  isEnableSupport?: boolean;
}

export interface LoginPayload {
  user: User;
  token: string;
}

// Load auth state from localStorage
const loadAuthFromStorage = (): Partial<AuthState> => {
  try {
    const token = localStorage.getItem('hr-auth-token');
    const userString = localStorage.getItem('hr-auth-user');
    const supportString = localStorage.getItem('hr-auth-support');
    
    if (token && userString) {
      const user = JSON.parse(userString);
      const isEnableSupport = supportString ? JSON.parse(supportString) : false;
      return {
        isAuthenticated: true,
        user,
        token,
        isEnableSupport,
        loading: false,
        error: null,
      };
    }
  } catch (error) {
    console.error('Error loading auth from storage:', error);
  }
  
  return {
    isAuthenticated: false,
    user: null,
    token: null,
    isEnableSupport: false,
    loading: false,
    error: null,
  };
};

const initialState: AuthState = {
  ...loadAuthFromStorage(),
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Login actions
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<{ user: User; token: string; isEnableSupport?: boolean }>) => {
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isEnableSupport = action.payload.isEnableSupport;
      state.loading = false;
      state.error = null;
      
      // Persist to localStorage
      try {
        localStorage.setItem('hr-auth-token', action.payload.token);
        localStorage.setItem('hr-auth-user', JSON.stringify(action.payload.user));
        if (action.payload.isEnableSupport !== undefined) {
          localStorage.setItem('hr-auth-support', JSON.stringify(action.payload.isEnableSupport));
        }
      } catch (error) {
        console.error('Error saving auth to storage:', error);
      }
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    
    // Logout action
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.isEnableSupport = false;
      
      // Clear localStorage
      try {
        localStorage.removeItem('hr-auth-token');
        localStorage.removeItem('hr-auth-user');
        localStorage.removeItem('hr-auth-support');
      } catch (error) {
        console.error('Error clearing auth from storage:', error);
      }
    },
    
    // Update user profile
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        // Update localStorage
        try {
          localStorage.setItem('hr-auth-user', JSON.stringify(state.user));
        } catch (error) {
          console.error('Error updating user in storage:', error);
        }
      }
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
  },
});

// Export actions
export const { 
  loginStart, 
  loginSuccess, 
  loginFailure, 
  logout, 
  updateUserProfile,
  clearError,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: RootState) => state.auth;
export const selectUser = (state: RootState) => state.auth.user;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: RootState) => state.auth.loading;
export const selectAuthError = (state: RootState) => state.auth.error;

// Export the reducer
export default authSlice.reducer; 