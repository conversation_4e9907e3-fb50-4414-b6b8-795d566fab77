import { ReactElement } from "react"
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate
} from "react-router-dom"
import { Provider } from "react-redux"
import { Toaster } from "react-hot-toast"

import { ThemeProvider } from "./components/ThemeProvider"
import { AuthProvider } from "./components/AuthProvider"
import { store } from "./store/store"

// Import pages
import Login from "./pages/Login"
import Register from "./pages/Register"
import ForgotPassword from "./pages/ForgotPassword"
import ResetPassword from "./pages/ResetPassword"
import SetPassword from "./pages/SetPassword"
import RoleDashboard from "./pages/RoleDashboard"
import QAManagement from "./pages/QAManagement"
import CorporatesManagement from "./pages/CorporatesManagement"
import CorporateSettings from "./pages/CorporateSettings"
import MediaManagement from "./pages/MediaManagement"

import ConsultantManagement from "./pages/ConsultantManagement"
import InvitationManagement from "./pages/InvitationManagement"
import HRAdminDashboard from "./pages/HRAdminDashboard"
import HRAnalyticsDashboard from "./pages/HRAnalyticsDashboard"
import HREmployeeManagement from "./pages/HREmployeeManagement"
import HRNotificationManagement from "./pages/HRNotificationManagement"
import HRInvitationManagement from "./pages/HRInvitationManagement"
import AdminDashboardWrapper from "./pages/AdminDashboardWrapper"
import EmployeeOnboarding from "./pages/employee/EmployeeOnboarding"
import EmployeeMediaHub from "./pages/employee/EmployeeMediaHub"
import EmployeeDailyCheckin from "./pages/employee/EmployeeDailyCheckin"
import EmployeeProfile from "./pages/employee/EmployeeProfile"
import EmployeeChat from "./pages/employee/EmployeeChat"
import EmployeeDashboardWrapper from "./pages/employee/EmployeeDashboardWrapper"
import ArticleReaderWrapper from "./pages/employee/ArticleReaderWrapper"
import ConsultantChat from "./pages/consultant/ConsultantChat"
import ConsultantDashboard from "./pages/consultant/ConsultantDashboard"

// Import guards
import ProtectedRoute from "./guard/ProtectedRoute"
import RoleBasedRoute from "./guard/RoleBasedRoute"

// Route configuration types
interface RouteConfig {
  path: string
  element: ReactElement
  protected?: boolean
  allowedRoles?: string[]
}

// Route configurations
const routeConfigs: RouteConfig[] = [
  // Public routes
  { path: "/login", element: <Login /> },
  { path: "/register", element: <Register /> },
  { path: "/forgot-password", element: <ForgotPassword /> },
  { path: "/reset-password", element: <ResetPassword /> },
  { path: "/set-password", element: <SetPassword /> },

  // General protected routes
  {
    path: "/dashboard",
    element: <RoleDashboard />,
    protected: true
  },

  // Admin routes
  {
    path: "/admin/dashboard",
    element: <AdminDashboardWrapper />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/qa-management",
    element: <QAManagement />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/corporates",
    element: <CorporatesManagement />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/corporate/:id/settings",
    element: <CorporateSettings />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/corporate-settings/:companyId",
    element: <CorporateSettings />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/company-settings",
    element: <CorporateSettings />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/media-management",
    element: <MediaManagement />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/consultants",
    element: <ConsultantManagement />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },
  {
    path: "/admin/invitation-management",
    element: <InvitationManagement />,
    protected: true,
    allowedRoles: ["SYSTEM_ADMIN"]
  },

  // HR Admin routes
  {
    path: "/hr/dashboard",
    element: <HRAdminDashboard />,
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/employees",
    element: <HREmployeeManagement />,
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/notifications",
    element: <HRNotificationManagement />,
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/invitations",
    element: <HRInvitationManagement />,
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/analytics",
    element: <HRAnalyticsDashboard />,
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/surveys",
    element: (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Survey Management</h1>
        <p>Survey management coming soon...</p>
      </div>
    ),
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },
  {
    path: "/hr/settings",
    element: (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">HR Settings</h1>
        <p>Settings management coming soon...</p>
      </div>
    ),
    protected: true,
    allowedRoles: ["HR_ADMIN"]
  },

  // Employee routes
  {
    path: "/employee/onboarding",
    element: <EmployeeOnboarding />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/dashboard",
    element: <EmployeeDashboardWrapper />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/analytics",
    element: (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Analytics</h1>
        <p>Employee analytics coming soon...</p>
      </div>
    ),
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/checkin",
    element: <EmployeeDailyCheckin />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/media",
    element: <EmployeeMediaHub />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/chat",
    element: <EmployeeChat />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/profile",
    element: <EmployeeProfile />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },
  {
    path: "/employee/article/:articleId",
    element: <ArticleReaderWrapper />,
    protected: true,
    allowedRoles: ["EMPLOYEE"]
  },

  // Consultant routes
  {
    path: "/consultant/dashboard",
    element: <ConsultantDashboard />,
    protected: true,
    allowedRoles: ["CONSULTANT"]
  },
  {
    path: "/consultant/chat",
    element: <ConsultantChat />,
    protected: true,
    allowedRoles: ["CONSULTANT"]
  }
]

// Helper function to wrap element with protection
const wrapWithProtection = (config: RouteConfig): ReactElement => {
  let element = config.element

  if (config.allowedRoles) {
    element = (
      <RoleBasedRoute allowedRoles={config.allowedRoles}>
        {element}
      </RoleBasedRoute>
    )
  }

  if (config.protected) {
    element = <ProtectedRoute>{element}</ProtectedRoute>
  }

  return element
}

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider defaultTheme="light" storageKey="sehatti-theme">
        <AuthProvider>
          <Router>
            <Routes>
              {/* Render routes from configuration array */}
              {routeConfigs.map((config, index) => (
                <Route
                  key={index}
                  path={config.path}
                  element={wrapWithProtection(config)}
                />
              ))}

              {/* Redirect routes */}
              <Route
                path="/hr"
                element={<Navigate to="/hr/dashboard" replace />}
              />
              <Route
                path="/consultant"
                element={<Navigate to="/consultant/dashboard" replace />}
              />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>

          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              style: {
                background: "#fff",
                color: "#374151",
                border: "1px solid #e5e7eb"
              }
            }}
          />
        </AuthProvider>
      </ThemeProvider>
    </Provider>
  )
}

export default App
