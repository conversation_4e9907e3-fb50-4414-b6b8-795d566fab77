import axios, { AxiosError } from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { getApiUrl } from '../config/api';

// Create axios instance with environment-based URL
export const apiClient = axios.create({
  baseURL: `${getApiUrl()}/api/v2`,
  timeout: 30000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Token management
const TOKEN_KEY = 'hr-auth-token';
const REFRESH_TOKEN_KEY = 'hr-refresh-token';
const REMEMBER_ME_KEY = 'hr-remember-me';

// Token refresh state management
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: string) => void;
  reject: (error) => void;
}> = [];

// Get the appropriate storage based on remember me preference
const getStorage = (): Storage => {
  const rememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
  return rememberMe ? localStorage : sessionStorage;
};

export const getAuthToken = (): string | null => {
  // Check both storages to maintain backwards compatibility
  return getStorage().getItem(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY);
};

export const getRefreshToken = (): string | null => {
  // Check both storages to maintain backwards compatibility
  return getStorage().getItem(REFRESH_TOKEN_KEY) || localStorage.getItem(REFRESH_TOKEN_KEY) || sessionStorage.getItem(REFRESH_TOKEN_KEY);
};

export const setAuthToken = (token: string, rememberMe?: boolean): void => {
  const storage = rememberMe !== undefined ? (rememberMe ? localStorage : sessionStorage) : getStorage();
  
  // Clear token from other storage to avoid conflicts
  if (storage === localStorage) {
    sessionStorage.removeItem(TOKEN_KEY);
  } else {
    localStorage.removeItem(TOKEN_KEY);
  }
  
  storage.setItem(TOKEN_KEY, token);
};

export const setRefreshToken = (refreshToken: string, rememberMe?: boolean): void => {
  const storage = rememberMe !== undefined ? (rememberMe ? localStorage : sessionStorage) : getStorage();
  
  // Clear refresh token from other storage to avoid conflicts
  if (storage === localStorage) {
    sessionStorage.removeItem(REFRESH_TOKEN_KEY);
  } else {
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  }
  
  storage.setItem(REFRESH_TOKEN_KEY, refreshToken);
};

export const setTokens = (token: string, refreshToken?: string, rememberMe?: boolean): void => {
  // Store remember me preference if provided
  if (rememberMe !== undefined) {
    localStorage.setItem(REMEMBER_ME_KEY, rememberMe.toString());
  }
  
  setAuthToken(token, rememberMe);
  if (refreshToken) {
    setRefreshToken(refreshToken, rememberMe);
  }
};

export const removeAuthToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  sessionStorage.removeItem(TOKEN_KEY);
};

export const removeRefreshToken = (): void => {
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  sessionStorage.removeItem(REFRESH_TOKEN_KEY);
};

export const clearTokens = (): void => {
  removeAuthToken();
  removeRefreshToken();
  localStorage.removeItem(REMEMBER_ME_KEY);
};

// Check if token is expired (basic JWT check)
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp < currentTime;
  } catch {
    return true;
  }
};

// Process failed queue after token refresh
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });
  
  failedQueue = [];
};

// Enhanced token refresh function
const refreshAuthToken = async (): Promise<string> => {
  if (isRefreshing) {
    // If already refreshing, queue the request
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject });
    });
  }

  isRefreshing = true;

  try {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // Call refresh endpoint
    const response = await axios.post(`${getApiUrl()}/api/v2/auth/refresh`, {}, {
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });

    const { token: newToken, refreshToken: newRefreshToken } = response.data;
    
    // Update tokens maintaining the current remember me preference
    const currentRememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
    setTokens(newToken, newRefreshToken, currentRememberMe);
    
    // Process queued requests
    processQueue(null, newToken);
    
    return newToken;
  } catch (error) {
    // Process queued requests with error
    processQueue(error, null);
    
    // Clear tokens and redirect to login
    clearTokens();
    
    // Dispatch logout event for any listeners
    window.dispatchEvent(new CustomEvent('auth:logout'));
    
    throw error;
  } finally {
    isRefreshing = false;
  }
};

// Public endpoints that don't require authentication
const PUBLIC_ENDPOINTS = [
  '/auth/login',
  '/hr-admins/register', 
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/verify-token',
  '/employees/set-password',
  '/consultants/set-password',
];

// Request interceptor
apiClient.interceptors.request.use(
  async (config) => {
    // Add auth token for protected endpoints
    const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint => 
      config.url?.includes(endpoint)
    );
    
    if (!isPublicEndpoint) {
      const token = getAuthToken();
      
      if (token) {
        // Check if token is expired and try to refresh
        if (isTokenExpired(token) && !config.url?.includes('/auth/refresh')) {
          try {
            const newToken = await refreshAuthToken();
            config.headers.Authorization = `Bearer ${newToken}`;
          } catch (error) {
            // If refresh fails, proceed with expired token (will be handled by response interceptor)
            config.headers.Authorization = `Bearer ${token}`;
          }
        } else {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }
    }

    // Handle FormData
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle 401 Unauthorized
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // Check if this is a refresh endpoint - don't try to refresh if it's already the refresh call
      const isRefreshEndpoint = originalRequest.url?.includes('/auth/refresh');
      
      if (isRefreshEndpoint) {
        // Refresh token is invalid, clear tokens and redirect
        clearTokens();
        window.dispatchEvent(new CustomEvent('auth:logout'));
        return Promise.reject(error);
      }
      
      // Try to refresh token using enhanced refresh function
      try {
        const newToken = await refreshAuthToken();
        
        // Retry original request with new token
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
        }
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Refresh failed, the refreshAuthToken function already handled cleanup
        return Promise.reject(refreshError);
      }
    }

    // Check if this is a public endpoint - don't show toast errors for these
    // as they should be handled by the UI components
    const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint => 
      originalRequest.url?.includes(endpoint)
    );

    // Only show toast errors for protected endpoints
    if (!isPublicEndpoint) {
      // Handle different error status codes
      switch (error.response?.status) {
        case 400:
          toast.error((error.response.data as any)?.message || 'Invalid request');
          break;
        case 403:
          toast.error('Access denied. You do not have permission to perform this action.');
          break;
        case 404:
          toast.error('The requested resource was not found.');
          break;
        case 409:
          toast.error((error.response.data as any)?.message || 'Conflict occurred');
          break;
        case 422:
          // Validation errors
          const validationErrors = (error.response.data as any)?.errors;
          if (validationErrors && Array.isArray(validationErrors)) {
            validationErrors.forEach((err: any) => {
              toast.error(err.message || 'Validation error');
            });
          } else {
            toast.error((error.response.data as any)?.message || 'Validation failed');
          }
          break;
        case 429:
          toast.error('Too many requests. Please try again later.');
          break;
        case 500:
          toast.error('Internal server error. Please try again later.');
          break;
        case 503:
          toast.error('Service temporarily unavailable. Please try again later.');
          break;
        default:
          if (!navigator.onLine) {
            toast.error('No internet connection. Please check your network.');
          } else if (error.code === 'ECONNABORTED') {
            toast.error('Request timeout. Please try again.');
          } else {
            toast.error((error.response?.data as any)?.message || 'An unexpected error occurred');
          }
      }
    }

    return Promise.reject(error);
  }
);

// Retry mechanism for failed requests
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error instanceof AxiosError && error.response?.status && error.response.status < 500) {
        throw error;
      }
      
      // Wait before retrying (exponential backoff)
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }
  
  throw lastError;
};

// Upload progress handler
export const createUploadConfig = (
  onProgress?: (progress: number) => void
): AxiosRequestConfig => ({
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  onUploadProgress: (progressEvent) => {
    if (onProgress && progressEvent.total) {
      const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(progress);
    }
  },
});

export default apiClient; 