/**
 * Sehatti Theme Configuration
 * 
 * This file defines the global theme configuration for the <PERSON><PERSON><PERSON> application.
 * It includes color palettes, spacing, typography, and other design tokens.
 */

// Golden color palette for Sehatti
export const colors = {
  // Primary gold colors
  gold: {
    50: '#FFF9E6',
    100: '#FFF0C2',
    200: '#FFE499',
    300: '#FFD870',
    400: '#F8C648',
    500: '#D2B37A', // Main gold
    600: '#C19648', // Darker gold
    700: '#A37A2D',
    800: '#7D5E22',
    900: '#5A4319',
    950: '#3A2C10',
  },
  // Warm gray palette
  warmGray: {
    50: '#FAF9F7',
    100: '#F5F3EF',
    200: '#E8E4DC',
    300: '#D5CEC3',
    400: '#B8AE9E',
    500: '#9C9080',
    600: '#7D7265',
    700: '#645A4F',
    800: '#4A433B',
    900: '#332E29',
    950: '#1F1C18',
  },
  // Accent colors
  accent: {
    blue: {
      light: '#E9F5FF',
      DEFAULT: '#0080FF',
      dark: '#0059B3',
    },
    green: {
      light: '#E6F7ED',
      DEFAULT: '#00B359',
      dark: '#008040',
    },
    red: {
      light: '#FFEBEB',
      DEFAULT: '#FF3333',
      dark: '#CC0000',
    },
  },
};

// Typography configuration
export const typography = {
  fontFamily: {
    sans: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    heading: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    black: '900',
  },
  fontSize: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
  },
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
};

// Spacing and sizing
export const spacing = {
  0: '0',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  11: '2.75rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
};

// Border radius
export const borderRadius = {
  none: '0',
  sm: '0.125rem',
  DEFAULT: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px',
};

// Shadows
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  gold: {
    sm: '0 1px 2px 0 rgba(210, 179, 122, 0.05)',
    DEFAULT: '0 1px 3px 0 rgba(210, 179, 122, 0.1), 0 1px 2px 0 rgba(210, 179, 122, 0.06)',
    md: '0 4px 6px -1px rgba(210, 179, 122, 0.1), 0 2px 4px -1px rgba(210, 179, 122, 0.06)',
    lg: '0 10px 15px -3px rgba(210, 179, 122, 0.1), 0 4px 6px -2px rgba(210, 179, 122, 0.05)',
    xl: '0 20px 25px -5px rgba(210, 179, 122, 0.1), 0 10px 10px -5px rgba(210, 179, 122, 0.04)',
    '2xl': '0 25px 50px -12px rgba(210, 179, 122, 0.25)',
    glow: '0 0 15px rgba(210, 179, 122, 0.3)',
  },
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
};

// Transitions
export const transitions = {
  DEFAULT: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
  fast: '100ms cubic-bezier(0.4, 0, 0.2, 1)',
  slow: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
  ease: {
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
};

// Z-index
export const zIndex = {
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  auto: 'auto',
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modalBackdrop: '1040',
  modal: '1050',
  popover: '1060',
  tooltip: '1070',
};

// Export the complete theme
export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};

export default theme; 