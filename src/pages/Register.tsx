import { 
  Button, 
  Input, 
  PasswordInput, 
  GlassCard, 
  GlassCardHeader, 
  GlassCardContent,
  Divider,
  Link,
  GradientText,
  Alert
} from '@/components/ui';
import { useRegisterFunc } from '@/hooks/useRegisterFunc';
import { getErrorMessage } from '@/utils/errorUtils';

// User Icon Component (for register)
const UserIcon = () => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
    <path d="M16 7C16 9.2 14.2 11 12 11S8 9.2 8 7 9.8 3 12 3 16 4.8 16 7ZM12 14C16.42 14 20 15.79 20 18V21H4V18C4 15.79 7.58 14 12 14Z" fill="white"/>
  </svg>
);

// Arrow Left Icon Component
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M19 12H5m7-7l-7 7 7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export default function Register() {
  const {
    registerData,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
  } = useRegisterFunc();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Register Card */}
      <GlassCard 
        variant="default" 
        decorativeTop={true}
        className="w-full max-w-[420px]"
      >
        <GlassCardHeader>
          <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6 shadow-[0_8px_20px_rgba(210,179,122,0.3)]">
            <UserIcon />
          </div>
          <GradientText 
            gradient="goldRich" 
            size="2xl" 
            className="font-bold mb-2"
          >
            Create Account
          </GradientText>
          <p className="text-[15px] text-gray-500 font-normal leading-relaxed">
            Join Sehatti to manage your organization's employee wellness and assessment programs
          </p>
        </GlassCardHeader>

        <GlassCardContent>
          {error ? (
            <Alert 
              variant="destructive" 
              className="mb-6"
              title="Registration Error"
              description={getErrorMessage(error)}
            />
          ) : null}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Company Name Field */}
            <Input
              label={
                <>
                  Company Name <span className="text-red-500">*</span>
                </>
              }
              type="text"
              placeholder="Enter your company name"
              value={registerData.corporateName}
              onChange={(e) => handleInputChange('corporateName', e.target.value)}
              autoComplete="organization"
              required
            />

            {/* Email Field */}
            <Input
              label={
                <>
                  Business Email <span className="text-red-500">*</span>
                </>
              }
              type="email"
              placeholder="Enter your business email"
              value={registerData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              autoComplete="email"
              required
            />

            {/* Phone Field */}
            <Input
              label={
                <>
                  Phone Number <span className="text-red-500">*</span>
                </>
              }
              type="tel"
              placeholder="Enter your phone number"
              value={registerData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              autoComplete="tel"
              required
            />

            {/* Password Field */}
            <PasswordInput
              label={
                <>
                  Password <span className="text-red-500">*</span>
                </>
              }
              placeholder="Create a secure password"
              value={registerData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              autoComplete="new-password"
            />

            {/* Confirm Password Field */}
            <PasswordInput
              label={
                <>
                  Confirm Password <span className="text-red-500">*</span>
                </>
              }
              placeholder="Confirm your password"
              value={registerData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              autoComplete="new-password"
            />

            {/* Password Requirements */}
            <div className="p-4 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
              <h4 className="text-sm font-semibold text-[#374151] mb-2">
                Password Requirements:
              </h4>
              <ul className="text-xs text-[#6b7280] leading-relaxed space-y-1 list-disc list-inside">
                <li>At least 8 characters long</li>
                <li>Contains uppercase and lowercase letters</li>
                <li>Contains at least one number</li>
                <li>Contains at least one special character</li>
                <li>Both passwords must match</li>
              </ul>
            </div>

            {/* Create Account Button */}
            <Button
              type="submit"
              variant="default"
              size="default"
              fullWidth={true}
              isLoading={isLoading}
              className="mb-6"
            >
              {isLoading ? "Creating Account..." : "Create Account"}
            </Button>

            {/* Divider */}
            <Divider label="OR" />

            {/* Back to Login Link */}
            <Link
              href="/"
              variant="button"
              className="w-full text-center"
              leftIcon={<ArrowLeftIcon />}
            >
              Back to Login
            </Link>
          </form>
        </GlassCardContent>
      </GlassCard>

      {/* Terms and Privacy */}
      <div className="mt-6 text-center max-w-[420px] px-5">
        <p className="text-xs text-gray-500 leading-relaxed">
          By creating an account, you agree to our{' '}
          <a href="#" className="text-[#d2b37a] hover:underline font-medium">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="#" className="text-[#d2b37a] hover:underline font-medium">
            Privacy Policy
          </a>
        </p>
      </div>

      {/* Copyright */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-400 font-medium">
          All rights reserved © Sehatti. An Elevate Academy ecosystem.
        </p>
      </div>
    </div>
  );
} 