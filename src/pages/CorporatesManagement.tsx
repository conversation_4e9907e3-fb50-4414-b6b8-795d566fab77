import React, { useState } from 'react';
import { 
  FaBuilding, 
  FaCog, 
  FaEye, 
  FaTrash, 
  FaSearch, 
  FaFilter, 
  FaPlus,
  FaEllipsisH,
  FaUsers,
  FaCalendarAlt,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaShieldAlt,
  FaChartLine
} from 'react-icons/fa';
import { useGetCompaniesQuery, useUpdateCompanyStatusMutation, useDeleteCompanyMutation, type Company } from '@/store/api/companyApi';
import { useRegisterMutation } from '@/store/api/authApi';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { GradientText } from '@/components/ui/GradientText';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';
import { toast } from 'react-hot-toast';
import { useAppSelector } from '@/store/store';
import { selectAuth } from '@/features/auth/auth-slice';

// Use Company interface directly from the API to avoid type mismatches
type Corporate = Company;

const CorporatesManagement: React.FC = () => {
  // Get user from auth store
  const authState = useAppSelector(selectAuth);
  const user = authState?.user;

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCorporate, setSelectedCorporate] = useState<Corporate | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  
  // Create form state
  const [createForm, setCreateForm] = useState({
    name: '',
    email: '',
    phone: '',
    password: 'TempPassword123!',
    confirmPassword: 'TempPassword123!',
    is_share_data: false,
  });

  // Fetch companies data with proper V2 endpoint
  const { 
    data: companiesData, 
    isLoading, 
    error, 
    refetch 
  } = useGetCompaniesQuery({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // API mutations
  const [registerHrAdmin, { isLoading: isCreating }] = useRegisterMutation();
  const [updateCompanyStatus, { isLoading: isUpdatingStatus }] = useUpdateCompanyStatusMutation();
  const [deleteCompany, { isLoading: isDeleting }] = useDeleteCompanyMutation();

  // Extract companies from response
  const companies: Corporate[] = companiesData?.items || [];

  // Filter companies by status
  const filteredCompanies = companies.filter(company => {
    const matchesStatus = statusFilter === 'all' || company.status === statusFilter;
    return matchesStatus;
  });

  // Stats calculations
  const stats = {
    total: companiesData?.total || 0,
    active: companies.filter(c => c.status === 'ACTIVE').length,
    pending: companies.filter(c => c.status === 'PENDING').length,
    in_progress: companies.filter(c => c.status === 'IN_PROGRESS').length,
    rejected: companies.filter(c => c.status === 'REJECTED').length,
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'success',
      PENDING: 'warning',
      IN_PROGRESS: 'secondary',
      REJECTED: 'destructive'
    } as const;
    
    const displayStatus = {
      ACTIVE: 'Active',
      PENDING: 'Pending',
      IN_PROGRESS: 'In Progress',
      REJECTED: 'Rejected'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {displayStatus[status as keyof typeof displayStatus] || status}
      </Badge>
    );
  };

  const handleViewCorporate = (corporate: Corporate) => {
    setSelectedCorporate(corporate);
    setIsViewModalOpen(true);
  };

  const handleSettingsCorporate = (corporate: Corporate) => {
    // Navigate to corporate settings page using company_id
    const companyId = corporate.company_id || corporate.id;
    window.open(`/admin/corporate-settings/${companyId}`, '_blank');
  };

  const handleStatusChange = async (corporate: Corporate, newStatus: string) => {
    try {
      const loadingToast = toast.loading(`Updating ${corporate.name} status...`);
      
      await updateCompanyStatus({
        companyId: corporate.id,
        status: newStatus // Status will be converted to uppercase in API layer
      }).unwrap();
      
      toast.success(`Company status updated to ${newStatus.toLowerCase()}`, {
        id: loadingToast,
      });
      
      // If status changed to active from pending, show success message about email
      if (newStatus === 'ACTIVE' && corporate.status === 'PENDING') {
        toast.success(`Welcome email sent to ${corporate.email}`, {
          duration: 5000,
        });
      }
      
      // Refresh the list
      refetch();
      
    } catch (error: any) {
      console.error('❌ Failed to update company status:', error);
      
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          'Failed to update company status. Please try again.';
      
      toast.error(errorMessage);
    }
  };

  const handleDeleteCorporate = (corporate: Corporate) => {
    setSelectedCorporate(corporate);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedCorporate) return;
    
    try {
      const loadingToast = toast.loading(`Deleting ${selectedCorporate.name}...`);
      
      await deleteCompany(selectedCorporate.id).unwrap();
      
      toast.success(`Corporate "${selectedCorporate.name}" deleted successfully!`, {
        id: loadingToast,
      });
      
      // Close modal and clear selection
      setIsDeleteModalOpen(false);
      setSelectedCorporate(null);
      
      // Refresh the list
      refetch();
      
    } catch (error: any) {
      console.error('❌ Failed to delete corporate:', error);
      
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          'Failed to delete corporate. Please try again.';
      
      toast.error(errorMessage);
    }
  };

  // Handle form input changes
  const handleFormChange = (field: string, value: any) => {
    setCreateForm(prev => ({ ...prev, [field]: value }));
  };

  // Handle create corporate submission
  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!createForm.name || !createForm.email || !createForm.phone) {
      toast.error('Please fill in all required fields (Company Name, Email, Phone)');
      return;
    }

    try {
      console.log('🚀 Creating HR Admin account with company...');
      
      // Prepare data for HR Admin registration (creates company + user + sends email)
      const hrAdminData = {
        name: `HR Manager`, // HR admin personal name
        company_name: createForm.name, // Company name
        email: createForm.email,
        phone: createForm.phone,
        password: createForm.password,
        confirmPassword: createForm.confirmPassword,
        is_share_data: createForm.is_share_data,
      };
      
      const result = await registerHrAdmin(hrAdminData).unwrap();
      
      console.log('✅ HR Admin created successfully:', result);
      toast.success(`Corporate "${createForm.name}" created successfully! HR Admin invitation email sent.`);
      
      // Reset form and close modal
      setCreateForm({
        name: '',
        email: '',
        phone: '',
        password: 'TempPassword123!',
        confirmPassword: 'TempPassword123!',
        is_share_data: false,
      });
      setIsCreateModalOpen(false);
      
      // Refresh the list
      refetch();
      
    } catch (error: any) {
      console.error('❌ Failed to create corporate:', error);
      
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          'Failed to create corporate. Please try again.';
      
      toast.error(errorMessage);
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleLogout = () => {
    // Handle logout logic
    console.log('Logout clicked');
  };

  if (isLoading) {
    return (
      <AdminLayout user={user || undefined} onLogout={handleLogout}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold-600 mx-auto"></div>
            <p className="mt-4 text-sehatti-warm-gray-600">Loading companies...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout user={user || undefined} onLogout={handleLogout}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Error loading companies</p>
            <Button onClick={() => refetch()} className="mt-4">
              Try Again
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout user={user || undefined} onLogout={handleLogout}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">
              <GradientText>Corporate Management</GradientText>
            </h1>
            <p className="text-sehatti-warm-gray-600 mt-2">
              Manage companies and HR administrators
            </p>
          </div>
          <Button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
          >
            <FaPlus className="mr-2" />
            Add Corporate
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-sehatti-warm-gray-500">Total Companies</p>
                  <p className="text-2xl font-bold text-sehatti-warm-gray-900">
                    {stats.total}
                  </p>
                </div>
                <FaBuilding className="h-8 w-8 text-sehatti-gold-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-sehatti-warm-gray-500">Active</p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.active}
                  </p>
                </div>
                <FaShieldAlt className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-sehatti-warm-gray-500">Pending</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {stats.pending}
                  </p>
                </div>
                <FaCalendarAlt className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-sehatti-warm-gray-500">Rejected</p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.rejected}
                  </p>
                </div>
                <FaUsers className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search companies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<FaSearch className="h-4 w-4" />}
                />
              </div>
              <div className="md:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-sehatti-warm-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500"
                >
                  <option value="all">All Status</option>
                  <option value="ACTIVE">Active</option>
                  <option value="PENDING">Pending</option>
                  <option value="IN_PROGRESS">In Progress</option>
                  <option value="REJECTED">Rejected</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Companies Table */}
        <Card>
          <CardHeader>
            <CardTitle>Companies ({stats.total})</CardTitle>
            <CardDescription>
              Manage corporate accounts and their settings
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {filteredCompanies.length === 0 ? (
              <div className="text-center py-12">
                <FaBuilding className="mx-auto h-12 w-12 text-sehatti-warm-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-sehatti-warm-gray-900">No companies</h3>
                <p className="mt-1 text-sm text-sehatti-warm-gray-500">
                  Get started by creating a new corporate account.
                </p>
                <div className="mt-6">
                  <Button 
                    onClick={() => setIsCreateModalOpen(true)}
                    className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
                  >
                    <FaPlus className="mr-2" />
                    Add Corporate
                  </Button>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-sehatti-warm-gray-200">
                  <thead className="bg-sehatti-warm-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-sehatti-warm-gray-500 uppercase tracking-wider">
                        Company
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-sehatti-warm-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-sehatti-warm-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-sehatti-warm-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-sehatti-warm-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-sehatti-warm-gray-200">
                    {filteredCompanies.map((corporate) => (
                      <tr key={corporate.id} className="hover:bg-sehatti-warm-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-sehatti-gold-100 flex items-center justify-center">
                                <FaBuilding className="h-5 w-5 text-sehatti-gold-600" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-sehatti-warm-gray-900">
                                {corporate.name}
                              </div>
                              <div className="text-sm text-sehatti-warm-gray-500">
                                ID: {corporate.company_id || corporate.id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-sehatti-warm-gray-900">{corporate.email}</div>
                          <div className="text-sm text-sehatti-warm-gray-500">{corporate.phone}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <div className="cursor-pointer">
                                {getStatusBadge(corporate.status)}
                              </div>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => handleStatusChange(corporate, 'ACTIVE')}>
                                Set Active
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(corporate, 'PENDING')}>
                                Set Pending
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(corporate, 'IN_PROGRESS')}>
                                Set In Progress
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(corporate, 'REJECTED')}>
                                Reject
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-sehatti-warm-gray-500">
                          {corporate.created_at ? new Date(corporate.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <FaEllipsisH className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => handleViewCorporate(corporate)}>
                                <FaEye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSettingsCorporate(corporate)}>
                                <FaCog className="mr-2 h-4 w-4" />
                                Settings
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteCorporate(corporate)}>
                                <FaTrash className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {companiesData && companiesData.total > pageSize && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-sehatti-warm-gray-700">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, companiesData.total)} of {companiesData.total} companies
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!companiesData.has_previous}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!companiesData.has_next}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Create Corporate Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Corporate</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Company Name *
              </label>
              <Input
                value={createForm.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                placeholder="Enter company name"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                HR Admin Email *
              </label>
              <Input
                type="email"
                value={createForm.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                placeholder="Enter HR admin email"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-2">
                Phone Number *
              </label>
              <Input
                value={createForm.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                placeholder="Enter phone number"
                required
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isShareData"
                checked={createForm.is_share_data}
                onChange={(e) => handleFormChange('is_share_data', e.target.checked)}
                className="h-4 w-4 text-sehatti-gold-600 focus:ring-sehatti-gold-500 border-sehatti-warm-gray-300 rounded"
              />
              <label htmlFor="isShareData" className="ml-2 block text-sm text-sehatti-warm-gray-900">
                Allow data sharing
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating}
                className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
              >
                {isCreating ? 'Creating...' : 'Create Corporate'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Corporate Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Corporate Details</DialogTitle>
          </DialogHeader>
          {selectedCorporate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Company Name</label>
                  <p className="text-sm text-sehatti-warm-gray-900">{selectedCorporate.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedCorporate.status)}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Email</label>
                  <p className="text-sm text-sehatti-warm-gray-900">{selectedCorporate.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Phone</label>
                  <p className="text-sm text-sehatti-warm-gray-900">{selectedCorporate.phone || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Company ID</label>
                  <p className="text-sm text-sehatti-warm-gray-900">{selectedCorporate.company_id || selectedCorporate.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-sehatti-warm-gray-500">Created Date</label>
                  <p className="text-sm text-sehatti-warm-gray-900">
                    {selectedCorporate.created_at ? new Date(selectedCorporate.created_at).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setIsViewModalOpen(false)}>
                  Close
                </Button>
                <Button 
                  onClick={() => handleSettingsCorporate(selectedCorporate)}
                  className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
                >
                  <FaCog className="mr-2" />
                  Manage Settings
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          {selectedCorporate && (
            <div className="space-y-4">
              <p className="text-sm text-sehatti-warm-gray-600">
                Are you sure you want to delete <strong>{selectedCorporate.name}</strong>? 
                This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default CorporatesManagement; 