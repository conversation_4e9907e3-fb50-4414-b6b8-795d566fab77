import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { 
  FaUsers, 
  FaComments, 
  FaStar, 
  FaDollarSign,
  FaCalendarAlt,
  FaChartLine,
  FaUserCheck,
  FaClock,
  FaPhone,
  FaVideo,
  FaEnvelope,
  FaEdit,
  FaEye,
  FaBell,
  FaCheckCircle,
  FaTimesCircle,
  FaUserTie,
  FaBuilding,
  FaGraduationCap,
  FaLanguage
} from 'react-icons/fa'
import { 
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Avatar,
  Spinner,
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '../../components/ui'
import { useAuth } from '../../hooks/useAuth'

// Mock data for consultant dashboard (will be replaced with API calls)
const mockConsultantData = {
  id: 'consultant-123',
  name: 'Dr. <PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  bio: 'Licensed mental health counselor with 10+ years of experience in corporate wellness programs.',
  major: 'Clinical Psychology',
  image: 'https://ui-avatars.com/api/?name=Dr.+Sarah+<PERSON>&background=d4a574&color=ffffff',
  languages: ['ENGLISH', 'SPANISH', 'FRENCH'],
  specializations: ['MENTAL_HEALTH', 'STRESS_MANAGEMENT', 'WELLNESS_COACHING'],
  status: 'ACTIVE',
  isVerified: true,
  yearsOfExperience: 10,
  hourlyRate: 85,
  currency: 'USD',
  rating: {
    averageRating: 4.8,
    totalReviews: 127
  },
  availability: {
    isAvailable: true,
    timezone: 'EST',
    workingHours: {
      monday: { start: '09:00', end: '17:00' },
      tuesday: { start: '09:00', end: '17:00' },
      wednesday: { start: '09:00', end: '17:00' },
      thursday: { start: '09:00', end: '17:00' },
      friday: { start: '09:00', end: '17:00' },
    }
  },
  metrics: {
    totalSessions: 245,
    activeClients: 18,
    thisMonthSessions: 32,
    totalEarnings: 20825,
    thisMonthEarnings: 2720
  },
  companies: [
    { id: 'comp-1', name: 'Tech Corp Inc.', employees: 45 },
    { id: 'comp-2', name: 'Healthcare Solutions', employees: 23 },
    { id: 'comp-3', name: 'Financial Services Ltd.', employees: 38 }
  ]
}

const mockRecentSessions = [
  {
    id: 'session-1',
    clientName: 'John Smith',
    company: 'Tech Corp Inc.',
    date: '2025-01-15',
    time: '14:00',
    duration: 45,
    type: 'Mental Health',
    status: 'completed',
    rating: 5
  },
  {
    id: 'session-2',
    clientName: 'Maria Garcia',
    company: 'Healthcare Solutions',
    date: '2025-01-15',
    time: '10:30',
    duration: 60,
    type: 'Stress Management',
    status: 'completed',
    rating: 4
  },
  {
    id: 'session-3',
    clientName: 'David Chen',
    company: 'Financial Services Ltd.',
    date: '2025-01-14',
    time: '16:00',
    duration: 30,
    type: 'Wellness Coaching',
    status: 'completed',
    rating: 5
  }
]

const mockUpcomingSessions = [
  {
    id: 'upcoming-1',
    clientName: 'Sarah Wilson',
    company: 'Tech Corp Inc.',
    date: '2025-01-16',
    time: '09:00',
    duration: 45,
    type: 'Mental Health',
    status: 'scheduled'
  },
  {
    id: 'upcoming-2',
    clientName: 'Michael Brown',
    company: 'Healthcare Solutions',
    date: '2025-01-16',
    time: '11:00',
    duration: 60,
    type: 'Stress Management',
    status: 'scheduled'
  }
]

const mockChatRequests = [
  {
    id: 'chat-1',
    clientName: 'Emily Davis',
    company: 'Tech Corp Inc.',
    message: 'Hi, I would like to discuss stress management techniques for my work environment.',
    timestamp: '2025-01-15T10:30:00Z',
    priority: 'high',
    status: 'pending'
  },
  {
    id: 'chat-2',
    clientName: 'Robert Johnson',
    company: 'Financial Services Ltd.',
    message: 'Looking for guidance on work-life balance strategies.',
    timestamp: '2025-01-15T09:15:00Z',
    priority: 'medium',
    status: 'pending'
  }
]

const ConsultantDashboard: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [consultantData, setConsultantData] = useState(mockConsultantData)
  const [recentSessions, setRecentSessions] = useState(mockRecentSessions)
  const [upcomingSessions, setUpcomingSessions] = useState(mockUpcomingSessions)
  const [chatRequests, setChatRequests] = useState(mockChatRequests)
  const [loading, setLoading] = useState(false)

  // Simulate API calls
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      // Simulate API loading time
      await new Promise(resolve => setTimeout(resolve, 1000))
      setLoading(false)
    }
    loadData()
  }, [])

  const handleChatRequestAction = (requestId: string, action: 'accept' | 'decline') => {
    setChatRequests(prev => prev.map(request => 
      request.id === requestId 
        ? { ...request, status: action === 'accept' ? 'accepted' : 'declined' }
        : request
    ))
    
    const actionText = action === 'accept' ? 'accepted' : 'declined'
    toast.success(`Chat request ${actionText} successfully`)
    
    if (action === 'accept') {
      // Navigate to chat interface
      navigate('/consultant/chat')
    }
  }

  const handleStartSession = (sessionId: string) => {
    toast.success('Session started successfully')
    // In a real app, this would open the session interface
  }

  const handleViewProfile = () => {
    navigate('/consultant/profile')
  }

  const handleManageSchedule = () => {
    navigate('/consultant/schedule')
  }

  // Stats calculations
  const stats = [
    {
      title: 'Active Clients',
      value: consultantData.metrics.activeClients,
      icon: <FaUsers className="w-6 h-6" />,
      color: 'blue',
      description: 'Currently serving'
    },
    {
      title: 'This Month Sessions',
      value: consultantData.metrics.thisMonthSessions,
      icon: <FaCalendarAlt className="w-6 h-6" />,
      color: 'green',
      description: 'Sessions completed'
    },
    {
      title: 'Average Rating',
      value: consultantData.rating.averageRating.toFixed(1),
      icon: <FaStar className="w-6 h-6" />,
      color: 'yellow',
      description: `${consultantData.rating.totalReviews} reviews`
    },
    {
      title: 'This Month Earnings',
      value: `$${consultantData.metrics.thisMonthEarnings.toLocaleString()}`,
      icon: <FaDollarSign className="w-6 h-6" />,
      color: 'emerald',
      description: 'Total earnings'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner size="lg" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar 
                src={consultantData.image} 
                alt={consultantData.name}
                size="lg"
                fallback={<FaUserTie className="w-8 h-8" />}
              />
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {consultantData.name}
                  </h1>
                  {consultantData.isVerified && (
                    <Badge variant="success" className="text-xs">
                      <FaUserCheck className="w-3 h-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  {consultantData.major} • {consultantData.yearsOfExperience} years experience
                </p>
                <div className="flex items-center gap-4 mt-1">
                  <div className="flex items-center gap-1">
                    <FaStar className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm font-medium">{consultantData.rating.averageRating}</span>
                    <span className="text-sm text-gray-500">({consultantData.rating.totalReviews} reviews)</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className={`w-2 h-2 rounded-full ${consultantData.availability.isAvailable ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {consultantData.availability.isAvailable ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleViewProfile}
                leftIcon={<FaEdit className="w-4 h-4" />}
              >
                Edit Profile
              </Button>
              <Button
                variant="outline"
                onClick={handleManageSchedule}
                leftIcon={<FaClock className="w-4 h-4" />}
              >
                Manage Schedule
              </Button>
              <Button
                onClick={() => navigate('/consultant/chat')}
                leftIcon={<FaComments className="w-4 h-4" />}
                className="bg-sehatti-gold-600 hover:bg-sehatti-gold-700"
              >
                Open Chat
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {stat.value}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {stat.description}
                      </p>
                    </div>
                    <div className={`p-3 rounded-lg bg-${stat.color}-100 dark:bg-${stat.color}-900/30`}>
                      {React.cloneElement(stat.icon, {
                        className: `w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Chat Requests */}
          {chatRequests.filter(req => req.status === 'pending').length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaBell className="w-5 h-5 text-sehatti-gold-600" />
                  Pending Chat Requests
                  <Badge variant="warning" className="ml-2">
                    {chatRequests.filter(req => req.status === 'pending').length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {chatRequests.filter(req => req.status === 'pending').map((request) => (
                    <div key={request.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {request.clientName}
                            </h4>
                            <Badge variant="outline" className="text-xs">
                              {request.company}
                            </Badge>
                            <Badge 
                              variant={request.priority === 'high' ? 'destructive' : request.priority === 'medium' ? 'warning' : 'secondary'}
                              className="text-xs"
                            >
                              {request.priority} priority
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {request.message}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(request.timestamp).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleChatRequestAction(request.id, 'accept')}
                            className="text-green-600 hover:text-green-700 border-green-600 hover:border-green-700"
                          >
                            <FaCheckCircle className="w-4 h-4 mr-1" />
                            Accept
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleChatRequestAction(request.id, 'decline')}
                            className="text-red-600 hover:text-red-700 border-red-600 hover:border-red-700"
                          >
                            <FaTimesCircle className="w-4 h-4 mr-1" />
                            Decline
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Upcoming Sessions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaCalendarAlt className="w-5 h-5 text-sehatti-gold-600" />
                  Upcoming Sessions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingSessions.map((session) => (
                    <div key={session.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {session.clientName}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {session.company} • {session.type}
                          </p>
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1">
                              <FaCalendarAlt className="w-4 h-4 text-gray-400" />
                              <span className="text-sm">{session.date}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <FaClock className="w-4 h-4 text-gray-400" />
                              <span className="text-sm">{session.time} ({session.duration}min)</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStartSession(session.id)}
                          >
                            <FaVideo className="w-4 h-4 mr-1" />
                            Start
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {upcomingSessions.length === 0 && (
                    <p className="text-center text-gray-500 py-8">
                      No upcoming sessions scheduled
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Sessions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaChartLine className="w-5 h-5 text-sehatti-gold-600" />
                  Recent Sessions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentSessions.map((session) => (
                    <div key={session.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {session.clientName}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {session.company} • {session.type}
                          </p>
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1">
                              <FaCalendarAlt className="w-4 h-4 text-gray-400" />
                              <span className="text-sm">{session.date}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <FaClock className="w-4 h-4 text-gray-400" />
                              <span className="text-sm">{session.duration}min</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="success" className="mb-2">
                            Completed
                          </Badge>
                          <div className="flex items-center gap-1">
                            <FaStar className="w-4 h-4 text-yellow-500" />
                            <span className="text-sm font-medium">{session.rating}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Companies & Profile Info */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Assigned Companies */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaBuilding className="w-5 h-5 text-sehatti-gold-600" />
                  Assigned Companies
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {consultantData.companies.map((company) => (
                    <div key={company.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                          <FaBuilding className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {company.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {company.employees} employees
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/consultant/company/${company.id}`)}
                      >
                        <FaEye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Professional Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaUserTie className="w-5 h-5 text-sehatti-gold-600" />
                  Professional Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Specializations</h4>
                    <div className="flex flex-wrap gap-2">
                      {consultantData.specializations.map((spec, idx) => (
                        <Badge key={idx} variant="outline">
                          {spec.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Languages</h4>
                    <div className="flex flex-wrap gap-2">
                      {consultantData.languages.map((lang, idx) => (
                        <Badge key={idx} variant="outline">
                          <FaLanguage className="w-3 h-3 mr-1" />
                          {lang}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Experience</h4>
                    <div className="flex items-center gap-2">
                      <FaGraduationCap className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{consultantData.yearsOfExperience} years</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Hourly Rate</h4>
                    <div className="flex items-center gap-2">
                      <FaDollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">${consultantData.hourlyRate} {consultantData.currency}</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Contact</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FaEnvelope className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{consultantData.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FaPhone className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{consultantData.phone}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConsultantDashboard 