import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import ConsultantNotifications from '../../components/chat/ConsultantNotifications'
import ChatInterface from '../../components/chat/ChatInterface'
import { useAuth } from '../../hooks/useAuth'

interface ChatRoom {
  id: string
  chatId: string
  type: 'DIRECT' | 'GROUP' | 'SUPPORT'
  participants: Array<{
    id: string
    name: string
    avatar?: string
    role: 'CONSULTANT' | 'EMPLOYEE'
    isOnline: boolean
    lastSeen?: string
  }>
  title?: string
  description?: string
  lastActivity: string
  unreadCount: number
  messages: Array<{
    id: string
    chatId: string
    senderId: string
    senderName: string
    senderAvatar?: string
    content: string
    type: 'TEXT' | 'IMAGE' | 'FILE' | 'VOICE' | 'SYSTEM'
    timestamp: string
    isRead: boolean
    isDelivered: boolean
    isEdited: boolean
    replyTo?: string
    attachments?: Array<{
      url: string
      name: string
      type: string
      size: number
    }>
  }>
}

const ConsultantChat: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null)
  const [activeChat, setActiveChat] = useState<ChatRoom | null>(null)
  const [isLoadingChat, setIsLoadingChat] = useState(false)

  const handleSelectChat = useCallback(async (chatId: string) => {
    if (chatId === selectedChatId) return

    setSelectedChatId(chatId)
    setIsLoadingChat(true)

    try {
      // Load chat room data
      const chatRoom = await loadChatRoom(chatId)
      setActiveChat(chatRoom)
    } catch (error) {
      console.error('Error loading chat:', error)
      toast.error('Failed to load chat. Please try again.')
    } finally {
      setIsLoadingChat(false)
    }
  }, [selectedChatId])

  const loadChatRoom = async (chatId: string): Promise<ChatRoom> => {
    // TODO: Replace with actual API call
    // Mock implementation for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockChatRoom: ChatRoom = {
          id: chatId,
          chatId: chatId,
          type: 'DIRECT',
          participants: [
            {
              id: user?.id || '',
              name: user?.name || '',
              avatar: user?.image,
              role: 'CONSULTANT',
              isOnline: true,
            },
            {
              id: 'emp-1',
              name: 'John Smith',
              avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
              role: 'EMPLOYEE',
              isOnline: true,
            }
          ],
          lastActivity: new Date().toISOString(),
          unreadCount: 0,
          messages: [
            {
              id: 'msg-1',
              chatId: chatId,
              senderId: 'emp-1',
              senderName: 'John Smith',
              senderAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
              content: 'Hi, I would like to discuss stress management techniques for our upcoming project deadline.',
              type: 'TEXT',
              timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              isRead: true,
              isDelivered: true,
              isEdited: false
            },
            {
              id: 'msg-2',
              chatId: chatId,
              senderId: user?.id || '',
              senderName: user?.name || '',
              senderAvatar: user?.image,
              content: 'Hello John! I\'d be happy to help you with stress management. Let\'s start with some breathing techniques that you can use during high-pressure situations.',
              type: 'TEXT',
              timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
              isRead: true,
              isDelivered: true,
              isEdited: false
            },
            {
              id: 'msg-3',
              chatId: chatId,
              senderId: 'emp-1',
              senderName: 'John Smith',
              senderAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
              content: 'That sounds great! I\'ve been feeling overwhelmed lately with the workload.',
              type: 'TEXT',
              timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
              isRead: true,
              isDelivered: true,
              isEdited: false
            }
          ]
        }
        resolve(mockChatRoom)
      }, 500)
    })
  }

  const handleAcceptChatRequest = useCallback(async (notificationId: string) => {
    try {
      // TODO: Replace with actual API call
      // Accept the chat request
      console.log('Accepting chat request:', notificationId)
      toast.success('Chat request accepted')
      
      // The notification component will handle moving the request to active chats
    } catch (error) {
      console.error('Error accepting chat request:', error)
      toast.error('Failed to accept chat request')
    }
  }, [])

  const handleDeclineChatRequest = useCallback(async (notificationId: string) => {
    try {
      // TODO: Replace with actual API call
      // Decline the chat request
      console.log('Declining chat request:', notificationId)
      toast.success('Chat request declined')
    } catch (error) {
      console.error('Error declining chat request:', error)
      toast.error('Failed to decline chat request')
    }
  }, [])

  const handleBackToNotifications = useCallback(() => {
    setSelectedChatId(null)
    setActiveChat(null)
  }, [])

  const handleStartCall = useCallback((type: 'voice' | 'video') => {
    if (!activeChat) return
    
    const otherParticipant = activeChat.participants.find(p => p.id !== user?.id)
    if (otherParticipant) {
      toast.success(`Starting ${type} call with ${otherParticipant.name}...`)
      // TODO: Implement call functionality
    }
  }, [activeChat, user?.id])

  const getActiveChatParticipant = () => {
    if (!activeChat || !user) return null
    return activeChat.participants.find(p => p.id !== user.id) || null
  }

  const activeChatParticipant = getActiveChatParticipant()

  return (
    <div className="h-screen bg-sehatti-warm-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-sehatti-warm-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-sehatti-warm-gray-900">
                Consultant Chat
              </h1>
              <p className="text-sm text-sehatti-warm-gray-600">
                Manage your client conversations and chat requests
              </p>
            </div>
            
            {activeChatParticipant && (
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-sehatti-warm-gray-900">
                    Chatting with
                  </p>
                  <p className="text-xs text-sehatti-warm-gray-600">
                    {activeChatParticipant.name}
                  </p>
                </div>
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-sehatti-gold-500 flex items-center justify-center">
                    {activeChatParticipant.avatar ? (
                      <img 
                        src={activeChatParticipant.avatar} 
                        alt={activeChatParticipant.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-white font-medium">
                        {activeChatParticipant.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  {activeChatParticipant.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="h-[calc(100vh-80px)] flex">
        {!selectedChatId ? (
          // Notifications & Chat List
          <div className="w-full">
            <ConsultantNotifications
              onSelectChat={handleSelectChat}
              onAcceptChatRequest={handleAcceptChatRequest}
              onDeclineChatRequest={handleDeclineChatRequest}
              selectedChatId={selectedChatId}
            />
          </div>
        ) : (
          // Split view: Notifications sidebar + Chat
          <>
            {/* Notifications Sidebar */}
            <div className="w-96 bg-white border-r border-sehatti-warm-gray-200">
              <ConsultantNotifications
                onSelectChat={handleSelectChat}
                onAcceptChatRequest={handleAcceptChatRequest}
                onDeclineChatRequest={handleDeclineChatRequest}
                selectedChatId={selectedChatId}
              />
            </div>

            {/* Chat Interface */}
            <div className="flex-1">
              {isLoadingChat ? (
                <div className="h-full flex items-center justify-center bg-sehatti-warm-gray-50">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold-500 mx-auto mb-4"></div>
                    <p className="text-sehatti-warm-gray-600">Loading chat...</p>
                  </div>
                </div>
              ) : (
                <ChatInterface
                  chatRoom={activeChat}
                  onBack={handleBackToNotifications}
                  onStartCall={handleStartCall}
                />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ConsultantChat 