import { useGetMeQuery } from '@/store/api'
import { useAppSelector } from '@/store/store'
import { selectAuth } from '@/features/auth/auth-slice'
import { Spinner } from '@/components/ui/Spinner'
import { Alert } from '@/components/ui/Alert'
import { Navigate } from 'react-router-dom'
import ConsultantDashboard from './dashboards/ConsultantDashboard'

const RoleDashboard = () => {
  const localAuthState = useAppSelector(selectAuth)
  
  // First try to use local auth state (like our working ProtectedRoute)
  if (localAuthState.isAuthenticated && localAuthState.user) {
    const user = localAuthState.user
    const userRole = user.role
    
    // Route to appropriate dashboard based on role
    switch (userRole) {
        case 'SYSTEM_ADMIN':
    // Redirect system admin to admin dashboard
    return <Navigate to="/admin/dashboard" replace />
      case 'HR_ADMIN':
        // Redirect HR admin to HR dashboard
        return <Navigate to="/hr/dashboard" replace />
      case 'EMPLOYEE':
        // Check if employee has completed onboarding
        const isOnboardingCompleted = localStorage.getItem('sehatti-onboarding-completed') === 'true'
        
        if (!isOnboardingCompleted) {
          // Also check the detailed progress for backward compatibility
          const onboardingProgress = localStorage.getItem('sehatti-onboarding-progress')
          let isOnboardingComplete = false
          
          if (onboardingProgress) {
            try {
              const progress = JSON.parse(onboardingProgress)
              isOnboardingComplete = progress.currentStep === 'completed' || progress.completedAt
              
              // If completed, set the completion flag for future checks
              if (isOnboardingComplete) {
                localStorage.setItem('sehatti-onboarding-completed', 'true')
              }
            } catch (error) {
              console.error('Failed to parse onboarding progress:', error)
            }
          }
          
          // Redirect to onboarding if not completed
          if (!isOnboardingComplete) {
            return <Navigate to="/employee/onboarding" replace />
          }
        }
        
        // Employee has completed onboarding, go to dashboard
        return <Navigate to="/employee/dashboard" replace />
      case 'CONSULTANT':
        return <ConsultantDashboard user={user} />
      default:
        return (
          <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
            <Alert variant="destructive" title="Invalid Role">
              Your user role '{userRole}' is not recognized. Please contact support.
            </Alert>
          </div>
        )
    }
  }

  // Fallback: try API query if local auth fails
  const { data: userData, isLoading, error } = useGetMeQuery()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
        <Alert variant="destructive" title="Error Loading Dashboard">
          Failed to load user data. Please try refreshing the page.
        </Alert>
      </div>
    )
  }

  const user = userData.data.user
  const userRole = user.role

  // Route to appropriate dashboard based on role
  switch (userRole) {
    case 'SYSTEM_ADMIN':
      // Redirect system admin to admin dashboard
      return <Navigate to="/admin/dashboard" replace />
    case 'HR_ADMIN':
      // Redirect HR admin to HR dashboard
      return <Navigate to="/hr/dashboard" replace />
    case 'EMPLOYEE':
      // Check if employee has completed onboarding
      const isOnboardingCompletedFallback = localStorage.getItem('sehatti-onboarding-completed') === 'true'
      
      if (!isOnboardingCompletedFallback) {
        // Also check the detailed progress for backward compatibility
        const onboardingProgressFallback = localStorage.getItem('sehatti-onboarding-progress')
        let isOnboardingCompleteFallback = false
        
        if (onboardingProgressFallback) {
          try {
            const progress = JSON.parse(onboardingProgressFallback)
            isOnboardingCompleteFallback = progress.currentStep === 'completed' || progress.completedAt
            
            // If completed, set the completion flag for future checks
            if (isOnboardingCompleteFallback) {
              localStorage.setItem('sehatti-onboarding-completed', 'true')
            }
          } catch (error) {
            console.error('Failed to parse onboarding progress:', error)
          }
        }
        
        // Redirect to onboarding if not completed
        if (!isOnboardingCompleteFallback) {
          return <Navigate to="/employee/onboarding" replace />
        }
      }
      
      // Employee has completed onboarding, go to dashboard
      return <Navigate to="/employee/dashboard" replace />
    case 'CONSULTANT':
      return <ConsultantDashboard user={user} />
    default:
      return (
        <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
          <Alert variant="destructive" title="Invalid Role">
            Your user role '{userRole}' is not recognized. Please contact support.
          </Alert>
        </div>
      )
  }
}

export default RoleDashboard