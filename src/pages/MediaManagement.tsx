import React, { useState } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card } from '@/components/ui/Card';
import { <PERSON><PERSON>, TabList, Tab, TabPanels, TabPanel } from '@/components/ui/Tabs';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { GradientText } from '@/components/ui/GradientText';
import { 
  FaVideo, 
  FaNewspaper, 
  FaLayerGroup, 
  FaPlus,
  FaChartBar,
  FaGlobe,
  FaEye
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import HubManagement from '@/components/media/HubManagement';
import ArticleManagement from '@/components/media/ArticleManagement';
import VideoManagement from '@/components/media/VideoManagement';
import { MediaList } from '@/components/media/MediaList';
import { MediaAPI } from '@/services/mediaApi';
import { useGetArticlesQuery } from '@/services/articlesApi';

const MediaManagement: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('hubs');

  // Fetch real data from APIs
  const { data: hubsData, isLoading: hubsLoading, error: hubsError } = MediaAPI.useGetHubsQuery({});
  const { data: articlesData, isLoading: articlesLoading } = useGetArticlesQuery({});
  const { data: mediaData, isLoading: mediaLoading } = MediaAPI.useListContentQuery({});

  // Debug logging
  console.log('🔍 Debug - Hub data:', { hubsData, hubsLoading, hubsError });
  console.log('🔍 Debug - Articles data:', { articlesData, articlesLoading });
  console.log('🔍 Debug - Media data:', { mediaData, mediaLoading });

  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token");
    window.location.href = "/login";
  };

  const tabs = [
    {
      id: 'hubs',
      label: 'Content Hubs',
      icon: FaLayerGroup,
      description: 'Manage content hubs and their multilingual content',
      color: 'text-blue-600'
    },
    {
      id: 'browse',
      label: 'Browse Content',
      icon: FaEye,
      description: 'Browse and view all content with video player and article reader',
      color: 'text-purple-600'
    },
    {
      id: 'articles',
      label: 'Articles',
      icon: FaNewspaper,
      description: 'Create and manage written content',
      color: 'text-green-600'
    },
    {
      id: 'media',
      label: 'Videos & Media',
      icon: FaVideo,
      description: 'Upload, stream, and manage video content',
      color: 'text-orange-600'
    }
  ];

  // Calculate real stats from API data with fallbacks
  const totalHubs = hubsData?.total || hubsData?.data?.length || 0;
  const publishedArticles = articlesData?.data?.filter((article: any) => article.status === 'published').length || 0;
  const videoContent = mediaData?.data?.length || 0;
  const totalContent = (articlesData?.data?.length || 0) + (mediaData?.data?.length || 0);
  
  // Debug: Log the actual values being calculated
  console.log('📊 Stats calculation:', {
    totalHubs,
    publishedArticles,
    videoContent,
    totalContent,
    hubsDataStructure: hubsData,
    articlesDataStructure: articlesData,
    mediaDataStructure: mediaData
  });

  const stats = [
    { label: 'Total Hubs', value: totalHubs.toString(), icon: FaLayerGroup, color: 'text-blue-600' },
    { label: 'Total Content', value: totalContent.toString(), icon: FaEye, color: 'text-purple-600' },
    { label: 'Published Articles', value: publishedArticles.toString(), icon: FaNewspaper, color: 'text-green-600' },
    { label: 'Video Content', value: videoContent.toString(), icon: FaVideo, color: 'text-orange-600' }
  ];

  return (
    <AdminLayout
      user={user}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      containerSize="xl"
    >
      <div className="space-y-6">
        {/* Header */}
        <header className="space-y-4">
          <div className="text-center sm:text-left">
            <h1 className="text-3xl lg:text-4xl font-bold mb-2">
              <GradientText gradient="goldRich" size="3xl" className="lg:text-4xl">
                Media Management
              </GradientText>
            </h1>
            <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Manage content hubs, browse media, and create articles with multilingual support
            </p>
          </div>
        </header>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-white dark:bg-sehatti-warm-gray-800 ${stat.color}`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      {stat.value}
                    </p>
                    <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Main Content with Tabs */}
        <Card className="overflow-hidden shadow-lg border-sehatti-warm-gray-200/60 dark:border-sehatti-warm-gray-700/60">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            {/* Tab Navigation */}
            <div className="p-4 lg:p-6 bg-gradient-to-r from-sehatti-gold-50/30 to-white dark:from-sehatti-gold-900/10 dark:to-sehatti-warm-gray-950 border-b border-sehatti-warm-gray-200/60 dark:border-sehatti-warm-gray-700/60">
              <TabList className="mb-4 bg-white/50 dark:bg-sehatti-warm-gray-900/50 rounded-xl p-1 backdrop-blur-sm overflow-x-auto">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <Tab
                      key={tab.id}
                      value={tab.id}
                      className="flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-200 min-w-0 flex-shrink-0"
                    >
                      <Icon className={`w-4 h-4 flex-shrink-0 ${tab.color}`} />
                      <span className="font-medium text-sm sm:text-base whitespace-nowrap">
                        {tab.label}
                      </span>
                    </Tab>
                  );
                })}
              </TabList>

              {/* Tab Description */}
              <div className="text-center sm:text-left">
                {tabs.map((tab) => (
                  activeTab === tab.id && (
                    <p key={tab.id} className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 text-sm">
                      {tab.description}
                    </p>
                  )
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <TabPanels className="p-0">
              <TabPanel value="hubs" className="p-4 lg:p-6">
                <HubManagement />
              </TabPanel>

              <TabPanel value="browse" className="p-4 lg:p-6">
                <MediaList />
              </TabPanel>

              <TabPanel value="articles" className="p-4 lg:p-6">
                <ArticleManagement />
              </TabPanel>

              <TabPanel value="media" className="p-4 lg:p-6">
                <VideoManagement />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default MediaManagement; 