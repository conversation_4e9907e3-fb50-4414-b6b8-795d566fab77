import { useResetPasswordFunc } from '@/hooks/useResetPasswordFunc';
import { 
  GlassCard, 
  GlassCardHeader, 
  GlassCardContent,
  Button,
  PasswordInput,
  Divider,
  Link,
  GradientText,
  Alert
} from '@/components/ui';

// Lock Icon Component
const LockIcon = () => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
    <path d="M6 10V8C6 5.79 7.79 4 10 4H14C16.21 4 18 5.79 18 8V10C19.1 10 20 10.9 20 12V20C20 21.1 19.1 22 18 22H6C4.9 22 4 21.1 4 20V12C4 10.9 4.9 10 6 10ZM8 8V10H16V8C16 6.9 15.1 6 14 6H10C8.9 6 8 6.9 8 8Z" fill="white"/>
    <circle cx="12" cy="16" r="2" fill="white"/>
  </svg>
);

// Security Icon Component
const SecurityIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Success Icon Component
const SuccessIcon = () => (
  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
    <path d="M20 6L9 17l-5-5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow Left Icon Component
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M19 12H5m7-7l-7 7 7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow Right Icon Component  
const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export default function ResetPassword() {
  const {
    resetData,
    tokenValid,
    showSuccess,
    isLoading,
    handleInputChange,
    handleSubmit,
    navigateToLogin,
  } = useResetPasswordFunc();

  // If no token or invalid token, show error
  if (tokenValid === false) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
        <GlassCard variant="default" decorativeTop={true} className="w-full max-w-[420px]">
          <GlassCardHeader>
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <GradientText gradient="goldRich" size="2xl" className="font-bold mb-2">
              Invalid Reset Link
            </GradientText>
            <p className="text-[15px] text-gray-500 font-normal">
              This password reset link is invalid or has expired
            </p>
          </GlassCardHeader>
          <GlassCardContent>
            <Link href="/" variant="button" className="w-full text-center">
              ← Back to Login
            </Link>
          </GlassCardContent>
        </GlassCard>
      </div>
    );
  }

  // Loading state while verifying token
  if (tokenValid === null) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
        <GlassCard variant="default" decorativeTop={true} className="w-full max-w-[420px]">
          <GlassCardHeader>
            <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
            <GradientText gradient="goldRich" size="2xl" className="font-bold mb-2">
              Verifying Reset Link
            </GradientText>
            <p className="text-[15px] text-gray-500 font-normal">
              Please wait while we verify your reset link...
            </p>
          </GlassCardHeader>
        </GlassCard>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Reset Password Card */}
      <GlassCard 
        variant="default" 
        decorativeTop={true}
        className="w-full max-w-[420px]"
      >
        <GlassCardHeader>
          <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6 shadow-[0_8px_20px_rgba(210,179,122,0.3)]">
            {showSuccess ? <SuccessIcon /> : <LockIcon />}
          </div>
          <GradientText 
            gradient="goldRich" 
            size="2xl" 
            className="font-bold mb-2"
          >
            {showSuccess ? "Password Reset Successful" : "Reset Password"}
          </GradientText>
          <p className="text-[15px] text-gray-500 font-normal leading-relaxed">
            {showSuccess 
              ? "Your password has been successfully reset. You can now login with your new password."
              : "Create a new secure password for your account"
            }
          </p>
        </GlassCardHeader>

        <GlassCardContent>
          {showSuccess && (
            <Alert 
              variant="default" 
              className="mb-6"
              title="Success"
              description="Your password has been reset successfully!"
            />
          )}

          {!showSuccess ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* New Password Field */}
              <PasswordInput
                label={
                  <>
                    New Password <span className="text-red-500">*</span>
                  </>
                }
                placeholder="Enter your new password"
                value={resetData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                autoComplete="new-password"
              />

              {/* Confirm Password Field */}
              <PasswordInput
                label={
                  <>
                    Confirm New Password <span className="text-red-500">*</span>
                  </>
                }
                placeholder="Confirm your new password"
                value={resetData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                autoComplete="new-password"
              />

              {/* Password Requirements */}
              <div className="p-4 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
                <h4 className="text-sm font-semibold text-[#374151] mb-2">
                  Password Requirements:
                </h4>
                <ul className="text-xs text-[#6b7280] leading-relaxed space-y-1 list-disc list-inside">
                  <li>At least 8 characters long</li>
                  <li>Contains uppercase and lowercase letters</li>
                  <li>Contains at least one number</li>
                  <li>Contains at least one special character</li>
                  <li>Both passwords must match</li>
                </ul>
              </div>

              {/* Reset Password Button */}
              <Button
                type="submit"
                variant="default"
                size="default"
                fullWidth={true}
                isLoading={isLoading}
                rightIcon={!isLoading ? <ArrowRightIcon /> : undefined}
                className="mb-6"
              >
                {isLoading ? "Resetting Password..." : "Reset Password"}
              </Button>

              {/* Divider */}
              <Divider label="OR" />

              {/* Back to Login Link */}
              <Link
                href="/"
                variant="button"
                className="w-full text-center"
                leftIcon={<ArrowLeftIcon />}
              >
                Back to Login
              </Link>
            </form>
          ) : (
            <div className="space-y-6">
              {/* Success Actions */}
              <div className="space-y-3">
                <Button
                  onClick={navigateToLogin}
                  variant="default"
                  size="default"
                  fullWidth={true}
                  rightIcon={<ArrowRightIcon />}
                >
                  Go to Login
                </Button>
              </div>
            </div>
          )}
        </GlassCardContent>

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 mt-8 p-3 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="text-xs text-[#6b7280] font-medium">
            Secure Password Reset
          </span>
        </div>
      </GlassCard>

      {/* Privacy Disclaimer and Copyright */}
      <div className="mt-8 text-center max-w-[420px] px-5">
        <div className="p-5 bg-[rgba(210,179,122,0.03)] rounded-2xl border border-[rgba(210,179,122,0.1)] mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <SecurityIcon />
            <span className="text-sm font-semibold text-[#d2b37a]">
              Privacy & Security
            </span>
          </div>
          <p className="text-[13px] text-gray-500 leading-relaxed">
            Sehatti respects your privacy and protects your information with the highest standards of security and confidentiality
          </p>
        </div>
        
        <div className="p-4 bg-[rgba(0,0,0,0.02)] rounded-xl border border-[rgba(0,0,0,0.05)]">
          <p className="text-xs text-gray-400 font-medium">
            All rights reserved © Sehatti. An Elevate Academy ecosystem.
          </p>
        </div>
      </div>
    </div>
  );
} 