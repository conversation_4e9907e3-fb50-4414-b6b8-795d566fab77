import { 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2, 
  Calendar, 
  Globe,
  MapPin,
  Users
} from 'lucide-react'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../components/ui/Dialog'
import { Input } from '../components/ui/Input'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/Table'
import {
  RadixSelect as Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui'
import { AdminLayout } from '../components/ui'

import { useState } from 'react'
import toast from 'react-hot-toast'

import { 
  useGetInvitationsQuery, 
  useDeleteInvitationMutation,
  type Invitation 
} from '@/store/api'
import InvitationForm from './InvitationForm'
import InvitationDetailsModal from './InvitationDetailsModal'
import { useAppSelector } from '../store/store'
import { selectAuth } from '../features/auth/auth-slice'

const InvitationManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [isViewOpen, setIsViewOpen] = useState(false)
  const [isDeleteOpen, setIsDeleteOpen] = useState(false)
  const [selectedInvitation, setSelectedInvitation] = useState<Invitation | null>(null)

  const itemsPerPage = 10

  // Get current user from auth store
  const authState = useAppSelector(selectAuth)
  const currentUser = authState?.user
  const companyId = currentUser?.companyId || currentUser?.company_id

  // API hooks
  const { 
    data: invitationsData, 
    isLoading, 
    error 
  } = useGetInvitationsQuery({
    company_id: companyId,
    page: currentPage,
    limit: itemsPerPage,
    status: statusFilter === 'all' ? undefined : statusFilter
  }, {
    skip: !companyId // Skip the query if no company ID is available
  })

  const [deleteInvitation, { isLoading: isDeleting }] = useDeleteInvitationMutation()

  const invitations = invitationsData?.data?.data || []
  const totalPages = Math.ceil((invitationsData?.data?.pagination?.totalRecords || 0) / itemsPerPage)

  // Filter invitations based on search and status
  const filteredInvitations = invitations.filter((invitation) => {
    const matchesSearch = 
      invitation.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.sessionType?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || invitation.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleEdit = (invitation: Invitation) => {
    setSelectedInvitation(invitation)
    setIsEditOpen(true)
  }

  const handleView = (invitation: Invitation) => {
    setSelectedInvitation(invitation)
    setIsViewOpen(true)
  }

  const handleDeleteClick = (invitation: Invitation) => {
    setSelectedInvitation(invitation)
    setIsDeleteOpen(true)
  }

  const handleDelete = async () => {
    if (!selectedInvitation || !companyId) {
      toast.error('Missing required information for deletion')
      return
    }

    try {
      await deleteInvitation({ 
        id: selectedInvitation.id, 
        company_id: companyId 
      }).unwrap()
      toast.success('Invitation deleted successfully')
      setIsDeleteOpen(false)
      setSelectedInvitation(null)
    } catch (error: any) {
      console.error('Delete error:', error)
      toast.error(error?.data?.message || 'Failed to delete invitation')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    return type === 'online' ? <Globe className="h-4 w-4" /> : <MapPin className="h-4 w-4" />
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <p className="text-red-600">Failed to load invitations</p>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    )
  }

  if (!companyId) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <p className="text-red-600">Company ID is required to view invitations</p>
              <p className="text-gray-600 mt-2">Please contact your administrator</p>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Invitation Management</h1>
            <p className="text-gray-600">Manage and organize invitations</p>
          </div>
          
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700">
                <Plus className="mr-2 h-4 w-4" />
                Create Invitation
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Invitation</DialogTitle>
              </DialogHeader>
              <InvitationForm 
                onSuccess={() => setIsCreateOpen(false)}
                onCancel={() => setIsCreateOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search invitations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Invitations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Invitations ({filteredInvitations.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
                ))}
              </div>
            ) : filteredInvitations.length === 0 ? (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No invitations found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters' 
                    : 'Get started by creating your first invitation'}
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <Button 
                    onClick={() => setIsCreateOpen(true)}
                    className="mt-4 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create Invitation
                  </Button>
                )}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>#</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Session Type</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInvitations.map((invitation, index) => (
                    <TableRow key={invitation._id}>
                      <TableCell className="font-medium">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{invitation.title}</div>
                        <div className="text-sm text-gray-500 truncate max-w-[200px]">
                          {invitation.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(invitation.type)}
                          <span className="capitalize">{invitation.type}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="capitalize">{invitation.sessionType}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span>{invitation.recipients?.length || 0}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(invitation.status)}>
                          {invitation.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {new Date(invitation.time).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleView(invitation)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(invitation)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClick(invitation)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
              {Math.min(currentPage * itemsPerPage, invitationsData?.data?.pagination?.totalRecords || 0)} of{' '}
              {invitationsData?.data?.pagination?.totalRecords || 0} results
            </p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Edit Dialog */}
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Invitation</DialogTitle>
            </DialogHeader>
            {selectedInvitation && (
              <InvitationForm 
                invitation={selectedInvitation}
                onSuccess={() => setIsEditOpen(false)}
                onCancel={() => setIsEditOpen(false)}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* View Dialog */}
        <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Invitation Details</DialogTitle>
            </DialogHeader>
            {selectedInvitation && (
              <InvitationDetailsModal invitation={selectedInvitation} />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Invitation</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p>Are you sure you want to delete this invitation? This action cannot be undone.</p>
              <div className="flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteOpen(false)}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  )
}

export default InvitationManagement
