import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { 
  FaBuilding, 
  FaQuestionCircle, 
  FaBroadcastTower, 
  FaVideo, 
  FaRoad, 
  FaComments, 
  FaCog,
  FaArrowLeft
} from "react-icons/fa";
import { AdminLayout } from "../components/layout/AdminLayout";
import { Card } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import { GradientText } from "@/components/ui/GradientText";
import { useAppSelector } from "../store/store";
import { selectAuth } from "../features/auth/auth-slice";
import { useGetCompaniesQuery } from "@/store/api";
import { useGetCompanySettingsByCompanyIdQuery } from '@/store/api';
import QuestionsManager from "../components/qa/QuestionsManager";
import BroadcastManager from "../components/qa/BroadcastManager";

const CorporateSettings: React.FC = () => {
  const { companyId, id } = useParams<{ companyId?: string; id?: string }>();
  const actualCompanyId = companyId || id;
  
  const navigate = useNavigate();
  const authState = useAppSelector(selectAuth);
  const user = authState.user;
  
  const [activeTab, setActiveTab] = useState<string>("questions");

  const { data: corporatesData, isLoading } = useGetCompaniesQuery();
  
  const corporate = corporatesData?.items?.find((c: any) => 
    c.company_id === actualCompanyId || c.id === actualCompanyId
  );

  const { 
    data: companySettings, 
    isLoading: companySettingsLoading, 
    error: companySettingsError 
  } = useGetCompanySettingsByCompanyIdQuery(actualCompanyId || '', {
    skip: !actualCompanyId
  });

  if (!user) {
    navigate("/login");
    return null;
  }

  if (!actualCompanyId) {
    return (
      <AdminLayout user={user as any}>
        <div className="text-center py-8">
          <p className="text-red-600">Corporate ID is required</p>
          <Button 
            variant="outline" 
            onClick={() => navigate("/admin/corporates")}
            className="mt-4"
          >
            Back to Corporate Management
          </Button>
        </div>
      </AdminLayout>
    );
  }

  if (companySettingsLoading) {
    return (
      <AdminLayout user={user as any}>
        <div className="p-6 max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (companySettingsError) {
    const errorMessage = companySettingsError && typeof companySettingsError === 'object' && 'data' in companySettingsError 
      ? (companySettingsError.data as any)?.detail || 'Failed to load corporate settings'
      : 'Network error occurred';
      
    return (
      <AdminLayout user={user as any}>
        <div className="p-6 max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="text-red-600 text-lg font-medium">Failed to load corporate settings</div>
            <div className="text-gray-600 mt-2">{errorMessage}</div>
            <div className="text-sm text-gray-500 mt-2">Company ID: {actualCompanyId}</div>
            <div className="flex gap-4 justify-center mt-6">
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
              <Button 
                variant="outline" 
                onClick={() => navigate("/admin/corporates")}
              >
                Back to Corporate Management
              </Button>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const settings = companySettings;

  const tabs = [
    { 
      id: "questions", 
      label: "Questions", 
      icon: FaQuestionCircle,
      description: "Manage assessment questions and assignments"
    },
    { 
      id: "broadcast", 
      label: "Broadcast", 
      icon: FaBroadcastTower,
      description: "Configure broadcast and communication settings"
    },
    { 
      id: "webinar", 
      label: "Webinars", 
      icon: FaVideo,
      description: "Manage webinar schedules and configurations"
    },
    { 
      id: "roadmap", 
      label: "Roadmap", 
      icon: FaRoad,
      description: "Set up development and progress roadmaps"
    },
    { 
      id: "feedback", 
      label: "Feedback", 
      icon: FaComments,
      description: "Configure feedback collection and management"
    },
    { 
      id: "settings", 
      label: "Settings", 
      icon: FaCog,
      description: "General corporate settings and preferences"
    },
  ];

  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token");
    navigate("/login");
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "questions":
        return (
          <QuestionsManager 
            corporateId={actualCompanyId}
            className="mt-6"
          />
        );
      
      case "broadcast":
        return (
          <BroadcastManager 
            corporateId={actualCompanyId}
            className="mt-6"
          />
        );
      
      case "webinar":
        return (
          <Card className="p-8">
            <div className="text-center">
              <FaVideo className="mx-auto h-16 w-16 text-sehatti-gold-600 mb-4" />
              <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">Webinar Management</h3>
              <p className="text-sehatti-warm-gray-600 mb-6">
                Schedule and manage webinars for corporate training and communication.
              </p>
              <Button variant="outline">Configure Webinars</Button>
            </div>
          </Card>
        );
      
      case "roadmap":
        return (
          <Card className="p-8">
            <div className="text-center">
              <FaRoad className="mx-auto h-16 w-16 text-sehatti-gold-600 mb-4" />
              <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">Roadmap Configuration</h3>
              <p className="text-sehatti-warm-gray-600 mb-6">
                Set up development and progress roadmaps for employee growth.
              </p>
              <Button variant="outline">Configure Roadmap</Button>
            </div>
          </Card>
        );
      
      case "feedback":
        return (
          <Card className="p-8">
            <div className="text-center">
              <FaComments className="mx-auto h-16 w-16 text-sehatti-gold-600 mb-4" />
              <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">Feedback Management</h3>
              <p className="text-sehatti-warm-gray-600 mb-6">
                Configure feedback collection and analysis settings.
              </p>
              <Button variant="outline">Configure Feedback</Button>
            </div>
          </Card>
        );
      
      case "settings":
        return (
          <Card className="p-8">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <FaCog className="mx-auto h-16 w-16 text-sehatti-gold-600 mb-4" />
                <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">General Settings</h3>
                <p className="text-sehatti-warm-gray-600">
                  General corporate settings and preferences.
                </p>
              </div>

              {settings && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Company Information */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-sehatti-warm-gray-900">Company Information</h4>
                    
                    <div>
                      <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                        Company ID
                      </label>
                      <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                        {settings.companyId || actualCompanyId}
                      </div>
                    </div>

                    {(settings as any).companyName && (
                      <div>
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                          Company Name
                        </label>
                        <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                          {(settings as any).companyName}
                        </div>
                      </div>
                    )}

                    {(settings as any).contactEmail && (
                      <div>
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                          Contact Email
                        </label>
                        <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                          {(settings as any).contactEmail}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Status & Settings */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-sehatti-warm-gray-900">Status & Configuration</h4>
                    
                    <div>
                      <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                        Status
                      </label>
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={(settings as any).status === 'Active' ? 'success' : 
                                  (settings as any).status === 'Pending' ? 'warning' : 'secondary'}
                        >
                          {(settings as any).status || 'Unknown'}
                        </Badge>
                      </div>
                    </div>

                    {(settings as any).contractStartDate && (
                      <div>
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                          Contract Start Date
                        </label>
                        <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                          {new Date((settings as any).contractStartDate).toLocaleDateString()}
                        </div>
                      </div>
                    )}

                    {(settings as any).contractEndDate && (
                      <div>
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                          Contract End Date
                        </label>
                        <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                          {new Date((settings as any).contractEndDate).toLocaleDateString()}
                        </div>
                      </div>
                    )}

                    {typeof (settings as any).maxEmployees === 'number' && (
                      <div>
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 mb-1">
                          Max Employees
                        </label>
                        <div className="text-sm text-sehatti-warm-gray-900 bg-sehatti-warm-gray-50 p-2 rounded">
                          {(settings as any).maxEmployees}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="pt-6 border-t">
                <Button variant="outline" className="w-full">
                  Edit Settings
                </Button>
              </div>
            </div>
          </Card>
        );
      
      default:
        return (
          <Card className="p-8">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-sehatti-warm-gray-900 mb-2">
                Tab Under Development
              </h3>
              <p className="text-sehatti-warm-gray-600">
                This feature is currently being developed.
              </p>
            </div>
          </Card>
        );
    }
  };

  return (
    <AdminLayout user={user as any}>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/admin/corporates")}
              className="flex items-center gap-2"
            >
              <FaArrowLeft className="w-4 h-4" />
              Back to Corporates
            </Button>
          </div>
          
                     <div className="flex items-center gap-3 mb-2">
             <FaBuilding className="text-sehatti-gold-600 text-2xl" />
             <h1 className="text-3xl font-bold bg-gradient-to-r from-sehatti-gold-600 to-sehatti-gold-400 bg-clip-text text-transparent">
               Corporate Settings
             </h1>
           </div>
          
          <div className="flex items-center gap-4 text-sehatti-warm-gray-600">
            <span>Company ID: {actualCompanyId}</span>
                         {corporate?.name && (
               <>
                 <span>•</span>
                 <span>{corporate.name}</span>
               </>
             )}
                         {(settings as any)?.status && (
               <>
                 <span>•</span>
                 <Badge 
                   variant={(settings as any).status === 'Active' ? 'success' : 
                           (settings as any).status === 'Pending' ? 'warning' : 'secondary'}
                 >
                   {(settings as any).status}
                 </Badge>
               </>
             )}
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b border-sehatti-warm-gray-200 mb-8">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? "border-sehatti-gold-500 text-sehatti-gold-600"
                      : "border-transparent text-sehatti-warm-gray-500 hover:text-sehatti-warm-gray-700 hover:border-sehatti-warm-gray-300"
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div>
          {renderTabContent()}
        </div>
      </div>
    </AdminLayout>
  );
};

export default CorporateSettings; 