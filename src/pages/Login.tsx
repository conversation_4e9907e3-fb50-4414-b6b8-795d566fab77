import { 
  GlassCard, 
  GlassCardHeader, 
  GlassCardContent, 
  GlassCardFooter,
  Input,
  PasswordInput,
  Button,
  Checkbox,
  Link,
  Divider,
  GradientText,
  Alert
} from "@/components/ui";
import { useLoginFunc } from "@/hooks/useLoginFunc";
import { getErrorMessage } from '@/utils/errorUtils';

// Security icon component
const SecurityIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow right icon component
const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M5 12h14m-7-7l7 7-7 7"/>
  </svg>
);

export default function Login() {
  const {
    loginData,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
  } = useLoginFunc();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Login Card */}
      <GlassCard 
        variant="default" 
        decorativeTop={true}
        className="w-full max-w-[420px]"
      >
        <GlassCardHeader>
          <GradientText 
            gradient="goldRich" 
            size="2xl" 
            className="font-bold mb-2"
          >
            Welcome Back
          </GradientText>
          <p className="text-[15px] text-gray-500 font-normal">
            Sign in to your enterprise account
          </p>
        </GlassCardHeader>

        <GlassCardContent>
          {error ? (
            <Alert 
              variant="destructive" 
              className="mb-6"
              title="Login Error"
              description={getErrorMessage(error)}
            />
          ) : null}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <Input
              label="Email Address"
              type="email"
              placeholder="Enter your email"
              value={loginData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              autoComplete="email"
            />

            {/* Password Field */}
            <PasswordInput
              label="Password"
              placeholder="Enter your password"
              value={loginData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              autoComplete="current-password"
            />

            {/* Remember Me & Forgot Password */}
            <div className="flex justify-between items-center">
              <Checkbox
                label="Remember me"
                checked={loginData.rememberMe}
                onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
              />
              <Link 
                href="/forgot-password"
                variant="default"
                size="sm"
              >
                Forgot Password?
              </Link>
            </div>

            {/* Login Button */}
            <Button
              type="submit"
              variant="default"
              size="default"
              fullWidth={true}
              isLoading={isLoading}
              rightIcon={!isLoading ? <ArrowRightIcon /> : undefined}
              className="mb-6"
            >
              {isLoading ? "Signing In..." : "Sign In"}
            </Button>

            {/* Divider */}
            <Divider label="OR" />

            {/* Register Link */}
            <Link
              href="/register"
              variant="button"
              className="w-full text-center"
            >
              Create New Account
            </Link>
          </form>
        </GlassCardContent>

        <GlassCardFooter>
          {/* Security Badge */}
          <div className="flex items-center justify-center gap-2 p-3 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
            <SecurityIcon />
            <span className="text-xs text-gray-500 font-medium">
              Secure Enterprise Authentication
            </span>
          </div>
        </GlassCardFooter>
      </GlassCard>

      {/* Privacy Disclaimer and Copyright */}
      <div className="mt-8 text-center max-w-[420px] px-5">
        <div className="p-5 bg-[rgba(210,179,122,0.03)] rounded-2xl border border-[rgba(210,179,122,0.1)] mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <SecurityIcon />
            <span className="text-sm font-semibold text-[#d2b37a]">
              Privacy & Security
            </span>
          </div>
          <p className="text-[13px] text-gray-500 leading-relaxed">
            Sehatti respects your privacy and protects your information with the highest standards of security and confidentiality
          </p>
        </div>
        
        <div className="p-4 bg-[rgba(0,0,0,0.02)] rounded-xl border border-[rgba(0,0,0,0.05)]">
          <p className="text-xs text-gray-400 font-medium">
            All rights reserved © Sehatti. An Elevate Academy ecosystem.
          </p>
        </div>
      </div>


    </div>
  );
} 