import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from 'react-hot-toast'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'

import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Textarea } from '../components/ui/Textarea'
import { Label } from '../components/ui/Label'
import { 
  RadixSelect as Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../components/ui'
import { Calendar } from '../components/ui/Calendar'
import { Popover, PopoverContent, PopoverTrigger } from '../components/ui/Popover'
import { cn } from '../lib/utils'

import {
  useCreateInvitationMutation,
  useUpdateInvitationMutation,
  type Invitation,
  type CreateInvitationRequest,
} from '@/store/api'

// Validation schema
const invitationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['online', 'physical'], {
    required_error: 'Type is required',
  }),
  sessionType: z.string().min(1, 'Session type is required'),
  status: z.enum(['active', 'inactive']).default('active'),
  language: z.enum(['en', 'ar', 'ur'], {
    required_error: 'Language is required',
  }),
  time: z.string().min(1, 'Date and time is required'),
  consultantId: z.string().optional(),
  eventManagerId: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  recipients: z.array(z.string()).default([]),
  recipientBase: z.array(z.string()).default(['all']),
})

type InvitationFormData = z.infer<typeof invitationSchema>

interface InvitationFormProps {
  invitation?: Invitation
  onSuccess?: () => void
  onCancel?: () => void
}

const sessionTypes = [
  'Consultation',
  'Workshop',
  'Seminar',
  'Training',
  'Meeting',
  'Presentation',
  'Conference',
  'Webinar',
]

const languages = [
  { value: 'en', label: 'English' },
  { value: 'ar', label: 'Arabic' },
  { value: 'ur', label: 'Urdu' },
]

const InvitationForm: React.FC<InvitationFormProps> = ({
  invitation,
  onSuccess,
  onCancel,
}) => {
  const [date, setDate] = useState<Date>()
  const [time, setTime] = useState('')
  
  const isEditing = !!invitation

  // API hooks
  const [createInvitation, { isLoading: isCreating }] = useCreateInvitationMutation()
  const [updateInvitation, { isLoading: isUpdating }] = useUpdateInvitationMutation()
  const { data: consultantsData } = useGetConsultantsQuery({ page: 1, limit: 100 })

  const consultants = consultantsData?.data?.data?.filter(c => c.status === 'active') || []

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<InvitationFormData>({
    resolver: zodResolver(invitationSchema),
    defaultValues: {
      title: '',
      description: '',
      type: 'online',
      sessionType: '',
      status: 'active',
      language: 'en',
      time: '',
      consultantId: '',
      eventManagerId: '',
      recipients: [],
      recipientBase: ['all'],
    },
  })

  const watchedType = watch('type')

  // Initialize form when editing
  useEffect(() => {
    if (invitation) {
      reset({
        title: invitation.title,
        description: invitation.description,
        type: invitation.type,
        sessionType: invitation.sessionType,
        status: invitation.status,
        language: invitation.language,
        time: invitation.time,
        consultantId: invitation.consultantId || '',
        eventManagerId: invitation.eventManagerId || '',
        recipients: invitation.recipients || [],
        recipientBase: invitation.recipientBase || ['all'],
      })

      // Set date and time from invitation
      if (invitation.time) {
        const invitationDate = new Date(invitation.time)
        setDate(invitationDate)
        setTime(format(invitationDate, 'HH:mm'))
      }
    }
  }, [invitation, reset])

  // Update time field when date or time changes
  useEffect(() => {
    if (date && time) {
      const [hours, minutes] = time.split(':')
      const dateTime = new Date(date)
      dateTime.setHours(parseInt(hours), parseInt(minutes))
      setValue('time', dateTime.toISOString())
    }
  }, [date, time, setValue])

  const onSubmit = async (data: InvitationFormData) => {
    try {
      if (isEditing && invitation) {
        await updateInvitation({
          id: invitation._id,
          data: {
            ...data,
            latitude: data.latitude || undefined,
            longitude: data.longitude || undefined,
          },
        }).unwrap()
        toast.success('Invitation updated successfully')
      } else {
        await createInvitation({
          ...data,
          latitude: data.latitude || undefined,
          longitude: data.longitude || undefined,
        } as CreateInvitationRequest).unwrap()
        toast.success('Invitation created successfully')
      }
      onSuccess?.()
    } catch (error: any) {
      toast.error(error?.data?.message || 'An error occurred')
    }
  }

  const isLoading = isCreating || isUpdating

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">Title *</Label>
        <Controller
          name="title"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              id="title"
              placeholder="Enter invitation title"
              className={errors.title ? 'border-red-500' : ''}
            />
          )}
        />
        {errors.title && (
          <p className="text-sm text-red-600">{errors.title.message}</p>
        )}
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              id="description"
              placeholder="Enter invitation description"
              rows={4}
              className={errors.description ? 'border-red-500' : ''}
            />
          )}
        />
        {errors.description && (
          <p className="text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>

      {/* Type and Session Type Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="type">Type *</Label>
          <Controller
            name="type"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="online">Online</SelectItem>
                  <SelectItem value="physical">Physical</SelectItem>
                </SelectContent>
              </Select>
            )}
          />
          {errors.type && (
            <p className="text-sm text-red-600">{errors.type.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="sessionType">Session Type *</Label>
          <Controller
            name="sessionType"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger className={errors.sessionType ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select session type" />
                </SelectTrigger>
                <SelectContent>
                  {sessionTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.sessionType && (
            <p className="text-sm text-red-600">{errors.sessionType.message}</p>
          )}
        </div>
      </div>

      {/* Language and Status Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="language">Language *</Label>
          <Controller
            name="language"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger className={errors.language ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.language && (
            <p className="text-sm text-red-600">{errors.language.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Controller
            name="status"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            )}
          />
        </div>
      </div>

      {/* Date and Time Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !date && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, 'PPP') : 'Pick a date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="time">Time *</Label>
          <Input
            type="time"
            value={time}
            onChange={(e) => setTime(e.target.value)}
            className={!time ? 'border-red-500' : ''}
          />
        </div>
      </div>

      {/* Consultant Selection - Only for online sessions */}
      {watchedType === 'online' && (
        <div className="space-y-2">
          <Label htmlFor="consultantId">Consultant</Label>
          <Controller
            name="consultantId"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Select consultant" />
                </SelectTrigger>
                <SelectContent>
                  {consultants.map((consultant) => (
                    <SelectItem key={consultant._id} value={consultant._id}>
                      {consultant.name} - {consultant.major}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </div>
      )}

      {/* Location inputs for physical sessions */}
      {watchedType === 'physical' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="latitude">Latitude</Label>
            <Controller
              name="latitude"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="any"
                  placeholder="Enter latitude"
                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                  value={field.value || ''}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="longitude">Longitude</Label>
            <Controller
              name="longitude"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="any"
                  placeholder="Enter longitude"
                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                  value={field.value || ''}
                />
              )}
            />
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
        >
          {isLoading 
            ? (isEditing ? 'Updating...' : 'Creating...') 
            : (isEditing ? 'Update Invitation' : 'Create Invitation')
          }
        </Button>
      </div>
    </form>
  )
}

export default InvitationForm
