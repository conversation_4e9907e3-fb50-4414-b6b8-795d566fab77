import React from 'react';
import CompanySettingsManager from '../components/corporate/CompanySettingsManager';
import { AdminLayout } from '../components/layout/AdminLayout';
import type { User } from '../types/api';

interface CompanySettingsManagementProps {
  user?: User;
}

const CompanySettingsManagement: React.FC<CompanySettingsManagementProps> = ({ user }) => {
  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token");
    window.location.href = "/login";
  };

  // If no user is provided, try to get it from localStorage or redirect to login
  if (!user) {
    const token = localStorage.getItem('hr-auth-token');
    if (!token) {
      window.location.href = '/login';
      return null;
    }
  }

  return (
    <AdminLayout
      user={user || { name: 'Admin', email: '<EMAIL>' }}
      onLogout={handleLogout}
      variant="glass"
    >
      <CompanySettingsManager variant="full" showCreateButton={true} />
    </AdminLayout>
  );
};

export default CompanySettingsManagement; 