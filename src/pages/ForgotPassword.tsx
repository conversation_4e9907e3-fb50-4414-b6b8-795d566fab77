import { 
  <PERSON><PERSON>, 
  Input, 
  <PERSON><PERSON>ard, 
  GlassCard<PERSON>eader, 
  GlassCardContent,
  Divider,
  Link,
  GradientText,
  Alert
} from '@/components/ui';
import { useForgotPasswordFunc } from '@/hooks/useForgotPasswordFunc';
import { getErrorMessage } from '@/utils/errorUtils';

// Refresh Icon Component (for forgot password)
const RefreshIcon = () => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.1 3.89 23 5 23H11V21H5V3H13V9H21Z" fill="white"/>
    <path d="M21 12L18 15H20C20 18.31 17.31 21 14 21V23C18.42 23 22 19.42 22 15H24L21 12Z" fill="white"/>
  </svg>
);

// Security Icon Component
const SecurityIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Success Icon Component
const SuccessIcon = () => (
  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
    <path d="M20 6L9 17l-5-5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow Left Icon Component
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M19 12H5m7-7l-7 7 7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow Right Icon Component  
const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export default function ForgotPassword() {
  const {
    email,
    emailSent,
    isLoading,
    error,
    handleEmailChange,
    handleSubmit,
  } = useForgotPasswordFunc();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Forgot Password Card */}
      <GlassCard 
        variant="default" 
        decorativeTop={true}
        className="w-full max-w-[420px]"
      >
        <GlassCardHeader>
          <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6 shadow-[0_8px_20px_rgba(210,179,122,0.3)]">
            {emailSent ? <SuccessIcon /> : <RefreshIcon />}
          </div>
          <GradientText 
            gradient="goldRich" 
            size="2xl" 
            className="font-bold mb-2"
          >
            {emailSent ? "Check Your Email" : "Forgot Password?"}
          </GradientText>
          <p className="text-[15px] text-gray-500 font-normal leading-relaxed">
            {emailSent 
              ? "We've sent password reset instructions to your email address. Please check your inbox and follow the instructions."
              : "No worries! Enter your email address and we'll send you instructions to reset your password."
            }
          </p>
        </GlassCardHeader>

        <GlassCardContent>
          {error ? (
            <Alert 
              variant="destructive" 
              className="mb-6"
              title="Error"
              description={getErrorMessage(error)}
            />
          ) : null}

          {!emailSent ? (
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Email Field */}
              <Input
                label={
                  <>
                    Email Address <span className="text-red-500">*</span>
                  </>
                }
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => handleEmailChange(e.target.value)}
                autoComplete="email"
                required
              />

              {/* Send Instructions Button */}
              <Button
                type="submit"
                variant="default"
                size="default"
                fullWidth={true}
                isLoading={isLoading}
                rightIcon={!isLoading ? <ArrowRightIcon /> : undefined}
                className="mb-6"
              >
                {isLoading ? "Sending Instructions..." : "Send Reset Instructions"}
              </Button>

              {/* Divider */}
              <Divider label="OR" />

              {/* Back to Login Link */}
              <Link
                href="/"
                variant="button"
                className="w-full text-center"
                leftIcon={<ArrowLeftIcon />}
              >
                Back to Login
              </Link>
            </form>
          ) : (
            <div className="space-y-6">
              {/* Success Message */}
              <div className="text-center space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-xl">
                  <p className="text-sm text-green-700 font-medium">
                    Instructions sent successfully!
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    If you don't see the email, please check your spam folder.
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={() => handleEmailChange('')}
                  variant="outline"
                  size="default"
                  fullWidth={true}
                >
                  Send to Different Email
                </Button>
                
                <Link
                  href="/"
                  variant="button"
                  className="w-full text-center"
                  leftIcon={<ArrowLeftIcon />}
                >
                  Back to Login
                </Link>
              </div>
            </div>
          )}
        </GlassCardContent>

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 mt-8 p-3 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="text-xs text-[#6b7280] font-medium">
            Secure Password Recovery
          </span>
        </div>
      </GlassCard>

      {/* Privacy Disclaimer and Copyright */}
      <div className="mt-8 text-center max-w-[420px] px-5">
        <div className="p-5 bg-[rgba(210,179,122,0.03)] rounded-2xl border border-[rgba(210,179,122,0.1)] mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <SecurityIcon />
            <span className="text-sm font-semibold text-[#d2b37a]">
              Privacy & Security
            </span>
          </div>
          <p className="text-[13px] text-gray-500 leading-relaxed">
            Sehatti respects your privacy and protects your information with the highest standards of security and confidentiality
          </p>
        </div>
        
        <div className="p-4 bg-[rgba(0,0,0,0.02)] rounded-xl border border-[rgba(0,0,0,0.05)]">
          <p className="text-xs text-gray-400 font-medium">
            All rights reserved © Sehatti. An Elevate Academy ecosystem.
          </p>
        </div>
      </div>
    </div>
  );
} 