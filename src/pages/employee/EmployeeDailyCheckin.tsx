import React, { useState } from "react"
import { toast } from "react-hot-toast"
import {
  FaHeart,
  FaBrain,
  FaDumbbell,
  FaSmile,
  FaClock,
  FaCheckCircle,
  FaChevronRight,
  FaChevronLeft,
  FaFire
} from "react-icons/fa"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "../../components/ui/Card"
import { Button } from "../../components/ui/Button"
import { Badge } from "../../components/ui/Badge"
import { AdminLayout } from "../../components/layout/AdminLayout"
import { useGetMeQuery } from "@/store/api"
import { useAppSelector, useAppDispatch } from "@/store/store"
import { selectAuth, logout } from "@/features/auth/auth-slice"
import { Spinner } from "../../components/ui/Spinner"
import { Alert } from "../../components/ui/Alert"
import { useNavigate } from "react-router-dom"

// Mock daily questions (in real app, these would come from QA Service)
const DAILY_QUESTIONS = [
  {
    id: "1",
    question: "How would you rate your stress level today?",
    type: "rating",
    category: "Mental Health",
    icon: FaBrain,
    options: ["1", "2", "3", "4", "5"],
    labels: ["Very Low", "Low", "Moderate", "High", "Very High"],
    color: "blue"
  },
  {
    id: "2",
    question: "How many hours did you sleep last night?",
    type: "number",
    category: "Sleep Health",
    icon: FaClock,
    min: 0,
    max: 12,
    step: 0.5,
    color: "purple"
  },
  {
    id: "3",
    question: "Did you exercise today?",
    type: "boolean",
    category: "Physical Health",
    icon: FaDumbbell,
    options: ["Yes", "No"],
    color: "green"
  },
  {
    id: "4",
    question: "How is your overall mood today?",
    type: "rating",
    category: "Mental Health",
    icon: FaSmile,
    options: ["1", "2", "3", "4", "5"],
    labels: ["Very Sad", "Sad", "Neutral", "Happy", "Very Happy"],
    color: "yellow"
  },
  {
    id: "5",
    question: "How many glasses of water have you had today?",
    type: "number",
    category: "Nutrition",
    icon: FaHeart,
    min: 0,
    max: 15,
    step: 1,
    color: "cyan"
  }
]

const EmployeeDailyCheckinContent: React.FC<{ user: any }> = ({ user }) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [responses, setResponses] = useState<Record<string, any>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isCompleted, setIsCompleted] = useState(false)

  const currentQuestion = DAILY_QUESTIONS[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === DAILY_QUESTIONS.length - 1
  const isFirstQuestion = currentQuestionIndex === 0

  const handleResponseChange = (questionId: string, value: any) => {
    setResponses((prev) => ({
      ...prev,
      [questionId]: value
    }))
  }

  const handleNext = () => {
    if (currentQuestionIndex < DAILY_QUESTIONS.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1)
    }
  }

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    // Simulate API submission to QA Service
    await new Promise((resolve) => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setIsCompleted(true)
    toast.success("Daily check-in completed! 🎉")
  }

  const handleLogout = () => {
    dispatch(logout())
    navigate("/login")
  }

  const getResponseForQuestion = (questionId: string) => {
    return responses[questionId]
  }

  const isCurrentQuestionAnswered = () => {
    const response = getResponseForQuestion(currentQuestion.id)
    return response !== undefined && response !== null && response !== ""
  }

  const getTotalProgress = () => {
    const answeredCount = DAILY_QUESTIONS.filter(
      (q) => getResponseForQuestion(q.id) !== undefined
    ).length
    return Math.round((answeredCount / DAILY_QUESTIONS.length) * 100)
  }

  const renderQuestion = () => {
    const QuestionIcon = currentQuestion.icon
    const response = getResponseForQuestion(currentQuestion.id)

    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div
            className={`w-16 h-16 bg-gradient-to-r from-${currentQuestion.color}-400 to-${currentQuestion.color}-600 rounded-full flex items-center justify-center mx-auto mb-4`}
          >
            <QuestionIcon className="w-8 h-8 text-white" />
          </div>
          <Badge variant="outline" className="mb-2">
            {currentQuestion.category}
          </Badge>
          <CardTitle className="text-xl font-semibold">
            {currentQuestion.question}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {currentQuestion.type === "rating" && (
            <div className="space-y-4">
              <div className="flex justify-center gap-3">
                {currentQuestion.options.map((option, index) => (
                  <button
                    key={option}
                    onClick={() =>
                      handleResponseChange(currentQuestion.id, option)
                    }
                    className={`
                      w-14 h-14 rounded-full border-2 font-semibold transition-all
                      ${
                        response === option
                          ? `bg-${currentQuestion.color}-500 border-${currentQuestion.color}-500 text-white`
                          : "border-gray-300 hover:border-gray-400 text-gray-600"
                      }
                    `}
                  >
                    {option}
                  </button>
                ))}
              </div>

              {currentQuestion.labels && (
                <div className="flex justify-between text-sm text-gray-500 px-4">
                  <span>{currentQuestion.labels[0]}</span>
                  <span>
                    {currentQuestion.labels[currentQuestion.labels.length - 1]}
                  </span>
                </div>
              )}
            </div>
          )}

          {currentQuestion.type === "number" && (
            <div className="space-y-4">
              <div className="flex items-center justify-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const current = response || currentQuestion.min || 0
                    const newValue = Math.max(
                      current - (currentQuestion.step || 1),
                      currentQuestion.min || 0
                    )
                    handleResponseChange(currentQuestion.id, newValue)
                  }}
                  disabled={response <= (currentQuestion.min || 0)}
                >
                  -
                </Button>

                <div
                  className={`text-3xl font-bold text-${currentQuestion.color}-600 min-w-[80px] text-center`}
                >
                  {response || 0}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const current = response || 0
                    const newValue = Math.min(
                      current + (currentQuestion.step || 1),
                      currentQuestion.max || 10
                    )
                    handleResponseChange(currentQuestion.id, newValue)
                  }}
                  disabled={response >= (currentQuestion.max || 10)}
                >
                  +
                </Button>
              </div>

              <input
                type="range"
                min={currentQuestion.min || 0}
                max={currentQuestion.max || 10}
                step={currentQuestion.step || 1}
                value={response || 0}
                onChange={(e) =>
                  handleResponseChange(
                    currentQuestion.id,
                    parseFloat(e.target.value)
                  )
                }
                className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-${currentQuestion.color}`}
              />
            </div>
          )}

          {currentQuestion.type === "boolean" && (
            <div className="flex gap-4 justify-center">
              {currentQuestion.options.map((option) => (
                <button
                  key={option}
                  onClick={() =>
                    handleResponseChange(currentQuestion.id, option)
                  }
                  className={`
                    px-8 py-4 rounded-lg border-2 font-semibold transition-all
                    ${
                      response === option
                        ? `bg-${currentQuestion.color}-500 border-${currentQuestion.color}-500 text-white`
                        : "border-gray-300 hover:border-gray-400 text-gray-600"
                    }
                  `}
                >
                  {option}
                </button>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  if (isCompleted) {
    return (
      <AdminLayout user={user} onLogout={handleLogout}>
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Card className="text-center p-8">
            <CardContent>
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaCheckCircle className="w-10 h-10 text-green-600" />
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Daily Check-in Complete! 🎉
              </h1>

              <p className="text-gray-600 mb-6">
                Thank you for taking time to check in with your wellness today.
                Your responses help us provide better support for your health
                journey.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <FaFire className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold">5 Day Streak!</h3>
                  <p className="text-sm text-gray-600">Keep it going!</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <FaHeart className="w-6 h-6 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold">+50 Points</h3>
                  <p className="text-sm text-gray-600">Daily bonus earned</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <FaBrain className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold">Wellness Score</h3>
                  <p className="text-sm text-gray-600">
                    Updated based on check-in
                  </p>
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <Button
                  onClick={() => (window.location.href = "/employee/dashboard")}
                >
                  Back to Dashboard
                </Button>
                <Button
                  variant="outline"
                  onClick={() => (window.location.href = "/employee/media")}
                >
                  Explore Wellness Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout user={user} onLogout={handleLogout}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Daily Wellness Check-in ✨
          </h1>
          <p className="text-gray-600">
            Take a moment to check in with your wellness today
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Question {currentQuestionIndex + 1} of {DAILY_QUESTIONS.length}
            </span>
            <span className="text-sm font-medium text-gray-700">
              {getTotalProgress()}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${
                  ((currentQuestionIndex + 1) / DAILY_QUESTIONS.length) * 100
                }%`
              }}
            />
          </div>
        </div>

        {/* Question */}
        {renderQuestion()}

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 max-w-2xl mx-auto">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstQuestion}
            className="flex items-center gap-2"
          >
            <FaChevronLeft className="w-4 h-4" />
            Previous
          </Button>

          <div className="flex gap-2">
            {DAILY_QUESTIONS.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentQuestionIndex
                    ? "bg-blue-500"
                    : index < currentQuestionIndex ||
                      getResponseForQuestion(DAILY_QUESTIONS[index].id) !==
                        undefined
                    ? "bg-green-500"
                    : "bg-gray-300"
                }`}
              />
            ))}
          </div>

          {isLastQuestion ? (
            <Button
              onClick={handleSubmit}
              disabled={!isCurrentQuestionAnswered() || isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? "Submitting..." : "Complete Check-in"}
              <FaCheckCircle className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!isCurrentQuestionAnswered()}
              className="flex items-center gap-2"
            >
              Next
              <FaChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Question Summary */}
        <div className="mt-12 max-w-2xl mx-auto">
          <h3 className="text-lg font-semibold mb-4">Your Responses</h3>
          <div className="grid grid-cols-1 gap-3">
            {DAILY_QUESTIONS.map((question, index) => {
              const response = getResponseForQuestion(question.id)
              const isAnswered = response !== undefined
              const QuestionIcon = question.icon

              return (
                <div
                  key={question.id}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    index === currentQuestionIndex
                      ? "border-blue-300 bg-blue-50"
                      : isAnswered
                      ? "border-green-300 bg-green-50"
                      : "border-gray-200 bg-gray-50"
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <QuestionIcon
                      className={`w-5 h-5 ${
                        isAnswered ? "text-green-600" : "text-gray-400"
                      }`}
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {question.question}
                      </p>
                      {isAnswered && (
                        <p className="text-sm text-gray-600">
                          Answer:{" "}
                          {typeof response === "boolean"
                            ? response
                              ? "Yes"
                              : "No"
                            : response}
                        </p>
                      )}
                    </div>
                    {isAnswered && (
                      <FaCheckCircle className="w-5 h-5 text-green-600" />
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

// Main wrapper component that fetches user data
const EmployeeDailyCheckin: React.FC = () => {
  const localAuthState = useAppSelector(selectAuth)

  // First try to use local auth state
  if (localAuthState.isAuthenticated && localAuthState.user) {
    return <EmployeeDailyCheckinContent user={localAuthState.user} />
  }

  // Fallback: try API query if local auth fails
  const { data: userData, isLoading, error } = useGetMeQuery()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
        <Alert variant="destructive" title="Error Loading Check-in">
          Failed to load user data. Please try refreshing the page.
        </Alert>
      </div>
    )
  }

  const user = userData.data.user

  return <EmployeeDailyCheckinContent user={user} />
}

export default EmployeeDailyCheckin
