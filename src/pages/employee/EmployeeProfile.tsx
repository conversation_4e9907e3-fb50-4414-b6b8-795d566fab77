import React, { useState } from 'react'
import { toast } from 'react-hot-toast'
import { 
  FaUser, 
  FaEdit, 
  FaSave, 
  FaTimes, 
  FaHeart, 
  FaBrain, 
  FaDumbbell, 
  FaBullseye,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaBriefcase,
  FaBuilding,
  FaCog,
  FaBell,
  FaGlobe,
  FaChartLine,
  FaTrophy,
  FaFire
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Button } from '../../components/ui/Button'
import { Badge } from '../../components/ui/Badge'
import { Input } from '../../components/ui/Input'
import { AdminLayout } from '../../components/layout/AdminLayout'
import { useGetMeQuery } from '@/store/api'
import { useAppSelector, useAppDispatch } from '@/store/store'
import { selectAuth, logout } from '@/features/auth/auth-slice'
import { Spinner } from '../../components/ui/Spinner'
import { Alert } from '../../components/ui/Alert'
import { useNavigate } from 'react-router-dom'

// Mock user data
const MOCK_USER = {
  id: 'emp-001',
  name: 'Ahmed Al-Mahmoud',
  email: '<EMAIL>',
  phone: '+971-50-123-4567',
  position: 'Senior Software Engineer',
  department: 'Technology',
  company: 'Innovative Tech Solutions',
  location: 'Dubai, UAE',
  joinDate: '2023-03-15',
  avatar: null,
  language: 'en',
  timezone: 'Asia/Dubai'
}

const WELLNESS_GOALS = [
  { id: 'stress', label: 'Reduce Stress', icon: FaBrain, color: 'blue', current: 3, target: 2, unit: '/5' },
  { id: 'exercise', label: 'Exercise More', icon: FaDumbbell, color: 'green', current: 3, target: 5, unit: ' days/week' },
  { id: 'sleep', label: 'Better Sleep', icon: FaHeart, color: 'purple', current: 6.5, target: 8, unit: ' hours' },
  { id: 'water', label: 'Drink More Water', icon: FaHeart, color: 'cyan', current: 6, target: 8, unit: ' glasses' }
]

const RECENT_ACTIVITY = [
  { id: '1', type: 'checkin', title: 'Daily Check-in Completed', date: '2025-01-15', icon: FaHeart },
  { id: '2', type: 'exercise', title: 'Desk Yoga Session', date: '2025-01-15', icon: FaDumbbell },
  { id: '3', type: 'content', title: 'Read: Stress Management Tips', date: '2025-01-14', icon: FaBrain },
  { id: '4', type: 'achievement', title: 'Earned 5-Day Streak Badge', date: '2025-01-14', icon: FaTrophy }
]

const ACHIEVEMENT_BADGES = [
  { id: '1', name: 'Early Bird', description: '5 consecutive morning check-ins', icon: '🌅', earned: true },
  { id: '2', name: 'Consistency Champion', description: '7-day check-in streak', icon: '🔥', earned: true },
  { id: '3', name: 'Wellness Explorer', description: 'Viewed 10 wellness articles', icon: '📚', earned: true },
  { id: '4', name: 'Stress Master', description: 'Reduced stress level for 5 days', icon: '🧘', earned: false },
  { id: '5', name: 'Fitness Enthusiast', description: 'Exercise 5 days in a week', icon: '💪', earned: false },
  { id: '6', name: 'Sleep Champion', description: 'Achieve 8+ hours sleep for a week', icon: '😴', earned: false }
]

const EmployeeProfileContent: React.FC<{ user: any }> = ({ user }) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [activeTab, setActiveTab] = useState<string>('overview')
  const [isEditing, setIsEditing] = useState(false)
  const [editedUser, setEditedUser] = useState(MOCK_USER)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaUser },
    { id: 'goals', label: 'Wellness Goals', icon: FaBullseye },
    { id: 'activity', label: 'Activity', icon: FaChartLine },
    { id: 'achievements', label: 'Achievements', icon: FaTrophy },
    { id: 'settings', label: 'Settings', icon: FaCog },
  ]

  const handleSave = async () => {
    toast.success('Profile updated successfully!')
    setIsEditing(false)
    // Here you would typically call an API to save the changes
  }

  const handleCancel = () => {
    setEditedUser(MOCK_USER)
    setIsEditing(false)
  }

  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  const getGoalProgress = (goal: any) => {
    return Math.min((goal.current / goal.target) * 100, 100)
  }

  return (
    <AdminLayout user={user} onLogout={handleLogout}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">
                  {MOCK_USER.name.charAt(0)}
                </span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {MOCK_USER.name}
                </h1>
                <p className="text-gray-600">
                  {MOCK_USER.position} • {MOCK_USER.department}
                </p>
              </div>
            </div>
            
            <Button
              onClick={() => setIsEditing(!isEditing)}
              className="flex items-center gap-2"
            >
              <FaEdit className="w-4 h-4" />
              {isEditing ? 'Cancel Edit' : 'Edit Profile'}
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const TabIcon = tab.icon
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center gap-2 py-4 border-b-2 transition-colors
                    ${isActive 
                      ? 'border-blue-500 text-blue-600' 
                      : 'border-transparent text-gray-600 hover:text-gray-900'
                    }
                  `}
                >
                  <TabIcon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Personal Information
                  {isEditing && (
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleSave}>
                        <FaSave className="w-4 h-4 mr-2" />
                        Save
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleCancel}>
                        <FaTimes className="w-4 h-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaUser className="inline w-4 h-4 mr-2" />
                        Full Name
                      </label>
                      {isEditing ? (
                        <Input
                          value={editedUser.name}
                          onChange={(e) => setEditedUser(prev => ({ ...prev, name: e.target.value }))}
                        />
                      ) : (
                        <p className="text-gray-900">{MOCK_USER.name}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaEnvelope className="inline w-4 h-4 mr-2" />
                        Email
                      </label>
                      {isEditing ? (
                        <Input
                          type="email"
                          value={editedUser.email}
                          onChange={(e) => setEditedUser(prev => ({ ...prev, email: e.target.value }))}
                        />
                      ) : (
                        <p className="text-gray-900">{MOCK_USER.email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaPhone className="inline w-4 h-4 mr-2" />
                        Phone
                      </label>
                      {isEditing ? (
                        <Input
                          value={editedUser.phone}
                          onChange={(e) => setEditedUser(prev => ({ ...prev, phone: e.target.value }))}
                        />
                      ) : (
                        <p className="text-gray-900">{MOCK_USER.phone}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaBriefcase className="inline w-4 h-4 mr-2" />
                        Position
                      </label>
                      <p className="text-gray-900">{MOCK_USER.position}</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaBuilding className="inline w-4 h-4 mr-2" />
                        Department
                      </label>
                      <p className="text-gray-900">{MOCK_USER.department}</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaMapMarkerAlt className="inline w-4 h-4 mr-2" />
                        Location
                      </label>
                      <p className="text-gray-900">{MOCK_USER.location}</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaCalendarAlt className="inline w-4 h-4 mr-2" />
                        Join Date
                      </label>
                      <p className="text-gray-900">
                        {new Date(MOCK_USER.joinDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-indigo-600">Current Streak</p>
                    <p className="text-2xl font-bold text-indigo-700">5 days</p>
                  </div>
                  <FaFire className="w-8 h-8 text-indigo-500" />
                </div>
              </Card>

              <Card className="p-4 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-emerald-600">Wellness Score</p>
                    <p className="text-2xl font-bold text-emerald-700">78/100</p>
                  </div>
                  <FaHeart className="w-8 h-8 text-emerald-500" />
                </div>
              </Card>

              <Card className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600">Total Points</p>
                    <p className="text-2xl font-bold text-orange-700">2,450</p>
                  </div>
                  <FaTrophy className="w-8 h-8 text-orange-500" />
                </div>
              </Card>

              <Card className="p-4 bg-gradient-to-r from-purple-50 to-pink-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">Achievements</p>
                    <p className="text-2xl font-bold text-purple-700">3/6</p>
                  </div>
                  <FaTrophy className="w-8 h-8 text-purple-500" />
                </div>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'goals' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {WELLNESS_GOALS.map((goal) => {
                const GoalIcon = goal.icon
                const progress = getGoalProgress(goal)
                
                return (
                  <Card key={goal.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3">
                        <div className={`w-10 h-10 bg-gradient-to-r from-${goal.color}-400 to-${goal.color}-600 rounded-full flex items-center justify-center`}>
                          <GoalIcon className="w-5 h-5 text-white" />
                        </div>
                        {goal.label}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between text-sm">
                          <span>Current: {goal.current}{goal.unit}</span>
                          <span>Target: {goal.target}{goal.unit}</span>
                        </div>
                        
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div 
                            className={`bg-gradient-to-r from-${goal.color}-400 to-${goal.color}-600 h-3 rounded-full transition-all duration-300`}
                            style={{ width: `${progress}%` }}
                          />
                        </div>
                        
                        <div className="text-center">
                          <span className={`text-lg font-bold text-${goal.color}-600`}>
                            {Math.round(progress)}% Complete
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {activeTab === 'activity' && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {RECENT_ACTIVITY.map((activity) => {
                  const ActivityIcon = activity.icon
                  
                  return (
                    <div key={activity.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <ActivityIcon className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{activity.title}</h4>
                        <p className="text-sm text-gray-600">
                          {new Date(activity.date).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="outline">{activity.type}</Badge>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'achievements' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {ACHIEVEMENT_BADGES.map((badge) => (
              <Card key={badge.id} className={`${badge.earned ? 'border-yellow-300 bg-yellow-50' : 'border-gray-200'}`}>
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-4">{badge.icon}</div>
                  <h3 className="font-semibold mb-2">{badge.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{badge.description}</p>
                  <Badge variant={badge.earned ? 'default' : 'secondary'}>
                    {badge.earned ? 'Earned' : 'Locked'}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaGlobe className="inline w-4 h-4 mr-2" />
                    Language
                  </label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="en">English</option>
                    <option value="ar">العربية</option>
                    <option value="hi">हिंदी</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaBell className="inline w-4 h-4 mr-2" />
                    Notifications
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      Daily check-in reminders
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      Wellness tips and content
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      Weekly progress reports
                    </label>
                  </div>
                </div>

                <Button>Save Preferences</Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

// Main wrapper component that fetches user data
const EmployeeProfile: React.FC = () => {
  const localAuthState = useAppSelector(selectAuth)
  
  // First try to use local auth state
  if (localAuthState.isAuthenticated && localAuthState.user) {
    return <EmployeeProfileContent user={localAuthState.user} />
  }

  // Fallback: try API query if local auth fails
  const { data: userData, isLoading, error } = useGetMeQuery()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData?.data?.user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Alert variant="destructive">
          Failed to load user data. Please refresh the page.
        </Alert>
      </div>
    )
  }

  return <EmployeeProfileContent user={userData.data.user} />
}

export default EmployeeProfile 