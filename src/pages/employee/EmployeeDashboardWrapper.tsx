import React from 'react'
import { useGetMeQuery } from '@/store/api'
import { useAppSelector } from '@/store/store'
import { selectAuth } from '@/features/auth/auth-slice'
import { Spinner } from '@/components/ui/Spinner'
import { Alert } from '@/components/ui/Alert'
import EmployeeDashboard from '../dashboards/EmployeeDashboard'

const EmployeeDashboardWrapper = () => {
  const localAuthState = useAppSelector(selectAuth)
  const { data: userData, isLoading, error } = useGetMeQuery()
  
  // First try to use local auth state
  if (localAuthState.isAuthenticated && localAuthState.user) {
    return <EmployeeDashboard user={localAuthState.user} />
  }

  // Fallback: try API query if local auth fails

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
        <Alert variant="destructive" title="Error Loading Dashboard">
          Failed to load user data. Please try refreshing the page.
        </Alert>
      </div>
    )
  }

  const user = userData.data.user
  
  return <EmployeeDashboard user={user} />
}

export default EmployeeDashboardWrapper 