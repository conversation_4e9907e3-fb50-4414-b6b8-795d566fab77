import React, { useState } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { 
  FaArrowLeft, 
  FaShare, 
  FaLinkedin, 
  FaWhatsapp, 
  FaClock, 
  FaBookOpen, 
  FaGlobe,
  FaSlack
} from 'react-icons/fa'
// Microsoft Teams icon will be rendered as text for now
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Button } from '../../components/ui/Button'
import { Badge } from '../../components/ui/Badge'
import { AdminLayout } from '../../components/layout/AdminLayout'
import type { User } from '@/types/api'

interface ArticleReaderProps {
  user: User
}

// Same wellness content structure as dashboard
interface WellnessContentItem {
  id: string
  titleEN: string
  titleAR: string
  titleHI: string
  descriptionEN: string
  descriptionAR: string
  descriptionHI: string
  category: string
  division: string
  readTime: number
  source: string
  fullContentEN?: string
  fullContentAR?: string
  fullContentHI?: string
}

// Extended content with full articles
const WELLNESS_CONTENT: WellnessContentItem[] = [
  {
    id: 'EW01',
    titleEN: 'The place where you work affects the way you work: How does the environment shape your productivity?',
    titleAR: 'المكان الذي تعمل فيه يؤثر على الطريقة التي تعمل بها: كيف تشكّل البيئة إنتاجيتك؟',
    titleHI: 'जिस स्थान पर आप काम करते हैं, वह आपके काम करने के तरीके को प्रभावित करता है: आपकी उत्पादकता को पर्यावरण कैसे आकार देता है?',
    descriptionEN: 'An employee may be skilled, disciplined, and have high intentions, but if the work environment does not support them, their energy dissipates silently...',
    descriptionAR: 'قد يكون الموظف ماهرًا، منضبطًا، وذو نوايا عالية، لكن إن كانت بيئة العمل لا تدعمه، فإن طاقته تتبدد بصمت...',
    descriptionHI: 'कर्मचारी कुशल, अनुशासित और उच्च इरादों वाला हो सकता है, लेकिन अगर कार्यस्थल का वातावरण उसका समर्थन नहीं करता...',
    fullContentEN: `An employee may be skilled, disciplined, and have high intentions, but if the work environment does not support them, their energy dissipates silently.

**The Science Behind Environmental Impact**

Research from Harvard T.H. Chan School of Public Health reveals that environmental factors can impact productivity by up to 15%. The physical workspace isn't just a backdrop—it's an active participant in your daily performance.

**Key Environmental Factors:**

1. **Natural Light**: Studies show that exposure to natural light increases alertness and reduces eye strain by 51%
2. **Air Quality**: Poor ventilation can decrease cognitive function by up to 15%
3. **Noise Levels**: Excessive noise can reduce productivity by 66%
4. **Temperature**: The optimal temperature range is 68-72°F (20-22°C) for peak performance
5. **Color Psychology**: Blue enhances focus, green reduces eye strain, while red can increase urgency

**Creating Your Optimal Environment:**

- Position your workspace near natural light sources
- Invest in plants to improve air quality
- Use noise-canceling headphones in open offices
- Personalize your space with meaningful items
- Maintain a clutter-free, organized workspace

**The Psychological Impact**

Your environment doesn't just affect your body—it shapes your mindset. A well-designed workspace can:
- Increase motivation and engagement
- Reduce stress and anxiety
- Enhance creativity and problem-solving
- Improve overall job satisfaction

**Conclusion**

Remember: You have more control over your environment than you think. Small changes can yield significant improvements in your daily work experience and long-term career satisfaction.`,
    fullContentAR: `قد يكون الموظف ماهرًا، منضبطًا، وذو نوايا عالية، لكن إن كانت بيئة العمل لا تدعمه، فإن طاقته تتبدد بصمت.

**العلم وراء التأثير البيئي**

تكشف أبحاث من Harvard T.H. Chan School of Public Health أن العوامل البيئية يمكن أن تؤثر على الإنتاجية بنسبة تصل إلى 15%. مساحة العمل المادية ليست مجرد خلفية—بل مشارك نشط في أدائك اليومي.

**العوامل البيئية الرئيسية:**

1. **الضوء الطبيعي**: تظهر الدراسات أن التعرض للضوء الطبيعي يزيد من اليقظة ويقلل إجهاد العين بنسبة 51%
2. **جودة الهواء**: التهوية السيئة يمكن أن تقلل الوظيفة الإدراكية بنسبة تصل إلى 15%
3. **مستويات الضوضاء**: الضوضاء المفرطة يمكن أن تقلل الإنتاجية بنسبة 66%
4. **درجة الحرارة**: النطاق الأمثل لدرجة الحرارة هو 68-72°F (20-22°C) للأداء الأمثل
5. **علم نفس الألوان**: الأزرق يعزز التركيز، الأخضر يقلل إجهاد العين، بينما الأحمر يمكن أن يزيد الإلحاح

**إنشاء بيئتك المثلى:**

- ضع مساحة عملك بالقرب من مصادر الضوء الطبيعي
- استثمر في النباتات لتحسين جودة الهواء
- استخدم سماعات إلغاء الضوضاء في المكاتب المفتوحة
- شخصن مساحتك بالعناصر ذات المعنى
- حافظ على مساحة عمل خالية من الفوضى ومنظمة

**التأثير النفسي**

بيئتك لا تؤثر فقط على جسدك—بل تشكل عقليتك. مساحة العمل المصممة جيداً يمكن أن:
- تزيد الدافعية والمشاركة
- تقلل التوتر والقلق
- تعزز الإبداع وحل المشكلات
- تحسن الرضا الوظيفي العام

**الخلاصة**

تذكر: لديك سيطرة أكبر على بيئتك مما تعتقد. التغييرات الصغيرة يمكن أن تحقق تحسينات كبيرة في تجربة عملك اليومية ورضاك المهني طويل المدى.`,
    fullContentHI: `कर्मचारी कुशल, अनुशासित और उच्च इरादों वाला हो सकता है, लेकिन अगर कार्यस्थल का वातावरण उसका समर्थन नहीं करता, तो उसकी ऊर्जा चुपचाप बिखर जाती है।

**पर्यावरणीय प्रभाव के पीछे का विज्ञान**

Harvard T.H. Chan School of Public Health के अनुसंधान से पता चलता है कि पर्यावरणीय कारक उत्पादकता को 15% तक प्रभावित कर सकते हैं। भौतिक कार्यक्षेत्र केवल एक पृष्ठभूमि नहीं है—यह आपके दैनिक प्रदर्शन में एक सक्रिय भागीदार है।

**मुख्य पर्यावरणीय कारक:**

1. **प्राकृतिक प्रकाश**: अध्ययन दिखाते हैं कि प्राकृतिक प्रकाश के संपर्क से सतर्कता बढ़ती है और आंखों का तनाव 51% कम होता है
2. **हवा की गुणवत्ता**: खराब वेंटिलेशन संज्ञानात्मक कार्य को 15% तक कम कर सकता है
3. **शोर का स्तर**: अत्यधिक शोर उत्पादकता को 66% तक कम कर सकता है
4. **तापमान**: चरम प्रदर्शन के लिए इष्टतम तापमान सीमा 68-72°F (20-22°C) है
5. **रंग मनोविज्ञान**: नीला फोकस बढ़ाता है, हरा आंखों का तनाव कम करता है, जबकि लाल तात्कालिकता बढ़ा सकता है

**अपना इष्टतम वातावरण बनाना:**

- अपने कार्यक्षेत्र को प्राकृतिक प्रकाश स्रोतों के पास रखें
- हवा की गुणवत्ता सुधारने के लिए पौधों में निवेश करें
- खुले कार्यालयों में शोर-रद्द करने वाले हेडफ़ोन का उपयोग करें
- अर्थपूर्ण वस्तुओं के साथ अपने स्थान को व्यक्तिगत बनाएं
- एक अव्यवस्था-मुक्त, संगठित कार्यक्षेत्र बनाए रखें

**मनोवैज्ञानिक प्रभाव**

आपका वातावरण केवल आपके शरीर को प्रभावित नहीं करता—यह आपकी मानसिकता को आकार देता है। एक अच्छी तरह से डिज़ाइन किया गया कार्यक्षेत्र कर सकता है:
- प्रेरणा और सहभागिता बढ़ाना
- तनाव और चिंता कम करना
- रचनात्मकता और समस्या-समाधान बढ़ाना
- समग्र नौकरी संतुष्टि में सुधार

**निष्कर्ष**

याद रखें: आपका अपने वातावरण पर उतना नियंत्रण है जितना आप सोचते हैं। छोटे बदलाव आपके दैनिक कार्य अनुभव और दीर्घकालिक करियर संतुष्टि में महत्वपूर्ण सुधार ला सकते हैं।`,
    category: 'environmental',
    division: 'Environmental Wellbeing',
    readTime: 5,
    source: 'Harvard T.H. Chan School of Public Health'
  },
  // Add other articles with full content...
]

const ArticleReader: React.FC<ArticleReaderProps> = ({ user }) => {
  const { articleId } = useParams<{ articleId: string }>()
  const navigate = useNavigate()
  const [currentLanguage, setCurrentLanguage] = useState<'EN' | 'AR' | 'HI'>('EN')
  const [shareMenuOpen, setShareMenuOpen] = useState(false)

  // Find the article
  const article = WELLNESS_CONTENT.find(item => item.id === articleId)

  if (!article) {
    return (
      <AdminLayout user={user} onLogout={() => navigate('/login')}>
        <div className="max-w-4xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Article Not Found</h1>
          <Button onClick={() => navigate('/employee/dashboard')}>
            <FaArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </AdminLayout>
    )
  }

  // RTL helper function
  const isRTL = currentLanguage === 'AR'
  const textAlign = isRTL ? 'text-right' : 'text-left'

  // Get localized content
  const getLocalizedContent = () => {
    return {
      title: currentLanguage === 'EN' ? article.titleEN : 
             currentLanguage === 'AR' ? article.titleAR : article.titleHI,
      description: currentLanguage === 'EN' ? article.descriptionEN : 
                   currentLanguage === 'AR' ? article.descriptionAR : article.descriptionHI,
      fullContent: currentLanguage === 'EN' ? article.fullContentEN : 
                   currentLanguage === 'AR' ? article.fullContentAR : article.fullContentHI
    }
  }

  const localizedContent = getLocalizedContent()

  // Share functionality
  const shareToLinkedIn = () => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(localizedContent.title)
    const summary = encodeURIComponent(localizedContent.description)
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`, '_blank')
  }

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${localizedContent.title}\n\n${localizedContent.description}\n\nRead more: ${window.location.href}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
  }

  const shareToTeams = () => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(localizedContent.title)
    window.open(`https://teams.microsoft.com/share?href=${url}&msgText=${title}`, '_blank')
  }

  const shareToSlack = () => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(localizedContent.title)
    window.open(`https://slack.com/intl/en-in/help/articles/201259356-Share-links-in-Slack?text=${title}&url=${url}`, '_blank')
  }

  const copyLink = () => {
    navigator.clipboard.writeText(window.location.href)
    alert('Link copied to clipboard!')
  }

  return (
    <AdminLayout user={user} onLogout={() => navigate('/login')}>
      <div className={`max-w-4xl mx-auto px-4 py-8 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Button 
            variant="outline" 
            onClick={() => navigate('/employee/dashboard')}
            className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <FaArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </Button>

          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {/* Language Selector */}
            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FaGlobe className="w-4 h-4 text-gray-600" />
              <select 
                value={currentLanguage} 
                onChange={(e) => setCurrentLanguage(e.target.value as 'EN' | 'AR' | 'HI')}
                className={`px-3 py-1 border rounded-md text-sm ${textAlign}`}
              >
                <option value="EN">English</option>
                <option value="AR">العربية</option>
                <option value="HI">हिंदी</option>
              </select>
            </div>

            {/* Share Button */}
            <div className="relative">
              <Button 
                variant="outline"
                onClick={() => setShareMenuOpen(!shareMenuOpen)}
                className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <FaShare className="w-4 h-4" />
                Share
              </Button>

              {/* Share Dropdown */}
              {shareMenuOpen && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg z-10">
                  <div className="py-2">
                    <button
                      onClick={shareToLinkedIn}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                    >
                      <FaLinkedin className="w-4 h-4 text-blue-600" />
                      Share on LinkedIn
                    </button>
                    <button
                      onClick={shareToWhatsApp}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                    >
                      <FaWhatsapp className="w-4 h-4 text-green-600" />
                      Share on WhatsApp
                    </button>
                    <button
                      onClick={shareToTeams}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                    >
                      <div className="w-4 h-4 bg-purple-600 rounded text-white text-xs flex items-center justify-center font-bold">T</div>
                      Share on Teams
                    </button>
                    <button
                      onClick={shareToSlack}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                    >
                      <FaSlack className="w-4 h-4 text-purple-500" />
                      Share on Slack
                    </button>
                    <hr className="my-2" />
                    <button
                      onClick={copyLink}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                    >
                      <FaShare className="w-4 h-4 text-gray-600" />
                      Copy Link
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Article Content */}
        <Card>
          <CardHeader>
            <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Badge variant="outline">{article.division}</Badge>
              <div className={`flex items-center gap-1 text-sm text-gray-600 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaClock className="w-3 h-3" />
                {article.readTime} min read
              </div>
              <div className={`flex items-center gap-1 text-sm text-gray-600 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FaBookOpen className="w-3 h-3" />
                {article.source}
              </div>
            </div>
            
            <CardTitle className={`text-3xl font-bold leading-tight mb-4 ${textAlign}`}>
              {localizedContent.title}
            </CardTitle>
            
            <p className={`text-xl text-gray-600 leading-relaxed ${textAlign}`}>
              {localizedContent.description}
            </p>
          </CardHeader>

          <CardContent>
            <div className={`prose max-w-none ${textAlign} ${isRTL ? 'prose-rtl' : ''}`}>
              {localizedContent.fullContent ? (
                <div 
                  className="whitespace-pre-line leading-relaxed text-gray-800"
                  dangerouslySetInnerHTML={{ 
                    __html: localizedContent.fullContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') 
                  }}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">Full article content is being loaded...</p>
                  <p className="text-sm text-gray-500 mt-2">
                    This article is available in multiple languages and will be fully loaded from our content service.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Article Footer */}
        <div className="mt-8 flex items-center justify-between">
          <div className={`text-sm text-gray-600 ${textAlign}`}>
            <p>Source: {article.source}</p>
            <p>Category: {article.division}</p>
          </div>

          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Button 
              variant="outline"
              onClick={() => setShareMenuOpen(!shareMenuOpen)}
              className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <FaShare className="w-4 h-4" />
              Share Article
            </Button>
            
            <Button onClick={() => navigate('/employee/dashboard')}>
              Continue Reading
            </Button>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default ArticleReader 