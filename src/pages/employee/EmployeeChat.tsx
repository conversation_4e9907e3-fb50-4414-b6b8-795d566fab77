import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import ConsultantBrowser from '../../components/chat/ConsultantBrowser'
import ChatInterface from '../../components/chat/ChatInterface'
import { AdminLayout } from '../../components/layout/AdminLayout'
import { useGetMeQuery } from '@/store/api'
import { useAppSelector, useAppDispatch } from '@/store/store'
import { selectAuth, logout } from '@/features/auth/auth-slice'
import { Spinner } from '../../components/ui/Spinner'
import { Alert } from '../../components/ui/Alert'
import { useAuth } from '../../hooks/useAuth'

interface Consultant {
  id: string
  name: string
  email: string
  phone: string
  bio: string
  major: string
  image?: string
  languages: string[]
  specializations: string[]
  status: string
  isVerified: boolean
  yearsOfExperience: number
  rating: {
    averageRating: number
    totalReviews: number
  }
  availability: {
    isAvailable: boolean
    nextAvailable?: string
  }
  hourlyRate?: number
  currency: string
}

interface ChatRoom {
  id: string
  chatId: string
  type: 'DIRECT' | 'GROUP' | 'SUPPORT'
  participants: Array<{
    id: string
    name: string
    avatar?: string
    role: 'CONSULTANT' | 'EMPLOYEE'
    isOnline: boolean
    lastSeen?: string
  }>
  title?: string
  description?: string
  lastActivity: string
  unreadCount: number
  messages: Array<{
    id: string
    chatId: string
    senderId: string
    senderName: string
    senderAvatar?: string
    content: string
    type: 'TEXT' | 'IMAGE' | 'FILE' | 'VOICE' | 'SYSTEM'
    timestamp: string
    isRead: boolean
    isDelivered: boolean
    isEdited: boolean
    replyTo?: string
    attachments?: Array<{
      url: string
      name: string
      type: string
      size: number
    }>
  }>
}

const EmployeeChatContent: React.FC<{ user: any }> = ({ user }) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [selectedConsultant, setSelectedConsultant] = useState<Consultant | null>(null)
  const [activeChat, setActiveChat] = useState<ChatRoom | null>(null)
  const [isInitiatingChat, setIsInitiatingChat] = useState(false)

  const handleSelectConsultant = async (consultant: Consultant) => {
    setSelectedConsultant(consultant)
    setIsInitiatingChat(true)

    try {
      // Create or get existing chat room with this consultant
      const chatRoom = await createOrGetChatRoom(consultant)
      setActiveChat(chatRoom)
      toast.success(`Connected with ${consultant.name}`)
    } catch (error) {
      console.error('Error initiating chat:', error)
      toast.error('Failed to start chat. Please try again.')
    } finally {
      setIsInitiatingChat(false)
    }
  }

  const createOrGetChatRoom = async (consultant: Consultant): Promise<ChatRoom> => {
    // Mock implementation with realistic conversation history
    return new Promise((resolve) => {
      setTimeout(() => {
        // Create different conversation histories based on consultant
        const getConversationHistory = (consultantId: string, consultantName: string) => {
          const baseTime = Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
          
          switch (consultantId) {
            case '1': // Dr. Sarah Johnson - Stress Management
              return [
                {
                  id: 'msg-1',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: `Hi ${user?.name}! I'm Dr. Sarah Johnson, your wellness consultant. I specialize in stress management and work-life balance. How can I help you today?`,
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-2',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: user?.id || '',
                  senderName: user?.name || '',
                  senderAvatar: user?.image,
                  content: "Hi Dr. Johnson! I've been feeling quite overwhelmed with work lately. I'm struggling to manage my stress levels.",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 5 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-3',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: "I understand how challenging that can be. Workplace stress is very common, and you're taking the right step by reaching out. Can you tell me more about what specific aspects of work are causing you the most stress?",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 8 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-4',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: user?.id || '',
                  senderName: user?.name || '',
                  senderAvatar: user?.image,
                  content: "It's mainly the tight deadlines and heavy workload. I feel like I'm always racing against time, and it's affecting my sleep.",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 15 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-5',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: "Sleep disruption is a clear sign that stress is impacting your wellbeing. Let's work on some practical strategies. Have you tried any stress management techniques before, like deep breathing or time-blocking?",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 18 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                }
              ]

            case '2': // Ahmad Al-Hassan - Leadership
              return [
                {
                  id: 'msg-1',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: `Hello ${user?.name}! I'm Ahmad Al-Hassan, your leadership development consultant. I'm here to help you enhance your leadership skills and team communication. What would you like to work on?`,
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-2',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: user?.id || '',
                  senderName: user?.name || '',
                  senderAvatar: user?.image,
                  content: "Hi Ahmad! I've recently been promoted to a team lead position, and I'm struggling with giving feedback to my team members. Any advice?",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 10 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-3',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: "Congratulations on your promotion! Giving effective feedback is indeed one of the most important leadership skills. The key is to be specific, timely, and constructive. Are you finding it difficult to deliver critical feedback, or is it more about structuring the conversation?",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 12 * 60 * 1000).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                }
              ]

            case '3': // Priya Sharma - Nutrition & Fitness
              return [
                {
                  id: 'msg-1',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: `Hi ${user?.name}! I'm Priya Sharma, your nutrition and fitness consultant. I'm currently unavailable but will be back soon. In the meantime, feel free to share your wellness goals!`,
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime).toISOString(),
                  isRead: true,
                  isDelivered: true,
                  isEdited: false
                },
                {
                  id: 'msg-2',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: user?.id || '',
                  senderName: user?.name || '',
                  senderAvatar: user?.image,
                  content: "Hi Priya! I'm looking to improve my eating habits during work hours. I tend to skip meals or grab unhealthy snacks when I'm busy.",
                  type: 'TEXT' as const,
                  timestamp: new Date(baseTime + 30 * 60 * 1000).toISOString(),
                  isRead: false,
                  isDelivered: true,
                  isEdited: false
                }
              ]

            default:
              return [
                {
                  id: 'welcome-msg',
                  chatId: `chat-${consultantId}-${user?.id}`,
                  senderId: consultantId,
                  senderName: consultantName,
                  senderAvatar: consultant.image,
                  content: `Hi ${user?.name}! I'm ${consultantName}, your wellness consultant. How can I help you today?`,
                  type: 'TEXT' as const,
                  timestamp: new Date().toISOString(),
                  isRead: false,
                  isDelivered: true,
                  isEdited: false
                }
              ]
          }
        }

        const mockChatRoom: ChatRoom = {
          id: `chat-${consultant.id}-${user?.id}`,
          chatId: `chat-${consultant.id}-${user?.id}`,
          type: 'DIRECT',
          participants: [
            {
              id: consultant.id,
              name: consultant.name,
              avatar: consultant.image,
              role: 'CONSULTANT',
              isOnline: consultant.availability.isAvailable,
            },
            {
              id: user?.id || '',
              name: user?.name || '',
              avatar: user?.image,
              role: 'EMPLOYEE',
              isOnline: true,
            }
          ],
          lastActivity: new Date().toISOString(),
          unreadCount: consultant.id === '3' ? 1 : 0, // Priya has 1 unread message
          messages: getConversationHistory(consultant.id, consultant.name)
        }
        resolve(mockChatRoom)
      }, 1000)
    })
  }

  const handleBackToConsultants = () => {
    setSelectedConsultant(null)
    setActiveChat(null)
  }

  const handleStartCall = (type: 'voice' | 'video') => {
    if (!selectedConsultant) return
    
    toast.success(`Starting ${type} call with ${selectedConsultant.name}...`)
    // TODO: Implement call functionality
  }

  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  return (
    <AdminLayout user={user} onLogout={handleLogout}>
      <div className="h-[calc(100vh-60px)] bg-sehatti-warm-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-sehatti-warm-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-sehatti-warm-gray-900">
                Wellness Chat
              </h1>
              <p className="text-sm text-sehatti-warm-gray-600">
                Connect with professional wellness consultants
              </p>
            </div>
            
            {selectedConsultant && (
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-sehatti-warm-gray-900">
                    Chatting with
                  </p>
                  <p className="text-xs text-sehatti-warm-gray-600">
                    {selectedConsultant.name}
                  </p>
                </div>
                <div className="w-10 h-10 rounded-full bg-sehatti-gold-500 flex items-center justify-center">
                  {selectedConsultant.image ? (
                    <img 
                      src={selectedConsultant.image} 
                      alt={selectedConsultant.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-white font-medium">
                      {selectedConsultant.name.charAt(0)}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

              {/* Main Content */}
        <div className="h-[calc(100vh-140px)] flex">
        {!selectedConsultant ? (
          // Consultant Browser
          <div className="w-full">
            <ConsultantBrowser
              onSelectConsultant={handleSelectConsultant}
              selectedConsultantId={selectedConsultant?.id}
            />
          </div>
        ) : (
          // Split view: Consultant info + Chat
          <>
            {/* Consultant Info Sidebar */}
            <div className="w-80 bg-white border-r border-sehatti-warm-gray-200 overflow-y-auto">
              <div className="p-6">
                <div className="text-center mb-6">
                  <div className="relative inline-block">
                    {selectedConsultant.image ? (
                      <img 
                        src={selectedConsultant.image} 
                        alt={selectedConsultant.name}
                        className="w-20 h-20 rounded-full object-cover mx-auto"
                      />
                    ) : (
                      <div className="w-20 h-20 rounded-full bg-sehatti-gold-500 flex items-center justify-center mx-auto">
                        <span className="text-white font-bold text-2xl">
                          {selectedConsultant.name.charAt(0)}
                        </span>
                      </div>
                    )}
                    {selectedConsultant.availability.isAvailable && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 border-2 border-white rounded-full" />
                    )}
                  </div>
                  
                  <h3 className="text-lg font-bold text-sehatti-warm-gray-900 mt-3">
                    {selectedConsultant.name}
                  </h3>
                  <p className="text-sm text-sehatti-warm-gray-600">
                    {selectedConsultant.major}
                  </p>
                  
                  <div className="flex items-center justify-center gap-4 mt-3">
                    <div className="flex items-center gap-1">
                      <span className="text-yellow-500">⭐</span>
                      <span className="text-sm font-medium">
                        {selectedConsultant.rating.averageRating.toFixed(1)}
                      </span>
                    </div>
                    <div className="text-sm text-sehatti-warm-gray-600">
                      {selectedConsultant.yearsOfExperience} years exp.
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-semibold text-sehatti-warm-gray-900 mb-2">
                      About
                    </h4>
                    <p className="text-sm text-sehatti-warm-gray-700">
                      {selectedConsultant.bio}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-sehatti-warm-gray-900 mb-2">
                      Specializations
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {selectedConsultant.specializations.map((spec) => (
                        <span 
                          key={spec}
                          className="px-2 py-1 bg-sehatti-gold-100 text-sehatti-gold-800 rounded-full text-xs"
                        >
                          {spec.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-sehatti-warm-gray-900 mb-2">
                      Languages
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {selectedConsultant.languages.map((lang) => (
                        <span 
                          key={lang}
                          className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                        >
                          {lang.charAt(0) + lang.slice(1).toLowerCase()}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-sehatti-warm-gray-900 mb-2">
                      Availability
                    </h4>
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        selectedConsultant.availability.isAvailable ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <span className="text-sm text-sehatti-warm-gray-700">
                        {selectedConsultant.availability.isAvailable ? 'Available now' : 'Currently busy'}
                      </span>
                    </div>
                  </div>

                  {selectedConsultant.hourlyRate && (
                    <div>
                      <h4 className="text-sm font-semibold text-sehatti-warm-gray-900 mb-2">
                        Rate
                      </h4>
                      <p className="text-sm text-sehatti-warm-gray-700">
                        ${selectedConsultant.hourlyRate}/{selectedConsultant.currency} per hour
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Chat Interface */}
            <div className="flex-1">
              {isInitiatingChat ? (
                <div className="h-full flex items-center justify-center bg-sehatti-warm-gray-50">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold-500 mx-auto mb-4"></div>
                    <p className="text-sehatti-warm-gray-600">Connecting to {selectedConsultant.name}...</p>
                  </div>
                </div>
              ) : (
                <ChatInterface
                  chatRoom={activeChat}
                  onBack={handleBackToConsultants}
                  onStartCall={handleStartCall}
                />
              )}
            </div>
          </>
        )}
        </div>
      </div>
    </AdminLayout>
  )
}

// Main wrapper component that fetches user data
const EmployeeChat: React.FC = () => {
  const localAuthState = useAppSelector(selectAuth)
  
  // First try to use local auth state
  if (localAuthState.isAuthenticated && localAuthState.user) {
    return <EmployeeChatContent user={localAuthState.user} />
  }

  // Fallback: try API query if local auth fails
  const { data: userData, isLoading, error } = useGetMeQuery()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData?.data?.user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Alert variant="destructive">
          Failed to load user data. Please refresh the page.
        </Alert>
      </div>
    )
  }

  return <EmployeeChatContent user={userData.data.user} />
}

export default EmployeeChat 