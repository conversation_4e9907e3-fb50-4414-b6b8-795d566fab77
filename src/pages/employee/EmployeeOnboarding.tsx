import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import type { RootState } from '../../store'
import EmployeeOnboardingFlow from '../../components/employee/EmployeeOnboardingFlow'

const EmployeeOnboarding: React.FC = () => {
  const navigate = useNavigate()
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth)

  // Check if user is authenticated and is an employee
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login')
      return
    }

    if (user?.role !== 'EMPLOYEE') {
      // Redirect non-employees to their appropriate dashboard
      switch (user?.role) {
        case 'SYSTEM_ADMIN':
          navigate('/admin/dashboard')
          break
        case 'HR_ADMIN':
          navigate('/hr/dashboard')
          break
        case 'CONSULTANT':
          navigate('/consultant/dashboard')
          break
        default:
          navigate('/login')
      }
      return
    }

    // Check if onboarding is already completed
    const isOnboardingCompleted = localStorage.getItem('sehatti-onboarding-completed') === 'true'
    
    if (isOnboardingCompleted) {
      navigate('/employee/dashboard')
      return
    }
    
    // Also check the detailed progress for backward compatibility
    const onboardingProgress = localStorage.getItem('sehatti-onboarding-progress')
    if (onboardingProgress) {
      try {
        const progress = JSON.parse(onboardingProgress)
        if (progress.currentStep === 'completed' || progress.completedAt) {
          // Mark as completed for future quick checks
          localStorage.setItem('sehatti-onboarding-completed', 'true')
          navigate('/employee/dashboard')
          return
        }
      } catch (error) {
        console.error('Failed to parse onboarding progress:', error)
        // Continue with onboarding if parsing fails
      }
    }
  }, [isAuthenticated, user, navigate])

  // Show loading while checking authentication
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show error if user is not an employee
  if (user.role !== 'EMPLOYEE') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">This page is only accessible to employees.</p>
        </div>
      </div>
    )
  }

  return <EmployeeOnboardingFlow />
}

export default EmployeeOnboarding 