import React, { useState, useEffect } from 'react'
import { 
  FaPlay, 
  FaSearch, 
  FaGlobe,
  FaEye,
  FaClock,
  FaStar,
  FaTag,
  FaBookOpen,
  FaVideo,
  FaHeart,
  FaThumbsUp,
  FaArrowLeft,
  FaFolder,
  FaFilm,
  FaTimes
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Button } from '../../components/ui/Button'
import { Badge } from '../../components/ui/Badge'
import { Input } from '../../components/ui/Input'
import { AdminLayout } from '../../components/layout/AdminLayout'
import { useGetMeQuery } from '@/store/api'
import { useAppDispatch } from '@/store/store'
import { logout } from '@/features/auth/auth-slice'
import { GradientText } from '../../components/ui/GradientText'

interface Hub {
  hub_id: string
  content: {
    en?: {
      title: string
      description: string
    }
    ar?: {
      title: string
      description: string
    }
    hi?: {
      title: string
      description: string
    }
  }
  image_url?: string
  category?: string
  status: string
  content_count: number
  created_at: string
  tags?: string[]
}

interface ContentItem {
  content_id: string
  hub_id: string
  title: {
    en: string
    ar: string
    hi: string
  }
  description: {
    en: string
    ar: string
    hi: string
  }
  tags: {
    en: string[]
    ar: string[]
    hi: string[]
  }
  video_url: string
  thumbnail_url: string
  status: string
  created_at: string
}

interface WellnessArticle {
  id: string
  titleEN: string
  titleAR: string
  titleHI: string
  descriptionEN: string
  descriptionAR: string
  descriptionHI: string
  category: string
  division: string
  readTime: number
  source: string
}

// Articles from EmployeeDashboard
const WELLNESS_ARTICLES: WellnessArticle[] = [
  {
    id: 'EW01',
    titleEN: 'The place where you work affects the way you work: How does the environment shape your productivity?',
    titleAR: 'المكان الذي تعمل فيه يؤثر على الطريقة التي تعمل بها: كيف تشكّل البيئة إنتاجيتك؟',
    titleHI: 'जिस स्थान पर आप काम करते हैं, वह आपके काम करने के तरीके को प्रभावित करता है: आपकी उत्पादकता को पर्यावरण कैसे आकार देता है?',
    descriptionEN: 'An employee may be skilled, disciplined, and have high intentions, but if the work environment does not support them, their energy dissipates silently...',
    descriptionAR: 'قد يكون الموظف ماهرًا، منضبطًا، وذو نوايا عالية، لكن إن كانت بيئة العمل لا تدعمه، فإن طاقته تتبدد بصمت...',
    descriptionHI: 'कर्मचारी कुशल, अनुशासित और उच्च इरादों वाला हो सकता है, लेकिन अगर कार्यस्थल का वातावरण उसका समर्थन नहीं करता...',
    category: 'environmental',
    division: 'Environmental Wellbeing',
    readTime: 5,
    source: 'Harvard T.H. Chan School of Public Health'
  },
  {
    id: 'EW02',
    titleEN: 'Light, ventilation, colors: small details make a big difference in your performance.',
    titleAR: 'الضوء، التهوية، الألوان: تفاصيل صغيرة تُحدث فرقًا كبيرًا في أدائك',
    titleHI: 'प्रकाश, वेंटिलेशन, रंग: छोटी-छोटी बातें आपके प्रदर्शन में बड़ा अंतर ला सकती हैं।',
    descriptionEN: 'Research from the University of Oregon indicates that natural lighting reduces eye and mental stress and increases productivity by up to 40%...',
    descriptionAR: 'تشير أبحاث من University of Oregon إلى أن الإضاءة الطبيعية تقلل من الإجهاد العيني والعقلي، وتزيد من الإنتاجية بنسبة تصل إلى 40%...',
    descriptionHI: 'University of Oregon के शोध से पता चलता है कि प्राकृतिक प्रकाश आंखों और मानसिक तनाव को कम करता है, और उत्पादकता को 40% तक बढ़ा देता है...',
    category: 'environmental',
    division: 'Environmental Wellbeing',
    readTime: 4,
    source: 'University of Oregon'
  },
  {
    id: 'IW01',
    titleEN: 'Professional curiosity: How does it fuel thinking and drive performance?',
    titleAR: 'الفضول المهني: كيف يُغذي التفكير ويُحرّك الأداء؟',
    titleHI: 'पेशेवर जिज्ञासा: यह सोच को कैसे पोषित करती है और प्रदर्शन को कैसे प्रेरित करती है?',
    descriptionEN: 'Professional curiosity does not mean dissatisfaction, but rather a thirst for knowledge — not accepting things as they are without investigating them...',
    descriptionAR: 'الفضول المهني لا يعني عدم الرضا، بل يعني العطش المعرفي — أن لا تقبل الأمور كما هي دون أن تُفتّش خلفها...',
    descriptionHI: 'पेशेवर जिज्ञासा का मतलब असंतोष नहीं है, बल्कि ज्ञान की प्यास है — चीजों को वैसे ही स्वीकार न करना जैसा वे हैं...',
    category: 'intellectual',
    division: 'Intellectual Wellbeing',
    readTime: 6,
    source: 'Harvard Business School'
  },
  {
    id: 'EMW01',
    titleEN: 'How does your emotional state affect your professional day?',
    titleAR: 'كيف تؤثر حالتك العاطفية على يومك المهني؟',
    titleHI: 'आपकी भावनात्मक स्थिति आपके पेशेवर दिन को कैसे प्रभावित करती है?',
    descriptionEN: 'Research from the Yale Center for Emotional Intelligence indicates that employees who start their day in a negative mood achieve 20% less productivity...',
    descriptionAR: 'تشير أبحاث من Yale Center for Emotional Intelligence إلى أن الموظفين الذين يبدأون يومهم بمزاج سلبي يحققون إنتاجية أقل بنسبة 20%...',
    descriptionHI: 'Yale Center for Emotional Intelligence के शोध से पता चलता है कि जो कर्मचारी अपने दिन की शुरुआत नकारात्मक मूड के साथ करते हैं...',
    category: 'emotional',
    division: 'Emotional Wellbeing',
    readTime: 7,
    source: 'Yale Center for Emotional Intelligence'
  }
]

export default function EmployeeMediaHub() {
  const dispatch = useAppDispatch()
  const { data: userResponse } = useGetMeQuery()
  // Handle both response structures: ApiResponse<{user: User}> and direct {user: User}
  const user = userResponse?.data?.user || (userResponse as any)?.user

  // Debug logging for routing issues
  console.log('🎬 EmployeeMediaHub component rendered!')
  console.log('🎬 Current URL:', window.location.href)
  console.log('🎬 User response:', userResponse)
  console.log('🎬 User data:', user)
  console.log('🎬 User role:', user?.role)

  const [selectedTab, setSelectedTab] = useState<'media' | 'articles'>('media')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'ar' | 'hi'>('en')
  
  // Hub/Content state
  const [hubs, setHubs] = useState<Hub[]>([])
  const [selectedHub, setSelectedHub] = useState<Hub | null>(null)
  const [content, setContent] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(true)
  const [contentLoading, setContentLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedVideo, setSelectedVideo] = useState<ContentItem | null>(null)
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)

  const handleLogout = () => {
    dispatch(logout())
  }

  // Fetch hubs (categories)
  useEffect(() => {
    const fetchHubs = async () => {
      try {
        setLoading(true)
        const response = await fetch('https://apis.dev.sehatti.app/api/media/v1/hubs/?limit=50&status=active')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        console.log('🎬 Hubs response:', data)
        setHubs(data.data || [])
      } catch (err) {
        console.error('Error fetching hubs:', err)
        setError('Failed to load media hubs')
      } finally {
        setLoading(false)
      }
    }

    fetchHubs()
  }, [])

  // Fetch content for selected hub
  const fetchHubContent = async (hubId: string) => {
    try {
      setContentLoading(true)
      const response = await fetch(`https://apis.dev.sehatti.app/api/media/v1/media/content?hub_id=${hubId}&limit=50`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('🎬 Hub content response:', data)
      setContent(data.data || [])
    } catch (err) {
      console.error('Error fetching hub content:', err)
      setError('Failed to load hub content')
    } finally {
      setContentLoading(false)
    }
  }

  // Handle hub selection
  const handleHubSelect = (hub: Hub) => {
    setSelectedHub(hub)
    fetchHubContent(hub.hub_id)
  }

  // Handle back to hubs
  const handleBackToHubs = () => {
    setSelectedHub(null)
    setContent([])
  }

  // Get localized hub content
  const getLocalizedHubTitle = (hub: Hub) => {
    return hub.content[selectedLanguage]?.title || hub.content.en?.title || 'Untitled Hub'
  }

  const getLocalizedHubDescription = (hub: Hub) => {
    return hub.content[selectedLanguage]?.description || hub.content.en?.description || ''
  }

  // Get localized content
  const getLocalizedTitle = (item: ContentItem) => {
    return item.title[selectedLanguage] || item.title.en
  }

  const getLocalizedDescription = (item: ContentItem) => {
    return item.description[selectedLanguage] || item.description.en
  }

  const getLocalizedArticleTitle = (article: WellnessArticle) => {
    return selectedLanguage === 'en' ? article.titleEN : 
           selectedLanguage === 'ar' ? article.titleAR : article.titleHI
  }

  const getLocalizedArticleDescription = (article: WellnessArticle) => {
    return selectedLanguage === 'en' ? article.descriptionEN : 
           selectedLanguage === 'ar' ? article.descriptionAR : article.descriptionHI
  }

  // Get correct image URL for hubs
  const getCorrectImageUrl = (imageUrl: string | undefined) => {
    if (!imageUrl) return '/api/placeholder/320/180'
    
    // If it's already a full URL with correct domain, use it
    if (imageUrl.includes('apis.dev.sehatti.app/api/media')) {
      return imageUrl
    }
    
    // If it starts with localhost, replace with correct domain
    if (imageUrl.includes('localhost:8000')) {
      return imageUrl.replace('localhost:8000', 'apis.dev.sehatti.app/api/media')
    }
    
    // If it's a relative path, prepend the media service URL
    if (imageUrl.startsWith('/api/v1/stream/')) {
      return `https://apis.dev.sehatti.app/api/media${imageUrl}`
    }
    
    return imageUrl
  }

  // Filter hubs based on search
  const filteredHubs = hubs.filter(hub => 
    getLocalizedHubTitle(hub).toLowerCase().includes(searchTerm.toLowerCase()) ||
    getLocalizedHubDescription(hub).toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Filter content based on search
  const filteredContent = content.filter(item => 
    getLocalizedTitle(item).toLowerCase().includes(searchTerm.toLowerCase()) ||
    getLocalizedDescription(item).toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredArticles = WELLNESS_ARTICLES.filter(article => 
    getLocalizedArticleTitle(article).toLowerCase().includes(searchTerm.toLowerCase()) ||
    getLocalizedArticleDescription(article).toLowerCase().includes(searchTerm.toLowerCase())
  )

  const tabs = [
    { id: 'media', label: 'Media Hub', icon: FaVideo },
    { id: 'articles', label: 'Wellness Articles', icon: FaBookOpen }
  ]

  return (
    <AdminLayout user={user} onLogout={handleLogout}>
      <div className="flex flex-col min-h-screen">
        <div className="flex-1 max-w-7xl mx-auto px-4 py-8 w-full">
          {/* Header */}
          <div className="mb-8">
            <GradientText className="text-3xl font-bold mb-2">
              Wellness Content Hub
            </GradientText>
            <p className="text-sehatti-warm-gray-600">
              Access wellness videos, articles, and educational content
            </p>
          </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder={selectedHub ? "Search content..." : "Search hubs..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <FaGlobe className="w-4 h-4 text-gray-600" />
            <select 
              value={selectedLanguage} 
              onChange={(e) => setSelectedLanguage(e.target.value as 'en' | 'ar' | 'hi')}
              className="px-3 py-2 border rounded-md text-sm"
            >
              <option value="en">English</option>
              <option value="ar">العربية</option>
              <option value="hi">हिंदी</option>
            </select>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-sehatti-warm-gray-200 mb-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const TabIcon = tab.icon
              const isActive = selectedTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as 'media' | 'articles')}
                  className={`
                    flex items-center gap-2 py-4 border-b-2 transition-colors
                    ${isActive 
                      ? 'border-sehatti-gold-500 text-sehatti-gold-600' 
                      : 'border-transparent text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900'
                    }
                  `}
                >
                  <TabIcon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                  <Badge variant="secondary">
                    {tab.id === 'media' ? (selectedHub ? filteredContent.length : filteredHubs.length) : filteredArticles.length}
                  </Badge>
                </button>
              )
            })}
          </div>
        </div>

        {/* Media Content */}
        {selectedTab === 'media' && (
          <div>
            {/* Back button when viewing hub content */}
            {selectedHub && (
              <div className="mb-6">
                <Button 
                  variant="outline" 
                  onClick={handleBackToHubs}
                  className="mb-4"
                >
                  <FaArrowLeft className="w-4 h-4 mr-2" />
                  Back to All Hubs
                </Button>
                <div className="mb-4">
                  <h2 className="text-2xl font-bold text-sehatti-warm-gray-900 mb-2">
                    {getLocalizedHubTitle(selectedHub)}
                  </h2>
                  <p className="text-sehatti-warm-gray-600">
                    {getLocalizedHubDescription(selectedHub)}
                  </p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-sehatti-warm-gray-500">
                    <div className="flex items-center gap-1">
                      <FaFolder className="w-3 h-3" />
                      <span>{selectedHub.content_count} videos</span>
                    </div>
                    {selectedHub.category && (
                      <Badge variant="secondary">{selectedHub.category}</Badge>
                    )}
                  </div>
                </div>
              </div>
            )}

            {loading && (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold-500 mx-auto"></div>
                <p className="mt-4 text-sehatti-warm-gray-600">Loading wellness hubs...</p>
              </div>
            )}

            {error && (
              <Card className="p-8 text-center">
                <CardContent>
                  <p className="text-red-600 mb-4">Error: {error}</p>
                  <Button onClick={() => window.location.reload()}>
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Show hubs (categories) */}
            {!loading && !error && !selectedHub && (
              <div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  {filteredHubs.map((hub) => (
                    <Card key={hub.hub_id} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer" 
                          onClick={() => handleHubSelect(hub)}>
                      <div className="relative aspect-[4/3] sm:aspect-[16/10] bg-gray-100">
                        <img
                          src={getCorrectImageUrl(hub.image_url)}
                          alt={getLocalizedHubTitle(hub)}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUM5Qzk3Ii8+Cjwvc3ZnPgo='
                          }}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                          <div className="text-center text-white">
                            <FaFolder className="w-8 h-8 mx-auto mb-2" />
                            <p className="text-sm font-medium">View Content</p>
                          </div>
                        </div>
                      </div>
                      
                      <CardContent className="p-3 sm:p-4">
                        <h3 className="font-semibold text-base sm:text-lg mb-2 line-clamp-2">
                          {getLocalizedHubTitle(hub)}
                        </h3>
                        <p className="text-sehatti-warm-gray-600 text-xs sm:text-sm mb-3 line-clamp-2 sm:line-clamp-3">
                          {getLocalizedHubDescription(hub)}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs sm:text-sm text-sehatti-warm-gray-500 mb-3">
                          <div className="flex items-center gap-2 sm:gap-4">
                            <div className="flex items-center gap-1">
                              <FaFolder className="w-3 h-3" />
                              <span>{hub.content_count} videos</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <FaEye className="w-3 h-3" />
                              <span>{Math.floor(Math.random() * 500) + 100} views</span>
                            </div>
                          </div>
                          <Badge variant="secondary" className="text-xs px-2 py-1">
                            {hub.category || 'General'}
                          </Badge>
                        </div>
                        
                        <Button className="w-full text-sm py-2">
                          <FaFolder className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                          <span className="hidden sm:inline">Explore Hub</span>
                          <span className="sm:hidden">Explore</span>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                {!loading && !error && filteredHubs.length === 0 && (
                  <Card className="p-8 text-center">
                    <CardContent>
                      <FaFolder className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-600 mb-2">No hubs found</h3>
                      <p className="text-gray-500">Try adjusting your search terms or language selection.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Show content within selected hub */}
            {!loading && !error && selectedHub && (
              <div>
                {contentLoading && (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sehatti-gold-500 mx-auto"></div>
                    <p className="mt-4 text-sehatti-warm-gray-600">Loading hub content...</p>
                  </div>
                )}

                {!contentLoading && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {filteredContent.map((item) => (
                      <Card key={item.content_id} className="overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="relative aspect-[4/3] sm:aspect-[16/10] bg-gray-100">
                          <img
                            src={item.thumbnail_url}
                            alt={getLocalizedTitle(item)}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUM5Qzk3Ii8+Cjwvc3ZnPgo='
                            }}
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                            <Button 
                              size="lg"
                              className="rounded-full"
                              onClick={() => {
                                setSelectedVideo(item)
                                setIsVideoModalOpen(true)
                              }}
                            >
                              <FaPlay className="w-5 h-5" />
                            </Button>
                          </div>
                        </div>
                        
                        <CardContent className="p-3 sm:p-4">
                          <h3 className="font-semibold text-base sm:text-lg mb-2 line-clamp-2">
                            {getLocalizedTitle(item)}
                          </h3>
                          <p className="text-sehatti-warm-gray-600 text-xs sm:text-sm mb-3 line-clamp-2 sm:line-clamp-3">
                            {getLocalizedDescription(item)}
                          </p>
                          
                          <div className="flex items-center justify-between text-xs sm:text-sm text-sehatti-warm-gray-500 mb-3">
                            <div className="flex items-center gap-2 sm:gap-4">
                              <div className="flex items-center gap-1">
                                <FaEye className="w-3 h-3" />
                                <span className="hidden sm:inline">{Math.floor(Math.random() * 1000) + 100}</span>
                                <span className="sm:hidden">{Math.floor(Math.random() * 10) + 1}k</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <FaClock className="w-3 h-3" />
                                <span>{Math.floor(Math.random() * 10) + 5}m</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <FaStar className="w-3 h-3 text-yellow-500" />
                              <span>{(Math.random() * 2 + 3).toFixed(1)}</span>
                            </div>
                          </div>
                          
                          <div className="flex flex-wrap gap-1 mb-3">
                            {(item.tags[selectedLanguage] || item.tags.en).slice(0, 2).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs px-2 py-1">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          
                          <Button 
                            className="w-full text-sm py-2" 
                            onClick={() => {
                              setSelectedVideo(item)
                              setIsVideoModalOpen(true)
                            }}
                          >
                            <FaPlay className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                            <span className="hidden sm:inline">Watch Video</span>
                            <span className="sm:hidden">Watch</span>
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {!contentLoading && filteredContent.length === 0 && (
                  <Card className="p-8 text-center">
                    <CardContent>
                      <FaVideo className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-600 mb-2">No videos found in this hub</h3>
                      <p className="text-gray-500">This hub doesn't contain any videos yet or try adjusting your search.</p>
                      <Button onClick={handleBackToHubs} className="mt-4">
                        <FaArrowLeft className="w-4 h-4 mr-2" />
                        Back to All Hubs
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        )}

        {/* Articles Content */}
        {selectedTab === 'articles' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredArticles.map((article) => (
              <Card key={article.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-2 line-clamp-2">
                        {getLocalizedArticleTitle(article)}
                      </CardTitle>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="text-xs">
                          {article.division}
                        </Badge>
                        <div className="flex items-center gap-1 text-sm text-sehatti-warm-gray-500">
                          <FaClock className="w-3 h-3" />
                          <span>{article.readTime} min read</span>
                        </div>
                      </div>
                      <p className="text-sm text-sehatti-warm-gray-600 line-clamp-3">
                        {getLocalizedArticleDescription(article)}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-sehatti-warm-gray-500">
                      <span>Source: {article.source}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <FaBookOpen className="w-3 h-3 mr-1" />
                        Read
                      </Button>
                      <Button size="sm" variant="ghost">
                        <FaHeart className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {filteredArticles.length === 0 && (
              <div className="col-span-2">
                <Card className="p-8 text-center">
                  <CardContent>
                    <FaBookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No articles found</h3>
                    <p className="text-gray-500">Try adjusting your search terms or language selection.</p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        )}

        {/* Video Modal */}
        {isVideoModalOpen && selectedVideo && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-xl font-semibold line-clamp-1">
                  {getLocalizedTitle(selectedVideo)}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsVideoModalOpen(false)
                    setSelectedVideo(null)
                  }}
                >
                  <FaTimes className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="p-4">
                <div className="aspect-video bg-black rounded-lg mb-4">
                  <video
                    key={selectedVideo.video_url}
                    controls
                    className="w-full h-full rounded-lg"
                    src={selectedVideo.video_url}
                    poster={selectedVideo.thumbnail_url}
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-lg mb-2">
                      {getLocalizedTitle(selectedVideo)}
                    </h4>
                    <p className="text-sehatti-warm-gray-600">
                      {getLocalizedDescription(selectedVideo)}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-sehatti-warm-gray-500">
                    <div className="flex items-center gap-1">
                      <FaEye className="w-3 h-3" />
                      <span>{Math.floor(Math.random() * 1000) + 100} views</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FaClock className="w-3 h-3" />
                      <span>{Math.floor(Math.random() * 10) + 5}m</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FaStar className="w-3 h-3 text-yellow-500" />
                      <span>{(Math.random() * 2 + 3).toFixed(1)}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {(selectedVideo.tags[selectedLanguage] || selectedVideo.tags.en).map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex items-center gap-2 pt-4 border-t">
                    <Button variant="outline" size="sm">
                      <FaHeart className="w-3 h-3 mr-1" />
                      Like
                    </Button>
                    <Button variant="outline" size="sm">
                      <FaThumbsUp className="w-3 h-3 mr-1" />
                      Helpful
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </AdminLayout>
  )
}