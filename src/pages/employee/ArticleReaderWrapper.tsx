import React from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '@/store/store'
import ArticleReader from './ArticleReader'

const ArticleReaderWrapper: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth.user)

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-gray-600">Please wait while we load your article.</p>
        </div>
      </div>
    )
  }

  return <ArticleReader user={user} />
}

export default ArticleReaderWrapper 