import { 
  FaUsers, 
  FaCog, 
  FaChartBar, 
  FaBuilding,
  FaFileAlt,
  FaShieldAlt,
  FaUserShield,
  FaChartLine,
  FaExclamationTriangle,
  FaServer,
  FaDatabase,
  FaBell,
  FaSync,
  FaVideo,
  FaQuestion,
  FaPoll,
  FaChartPie,
  FaUsers as FaUserGroup,
  FaPlay,
  FaPause,
  FaClock,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { GradientText } from '@/components/ui/GradientText'
import { Badge } from '@/components/ui/Badge'
import { AdminLayout } from '@/components/layout/AdminLayout'
import { useDashboardData } from '@/hooks/useDashboardData'
import type { User } from '@/types/api'

interface AdminDashboardProps {
  user: User
}

const AdminDashboard = ({ user }: AdminDashboardProps) => {
  const dashboardData = useDashboardData()
  const {
    systemStats,
    companies,
    recentActivities,
    systemAlerts,
    surveyAnalytics,
    mediaStats,
    loading,
    error,
    lastUpdated
  } = dashboardData

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-200'
      case 'error': return 'text-red-600 bg-red-100 border-red-200'
      case 'warning': return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case 'info': return 'text-blue-600 bg-blue-100 border-blue-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'system': return <FaServer className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
      case 'survey': return <FaPoll className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
      case 'media': return <FaVideo className="w-3 h-3 sm:w-4 sm:h-4 text-purple-600" />
      case 'user': return <FaUsers className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
      case 'company': return <FaBuilding className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
      case 'content': return <FaFileAlt className="w-3 h-3 sm:w-4 sm:h-4 text-purple-600" />
      case 'assessment': return <FaChartBar className="w-3 h-3 sm:w-4 sm:h-4 text-indigo-600" />
      default: return <FaCog className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  const formatLastUpdated = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime()
    const minutes = Math.floor(diff / 60000)
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    return new Date(timestamp).toLocaleDateString()
  }

  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token");
    window.location.href = "/login";
  }

  return (
    <AdminLayout
      user={user}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={systemAlerts?.length || 0}
      onNotificationClick={() => console.log('Notifications clicked')}
    >
      <div className="space-y-4 sm:space-y-6">
        {/* Header - Mobile First */}
        <header className="space-y-3 sm:space-y-4 py-4 sm:py-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">
              <GradientText gradient="goldRich" size="2xl" className="sm:text-3xl lg:text-4xl">
                System Admin Dashboard
              </GradientText>
            </h1>
            <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Welcome back, {user.name}
            </p>
          </div>
          
          <div className="flex flex-col items-center gap-2 sm:flex-row sm:gap-4">
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaUserShield className="mr-1 w-3 h-3" /> System Administrator
            </Badge>
            <div className="flex items-center gap-2">
              <Badge 
                variant={systemStats?.systemHealth === 'Healthy' ? 'success' : 'warning'}
                className="text-xs sm:text-sm"
              >
                {systemStats?.systemHealth || 'Loading...'}
              </Badge>
              {loading && <FaSync className="animate-spin text-sehatti-gold-600 w-3 h-3 sm:w-4 sm:h-4" />}
              {lastUpdated && (
                <span className="text-xs text-gray-500">
                  Updated {formatLastUpdated(lastUpdated)}
                </span>
              )}
            </div>
          </div>
        </header>

        {/* Error Display - Mobile Optimized */}
        {error && (
          <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-start gap-2">
              <FaExclamationTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span className="text-sm sm:text-base">Error loading dashboard data: {error}</span>
            </div>
          </div>
        )}

        {/* System Stats Overview - Mobile First Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card className={`${loading ? 'animate-pulse' : ''} hover:shadow-md transition-shadow`}>
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaUsers className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Total Users</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Platform-wide users</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-sehatti-gold-600">
                {loading ? '...' : systemStats?.totalUsers?.toLocaleString() || '0'}
              </p>
              <p className="text-xs text-green-600 mt-1">
                Active across all companies
              </p>
            </CardContent>
          </Card>
          
          <Card className={`${loading ? 'animate-pulse' : ''} hover:shadow-md transition-shadow`}>
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaBuilding className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Companies</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Active organizations</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-sehatti-gold-600">
                {loading ? '...' : systemStats?.totalCompanies || 0}
              </p>
              <p className="text-xs text-green-600 mt-1">
                {companies?.filter(c => c.status === 'active').length || 0} active
              </p>
            </CardContent>
          </Card>
          
          <Card className={`${loading ? 'animate-pulse' : ''} hover:shadow-md transition-shadow`}>
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaPoll className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Surveys</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Total & Active</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-sehatti-gold-600">
                {loading ? '...' : surveyAnalytics?.totalSurveys || 0}
              </p>
              <p className="text-xs text-blue-600 mt-1">
                {surveyAnalytics?.activeSurveys || 0} active surveys
              </p>
            </CardContent>
          </Card>
          
          <Card className={`${loading ? 'animate-pulse' : ''} hover:shadow-md transition-shadow`}>
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaVideo className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Media Files</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Content & Storage</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-sehatti-gold-600">
                {loading ? '...' : systemStats?.totalMediaFiles || 0}
              </p>
              <p className="text-xs text-purple-600 mt-1">
                {mediaStats?.totalStorage || 0}GB storage
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Survey Analytics Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Survey Statistics Overview */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaChartPie className="text-sehatti-gold-600 w-5 h-5" />
                Survey Statistics
              </CardTitle>
              <CardDescription>Key survey metrics and performance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <FaUserGroup className="w-4 h-4 text-blue-600" />
                    <span className="text-xs font-medium text-blue-600">Total Responses</span>
                  </div>
                  <p className="text-xl font-bold text-blue-700">
                    {surveyAnalytics?.totalResponses || 0}
                  </p>
                </div>
                
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <FaChartLine className="w-4 h-4 text-green-600" />
                    <span className="text-xs font-medium text-green-600">Response Rate</span>
                  </div>
                  <p className="text-xl font-bold text-green-700">
                    {surveyAnalytics?.responseRate || 0}%
                  </p>
                </div>
              </div>

              {/* Survey Types Breakdown */}
              {surveyAnalytics?.surveysByType && Object.keys(surveyAnalytics.surveysByType).length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Survey Types Distribution</h4>
                  <div className="space-y-2">
                    {Object.entries(surveyAnalytics.surveysByType).map(([type, count]) => {
                      const percentage = surveyAnalytics.totalSurveys > 0 ? 
                        Math.round(((count as number) / surveyAnalytics.totalSurveys) * 100) : 0;
                      return (
                        <div key={type} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 capitalize">{type}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-500">{percentage}%</span>
                              <Badge variant="outline" className="text-xs">
                                {count as number}
                              </Badge>
                            </div>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className="bg-sehatti-gold-600 h-1.5 rounded-full transition-all duration-300" 
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Average Completion Time */}
              {surveyAnalytics?.averageCompletionTime > 0 && (
                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaClock className="w-4 h-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-600">Avg. Completion</span>
                  </div>
                  <span className="text-lg font-bold text-purple-700">
                    {Math.floor(surveyAnalytics.averageCompletionTime / 60)}m {surveyAnalytics.averageCompletionTime % 60}s
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Survey Activity */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaClock className="text-sehatti-gold-600 w-5 h-5" />
                Recent Survey Activity
              </CardTitle>
              <CardDescription>Latest survey-related activities</CardDescription>
            </CardHeader>
            <CardContent>
              {surveyAnalytics?.recentSurveys && surveyAnalytics.recentSurveys.length > 0 ? (
                <div className="space-y-3">
                  {surveyAnalytics.recentSurveys.slice(0, 5).map((survey: any, index: number) => (
                    <div key={survey.id || index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex-shrink-0 mt-1">
                        {survey.status === 'active' ? (
                          <FaPlay className="w-3 h-3 text-green-600" />
                        ) : (
                          <FaPause className="w-3 h-3 text-gray-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {survey.title || 'Untitled Survey'}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          {survey.survey_type && (
                            <Badge variant="outline" className="text-xs">
                              {survey.survey_type}
                            </Badge>
                          )}
                          <span className="text-xs text-gray-500">
                            {survey.created_at && formatTimestamp(survey.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaPoll className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No recent survey activity</p>
                  <p className="text-xs">Surveys will appear here once created</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top Performing Surveys */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaArrowUp className="text-sehatti-gold-600 w-5 h-5" />
                Top Performing Surveys
              </CardTitle>
              <CardDescription>Surveys with highest engagement</CardDescription>
            </CardHeader>
            <CardContent>
              {surveyAnalytics?.topPerformingSurveys && surveyAnalytics.topPerformingSurveys.length > 0 ? (
                <div className="space-y-3">
                  {surveyAnalytics.topPerformingSurveys.map((survey: any, index: number) => (
                    <div key={survey.id || index} className="p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {survey.title || 'Untitled Survey'}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {survey.survey_type || 'Survey'}
                            </Badge>
                            <span className="text-xs text-green-600 font-medium">
                              {survey.completionRate || 0}% completion
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-700">
                            {survey.responseCount || 0}
                          </p>
                          <p className="text-xs text-gray-500">responses</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaArrowUp className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No performance data yet</p>
                  <p className="text-xs">Active surveys will appear here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Service Health Cards - Mobile First */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaServer className="text-blue-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">V2 APIs</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Core backend services</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <Badge 
                  variant={systemStats?.systemHealth === 'Healthy' ? 'success' : 'warning'}
                  className="text-xs sm:text-sm"
                >
                  {systemStats?.systemHealth || 'Unknown'}
                </Badge>
                <span className="text-xs text-gray-500">
                  {systemStats?.systemUptime || '0h'} uptime
                </span>
              </div>
              <div className="mt-2 text-xs text-gray-600">
                <p>API Requests: {systemStats?.apiRequests || '0'}</p>
                <p>Storage: {systemStats?.storageUsed || '0GB'}</p>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaDatabase className="text-green-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Survey Service</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Survey management</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <Badge 
                  variant={surveyAnalytics?.totalSurveys > 0 ? 'success' : 'warning'}
                  className="text-xs sm:text-sm"
                >
                  {surveyAnalytics?.totalSurveys > 0 ? 'Active' : 'No Data'}
                </Badge>
                <span className="text-xs text-gray-500">
                  {surveyAnalytics?.activeSurveys || 0} active
                </span>
              </div>
              <div className="mt-2 text-xs text-gray-600">
                <p>Total: {surveyAnalytics?.totalSurveys || 0} surveys</p>
                <p>Responses: {surveyAnalytics?.totalResponses || 0}</p>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 sm:pb-3">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FaVideo className="text-purple-600 w-4 h-4 sm:w-5 sm:h-5" />
                <span className="truncate">Media Service</span>
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Content management</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <Badge variant="warning" className="text-xs sm:text-sm">
                  Integration Pending
                </Badge>
                <span className="text-xs text-gray-500">
                  {mediaStats?.totalFiles || 0} files
                </span>
              </div>
              <div className="mt-2 text-xs text-gray-600">
                <p>Storage: {mediaStats?.totalStorage || 0}GB</p>
                <p>Hubs: {mediaStats?.totalHubs || 0}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Alerts & Recent Activities - Mobile Optimized */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* System Alerts */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaBell className="text-sehatti-gold-600 w-5 h-5" />
                System Alerts
                {systemAlerts && systemAlerts.length > 0 && (
                  <Badge variant="warning" className="ml-2">
                    {systemAlerts.length}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>Important system notifications and warnings</CardDescription>
            </CardHeader>
            <CardContent className="max-h-80 overflow-y-auto">
              {systemAlerts && systemAlerts.length > 0 ? (
                <div className="space-y-3">
                  {systemAlerts.map((alert) => (
                    <div 
                      key={alert.id} 
                      className={`p-3 rounded-lg border ${getSeverityColor(alert.severity)}`}
                    >
                      <div className="flex items-start gap-2">
                        <div className="flex-shrink-0 mt-1">
                          {alert.severity === 'critical' && <FaExclamationTriangle className="w-4 h-4" />}
                          {alert.severity === 'warning' && <FaExclamationTriangle className="w-4 h-4" />}
                          {alert.severity === 'info' && <FaBell className="w-4 h-4" />}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm">{alert.title}</p>
                          <p className="text-xs mt-1">{alert.message}</p>
                          <p className="text-xs mt-1 opacity-75">
                            {formatTimestamp(alert.time)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaShieldAlt className="w-8 h-8 mx-auto mb-2 text-green-300" />
                  <p className="text-sm">All systems operational</p>
                  <p className="text-xs">No alerts to display</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaChartLine className="text-sehatti-gold-600 w-5 h-5" />
                Recent Activities
              </CardTitle>
              <CardDescription>Latest system and user activities</CardDescription>
            </CardHeader>
            <CardContent className="max-h-80 overflow-y-auto">
              {recentActivities && recentActivities.length > 0 ? (
                <div className="space-y-3">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className="flex-shrink-0 mt-1">
                        {getTypeIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {activity.action}
                        </p>
                        {activity.company && (
                          <p className="text-xs text-gray-600 mt-1">
                            Company: {activity.company}
                          </p>
                        )}
                        {activity.details && (
                          <p className="text-xs text-gray-500 mt-1">
                            {activity.details}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-1">
                          {formatTimestamp(activity.time)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaChartLine className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No recent activities</p>
                  <p className="text-xs">Activities will appear here as they occur</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Companies Overview - Mobile Optimized */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaBuilding className="text-sehatti-gold-600 w-5 h-5" />
              Companies Overview
              {companies && companies.length > 0 && (
                <Badge variant="outline" className="ml-2">
                  {companies.length} total
                </Badge>
              )}
            </CardTitle>
            <CardDescription>Active organizations and their survey engagement</CardDescription>
          </CardHeader>
          <CardContent>
            {companies && companies.length > 0 ? (
              <>
                {/* Summary Stats */}
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-700">
                      {companies.filter(c => c.status === 'active').length}
                    </p>
                    <p className="text-xs text-blue-600">Active Companies</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-green-700">
                      {companies.reduce((sum, c) => sum + c.employees, 0)}
                    </p>
                    <p className="text-xs text-green-600">Total Employees</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-purple-700">
                      {companies.reduce((sum, c) => sum + c.assessments, 0)}
                    </p>
                    <p className="text-xs text-purple-600">Total Surveys</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-orange-700">
                      {surveyAnalytics?.totalResponses || 0}
                    </p>
                    <p className="text-xs text-orange-600">Total Responses</p>
                  </div>
                </div>

                {/* Companies Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {companies.slice(0, 9).map((company) => {
                    const surveyEngagement = company.assessments > 0 ? 
                      Math.min(100, Math.round((company.assessments / company.employees) * 100)) : 0;
                    
                    return (
                      <div key={company.id} className="p-4 border rounded-lg hover:shadow-sm transition-all duration-200 hover:border-sehatti-gold-300">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium text-gray-900 truncate pr-2">{company.name}</h4>
                          <Badge 
                            variant={company.status === 'active' ? 'success' : 
                                    company.status === 'pending' ? 'warning' : 'secondary'}
                            className="text-xs flex-shrink-0"
                          >
                            {company.status}
                          </Badge>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <FaUsers className="w-3 h-3 text-blue-600" />
                              <span className="text-gray-600">Employees</span>
                            </div>
                            <span className="font-medium text-blue-700">{company.employees}</span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <FaPoll className="w-3 h-3 text-green-600" />
                              <span className="text-gray-600">Surveys</span>
                            </div>
                            <span className="font-medium text-green-700">{company.assessments}</span>
                          </div>
                          
                          {/* Survey Engagement Indicator */}
                          <div className="mt-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-500">Survey Engagement</span>
                              <span className="text-xs font-medium text-gray-700">{surveyEngagement}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  surveyEngagement >= 80 ? 'bg-green-500' :
                                  surveyEngagement >= 50 ? 'bg-yellow-500' :
                                  surveyEngagement >= 20 ? 'bg-orange-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${surveyEngagement}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                {companies.length > 9 && (
                  <div className="text-center mt-6">
                    <Badge variant="outline" className="text-sm px-4 py-2">
                      +{companies.length - 9} more companies • View all companies →
                    </Badge>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <FaBuilding className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">No companies registered</p>
                <p className="text-sm">Companies will appear here once they register</p>
                <p className="text-xs mt-2 text-gray-400">
                  Invite organizations to join your platform
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Insights Section */}
        <Card className="hover:shadow-md transition-shadow bg-gradient-to-r from-sehatti-gold-50 to-amber-50 border-sehatti-gold-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaChartBar className="text-sehatti-gold-600 w-5 h-5" />
              Quick Insights
            </CardTitle>
            <CardDescription>AI-powered insights and recommendations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Survey Health Insight */}
              <div className="p-4 bg-white rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <FaPoll className="w-4 h-4 text-blue-600" />
                  <h4 className="text-sm font-medium text-blue-900">Survey Health</h4>
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {surveyAnalytics?.activeSurveys > 0 ? (
                    <>
                      <span className="text-green-600 font-medium">Good:</span> {surveyAnalytics.activeSurveys} active surveys collecting feedback
                    </>
                  ) : (
                    <>
                      <span className="text-orange-600 font-medium">Attention:</span> No active surveys currently running
                    </>
                  )}
                </p>
                <div className="flex items-center gap-2">
                  {surveyAnalytics?.responseRate > 70 ? (
                    <FaArrowUp className="w-3 h-3 text-green-600" />
                  ) : (
                    <FaArrowDown className="w-3 h-3 text-orange-600" />
                  )}
                  <span className="text-xs text-gray-500">
                    {surveyAnalytics?.responseRate || 0}% response rate
                  </span>
                </div>
              </div>

              {/* User Engagement Insight */}
              <div className="p-4 bg-white rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <FaUsers className="w-4 h-4 text-green-600" />
                  <h4 className="text-sm font-medium text-green-900">User Engagement</h4>
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {systemStats?.totalUsers > 0 ? (
                    <>
                      <span className="text-green-600 font-medium">Active:</span> {systemStats.totalUsers} users across {systemStats.totalCompanies} companies
                    </>
                  ) : (
                    <>
                      <span className="text-gray-600 font-medium">Growing:</span> Platform ready for user onboarding
                    </>
                  )}
                </p>
                <div className="flex items-center gap-2">
                  <FaChartLine className="w-3 h-3 text-green-600" />
                  <span className="text-xs text-gray-500">
                    {companies?.filter(c => c.status === 'active').length || 0} active companies
                  </span>
                </div>
              </div>

              {/* System Performance Insight */}
              <div className="p-4 bg-white rounded-lg border border-purple-200">
                <div className="flex items-center gap-2 mb-2">
                  <FaServer className="w-4 h-4 text-purple-600" />
                  <h4 className="text-sm font-medium text-purple-900">System Health</h4>
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {systemStats?.systemHealth === 'Healthy' ? (
                    <>
                      <span className="text-green-600 font-medium">Excellent:</span> All services running smoothly
                    </>
                  ) : (
                    <>
                      <span className="text-orange-600 font-medium">Monitoring:</span> System status: {systemStats?.systemHealth}
                    </>
                  )}
                </p>
                <div className="flex items-center gap-2">
                  <FaClock className="w-3 h-3 text-purple-600" />
                  <span className="text-xs text-gray-500">
                    Uptime: {systemStats?.systemUptime || '0h'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

export default AdminDashboard 