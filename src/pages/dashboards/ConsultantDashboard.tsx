import type { User } from '@/types/api'

interface ConsultantDashboardProps {
  user: User
}

const ConsultantDashboard = ({ user }: ConsultantDashboardProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-4">Consultant Dashboard</h1>
        <p>Welcome, {user.name}! Consultant dashboard coming soon...</p>
      </div>
    </div>
  )
}

export default ConsultantDashboard 