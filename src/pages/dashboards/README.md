# Role-Based Dashboard System

## Overview

This system provides role-specific dashboards for different user types in the Sehatti platform. Each dashboard is tailored to the specific needs and permissions of each role.

## Architecture

### Components Structure

```
src/pages/dashboards/
├── README.md                 # This documentation
├── index.ts                  # Export file for all dashboards
├── AdminDashboard.tsx        # System Admin dashboard (system-admin)
├── HRManagerDashboard.tsx    # HR Manager dashboard (hr-admin)
├── EmployeeDashboard.tsx     # Employee dashboard (employee)
└── ConsultantDashboard.tsx   # Consultant dashboard (consultant)
```

### Role Routing

The `RoleDashboard.tsx` component automatically routes users to their appropriate dashboard based on their role:

- **system-admin** → `AdminDashboard`
- **hr-admin** → `HRManagerDashboard`
- **employee** → `EmployeeDashboard`
- **consultant** → `ConsultantDashboard`

## Dashboard Features

### Admin Dashboard (System Admin)
**Current Status**: ✅ **IMPLEMENTED**

**Features**:
- System-wide statistics (Users, Companies, Assessments, Alerts)
- System health monitoring (Uptime, Storage, API Requests)
- Recent platform activity feed
- Critical system alerts
- Company management interface
- User management tools
- System analytics
- Global settings and configuration

**Navigation Sections**:
- System Overview (default)
- Companies Management
- User Management  
- Analytics
- System Health
- System Settings

### HR Manager Dashboard
**Current Status**: 🚧 **PLACEHOLDER**

**Planned Features**:
- Company-specific employee management
- Assessment creation and management
- Employee performance analytics
- Department/division oversight
- Consultant assignment
- Company settings

### Employee Dashboard
**Current Status**: 🚧 **PLACEHOLDER**

**Planned Features**:
- Personal progress tracking
- Assigned assessments
- Learning roadmap
- Consultant communication
- Personal profile management
- Achievement certificates

### Consultant Dashboard
**Current Status**: 🚧 **PLACEHOLDER**

**Planned Features**:
- Assigned employee overview
- Communication tools
- Session scheduling
- Progress reporting
- Resource library

## Design Principles

All dashboards follow the **Enterprise Minimalistic** design approach:

- **Color Scheme**: Golden/beige primary colors with warm grays
- **Glass Morphism**: Transparent cards with backdrop blur effects
- **Responsive Design**: Mobile-first approach with responsive grids
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Performance**: Lazy loading and optimized components

## Authentication & Security

- **Protected Routes**: All dashboards require authentication via `ProtectedRoute` guard
- **Role Validation**: User role is verified before rendering appropriate dashboard
- **Auto Redirect**: Users are automatically redirected based on their role
- **Token Verification**: JWT tokens are validated on each request

## Usage

### Adding a New Dashboard

1. Create new dashboard component in `src/pages/dashboards/`
2. Export it in `index.ts`
3. Add role mapping in `RoleDashboard.tsx`
4. Update user types in `types/api.ts` if needed

### Customizing Existing Dashboards

Each dashboard component accepts a `user` prop containing:
```typescript
interface User {
  id: string
  name: string
  email: string
  role: 'hr-admin' | 'system-admin' | 'employee' | 'consultant'
  company?: Company
  // ... other user properties
}
```

## Development Status

- ✅ **AdminDashboard**: Fully implemented with comprehensive features
- 🚧 **HRManagerDashboard**: Placeholder - ready for development
- 🚧 **EmployeeDashboard**: Placeholder - ready for development  
- 🚧 **ConsultantDashboard**: Placeholder - ready for development

## Next Steps

1. **Implement HR Manager Dashboard** with company-specific features
2. **Implement Employee Dashboard** with personal progress tracking
3. **Implement Consultant Dashboard** with client management tools
4. **Add Real API Integration** replacing mock data
5. **Add Advanced Analytics** with charts and reporting
6. **Implement Real-time Features** with WebSocket connections

## Technical Notes

- Uses **RTK Query** for API state management
- Implements **React Hook** patterns for reusability
- Follows **DRY principles** with shared UI components
- Uses **TypeScript** for type safety
- Implements **Error Boundaries** for graceful error handling 