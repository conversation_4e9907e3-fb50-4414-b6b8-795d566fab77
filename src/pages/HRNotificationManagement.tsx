import React, { useState, useEffect } from 'react'
import { 
  FaBell, 
  FaUsers, 
  FaExclamationTriangle,
  FaCheckCircle,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaPaperPlane,
  FaFilter,
  FaSync,
  FaBuilding,
  FaUserShield,
  FaHeart,
  FaBrain,
  FaLifeRing,
  FaAward,
  FaClock,
  FaCalendarAlt,
  FaSearch,
  FaBroadcastTower,
  FaComment,
  FaChartLine
} from 'react-icons/fa'
import { BiLoaderAlt } from 'react-icons/bi'

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { GradientText } from '../components/ui/GradientText'
import { Badge } from '../components/ui/Badge'
import { Button } from '../components/ui/Button'
import { StatCard } from '../components/ui/StatCard'
import { AdminLayout } from '../components/layout/AdminLayout'
import { Input } from '../components/ui/Input'
import { Textarea } from '../components/ui/Textarea'

// API & Types
import { useGetMyEmployeesQuery, useGetEmployeeStatsQuery } from '../store/api/employeeApi'
import { useAppSelector } from '../store/hooks'
import type { User } from '@/types/api'

// Notification Types
type NotificationType = 'wellness_alert' | 'milestone' | 'campaign' | 'system' | 'reminder' | 'achievement'
type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'
type NotificationStatus = 'draft' | 'scheduled' | 'sent' | 'failed'

interface WellnessNotification {
  id: string
  type: NotificationType
  title: string
  message: string
  priority: NotificationPriority
  status: NotificationStatus
  targetEmployees: string[]
  scheduledAt?: Date
  sentAt?: Date
  createdAt: Date
  wellnessMetrics?: {
    stressLevel?: number
    engagementScore?: number
    wellnessScore?: number
  }
  actionRequired?: boolean
}

export default function HRNotificationManagement() {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'create' | 'templates' | 'analytics'>('overview')
  const [notifications, setNotifications] = useState<WellnessNotification[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<NotificationType | 'all'>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Get user from auth store
  const auth = useAppSelector(state => state.auth.user)
  const currentUser = auth as User

  // API Queries - Conditional call based on user role
  const {
    data: employeesResponse,
    isLoading: employeesLoading,
    error: employeesError,
    refetch: refetchEmployees
  } = useGetMyEmployeesQuery({
    page: 1,
    limit: 100
  }, { 
    skip: !currentUser || (currentUser.role !== 'HR_ADMIN' && currentUser.role !== 'SYSTEM_ADMIN') // Allow both HR_ADMIN and SYSTEM_ADMIN for testing
  })

  // Employee Stats API
  const {
    data: employeeStatsResponse,
    isLoading: statsLoading,
    error: statsError
  } = useGetEmployeeStatsQuery(undefined, {
    skip: !currentUser || (currentUser.role !== 'HR_ADMIN' && currentUser.role !== 'SYSTEM_ADMIN')
  })

  const employees = employeesResponse?.data || []
  const totalEmployees = employees.length || 150

  // Debug logging and error handling
  React.useEffect(() => {
    console.log('🔍 HR Notification Management Debug:')
    console.log('Current User:', currentUser)
    console.log('User Role:', currentUser?.role)
    console.log('User Company ID:', currentUser?.companyId)
    console.log('Employees Error:', employeesError)
    console.log('Employees Response:', employeesResponse)
    console.log('Employee Stats Response:', employeeStatsResponse)
    console.log('Employee Stats Error:', statsError)
    console.log('Calculated Employee Stats:', employeeStats)
    console.log('Total Employees from API:', totalEmployees)
    
    if (employeesError) {
      console.error('❌ Employee API Error:', employeesError)
      // Show user-friendly error message
      if (typeof employeesError === 'object' && employeesError !== null && 'status' in employeesError && employeesError.status === 422) {
        console.warn('⚠️ 422 Error: User likely missing company_id field')
      }
    }
  }, [currentUser, employeesError, employeesResponse, employeeStatsResponse, statsError])

  // Initialize mock notifications once (prevent infinite loop)
  useEffect(() => {
    const mockNotifications: WellnessNotification[] = [
      {
        id: '1',
        type: 'wellness_alert',
        title: 'High Stress Level Alert',
        message: '12 employees showing elevated stress levels requiring immediate attention',
        priority: 'urgent',
        status: 'sent',
        targetEmployees: ['emp1', 'emp2', 'emp3'],
        sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
        wellnessMetrics: { stressLevel: 8.2 },
        actionRequired: true
      },
      {
        id: '2',
        type: 'milestone',
        title: 'Wellness Milestone Achieved',
        message: 'Sarah Johnson completed 30-day wellness challenge with 95% participation',
        priority: 'medium',
        status: 'sent',
        targetEmployees: ['sarah_j'],
        sentAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        wellnessMetrics: { wellnessScore: 95 }
      },
      {
        id: '3',
        type: 'campaign',
        title: 'Mental Health Awareness Week',
        message: 'Join our company-wide mental health awareness campaign starting Monday',
        priority: 'high',
        status: 'scheduled',
        targetEmployees: ['emp1', 'emp2', 'emp3', 'emp4', 'emp5'],
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
      },
      {
        id: '4',
        type: 'achievement',
        title: 'Team Wellness Goal Reached',
        message: 'Engineering team achieved 85% wellness program participation - Congratulations!',
        priority: 'medium',
        status: 'sent',
        targetEmployees: ['eng1', 'eng2', 'eng3'],
        sentAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        wellnessMetrics: { engagementScore: 85 }
      },
      {
        id: '5',
        type: 'reminder',
        title: 'Weekly Wellness Check-in',
        message: 'Don\'t forget to complete your weekly wellness assessment',
        priority: 'low',
        status: 'scheduled',
        targetEmployees: ['emp1', 'emp2', 'emp3', 'emp4', 'emp5'],
        scheduledAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
        createdAt: new Date()
      }
    ]
    setNotifications(mockNotifications)
  }, []) // Empty dependency array to prevent infinite loop

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || notification.type === filterType
    return matchesSearch && matchesType
  })

  // Analytics data - Combine notification stats with real employee data
  const employeeStats = employeeStatsResponse || {
    totalEmployees: totalEmployees,
    activeEmployees: employees.filter(emp => emp.status === 'ACTIVE').length,
    pendingEmployees: employees.filter(emp => emp.status === 'PENDING').length,
    inactiveEmployees: employees.filter(emp => emp.status === 'INACTIVE').length,
    suspendedEmployees: employees.filter(emp => emp.status === 'SUSPENDED').length,
  }

  const notificationStats = {
    totalSent: notifications.filter(n => n.status === 'sent').length,
    scheduled: notifications.filter(n => n.status === 'scheduled').length,
    highPriority: notifications.filter(n => n.priority === 'high' || n.priority === 'urgent').length,
    actionRequired: notifications.filter(n => n.actionRequired).length,
    openRate: 87.3,
    responseRate: 64.2
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await refetchEmployees()
    setTimeout(() => setRefreshing(false), 1000)
  }

  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token")
    window.location.href = "/login"
  }

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'wellness_alert': return <FaExclamationTriangle className="w-4 h-4 text-red-600" />
      case 'milestone': return <FaAward className="w-4 h-4 text-yellow-600" />
      case 'campaign': return <FaBroadcastTower className="w-4 h-4 text-blue-600" />
      case 'achievement': return <FaCheckCircle className="w-4 h-4 text-green-600" />
      case 'reminder': return <FaClock className="w-4 h-4 text-gray-600" />
      case 'system': return <FaBell className="w-4 h-4 text-purple-600" />
      default: return <FaBell className="w-4 h-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: NotificationPriority) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: NotificationStatus) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'failed': return 'bg-red-100 text-red-800'
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <AdminLayout
      user={currentUser}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={notificationStats.actionRequired}
    >
      <div className="space-y-4 sm:space-y-6">
        {/* Header */}
        <header className="space-y-3 sm:space-y-4 py-4 sm:py-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">
              <GradientText gradient="goldRich" size="2xl" className="sm:text-3xl lg:text-4xl">
                Wellness Notifications
              </GradientText>
            </h1>
            <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Manage wellness alerts, milestones, and employee communication
            </p>
          </div>
          
          <div className="flex flex-col items-center gap-2 sm:flex-row sm:gap-4">
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaUserShield className="mr-1 w-3 h-3" /> HR Administrator
            </Badge>
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaBuilding className="mr-1 w-3 h-3" /> Company
            </Badge>
            <Badge variant="success" className="text-xs sm:text-sm">
              <FaCheckCircle className="mr-1 w-3 h-3" /> Real-time Alerts
            </Badge>
            {refreshing && <FaSync className="animate-spin text-sehatti-gold-600 w-3 h-3 sm:w-4 sm:h-4" />}
          </div>
        </header>

        {/* Error Display */}
        {employeesError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 text-red-800">
              <FaExclamationTriangle className="w-4 h-4" />
              <span className="font-medium">Employee Data Unavailable</span>
            </div>
            <p className="text-red-700 text-sm mt-1">
              {typeof employeesError === 'object' && employeesError !== null && 'status' in employeesError && employeesError.status === 422
                ? 'Your account is missing company association. Please contact support.'
                : 'Unable to load employee data. Using demo data for notifications.'}
            </p>
          </div>
        )}

        {/* Employee & Notification Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <StatCard
            title="Total Employees"
            value={employeeStats.totalEmployees || totalEmployees}
            icon={<FaUsers className="text-blue-600 w-5 h-5" />}
          />
          
          <StatCard
            title="Active Employees"
            value={employeeStats.activeEmployees || 0}
            icon={<FaCheckCircle className="text-green-600 w-5 h-5" />}
          />
          
          <StatCard
            title="Pending Employees"
            value={employeeStats.pendingEmployees || 0}
            icon={<FaClock className="text-orange-600 w-5 h-5" />}
          />
          
          <StatCard
            title="Notifications Sent"
            value={notificationStats.totalSent}
            icon={<FaBell className="text-purple-600 w-5 h-5" />}
          />
        </div>

        {/* Tab Navigation - Mobile First */}
        <div className="border-b border-gray-200">
          {/* Mobile: Dropdown selector */}
          <div className="sm:hidden">
            <select
              value={selectedTab}
              onChange={(e) => setSelectedTab(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 bg-white"
            >
              <option value="overview">📊 Overview</option>
              <option value="create">➕ Create Alert</option>
              <option value="templates">💬 Templates</option>
              <option value="analytics">📈 Analytics</option>
            </select>
              </div>
          
          {/* Desktop: Tab buttons */}
          <div className="hidden sm:flex sm:space-x-4 lg:space-x-8 overflow-x-auto">
            {[
              { id: 'overview', label: 'Overview', icon: FaEye },
              { id: 'create', label: 'Create Alert', icon: FaPlus },
              { id: 'templates', label: 'Templates', icon: FaComment },
              { id: 'analytics', label: 'Analytics', icon: FaChartLine }
            ].map((tab) => {
              const TabIcon = tab.icon
              const isActive = selectedTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`
                    flex items-center gap-2 py-3 sm:py-4 border-b-2 transition-colors whitespace-nowrap
                    ${isActive 
                      ? 'border-sehatti-gold-500 text-sehatti-gold-600' 
                      : 'border-transparent text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900'
                    }
                  `}
                >
                  <TabIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="font-medium text-sm sm:text-base">{tab.label}</span>
                </button>
              )
            })}
          </div>
            </div>

        {/* Tab Content */}
        {selectedTab === 'overview' && (
          <div className="space-y-6">
            {/* Search and Filter - Mobile First */}
            <div className="space-y-3 sm:space-y-0 sm:flex sm:gap-4">
              {/* Search Bar */}
              <div className="flex-1">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full"
                  />
                </div>
              </div>
              
              {/* Filter and Refresh - Mobile: Stack, Desktop: Row */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500 bg-white text-sm sm:text-base"
                >
                  <option value="all">All Types</option>
                  <option value="wellness_alert">Wellness Alerts</option>
                  <option value="milestone">Milestones</option>
                  <option value="campaign">Campaigns</option>
                  <option value="achievement">Achievements</option>
                  <option value="reminder">Reminders</option>
                </select>
                
                <Button onClick={handleRefresh} variant="outline" className="flex items-center justify-center gap-2 w-full sm:w-auto">
                  <FaSync className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="text-sm sm:text-base">Refresh</span>
                </Button>
              </div>
            </div>

            {/* Notifications List - Mobile First */}
            <div className="space-y-3 sm:space-y-4">
                    {filteredNotifications.map((notification) => (
                <Card key={notification.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4 sm:p-6">
                    {/* Mobile Layout: Stack everything */}
                    <div className="space-y-3 sm:space-y-0 sm:flex sm:items-start sm:justify-between">
                      {/* Content Section */}
                      <div className="flex items-start gap-3 sm:gap-4 flex-1">
                        {/* Icon */}
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        {/* Main Content */}
                        <div className="flex-1 min-w-0">
                          {/* Title */}
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 leading-tight">
                            {notification.title}
                          </h3>
                          
                          {/* Badges - Mobile: Stack, Desktop: Row */}
                          <div className="flex flex-wrap gap-1 sm:gap-2 mb-3">
                            <Badge className={`${getPriorityColor(notification.priority)} text-xs`}>
                              {notification.priority}
                            </Badge>
                            <Badge className={`${getStatusColor(notification.status)} text-xs`}>
                              {notification.status}
                            </Badge>
                            {notification.actionRequired && (
                              <Badge variant="warning" className="text-xs">
                                Action Required
                              </Badge>
                            )}
                          </div>
                          
                          {/* Message */}
                          <p className="text-gray-600 mb-3 text-sm sm:text-base leading-relaxed">
                              {notification.message}
                          </p>
                          
                          {/* Meta Info - Mobile: Stack, Desktop: Row */}
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <FaUsers className="w-3 h-3" />
                              <span>{notification.targetEmployees.length} employees</span>
                            </div>
                            
                            {notification.sentAt && (
                              <div className="flex items-center gap-1">
                                <FaCheckCircle className="w-3 h-3" />
                                <span>Sent {formatDate(notification.sentAt)}</span>
                              </div>
                            )}
                            
                            {notification.scheduledAt && notification.status === 'scheduled' && (
                              <div className="flex items-center gap-1">
                                <FaClock className="w-3 h-3" />
                                <span>Scheduled {formatDate(notification.scheduledAt)}</span>
                              </div>
                            )}
                            
                            {notification.wellnessMetrics && (
                              <div className="flex items-center gap-1">
                                <FaHeart className="w-3 h-3" />
                                <span className="truncate">
                                  {notification.wellnessMetrics.wellnessScore && `Wellness: ${notification.wellnessMetrics.wellnessScore}`}
                                  {notification.wellnessMetrics.stressLevel && `Stress: ${notification.wellnessMetrics.stressLevel}`}
                                  {notification.wellnessMetrics.engagementScore && `Engagement: ${notification.wellnessMetrics.engagementScore}%`}
                                </span>
                            </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Action Buttons - Mobile: Full width row, Desktop: Vertical */}
                      <div className="flex sm:flex-col gap-2 sm:gap-1 sm:ml-4 w-full sm:w-auto">
                        <Button size="sm" variant="outline" className="flex-1 sm:flex-none">
                          <FaEye className="w-3 h-3 sm:mr-0 mr-1" />
                          <span className="sm:hidden">View</span>
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1 sm:flex-none">
                          <FaEdit className="w-3 h-3 sm:mr-0 mr-1" />
                          <span className="sm:hidden">Edit</span>
                              </Button>
                              {notification.status === 'draft' && (
                          <Button size="sm" variant="outline" className="flex-1 sm:flex-none">
                            <FaPaperPlane className="w-3 h-3 sm:mr-0 mr-1" />
                            <span className="sm:hidden">Send</span>
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
              </div>
            )}

        {selectedTab === 'create' && (
          <Card>
            <CardHeader>
              <CardTitle>Create Wellness Notification</CardTitle>
              <CardDescription>
                Send targeted wellness alerts and updates to your employees
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <div className="space-y-4">
              <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notification Type
                </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500">
                      <option value="wellness_alert">Wellness Alert</option>
                      <option value="milestone">Milestone Achievement</option>
                      <option value="campaign">Wellness Campaign</option>
                      <option value="achievement">Team Achievement</option>
                      <option value="reminder">Wellness Reminder</option>
                    </select>
              </div>
              
              <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority Level
                </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500">
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
              </div>
              
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Audience
                  </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500">
                      <option value="all">All Employees</option>
                      <option value="department">Specific Department</option>
                      <option value="high_risk">High Risk Employees</option>
                      <option value="low_engagement">Low Engagement</option>
                      <option value="custom">Custom Selection</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title
                  </label>
                    <Input placeholder="Enter notification title" />
              </div>
              
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message
                  </label>
                    <Textarea 
                      placeholder="Enter your message here..."
                      rows={4}
                  />
                </div>
                  
                  <div className="flex items-center gap-4">
                                         <Button className="flex items-center gap-2">
                       <FaPaperPlane className="w-4 h-4" />
                       Send Now
                     </Button>
                    <Button variant="outline" className="flex items-center gap-2">
                      <FaClock className="w-4 h-4" />
                      Schedule
              </Button>
                    <Button variant="outline">
                      Save Draft
              </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {selectedTab === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Notification Performance</CardTitle>
                <CardDescription>
                  Engagement metrics for wellness notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="text-sm font-medium">Open Rate</span>
                    <span className="text-lg font-bold text-blue-600">{notificationStats.openRate}%</span>
                </div>
                
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="text-sm font-medium">Response Rate</span>
                    <span className="text-lg font-bold text-green-600">{notificationStats.responseRate}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <span className="text-sm font-medium">Action Completion</span>
                    <span className="text-lg font-bold text-purple-600">72.8%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Wellness Impact</CardTitle>
                <CardDescription>
                  How notifications affect employee wellness
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="text-sm font-medium">Wellness Score Improvement</span>
                    <span className="text-lg font-bold text-green-600">+12.3%</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="text-sm font-medium">Engagement Increase</span>
                    <span className="text-lg font-bold text-blue-600">+18.7%</span>
                    </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <span className="text-sm font-medium">Stress Reduction</span>
                    <span className="text-lg font-bold text-yellow-600">-15.2%</span>
                    </div>
                </div>
              </CardContent>
            </Card>
              </div>
            )}
      </div>
    </AdminLayout>
  )
}
