import React, { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/AdminLayout'
import { 
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Badge,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  Spinner,
  Container,
  GlassCard,
  Modal,
  Input,
  Textarea,
  Label,
  Select,
  FileUpload
} from '@/components/ui'
import { useAppSelector, useAppDispatch } from '@/store/store'
import { selectUser } from '@/features/auth/auth-slice'
import { loginSuccess } from '@/features/auth/auth-slice'
import { FaNewspaper, FaPlus, FaEye, FaEdit, FaTrash } from 'react-icons/fa'

// Import the articles API from the modern frontend
import { 
  useGetArticlesQuery, 
  useCreateArticleMutation,
  useUpdateArticleMutation,
  useDeleteArticleMutation 
} from '@/features/API/mediaServiceApi'
import toast from 'react-hot-toast'

const ArticleManagementPoC: React.FC = () => {
  const dispatch = useAppDispatch()
  const user = useAppSelector(selectUser)
  
  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingArticle, setEditingArticle] = useState<any>(null)
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    status: 'draft',
    tags: [] as string[],
    author: '',
    thumbnail: null as File | null
  })

  // For PoC: Set up a mock user if not authenticated
  useEffect(() => {
    if (!user) {
      const mockUser = {
        id: 'mock-user-123',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'system_admin'
      }
      const mockToken = 'mock-token-for-poc'
      
      dispatch(loginSuccess({ user: mockUser, token: mockToken }))
    }
  }, [user, dispatch])
  
  // Fetch articles using RTK Query
  const { 
    data: articlesResponse, 
    isLoading, 
    error,
    refetch 
  } = useGetArticlesQuery({
    limit: 20
  })

  // Article mutations
  const [createArticle, { isLoading: isCreating }] = useCreateArticleMutation()
  const [updateArticle, { isLoading: isUpdating }] = useUpdateArticleMutation()
  const [deleteArticle, { isLoading: isDeleting }] = useDeleteArticleMutation()

  const articles = articlesResponse?.data || []

  const handleLogout = () => {
    // Handle logout logic
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      content: '',
      category: '',
      status: 'draft',
      tags: [],
      author: '',
      thumbnail: null
    })
  }

  const handleCreateArticle = () => {
    resetForm()
    setIsCreateModalOpen(true)
  }

  const handleSubmitCreate = async () => {
    try {
      const result = await createArticle({
        title: formData.title,
        description: formData.description,
        content: formData.content,
        category: formData.category,
        status: formData.status,
        tags: formData.tags,
        author: formData.author || user?.name || 'Admin',
        thumbnail: formData.thumbnail || undefined,
        default_language: 'en',
        auto_translate: true
      }).unwrap()
      
      toast.success('Article created successfully!')
      setIsCreateModalOpen(false)
      resetForm()
      refetch()
    } catch (error: any) {
      console.error('Create error:', error)
      toast.error(error?.data?.message || 'Failed to create article')
    }
  }

  const handleViewArticle = (articleId: string) => {
    // For PoC, just log the action
    console.log('View article:', articleId)
    toast.info('View functionality would open article details')
  }

  const handleEditArticle = (articleId: string) => {
    const article = articles.find((a: any) => a.article_id === articleId)
    if (article) {
      const localizedContent = article.content?.en || article.content?.[Object.keys(article.content || {})[0]] || {}
      setEditingArticle(article)
      setFormData({
        title: localizedContent.title || '',
        description: localizedContent.description || '',
        content: '',
        category: article.category || '',
        status: article.status || 'draft',
        tags: article.tags || [],
        author: article.author || '',
        thumbnail: null
      })
      setIsEditModalOpen(true)
    }
  }

  const handleSubmitEdit = async () => {
    if (!editingArticle) return
    
    try {
      await updateArticle({
        article_id: editingArticle.article_id,
        title: formData.title,
        description: formData.description,
        content: formData.content,
        category: formData.category,
        status: formData.status,
        tags: formData.tags,
        author: formData.author,
        thumbnail: formData.thumbnail || undefined,
        language: 'en'
      }).unwrap()
      
      toast.success('Article updated successfully!')
      setIsEditModalOpen(false)
      setEditingArticle(null)
      resetForm()
      refetch()
    } catch (error: any) {
      console.error('Update error:', error)
      toast.error(error?.data?.message || 'Failed to update article')
    }
  }

  const handleDeleteArticle = async (articleId: string) => {
    if (window.confirm('Are you sure you want to delete this article?')) {
      try {
        await deleteArticle({ article_id: articleId }).unwrap()
        toast.success('Article deleted successfully!')
        refetch()
      } catch (error: any) {
        console.error('Delete error:', error)
        toast.error(error?.data?.message || 'Failed to delete article')
      }
    }
  }

  // Show loading while setting up mock user
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Setting up PoC environment...</p>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout
      user={user as any}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={0}
      containerSize="full"
      className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"
    >
      <Container size="full" className="py-8">
        {/* Header Section */}
        <GlassCard className="mb-8 border-white/20 bg-white/10 backdrop-blur-xl">
          <div className="p-8">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    <FaNewspaper className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                      Article Management PoC
                    </h1>
                    <p className="text-gray-600 mt-1">
                      Proof of Concept for Article CRUD operations using new UI components
                    </p>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCreateArticle}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                leftIcon={<FaPlus />}
                size="lg"
              >
                Create Article
              </Button>
            </div>
          </div>
        </GlassCard>

        {/* Main Content */}
        <GlassCard className="border-white/20 bg-white/10 backdrop-blur-xl">
          <CardHeader className="border-b border-white/10 bg-white/5">
            <CardTitle className="flex items-center gap-3">
              <FaNewspaper className="h-5 w-5 text-blue-600" />
              Articles List
              <Badge variant="secondary" size="sm" className="bg-blue-100/80 text-blue-800">
                {articles.length} articles
              </Badge>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-8">
            {/* Debug Info */}
            <div className="mb-6 p-4 bg-gray-100 rounded-lg text-sm">
              <div className="font-medium text-gray-700 mb-2">PoC Debug Info:</div>
              <div className="text-gray-600">
                <div>API Status: {isLoading ? 'Loading...' : error ? 'Error' : 'Success'}</div>
                <div>Articles Found: {articles.length}</div>
                <div>Backend URL: http://localhost:8000</div>
                {error && (
                  <div className="text-red-600 mt-2">
                    Error: {error && 'data' in error ? JSON.stringify(error.data) : 'Network error'}
                  </div>
                )}
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <Spinner size="lg" />
                <span className="ml-3 text-gray-600">Loading articles...</span>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <div className="text-red-600 font-medium mb-2">Error Loading Articles</div>
                <p className="text-red-500 text-sm mb-4">
                  {error && 'data' in error ? error.data?.message || 'Failed to load articles' : 'Network error'}
                </p>
                <Button 
                  onClick={() => refetch()}
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  Try Again
                </Button>
              </div>
            )}

            {/* Articles Table */}
            {!isLoading && !error && (
              <>
                {articles.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Views</TableHead>
                          <TableHead>Likes</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {articles.map((article: any) => {
                          // Extract localized content
                          const localizedContent = article.content?.en || article.content?.[Object.keys(article.content || {})[0]] || {}
                          const title = localizedContent.title || article.title || 'Untitled Article'
                          
                          return (
                            <TableRow key={article.article_id || article.id}>
                              <TableCell className="font-medium">
                                <div className="flex items-center gap-3">
                                  {article.thumbnail_url && (
                                    <img 
                                      src={article.thumbnail_url} 
                                      alt={title}
                                      className="w-10 h-10 rounded-lg object-cover"
                                    />
                                  )}
                                  <div>
                                    <div className="font-medium text-gray-900 max-w-xs truncate">{title}</div>
                                    {article.available_languages && article.available_languages.length > 1 && (
                                      <div className="text-xs text-gray-500">
                                        {article.available_languages.length} languages
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge 
                                  variant={
                                    article.status === 'published' ? 'success' : 
                                    article.status === 'draft' ? 'warning' : 'secondary'
                                  }
                                  size="sm"
                                >
                                  {article.status || 'draft'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <span className="text-gray-600">{article.category || 'General'}</span>
                              </TableCell>
                              <TableCell>
                                <span className="text-gray-600">{article.view_count || 0}</span>
                              </TableCell>
                              <TableCell>
                                <span className="text-gray-600">{article.like_count || 0}</span>
                              </TableCell>
                              <TableCell>
                                <span className="text-gray-600">
                                  {article.created_at ? new Date(article.created_at).toLocaleDateString() : 'N/A'}
                                </span>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Button
                                    onClick={() => handleViewArticle(article.article_id || article.id)}
                                    variant="ghost"
                                    size="sm"
                                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                  >
                                    <FaEye className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    onClick={() => handleEditArticle(article.article_id || article.id)}
                                    variant="ghost"
                                    size="sm"
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                  >
                                    <FaEdit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    onClick={() => handleDeleteArticle(article.article_id || article.id)}
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <FaTrash className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FaNewspaper className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Articles Found</h3>
                    <p className="text-gray-500 mb-6">Get started by creating your first article</p>
                    <Button
                      onClick={handleCreateArticle}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                      leftIcon={<FaPlus />}
                    >
                      Create Article
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </GlassCard>
      </Container>

      {/* Create Article Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Article"
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter article title"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
              >
                <option value="">Select category</option>
                <option value="technology">Technology</option>
                <option value="health">Health</option>
                <option value="science">Science</option>
                <option value="business">Business</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter article description"
              rows={3}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Enter article content"
              rows={6}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="author">Author</Label>
              <Input
                id="author"
                value={formData.author}
                onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                placeholder="Enter author name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="thumbnail">Thumbnail Image</Label>
            <FileUpload
              accept="image/*"
              onFileSelect={(file) => setFormData(prev => ({ ...prev, thumbnail: file }))}
              maxSize={5 * 1024 * 1024} // 5MB
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitCreate}
              disabled={isCreating || !formData.title || !formData.description}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isCreating ? <Spinner size="sm" className="mr-2" /> : null}
              {isCreating ? 'Creating...' : 'Create Article'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Article Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Article"
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Title *</Label>
              <Input
                id="edit-title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter article title"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
              >
                <option value="">Select category</option>
                <option value="technology">Technology</option>
                <option value="health">Health</option>
                <option value="science">Science</option>
                <option value="business">Business</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-description">Description *</Label>
            <Textarea
              id="edit-description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter article description"
              rows={3}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-content">Content</Label>
            <Textarea
              id="edit-content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Enter article content"
              rows={6}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-author">Author</Label>
              <Input
                id="edit-author"
                value={formData.author}
                onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                placeholder="Enter author name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitEdit}
              disabled={isUpdating || !formData.title || !formData.description}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              {isUpdating ? <Spinner size="sm" className="mr-2" /> : null}
              {isUpdating ? 'Updating...' : 'Update Article'}
            </Button>
          </div>
        </div>
      </Modal>
    </AdminLayout>
  )
}

export default ArticleManagementPoC 