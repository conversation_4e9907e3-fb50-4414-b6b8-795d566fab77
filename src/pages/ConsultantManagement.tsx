import React from 'react'
import { AdminLayout } from '../components/layout/AdminLayout'
import { useAppSelector } from '../store/store'
import { selectAuth } from '../features/auth/auth-slice'
import ConsultantManager from '../components/admin/ConsultantManager'
import { Container } from '../components/ui'

const ConsultantManagement: React.FC = () => {
  const auth = useAppSelector(selectAuth)
  const user = auth?.user || undefined

  return (
    <AdminLayout user={user}>
      <Container size="full" className="py-8">
        <ConsultantManager showCompanyAssignment={true} />
      </Container>
    </AdminLayout>
  )
}

export default ConsultantManagement
