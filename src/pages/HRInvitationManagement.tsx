import React, { useState, useEffect } from 'react'
import { 
  FaCalendarPlus,
  FaUsers, 
  FaVideo,
  FaMapMarkerAlt,
  FaEdit,
  FaTrash,
  FaEye,
  FaFilter,
  FaSearch,
  FaPlus,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimes,
  FaUserCheck,
  FaUserTimes,
  FaChartLine,
  FaBuilding,
  FaSync,
  FaBell,
  FaDownload,
  FaPaperPlane,
  FaGraduationCap,
  FaBrain,
  FaHandshake,
  FaComments,
  FaTools,
  FaHeart,
  FaHeadset,
  FaDollarSign,
  FaProjectDiagram,
  FaEllipsisH
} from 'react-icons/fa'
import { BiLoaderAlt } from 'react-icons/bi'

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { GradientText } from '../components/ui/GradientText'
import { Badge } from '../components/ui/Badge'
import { Button } from '../components/ui/Button'
import { StatCard } from '../components/ui/StatCard'
import { AdminLayout } from '../components/layout/AdminLayout'
import type { User } from '@/types/api'

// Auth
import { useAppSelector } from '../store/hooks'

// Invitation Types and Enums (matching invitation.py)
type InvitationType = 'physical' | 'online'
type TrainingSessionType = 'leadership' | 'mental_health' | 'team_building' | 'communication' | 'technical_skills' | 'wellness' | 'diversity_inclusion' | 'customer_service' | 'sales_training' | 'project_management' | 'other'
type InvitationStatus = 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'SCHEDULED' | 'IN_PROGRESS' | 'EXPIRED'
type InvitationPriority = 'low' | 'medium' | 'high' | 'urgent'

// Mock data matching the invitation.py structure
const INVITATION_STATS = {
  totalInvitations: 156,
  activeInvitations: 23,
  completedInvitations: 98,
  scheduledInvitations: 35,
  averageAttendanceRate: 78.5,
  averageConfirmationRate: 89.2,
  totalParticipants: 1247
}

const INVITATIONS = [
  {
    id: '1',
    title: 'Leadership Excellence Workshop',
    type: 'physical' as InvitationType,
    sessionType: 'leadership' as TrainingSessionType,
    status: 'SCHEDULED' as InvitationStatus,
    priority: 'high' as InvitationPriority,
    time: new Date('2025-01-20T10:00:00'),
    duration: 240, // 4 hours
    location: {
      venue: 'Conference Room A',
      address: 'Building 1, Floor 3',
      capacity: 25
    },
    consultantId: 'consultant-1',
    consultant: { name: 'Dr. Sarah Johnson', specialization: 'Leadership Development' },
    recipients: ['emp-1', 'emp-2', 'emp-3', 'emp-4', 'emp-5'],
    confirmed: ['emp-1', 'emp-3', 'emp-5'],
    declined: ['emp-2'],
    maxParticipants: 25,
    spotsAvailable: 20,
    confirmationRate: 80,
    description: 'Develop essential leadership skills for team management and organizational success'
  },
  {
    id: '2',
    title: 'Mental Health & Wellness Session',
    type: 'online' as InvitationType,
    sessionType: 'mental_health' as TrainingSessionType,
    status: 'ACTIVE' as InvitationStatus,
    priority: 'medium' as InvitationPriority,
    time: new Date('2025-01-18T14:00:00'),
    duration: 90,
    meetingLink: 'https://zoom.us/j/123456789',
    consultantId: 'consultant-2',
    consultant: { name: 'Dr. Ahmed Hassan', specialization: 'Mental Health' },
    recipients: ['emp-6', 'emp-7', 'emp-8', 'emp-9', 'emp-10', 'emp-11'],
    confirmed: ['emp-6', 'emp-7', 'emp-9', 'emp-10'],
    declined: ['emp-8'],
    maxParticipants: 50,
    spotsAvailable: 44,
    confirmationRate: 83,
    description: 'Learn stress management techniques and mental wellness strategies'
  },
  {
    id: '3',
    title: 'Team Building Challenge',
    type: 'physical' as InvitationType,
    sessionType: 'team_building' as TrainingSessionType,
    status: 'COMPLETED' as InvitationStatus,
    priority: 'medium' as InvitationPriority,
    time: new Date('2025-01-10T09:00:00'),
    duration: 480, // Full day
    location: {
      venue: 'Outdoor Activity Center',
      address: 'Al Forsan International Sports Resort',
      capacity: 40
    },
    consultantId: 'consultant-3',
    consultant: { name: 'Coach Maria Rodriguez', specialization: 'Team Dynamics' },
    recipients: ['emp-12', 'emp-13', 'emp-14', 'emp-15', 'emp-16'],
    confirmed: ['emp-12', 'emp-13', 'emp-14', 'emp-15', 'emp-16'],
    participants: ['emp-12', 'emp-13', 'emp-14', 'emp-15'],
    declined: [],
    maxParticipants: 40,
    attendanceRate: 80,
    averageRating: 4.7,
    description: 'Outdoor team building activities to strengthen collaboration and trust'
  },
  {
    id: '4',
    title: 'Digital Communication Skills',
    type: 'online' as InvitationType,
    sessionType: 'communication' as TrainingSessionType,
    status: 'SCHEDULED' as InvitationStatus,
    priority: 'low' as InvitationPriority,
    time: new Date('2025-01-25T11:00:00'),
    duration: 120,
    meetingLink: 'https://teams.microsoft.com/l/meetup-join/19%3a...',
    consultantId: 'consultant-4',
    consultant: { name: 'Prof. John Smith', specialization: 'Communication' },
    recipients: ['emp-17', 'emp-18', 'emp-19', 'emp-20'],
    confirmed: ['emp-17', 'emp-19'],
    declined: [],
    maxParticipants: 30,
    spotsAvailable: 26,
    confirmationRate: 50,
    description: 'Master digital communication tools and virtual collaboration techniques'
  }
]

const SESSION_TYPE_ICONS = {
  leadership: FaGraduationCap,
  mental_health: FaBrain,
  team_building: FaHandshake,
  communication: FaComments,
  technical_skills: FaTools,
  wellness: FaHeart,
  diversity_inclusion: FaUsers,
  customer_service: FaHeadset,
  sales_training: FaDollarSign,
  project_management: FaProjectDiagram,
  other: FaEllipsisH
}

const HRInvitationManagement = () => {
  // Get user from auth store
  const auth = useAppSelector(state => state.auth.user)
  const currentUser = auth as User
  const companyId = currentUser?.companyId
  
  const [selectedTab, setSelectedTab] = useState<string>('overview')
  const [invitations, setInvitations] = useState(INVITATIONS)
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<InvitationType | 'all'>('all')
  const [filterStatus, setFilterStatus] = useState<InvitationStatus | 'all'>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedInvitation, setSelectedInvitation] = useState<any>(null)

  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = invitation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invitation.consultant.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || invitation.type === filterType
    const matchesStatus = filterStatus === 'all' || invitation.status === filterStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusColor = (status: InvitationStatus) => {
    switch (status) {
      case 'ACTIVE': return 'bg-blue-100 text-blue-800'
      case 'SCHEDULED': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-gray-100 text-gray-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800'
      case 'EXPIRED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: InvitationPriority) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaChartLine },
    { id: 'invitations', label: 'Invitations', icon: FaCalendarPlus },
    { id: 'analytics', label: 'Analytics', icon: FaChartLine },
  ]

  return (
    <AdminLayout user={currentUser}>
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
        {/* Mobile-First Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-sehatti-warm-gray-900 dark:text-white mb-2">
                <GradientText>Invitation Management</GradientText>
              </h1>
              <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
                Manage training sessions and wellness invitations
              </p>
            </div>
            
            <div className="flex items-center gap-2 sm:gap-4">
              <Badge variant="outline" className="text-xs sm:text-sm">
                <FaBuilding className="mr-1 w-3 h-3" /> Company
              </Badge>
              <Button 
                onClick={() => setShowCreateModal(true)}
                className="flex items-center gap-2 text-sm sm:text-base"
              >
                <FaPlus className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Create Invitation</span>
                <span className="sm:hidden">Create</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile-First Navigation Tabs */}
        <div className="border-b border-sehatti-warm-gray-200 mb-6 sm:mb-8">
          <div className="flex space-x-4 sm:space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const TabIcon = tab.icon
              const isActive = selectedTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id)}
                  className={`
                    flex items-center gap-2 py-3 sm:py-4 border-b-2 transition-colors whitespace-nowrap text-sm sm:text-base
                    ${isActive 
                      ? 'border-sehatti-gold-500 text-sehatti-gold-600' 
                      : 'border-transparent text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900'
                    }
                  `}
                >
                  <TabIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Tab Content */}
        {selectedTab === 'overview' && (
          <div className="space-y-4 sm:space-y-6">
            {/* Mobile-First Stats Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <StatCard
                title="Total Invitations"
                value={INVITATION_STATS.totalInvitations}
                icon={<FaCalendarPlus className="text-blue-600 w-4 h-4 sm:w-5 sm:h-5" />}
              />
              
              <StatCard
                title="Active"
                value={INVITATION_STATS.activeInvitations}
                icon={<FaCheckCircle className="text-green-600 w-4 h-4 sm:w-5 sm:h-5" />}
              />
              
              <StatCard
                title="Scheduled"
                value={INVITATION_STATS.scheduledInvitations}
                icon={<FaClock className="text-orange-600 w-4 h-4 sm:w-5 sm:h-5" />}
              />
              
              <StatCard
                title="Completed"
                value={INVITATION_STATS.completedInvitations}
                icon={<FaUserCheck className="text-purple-600 w-4 h-4 sm:w-5 sm:h-5" />}
              />
            </div>

            {/* Mobile-First Performance Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <FaChartLine className="w-4 h-4 sm:w-5 sm:h-5 text-sehatti-gold-500" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Attendance Rate</span>
                      <span className="text-sm font-medium text-green-600">
                        {INVITATION_STATS.averageAttendanceRate}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${INVITATION_STATS.averageAttendanceRate}%` }}
                      />
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Confirmation Rate</span>
                      <span className="text-sm font-medium text-blue-600">
                        {INVITATION_STATS.averageConfirmationRate}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${INVITATION_STATS.averageConfirmationRate}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <FaUsers className="w-4 h-4 sm:w-5 sm:h-5 text-sehatti-gold-500" />
                    Engagement Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-lg sm:text-xl font-bold text-blue-700">
                        {INVITATION_STATS.totalParticipants}
                      </div>
                      <div className="text-xs sm:text-sm text-blue-600">Total Participants</div>
                    </div>
                    
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-lg sm:text-xl font-bold text-green-700">
                        {Math.round(INVITATION_STATS.totalParticipants / INVITATION_STATS.totalInvitations)}
                      </div>
                      <div className="text-xs sm:text-sm text-green-600">Avg per Session</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Invitations Preview */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                  <FaCalendarPlus className="w-4 h-4 sm:w-5 sm:h-5 text-sehatti-gold-500" />
                  Recent Invitations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {INVITATIONS.slice(0, 3).map((invitation) => {
                    const SessionIcon = SESSION_TYPE_ICONS[invitation.sessionType]
                    return (
                      <div key={invitation.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center gap-3">
                          <SessionIcon className="w-4 h-4 text-sehatti-gold-600" />
                          <div>
                            <div className="font-medium text-sm">{invitation.title}</div>
                            <div className="text-xs text-gray-600">
                              {formatDate(invitation.time)} • {formatDuration(invitation.duration)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={`text-xs ${getStatusColor(invitation.status)}`}>
                            {invitation.status}
                          </Badge>
                          {invitation.type === 'online' ? 
                            <FaVideo className="w-3 h-3 text-blue-500" /> : 
                            <FaMapMarkerAlt className="w-3 h-3 text-green-500" />
                          }
                        </div>
                      </div>
                    )
                  })}
                  <Button 
                    variant="outline" 
                    className="w-full text-sm" 
                    onClick={() => setSelectedTab('invitations')}
                  >
                    View All Invitations
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'invitations' && (
          <div className="space-y-4 sm:space-y-6">
            {/* Mobile-First Filters */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search invitations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sehatti-gold-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>
              
              <div className="flex gap-2 sm:gap-3">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sehatti-gold-500 text-sm"
                >
                  <option value="all">All Types</option>
                  <option value="online">Online</option>
                  <option value="physical">Physical</option>
                </select>
                
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sehatti-gold-500 text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>

            {/* Mobile-First Invitation Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
              {filteredInvitations.map((invitation) => {
                const SessionIcon = SESSION_TYPE_ICONS[invitation.sessionType]
                return (
                  <Card key={invitation.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <SessionIcon className="w-4 h-4 text-sehatti-gold-600" />
                          <div className={`w-2 h-2 rounded-full ${getPriorityColor(invitation.priority)}`} />
                        </div>
                        <div className="flex items-center gap-2">
                          {invitation.type === 'online' ? 
                            <FaVideo className="w-4 h-4 text-blue-500" /> : 
                            <FaMapMarkerAlt className="w-4 h-4 text-green-500" />
                          }
                          <Badge className={`text-xs ${getStatusColor(invitation.status)}`}>
                            {invitation.status}
                          </Badge>
                        </div>
                      </div>
                      <CardTitle className="text-base sm:text-lg line-clamp-2">
                        {invitation.title}
                      </CardTitle>
                      <CardDescription className="text-sm line-clamp-2">
                        {invitation.description}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-3 text-xs sm:text-sm">
                        <div>
                          <span className="text-gray-600">Date & Time</span>
                          <div className="font-medium">{formatDate(invitation.time)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Duration</span>
                          <div className="font-medium">{formatDuration(invitation.duration)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Consultant</span>
                          <div className="font-medium text-xs">{invitation.consultant.name}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Participants</span>
                          <div className="font-medium">
                            {invitation.confirmed?.length || 0}/{invitation.maxParticipants}
                          </div>
                        </div>
                      </div>

                      {invitation.type === 'physical' && invitation.location && (
                        <div className="text-xs">
                          <span className="text-gray-600">Location: </span>
                          <span className="font-medium">{invitation.location.venue}</span>
                        </div>
                      )}

                      {invitation.confirmationRate && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-600">Confirmation Rate:</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                            <div 
                              className="bg-green-500 h-1.5 rounded-full transition-all duration-500"
                              style={{ width: `${invitation.confirmationRate}%` }}
                            />
                          </div>
                          <span className="text-xs font-medium text-green-600">
                            {invitation.confirmationRate}%
                          </span>
                        </div>
                      )}

                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1 text-xs">
                          <FaEye className="w-3 h-3 mr-1" />
                          View
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1 text-xs">
                          <FaEdit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline" className="text-xs">
                          <FaPaperPlane className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {filteredInvitations.length === 0 && (
              <Card className="p-8 text-center">
                <CardContent>
                  <FaCalendarPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No invitations found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || filterType !== 'all' || filterStatus !== 'all'
                      ? 'Try adjusting your search or filters'
                      : 'Create your first invitation to get started'
                    }
                  </p>
                  <Button onClick={() => setShowCreateModal(true)}>
                    <FaPlus className="w-4 h-4 mr-2" />
                    Create Invitation
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Analytics Tab - Enhanced for mobile */}
        {selectedTab === 'analytics' && (
          <div className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">Session Type Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { type: 'leadership', count: 24, color: 'bg-blue-500' },
                      { type: 'mental_health', count: 18, color: 'bg-green-500' },
                      { type: 'team_building', count: 15, color: 'bg-purple-500' },
                      { type: 'communication', count: 12, color: 'bg-orange-500' },
                      { type: 'wellness', count: 10, color: 'bg-pink-500' }
                    ].map((item) => (
                      <div key={item.type} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${item.color}`} />
                          <span className="text-sm capitalize">{item.type.replace('_', ' ')}</span>
                        </div>
                        <span className="text-sm font-medium">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">Monthly Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { month: 'Jan', invitations: 45, attendance: 78 },
                      { month: 'Dec', invitations: 38, attendance: 82 },
                      { month: 'Nov', invitations: 42, attendance: 75 },
                      { month: 'Oct', invitations: 35, attendance: 80 }
                    ].map((item) => (
                      <div key={item.month} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.month}</span>
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-sm">{item.invitations} invitations</div>
                            <div className="text-xs text-gray-600">{item.attendance}% attendance</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default HRInvitationManagement 