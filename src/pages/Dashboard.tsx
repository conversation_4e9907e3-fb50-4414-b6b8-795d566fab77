import { useState } from 'react'
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>ser, <PERSON>a<PERSON><PERSON>, FaChartBar, FaSignOutAlt } from 'react-icons/fa'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { GradientText } from '@/components/ui/GradientText'
import { Container } from '@/components/ui/Container'
import { GlassCard } from '@/components/ui/GlassCard'

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('overview')

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 p-4">
      {/* Decorative background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-80 h-80 rounded-full bg-se<PERSON>ti-gold-200/30 dark:bg-sehatti-gold-900/20 blur-3xl" />
        <div className="absolute top-1/4 -right-20 w-60 h-60 rounded-full bg-sehatti-gold-300/20 dark:bg-sehatti-gold-800/20 blur-3xl" />
        <div className="absolute -bottom-40 left-1/3 w-80 h-80 rounded-full bg-sehatti-gold-200/30 dark:bg-sehatti-gold-900/20 blur-3xl" />
      </div>

      <Container size="xl">
        {/* Header */}
        <header className="flex flex-col md:flex-row justify-between items-center py-6 mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              <GradientText gradient="goldRich" size="4xl">Dashboard</GradientText>
            </h1>
            <p className="text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Welcome back, John Doe
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex items-center gap-4">
            <Button variant="outline" leftIcon={<FaUser />}>
              Profile
            </Button>
            <Button variant="destructive" leftIcon={<FaSignOutAlt />}>
              Logout
            </Button>
          </div>
        </header>
        
        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <GlassCard variant="default" className="sticky top-6">
              <div className="p-4 space-y-1">
                <button 
                  onClick={() => setActiveTab('overview')} 
                  className={`flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-colors ${
                    activeTab === 'overview' 
                      ? 'bg-sehatti-gold-500 text-white' 
                      : 'hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800/50'
                  }`}
                >
                  <FaHome /> Overview
                </button>
                
                <button 
                  onClick={() => setActiveTab('analytics')} 
                  className={`flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-colors ${
                    activeTab === 'analytics' 
                      ? 'bg-sehatti-gold-500 text-white' 
                      : 'hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800/50'
                  }`}
                >
                  <FaChartBar /> Analytics
                </button>
                
                <button 
                  onClick={() => setActiveTab('settings')} 
                  className={`flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-colors ${
                    activeTab === 'settings' 
                      ? 'bg-sehatti-gold-500 text-white' 
                      : 'hover:bg-sehatti-warm-gray-100 dark:hover:bg-sehatti-warm-gray-800/50'
                  }`}
                >
                  <FaCog /> Settings
                </button>
              </div>
            </GlassCard>
          </div>
          
          {/* Main content area */}
          <div className="lg:col-span-3 space-y-6">
            {activeTab === 'overview' && (
              <>
                {/* Stats cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Total Users</CardTitle>
                      <CardDescription>All registered users</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-sehatti-gold-600">1,234</p>
                      <p className="text-xs text-green-600 mt-1">+12% from last month</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Active Sessions</CardTitle>
                      <CardDescription>Currently online</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-sehatti-gold-600">56</p>
                      <p className="text-xs text-red-600 mt-1">-3% from yesterday</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Revenue</CardTitle>
                      <CardDescription>Monthly income</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-sehatti-gold-600">$12,345</p>
                      <p className="text-xs text-green-600 mt-1">+8% from last month</p>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Recent activity */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                    <CardDescription>Latest user interactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[1, 2, 3, 4, 5].map((item) => (
                        <div key={item} className="flex items-center gap-4 p-3 rounded-lg hover:bg-sehatti-warm-gray-50 dark:hover:bg-sehatti-warm-gray-800/50 transition-colors">
                          <div className="w-10 h-10 rounded-full bg-sehatti-gold-100 dark:bg-sehatti-gold-900/30 flex items-center justify-center text-sehatti-gold-600">
                            <FaUser />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">User #{item} updated their profile</p>
                            <p className="text-sm text-sehatti-warm-gray-500">{item * 10} minutes ago</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
            
            {activeTab === 'analytics' && (
              <Card>
                <CardHeader>
                  <CardTitle>Analytics</CardTitle>
                  <CardDescription>Performance metrics and insights</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center border border-dashed border-sehatti-warm-gray-300 dark:border-sehatti-warm-gray-700 rounded-lg">
                    <p className="text-sehatti-warm-gray-500">Analytics charts will be displayed here</p>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {activeTab === 'settings' && (
              <Card>
                <CardHeader>
                  <CardTitle>Settings</CardTitle>
                  <CardDescription>Manage your account preferences</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Account Settings</h3>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-3 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg">
                          <span>Email Notifications</span>
                          <Button variant="outline" size="sm">Enable</Button>
                        </div>
                        <div className="flex items-center justify-between p-3 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg">
                          <span>Two-Factor Authentication</span>
                          <Button variant="outline" size="sm">Configure</Button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium mb-2">Privacy Settings</h3>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-3 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg">
                          <span>Profile Visibility</span>
                          <Button variant="outline" size="sm">Public</Button>
                        </div>
                        <div className="flex items-center justify-between p-3 border border-sehatti-warm-gray-200 dark:border-sehatti-warm-gray-700 rounded-lg">
                          <span>Data Sharing</span>
                          <Button variant="outline" size="sm">Manage</Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </Container>
    </div>
  )
}

export default Dashboard 