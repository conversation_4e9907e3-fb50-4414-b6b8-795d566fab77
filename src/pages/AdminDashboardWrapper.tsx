import { useGetMeQuery } from '@/store/api'
import { useAppSelector } from '@/store/store'
import { selectAuth } from '@/features/auth/auth-slice'
import { Spinner } from '@/components/ui/Spinner'
import { Alert } from '@/components/ui/Alert'
import AdminDashboard from './dashboards/AdminDashboard'

const AdminDashboardWrapper = () => {
  const localAuthState = useAppSelector(selectAuth)
  
  // Debug authentication state
  console.log('🔍 Auth Debug:', {
    localAuthState,
    token: localStorage.getItem('hr-auth-token'),
    user: localStorage.getItem('hr-auth-user')
  });
  
  // First try to use local auth state
  if (localAuthState.isAuthenticated && localAuthState.user) {
    console.log('✅ Using local auth state');
    return <AdminDashboard user={localAuthState.user} />
  }

  // Fallback: try API query if local auth fails
  const { data: userData, isLoading, error } = useGetMeQuery()

  console.log('🔍 API Query Result:', { userData, isLoading, error });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !userData?.data?.user) {
    console.error('❌ Auth Error:', error);
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
        <Alert variant="destructive" title="Error Loading Dashboard">
          Failed to load user data. Please try refreshing the page or logging in again.
        </Alert>
      </div>
    )
  }

  console.log('✅ Using API user data');
  return <AdminDashboard user={userData.data.user} />
}

export default AdminDashboardWrapper 