import { useState, useEffect } from 'react'
import { 
  <PERSON>a<PERSON><PERSON>s, 
  FaChartBar, 
  FaEnvelope, 
  FaBell, 
  FaUserShield,
  FaCog,
  FaBuilding,
  FaSync,
  FaCheckCircle,
  FaClock,
  FaExclamationTriangle,
  FaPlus,
  FaArrowRight,
  FaPoll,
  FaFileAlt,
  FaUpload
} from 'react-icons/fa'
import { BiLoaderAlt } from 'react-icons/bi'

// UI Components - Using pre-styled components consistently  
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { GradientText } from '../components/ui/GradientText'
import { Badge } from '../components/ui/Badge'
import { Button } from '../components/ui/Button'
import { StatCard } from '../components/ui/StatCard'

// Layout - Using AdminLayout for consistency
import { AdminLayout } from '../components/layout/AdminLayout'

// API & Types
import { 
  useGetMyEmployeesQuery, 
  useGetEmployeeStatsQuery 
} from '../store/api/employeeApi'
import { 
  useGetInvitationsQuery 
} from '../store/api/invitationApi'
import { useAppSelector } from '../store/hooks'
import type { User } from '@/types/api'

// Navigation hook
import { useNavigate } from 'react-router-dom'

// CSV Import Component
import EmployeeCsvImport from '../components/employee/EmployeeCsvImport'

const dashboardCards = [
  {
    id: 'employees',
    title: 'Employee Management',
    description: 'Manage team members, roles, and permissions',
    icon: <FaUsers className="w-6 h-6" />,
    path: '/hr/employees',
    color: 'from-blue-500 to-blue-600',
    stats: 'totalEmployees'
  },
  {
    id: 'analytics',
    title: 'HR Analytics',
    description: 'View employee performance and engagement metrics',
    icon: <FaChartBar className="w-6 h-6" />,
    path: '/hr/analytics',
    color: 'from-green-500 to-green-600',
    stats: 'activeEmployees'
  },
  {
    id: 'surveys',
    title: 'Survey Management',
    description: 'Create and manage employee surveys and assessments',
    icon: <FaPoll className="w-6 h-6" />,
    path: '/hr/surveys',
    color: 'from-purple-500 to-purple-600',
    stats: 'pendingInvitations'
  },
  {
    id: 'notifications',
    title: 'Notifications',
    description: 'Manage system notifications and announcements',
    icon: <FaBell className="w-6 h-6" />,
    path: '/hr/notifications',
    color: 'from-orange-500 to-orange-600',
    stats: 'notifications'
  },
  {
    id: 'invitations',
    title: 'Employee Invitations',
    description: 'Send and track employee invitation status',
    icon: <FaEnvelope className="w-6 h-6" />,
    path: '/hr/invitations',
    color: 'from-pink-500 to-pink-600',
    stats: 'pendingInvitations'
  },
  {
    id: 'settings',
    title: 'HR Settings',
    description: 'Configure HR policies and system preferences',
    icon: <FaCog className="w-6 h-6" />,
    path: '/hr/settings',
    color: 'from-gray-500 to-gray-600',
    stats: 'settings',
    comingSoon: true
  }
]

export default function HRAdminDashboard() {
  const navigate = useNavigate()
  const [showCsvImport, setShowCsvImport] = useState(false)
  
  // Get user from auth store
  const auth = useAppSelector(state => state.auth.user)
  const currentUser = auth as User
  const companyId = currentUser?.companyId

  // API Queries
  const {
    data: employeesResponse,
    isLoading: employeesLoading,
    error: employeesError,
    refetch: refetchEmployees
  } = useGetMyEmployeesQuery({
    page: 1,
    limit: 100
  }, {
    pollingInterval: 60000,
    refetchOnFocus: true
  })

  const {
    data: invitationsResponse,
    isLoading: invitationsLoading,
    error: invitationsError
  } = useGetInvitationsQuery({
    company_id: companyId!,
    page: 1,
    limit: 100
  }, {
    skip: !companyId,
    pollingInterval: 60000,
    refetchOnFocus: true
  })

  // Extract data with fallbacks for demo
  const employees = employeesResponse?.data || [
    // Fallback demo data if API fails
    { id: '1', name: 'John Doe', email: '<EMAIL>', status: 'ACTIVE', role: 'EMPLOYEE' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>', status: 'ACTIVE', role: 'EMPLOYEE' },
    { id: '3', name: 'Mike Johnson', email: '<EMAIL>', status: 'PENDING', role: 'EMPLOYEE' },
    { id: '4', name: 'Sarah Wilson', email: '<EMAIL>', status: 'ACTIVE', role: 'EMPLOYEE' },
    { id: '5', name: 'Tom Brown', email: '<EMAIL>', status: 'PENDING', role: 'EMPLOYEE' },
  ]
  const invitations = invitationsResponse?.data || [
    // Fallback demo data if API fails
    { id: '1', email: '<EMAIL>', status: 'PENDING', type: 'employee' },
    { id: '2', email: '<EMAIL>', status: 'PENDING', type: 'employee' },
    { id: '3', email: '<EMAIL>', status: 'ACCEPTED', type: 'employee' },
  ]
  
  // Debug logging
  console.log('🏢 HR Admin Dashboard Debug:')
  console.log('Current User:', currentUser)
  console.log('Company ID:', companyId)
  console.log('Employees Response:', employeesResponse)
  console.log('Employees Error:', employeesError)
  console.log('Invitations Response:', invitationsResponse)
  console.log('Invitations Error:', invitationsError)
  console.log('Employees Array:', employees)
  console.log('Invitations Array:', invitations)
  
  // Stats calculations
  const totalEmployees = employees.length
  const activeEmployees = employees.filter(emp => emp.status === 'ACTIVE').length
  const pendingEmployees = employees.filter(emp => emp.status === 'PENDING').length
  const pendingInvitations = invitations.filter(inv => inv.status === 'PENDING').length
  
  console.log('📊 Calculated Stats:')
  console.log('Total Employees:', totalEmployees)
  console.log('Active Employees:', activeEmployees)
  console.log('Pending Employees:', pendingEmployees)
  console.log('Pending Invitations:', pendingInvitations)
  
  // System health calculation
  const systemHealth = employeesError || invitationsError ? 'Issues Detected' : 'Healthy'
  
  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token")
    window.location.href = "/login"
  }

  const handleCardClick = (card: typeof dashboardCards[0]) => {
    if (card.comingSoon) {
      return
    }
    navigate(card.path)
  }

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'add-employee':
        navigate('/hr/employees?action=create')
        break
      case 'import-csv':
        setShowCsvImport(true)
        break
      case 'view-analytics':
        navigate('/hr/analytics')
        break
      case 'send-invitations':
        navigate('/hr/invitations?action=create')
        break
      case 'manage-surveys':
        navigate('/hr/surveys')
        break
      default:
        break
    }
  }

  const handleCsvImportSuccess = () => {
    // Refresh employee data after successful import
    refetchEmployees()
    setShowCsvImport(false)
  }

  const getStatValue = (statKey: string) => {
    switch (statKey) {
      case 'totalEmployees': return totalEmployees
      case 'activeEmployees': return activeEmployees
      case 'pendingInvitations': return pendingInvitations
      case 'notifications': return 0 // Placeholder
      case 'settings': return 0 // Placeholder
      default: return 0
    }
  }

  const isLoading = employeesLoading || invitationsLoading
  const hasError = employeesError || invitationsError

  return (
    <AdminLayout
      user={currentUser}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={pendingInvitations}
    >
      <div className="space-y-4 sm:space-y-6">
        {/* Header - Following Admin Dashboard Pattern */}
        <header className="space-y-3 sm:space-y-4 py-4 sm:py-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">
              <GradientText gradient="goldRich" size="2xl" className="sm:text-3xl lg:text-4xl">
                HR Admin Dashboard
              </GradientText>
            </h1>
            <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Welcome back, {currentUser?.name}
            </p>
          </div>
          
          <div className="flex flex-col items-center gap-2 sm:flex-row sm:gap-4">
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaUserShield className="mr-1 w-3 h-3" /> HR Administrator
            </Badge>
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaBuilding className="mr-1 w-3 h-3" /> {currentUser?.companyName || 'Company'}
            </Badge>
            <div className="flex items-center gap-2">
              <Badge 
                variant={systemHealth === 'Healthy' ? 'success' : 'warning'}
                className="text-xs sm:text-sm"
              >
                {systemHealth}
              </Badge>
              {isLoading && <FaSync className="animate-spin text-sehatti-gold-600 w-3 h-3 sm:w-4 sm:h-4" />}
            </div>
          </div>
        </header>

        {/* Error Display */}
        {hasError && (
          <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-start gap-2">
              <FaExclamationTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span className="text-sm sm:text-base">
                Error loading dashboard data. Please refresh the page.
              </span>
            </div>
          </div>
        )}

        {/* Stats Overview - Following Admin Dashboard Pattern */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <StatCard
            title="Total Employees"
            value={totalEmployees}
            icon={<FaUsers className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />}
          />
          <StatCard
            title="Active Employees"
            value={activeEmployees}
            icon={<FaCheckCircle className="text-green-600 w-4 h-4 sm:w-5 sm:h-5" />}
          />
          <StatCard
            title="Pending Status"
            value={pendingEmployees}
            icon={<FaClock className="text-yellow-600 w-4 h-4 sm:w-5 sm:h-5" />}
          />
          <StatCard
            title="Pending Invites"
            value={pendingInvitations}
            icon={<FaEnvelope className="text-blue-600 w-4 h-4 sm:w-5 sm:h-5" />}
          />
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaPlus className="text-sehatti-gold-600" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common HR management tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
              <Button
                variant="outline"
                className="flex flex-col items-center gap-2 h-auto p-4 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-gold-900/20 transition-colors"
                onClick={() => handleQuickAction('add-employee')}
              >
                <FaUsers className="w-5 h-5 text-sehatti-gold-600" />
                <span className="text-sm font-medium">Add Employee</span>
              </Button>
              
              <Button
                variant="outline"
                className="flex flex-col items-center gap-2 h-auto p-4 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                onClick={() => handleQuickAction('import-csv')}
              >
                <FaUpload className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium">Import CSV</span>
              </Button>
              
              <Button
                variant="outline"
                className="flex flex-col items-center gap-2 h-auto p-4 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
                onClick={() => handleQuickAction('view-analytics')}
              >
                <FaChartBar className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">View Analytics</span>
              </Button>
              
              <Button
                variant="outline"
                className="flex flex-col items-center gap-2 h-auto p-4 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                onClick={() => handleQuickAction('send-invitations')}
              >
                <FaEnvelope className="w-5 h-5 text-purple-600" />
                <span className="text-sm font-medium">Send Invites</span>
              </Button>
              
              <Button
                variant="outline"
                className="flex flex-col items-center gap-2 h-auto p-4 hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
                onClick={() => handleQuickAction('manage-surveys')}
              >
                <FaPoll className="w-5 h-5 text-orange-600" />
                <span className="text-sm font-medium">Manage Surveys</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* HR Management Cards - Following Admin Dashboard Pattern */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {dashboardCards.map((card) => (
            <Card 
              key={card.id} 
              className={`
                ${card.comingSoon ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg'} 
                transition-all duration-200 relative overflow-hidden
              `}
              onClick={() => handleCardClick(card)}
            >
              {/* Background Gradient */}
              <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${card.color} opacity-10 rounded-full -mr-10 -mt-10`} />
              
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${card.color} text-white`}>
                    {card.icon}
                  </div>
                  {card.comingSoon && (
                    <Badge variant="outline" className="text-xs">
                      Coming Soon
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg">{card.title}</CardTitle>
                <CardDescription className="text-sm">
                  {card.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {card.stats && (
                      <>
                        <span className="text-2xl font-bold text-sehatti-gold-600">
                          {isLoading ? '...' : getStatValue(card.stats)}
                        </span>
                        <span className="text-sm text-gray-500">
                          {card.stats === 'totalEmployees' && 'total'}
                          {card.stats === 'activeEmployees' && 'active'}
                          {card.stats === 'pendingInvitations' && 'pending'}
                        </span>
                      </>
                    )}
                  </div>
                  {!card.comingSoon && (
                    <FaArrowRight className="w-4 h-4 text-gray-400 group-hover:text-sehatti-gold-600 transition-colors" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity Summary */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Employee Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaUsers className="text-sehatti-gold-600" />
                Employee Overview
              </CardTitle>
              <CardDescription>
                Current team status and distribution
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <BiLoaderAlt className="animate-spin w-6 h-6 text-sehatti-gold-600" />
                  <span className="ml-2">Loading employees...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaCheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium">Active Employees</span>
                    </div>
                    <span className="text-lg font-bold text-green-600">{activeEmployees}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaClock className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm font-medium">Pending Setup</span>
                    </div>
                    <span className="text-lg font-bold text-yellow-600">{pendingEmployees}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaEnvelope className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium">Pending Invites</span>
                    </div>
                    <span className="text-lg font-bold text-blue-600">{pendingInvitations}</span>
                  </div>
                  
                  <Button 
                    variant="outline" 
                    className="w-full mt-4 hover:bg-sehatti-gold-50 dark:hover:bg-sehatti-gold-900/20 transition-colors"
                    onClick={() => navigate('/hr/employees')}
                  >
                    View All Employees
                    <FaArrowRight className="w-3 h-3 ml-2" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaCog className="text-sehatti-gold-600" />
                System Status
              </CardTitle>
              <CardDescription>
                HR system health and recent activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${systemHealth === 'Healthy' ? 'bg-green-500' : 'bg-yellow-500'}`} />
                    <span className="text-sm font-medium">System Health</span>
                  </div>
                  <Badge 
                    variant={systemHealth === 'Healthy' ? 'success' : 'warning'}
                    className="text-xs"
                  >
                    {systemHealth}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaSync className="w-3 h-3 text-blue-500" />
                    <span className="text-sm font-medium">Last Sync</span>
                  </div>
                  <span className="text-xs text-gray-500">Just now</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaFileAlt className="w-3 h-3 text-purple-500" />
                    <span className="text-sm font-medium">Data Backup</span>
                  </div>
                  <Badge variant="success" className="text-xs">
                    Up to date
                  </Badge>
                </div>
                
                <Button 
                  variant="outline" 
                  className="w-full mt-4 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                  onClick={() => refetchEmployees()}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <BiLoaderAlt className="animate-spin w-3 h-3 mr-2" />
                      Refreshing...
                    </>
                  ) : (
                    <>
                      <FaSync className="w-3 h-3 mr-2" />
                      Refresh Data
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CSV Import Modal */}
        <EmployeeCsvImport
          isOpen={showCsvImport}
          onClose={() => setShowCsvImport(false)}
          onSuccess={handleCsvImportSuccess}
        />
      </div>
    </AdminLayout>
  )
}
