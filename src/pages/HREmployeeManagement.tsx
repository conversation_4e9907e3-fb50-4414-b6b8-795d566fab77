import { useState } from 'react'
import { toast } from 'react-hot-toast'
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaEye, 
  FaSearch, 
  FaUsers, 
  FaUserCheck, 
  FaClock, 
  FaDownload, 
  FaUpload,
  FaFilter,
  FaEllipsisH,
  FaStar,
  FaEnvelope,
  FaPhone,
  FaCalendar,
  FaSync,
  FaBuilding,
  FaUserShield,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaPause
} from 'react-icons/fa'
import { BiLoaderAlt } from 'react-icons/bi'

// UI Components - Using pre-styled components consistently
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Badge } from '../components/ui/Badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { GradientText } from '../components/ui/GradientText'
import { 
  Modal, 
  ModalContent, 
  ModalHeader, 
  ModalTitle, 
  ModalDescription,
  ModalFooter
} from '../components/ui/Modal'
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from '../components/ui/Table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../components/ui/DropdownMenu'
import { Checkbox } from '../components/ui/Checkbox'
import { Select } from '../components/ui/Select'
import { Textarea } from '../components/ui/Textarea'
import { SearchInput } from '../components/ui/SearchInput'
import { StatCard } from '../components/ui/StatCard'

// API & Types
import {
  type Employee,
  useGetMyEmployeesQuery,
  useCreateEmployeeMutation,
  useUpdateEmployeeMutation,
  useUpdateEmployeeStatusMutation,
  useDeleteEmployeeMutation,
  useUploadEmployeesCsvMutation,
  useMakeEmployeeLeaderMutation,
  useRemoveEmployeeLeaderMutation
} from '../store/api/employeeApi'

// Layout - Using AdminLayout for consistency
import { AdminLayout } from '../components/layout/AdminLayout'

// Auth
import { useAppSelector } from '../store/hooks'
import type { User } from '@/types/api'

// CSV Import Component
import EmployeeCsvImport from '../components/employee/EmployeeCsvImport'

// Utility function to handle abnormal timestamps from API
const formatTimestamp = (timestamp: string | number | undefined): string => {
  if (!timestamp) return 'N/A'
  
  try {
    // Convert to number if string
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
    
    // Check if timestamp is abnormally large (likely nanoseconds or corrupted)
    // Normal timestamps should be around 1600000000000 (milliseconds) or 1600000000 (seconds)
    if (ts > 9999999999999) {
      // If it's abnormally large, try to convert from nanoseconds to milliseconds
      const msTimestamp = Math.floor(ts / 1000000)
      const date = new Date(msTimestamp)
      return date.getTime() > 0 && date.getFullYear() > 1970 ? date.toLocaleDateString() : 'Recently'
    } else if (ts > 9999999999) {
      // If it looks like milliseconds
      const date = new Date(ts)
      return date.getTime() > 0 && date.getFullYear() > 1970 ? date.toLocaleDateString() : 'Recently'
    } else if (ts > 999999999) {
      // If it looks like seconds, convert to milliseconds
      const date = new Date(ts * 1000)
      return date.getTime() > 0 && date.getFullYear() > 1970 ? date.toLocaleDateString() : 'Recently'
    } else {
      // If timestamp is too small, show fallback
      return 'Recently'
    }
  } catch (error) {
    console.warn('Failed to parse timestamp:', timestamp, error)
    return 'Recently'
  }
}

// Types
interface EmployeeFormData {
  name: string
  email: string
  phone: string
  department: string
}

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'ACTIVE', label: 'Active' },
  { value: 'active', label: 'Active' },
  { value: 'PENDING', label: 'Pending' },
  { value: 'pending', label: 'Pending' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'SUSPENDED', label: 'Suspended' },
  { value: 'suspended', label: 'Suspended' }
]

const statusOptionsForUpdate = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'PENDING', label: 'Pending' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'SUSPENDED', label: 'Suspended' }
]

const statusColors = {
  ACTIVE: 'bg-green-100 text-green-800 border-green-200',
  PENDING: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  INACTIVE: 'bg-gray-100 text-gray-800 border-gray-200',
  SUSPENDED: 'bg-red-100 text-red-800 border-red-200'
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'ACTIVE': return <FaCheckCircle className="w-3 h-3" />
    case 'PENDING': return <FaClock className="w-3 h-3" />
    case 'INACTIVE': return <FaPause className="w-3 h-3" />
    case 'SUSPENDED': return <FaTimesCircle className="w-3 h-3" />
    default: return <FaClock className="w-3 h-3" />
  }
}

export default function HREmployeeManagement() {
  // Get user from auth store
  const auth = useAppSelector(state => state.auth.user)
  const currentUser = auth as User
  // Try both field names for company ID
  const companyId = currentUser?.companyId || (currentUser as any)?.company_id
  
  // State
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showStatusModal, setShowStatusModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [showCsvImport, setShowCsvImport] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [newStatus, setNewStatus] = useState<string>('')
  const [formData, setFormData] = useState<EmployeeFormData>({
    name: '',
    email: '',
    phone: '',
    department: '',
  })
  const [editFormData, setEditFormData] = useState<EmployeeFormData>({
    name: '',
    email: '',
    phone: '',
    department: '',
  })
  


  // API Queries
  const {
    data: employeesResponse,
    isLoading,
    error,
    refetch,
    isFetching
  } = useGetMyEmployeesQuery({
    page: currentPage,
    limit: 10,
    search: searchQuery,
    status: statusFilter === 'all' ? undefined : statusFilter
  }, {
    pollingInterval: 30000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
    skip: !companyId // Skip query if no company ID
  })

  const [createEmployee, { isLoading: isCreating }] = useCreateEmployeeMutation()
  const [updateEmployee, { isLoading: isUpdating }] = useUpdateEmployeeMutation()
  const [updateEmployeeStatus, { isLoading: isUpdatingStatus }] = useUpdateEmployeeStatusMutation()
  const [deleteEmployee, { isLoading: isDeleting }] = useDeleteEmployeeMutation()
  const [uploadCsv, { isLoading: isUploading }] = useUploadEmployeesCsvMutation()
  const [makeLeader, { isLoading: isMakingLeader }] = useMakeEmployeeLeaderMutation()
  const [removeLeader, { isLoading: isRemovingLeader }] = useRemoveEmployeeLeaderMutation()

  // Additional state for bulk operations
  const [showBulkStatusModal, setShowBulkStatusModal] = useState(false)
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false)
  const [bulkStatus, setBulkStatus] = useState<string>('')

  // Extract data - Fix: API returns 'items' not 'data'
  const employees = employeesResponse?.items || employeesResponse?.data || []
  const totalEmployees = employeesResponse?.total || 0
  const totalPages = Math.ceil(totalEmployees / 10)

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('HR Employee Management Debug:', {
      employeesResponse,
      employees,
      totalEmployees,
      currentUser,
      companyId,
      isLoading,
      error
    })
  }

  // Stats calculations - Handle both uppercase and lowercase status
  const activeEmployees = employees.filter(emp => 
    emp.status?.toLowerCase() === 'active' || emp.status === 'ACTIVE'
  ).length
  const pendingEmployees = employees.filter(emp => 
    emp.status?.toLowerCase() === 'pending' || emp.status === 'PENDING'
  ).length
  const leaderEmployees = employees.filter(emp => emp.isLeader).length

  // Helper functions
  const resetForm = () => {
    setFormData({ name: '', email: '', phone: '', department: '' })
  }

  const resetEditForm = () => {
    setEditFormData({ name: '', email: '', phone: '', department: '' })
  }

  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token")
    window.location.href = "/login"
  }

  // CRUD Handlers
  const handleCreateEmployee = async () => {
    try {
      if (!companyId) {
        toast.error('Company ID not found. Please log in again.')
        return
      }

      const payload = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone || undefined,
        department: formData.department || undefined,
        company_id: companyId
      }

      await createEmployee(payload).unwrap()
      toast.success('Employee created successfully! Invitation email sent.')
      setShowCreateModal(false)
      resetForm()
    } catch (error: any) {
      console.error('Create employee error:', error)
      toast.error(error?.data?.detail || error?.data?.message || 'Failed to create employee')
    }
  }

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee)
    setEditFormData({
      name: employee.name || '',
      email: employee.email || '',
      phone: employee.phone || '',
      department: employee.department || '',
    })
    setShowEditModal(true)
  }

  const handleUpdateEmployee = async () => {
    if (!selectedEmployee) return
    
    try {
      if (!companyId) {
        toast.error('Company ID not found. Please log in again.')
        return
      }

      // Use the full updateEmployee mutation with proper data
      await updateEmployee({ 
        id: selectedEmployee.id,
        name: editFormData.name,
        email: editFormData.email, // Include email but backend will ignore it
        phone: editFormData.phone,
        department: editFormData.department,
        company_id: companyId
      }).unwrap()
      toast.success('Employee updated successfully')
      setShowEditModal(false)
      resetEditForm()
    } catch (error: any) {
      console.error('Update employee error:', error)
      toast.error(error?.data?.detail || error?.data?.message || 'Failed to update employee')
    }
  }

  const handleChangeStatus = (employee: Employee) => {
    setSelectedEmployee(employee)
    // Normalize status to uppercase for consistency
    const currentStatus = employee.status?.toUpperCase() || 'PENDING'
    setNewStatus(currentStatus)
    setShowStatusModal(true)
  }

  const handleUpdateStatus = async () => {
    if (!selectedEmployee) return
    
    try {
      await updateEmployeeStatus({ id: selectedEmployee.id, status: newStatus }).unwrap()
      toast.success('Employee status updated successfully')
      setShowStatusModal(false)
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to update employee status')
    }
  }

  const handleDeleteEmployee = async () => {
    if (!selectedEmployee) return
    
    try {
      await deleteEmployee(selectedEmployee.id).unwrap()
      toast.success('Employee deleted successfully')
      setShowDeleteModal(false)
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to delete employee')
    }
  }

  const handleViewEmployee = (employee: Employee) => {
    setSelectedEmployee(employee)
    setShowViewModal(true)
  }

  const handleCsvImportSuccess = () => {
    // Refresh employee data after successful import
    refetch()
    setShowCsvImport(false)
    toast.success('Employees imported successfully!')
  }

  const handleMakeLeader = async (employeeIds: string[]) => {
    try {
      await makeLeader({ ids: employeeIds }).unwrap()
      toast.success('Employees promoted to leaders successfully')
      setSelectedEmployees([])
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to make employees leaders')
    }
  }

  const handleRemoveLeader = async (employeeIds: string[]) => {
    try {
      await removeLeader({ ids: employeeIds }).unwrap()
      toast.success('Leadership removed from employees successfully')
      setSelectedEmployees([])
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to remove leadership')
    }
  }

  const handleBulkStatusChange = async () => {
    if (!bulkStatus || selectedEmployees.length === 0) return
    
    try {
      // Update each selected employee's status
      const promises = selectedEmployees.map(id => 
        updateEmployeeStatus({ id, status: bulkStatus }).unwrap()
      )
      await Promise.all(promises)
      toast.success(`Status updated for ${selectedEmployees.length} employee(s)`)
      setSelectedEmployees([])
      setShowBulkStatusModal(false)
      setBulkStatus('')
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to update employee statuses')
    }
  }

  const handleBulkDelete = async () => {
    if (selectedEmployees.length === 0) return
    
    try {
      // Delete each selected employee
      const promises = selectedEmployees.map(id => 
        deleteEmployee(id).unwrap()
      )
      await Promise.all(promises)
      toast.success(`${selectedEmployees.length} employee(s) deleted successfully`)
      setSelectedEmployees([])
      setShowBulkDeleteModal(false)
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to delete employees')
    }
  }

  return (
    <AdminLayout
      user={currentUser}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={0}
    >
      <div className="space-y-4 sm:space-y-6">
        {/* Header - Following Admin Dashboard Pattern */}
        <header className="space-y-3 sm:space-y-4 py-4 sm:py-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">
              <GradientText gradient="goldRich" size="2xl" className="sm:text-3xl lg:text-4xl">
                Employee Management
              </GradientText>
            </h1>
            <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
              Manage your company employees, roles, and permissions
            </p>
          </div>
          
          <div className="flex flex-col items-center gap-2 sm:flex-row sm:gap-4">
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaUserShield className="mr-1 w-3 h-3" /> HR Administrator
            </Badge>
            <Badge variant="outline" className="text-xs sm:text-sm">
              <FaBuilding className="mr-1 w-3 h-3" /> {currentUser?.companyName || 'Company'}
            </Badge>
            {(isLoading || isFetching) && <FaSync className="animate-spin text-sehatti-gold-600 w-3 h-3 sm:w-4 sm:h-4" />}
          </div>
        </header>

        {/* Error Display */}
        {error && (
          <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-start gap-2">
              <FaExclamationTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span className="text-sm sm:text-base">Error loading employees: {error.toString()}</span>
            </div>
          </div>
        )}

        {/* Stats Cards - Following Admin Dashboard Pattern */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <StatCard
            title="Total Employees"
            value={isLoading ? "..." : totalEmployees}
            icon={<FaUsers className="text-sehatti-gold-600 w-4 h-4 sm:w-5 sm:h-5" />}
            subtitle="All employees"
          />
          <StatCard
            title="Active Employees"
            value={isLoading ? "..." : activeEmployees}
            icon={<FaUserCheck className="text-green-600 w-4 h-4 sm:w-5 sm:h-5" />}
            subtitle="Currently active"
          />
          <StatCard
            title="Pending Invites"
            value={isLoading ? "..." : pendingEmployees}
            icon={<FaClock className="text-yellow-600 w-4 h-4 sm:w-5 sm:h-5" />}
            subtitle="Awaiting response"
          />
          <StatCard
            title="Team Leaders"
            value={isLoading ? "..." : leaderEmployees}
            icon={<FaStar className="text-purple-600 w-4 h-4 sm:w-5 sm:h-5" />}
            subtitle="Leadership roles"
          />
        </div>

        {/* Actions Bar */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center flex-1">
                <SearchInput
                  placeholder="Search employees..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full sm:w-64"
                />
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full sm:w-auto"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setShowCreateModal(true)}
                  disabled={isCreating}
                  leftIcon={<FaPlus className="w-3 h-3" />}
                >
                  Add Employee
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCsvImport(true)}
                  leftIcon={<FaUpload className="w-3 h-3" />}
                >
                  Import CSV
                </Button>
              </div>
            </div>

            {selectedEmployees.length > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex flex-col gap-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-700">
                      {selectedEmployees.length} employee(s) selected
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedEmployees([])}
                    >
                      Clear Selection
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleMakeLeader(selectedEmployees)}
                      disabled={isMakingLeader}
                      isLoading={isMakingLeader}
                      leftIcon={<FaStar className="w-3 h-3" />}
                    >
                      Make Leaders
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveLeader(selectedEmployees)}
                      disabled={isRemovingLeader}
                      isLoading={isRemovingLeader}
                      leftIcon={<FaStar className="w-3 h-3" />}
                    >
                      Remove Leadership
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowBulkStatusModal(true)}
                      disabled={isUpdatingStatus}
                      leftIcon={<FaSync className="w-3 h-3" />}
                    >
                      Change Status
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowBulkDeleteModal(true)}
                      disabled={isDeleting}
                      leftIcon={<FaTrash className="w-3 h-3" />}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      Delete Selected
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Employees Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaUsers className="text-sehatti-gold-600" />
              Employees List
            </CardTitle>
            <CardDescription>
              Manage employee accounts, statuses, and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <BiLoaderAlt className="animate-spin w-6 h-6 text-sehatti-gold-600" />
                <span className="ml-2">Loading employees...</span>
              </div>
            ) : employees.length === 0 ? (
              <div className="text-center p-8">
                <FaUsers className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No employees found</h3>
                <p className="text-gray-500 mb-4">
                  {searchQuery ? 'Try adjusting your search criteria' : 'Get started by adding your first employee'}
                </p>
                <Button
                  variant="default"
                  size="md"
                  onClick={() => setShowCreateModal(true)}
                  leftIcon={<FaPlus className="w-4 h-4" />}
                >
                  Add First Employee
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedEmployees.length === employees.length}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedEmployees(employees.map(emp => emp.id))
                            } else {
                              setSelectedEmployees([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead className="hidden sm:table-cell">Department</TableHead>
                      <TableHead className="hidden sm:table-cell">Status</TableHead>
                      <TableHead className="hidden lg:table-cell">Role</TableHead>
                      <TableHead className="hidden lg:table-cell">Joined</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {employees.map((employee) => (
                      <TableRow key={employee.id} className="hover:bg-gray-50">
                        <TableCell>
                          <Checkbox
                            checked={selectedEmployees.includes(employee.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedEmployees([...selectedEmployees, employee.id])
                              } else {
                                setSelectedEmployees(selectedEmployees.filter(id => id !== employee.id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-sehatti-gold-100 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-sehatti-gold-700">
                                {employee.name?.charAt(0)?.toUpperCase() || 'E'}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {employee.name || 'Unknown'}
                                {employee.isLeader && (
                                  <FaStar className="inline w-3 h-3 text-yellow-500 ml-1" />
                                )}
                              </div>
                              <div className="text-sm text-gray-500">{employee.email}</div>
                              <div className="text-xs text-gray-400 sm:hidden">
                                {employee.department || 'No department'}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <span className="text-sm text-gray-600">
                            {employee.department || 'No department'}
                          </span>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <Badge 
                            variant="outline"
                            className={`${statusColors[employee.status as keyof typeof statusColors] || statusColors.PENDING} text-xs`}
                          >
                            {getStatusIcon(employee.status || 'PENDING')}
                            <span className="ml-1">{employee.status || 'PENDING'}</span>
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <span className="text-sm text-gray-600">
                            {employee.isLeader ? 'Team Leader' : 'Employee'}
                          </span>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <span className="text-sm text-gray-500">
                            {formatTimestamp(employee.createdAt)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <FaEllipsisH className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewEmployee(employee)}>
                                <FaEye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditEmployee(employee)}>
                                <FaEdit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleChangeStatus(employee)}>
                                <FaSync className="mr-2 h-4 w-4" />
                                Change Status
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => {
                                  setSelectedEmployee(employee)
                                  setShowDeleteModal(true)
                                }}
                                className="text-red-600"
                              >
                                <FaTrash className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <div className="text-sm text-gray-500">
                  Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalEmployees)} of {totalEmployees} employees
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1 || isLoading}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages || isLoading}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create Employee Modal */}
        <Modal open={showCreateModal} onOpenChange={setShowCreateModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle>Add New Employee</ModalTitle>
              <ModalDescription>
                Create a new employee account. An invitation email will be sent.
              </ModalDescription>
            </ModalHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter employee name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="Enter email address"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <Input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <Input
                  type="text"
                  value={formData.department}
                  onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                  placeholder="Enter department"
                />
              </div>
            </div>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => {
                  setShowCreateModal(false)
                  resetForm()
                }}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="md"
                onClick={handleCreateEmployee}
                disabled={isCreating || !formData.name || !formData.email}
                isLoading={isCreating}
              >
                Create Employee
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Edit Employee Modal */}
        <Modal open={showEditModal} onOpenChange={setShowEditModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle>Edit Employee</ModalTitle>
              <ModalDescription>
                Update employee information for {selectedEmployee?.name}
              </ModalDescription>
            </ModalHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <Input
                  type="text"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                  placeholder="Enter employee name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <Input
                  type="email"
                  value={editFormData.email}
                  onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
                  placeholder="Enter email address"
                  required
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <Input
                  type="tel"
                  value={editFormData.phone}
                  onChange={(e) => setEditFormData({ ...editFormData, phone: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <Input
                  type="text"
                  value={editFormData.department}
                  onChange={(e) => setEditFormData({ ...editFormData, department: e.target.value })}
                  placeholder="Enter department"
                />
              </div>
            </div>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => {
                  setShowEditModal(false)
                  resetEditForm()
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="md"
                onClick={handleUpdateEmployee}
                disabled={isUpdating || !editFormData.name || !editFormData.email}
                isLoading={isUpdating}
              >
                Update Employee
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Status Update Modal */}
        <Modal open={showStatusModal} onOpenChange={setShowStatusModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle>Update Employee Status</ModalTitle>
              <ModalDescription>
                Change the status for {selectedEmployee?.name}
              </ModalDescription>
            </ModalHeader>
            <div className="space-y-4">
              <Select
                value={newStatus}
                onValueChange={setNewStatus}
                options={statusOptionsForUpdate}
                label="New Status"
              />
            </div>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => setShowStatusModal(false)}
                disabled={isUpdatingStatus}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="md"
                onClick={handleUpdateStatus}
                disabled={isUpdatingStatus}
                isLoading={isUpdatingStatus}
              >
                Update Status
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal open={showDeleteModal} onOpenChange={setShowDeleteModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle className="text-red-600">Delete Employee</ModalTitle>
              <ModalDescription>
                Are you sure you want to delete {selectedEmployee?.name}? This action cannot be undone.
              </ModalDescription>
            </ModalHeader>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => setShowDeleteModal(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                size="md"
                onClick={handleDeleteEmployee}
                disabled={isDeleting}
                isLoading={isDeleting}
              >
                Delete Employee
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* View Employee Modal */}
        <Modal open={showViewModal} onOpenChange={setShowViewModal}>
          <ModalContent className="sm:max-w-lg">
            <ModalHeader>
              <ModalTitle>Employee Details</ModalTitle>
              <ModalDescription>
                Complete information for {selectedEmployee?.name}
              </ModalDescription>
            </ModalHeader>
            {selectedEmployee && (
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-sehatti-gold-100 rounded-full flex items-center justify-center">
                    <span className="text-xl font-medium text-sehatti-gold-700">
                      {selectedEmployee.name?.charAt(0)?.toUpperCase() || 'E'}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{selectedEmployee.name}</h3>
                    <p className="text-gray-500">{selectedEmployee.email}</p>
                    <Badge className={statusColors[selectedEmployee.status as keyof typeof statusColors] || statusColors.PENDING}>
                      {selectedEmployee.status || 'PENDING'}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-sm">{selectedEmployee.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Department</label>
                    <p className="text-sm">{selectedEmployee.department || 'Not assigned'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Role</label>
                    <p className="text-sm">{selectedEmployee.isLeader ? 'Team Leader' : 'Employee'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Joined</label>
                    <p className="text-sm">
                      {formatTimestamp(selectedEmployee.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => setShowViewModal(false)}
              >
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Bulk Status Update Modal */}
        <Modal open={showBulkStatusModal} onOpenChange={setShowBulkStatusModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle>Bulk Status Update</ModalTitle>
              <ModalDescription>
                Change status for {selectedEmployees.length} selected employee(s)
              </ModalDescription>
            </ModalHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  New Status
                </label>
                <Select
                  value={bulkStatus}
                  onChange={(e) => setBulkStatus(e.target.value)}
                  className="w-full"
                >
                  <option value="">Select status...</option>
                  {statusOptionsForUpdate.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </div>
            </div>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => {
                  setShowBulkStatusModal(false)
                  setBulkStatus('')
                }}
                disabled={isUpdatingStatus}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="md"
                onClick={handleBulkStatusChange}
                disabled={isUpdatingStatus || !bulkStatus}
                isLoading={isUpdatingStatus}
              >
                Update Status
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Bulk Delete Confirmation Modal */}
        <Modal open={showBulkDeleteModal} onOpenChange={setShowBulkDeleteModal}>
          <ModalContent className="sm:max-w-md">
            <ModalHeader>
              <ModalTitle className="text-red-600">Delete Multiple Employees</ModalTitle>
              <ModalDescription>
                Are you sure you want to delete {selectedEmployees.length} selected employee(s)? This action cannot be undone.
              </ModalDescription>
            </ModalHeader>
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <FaExclamationTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-700">
                  <p className="font-medium">This will permanently delete:</p>
                  <ul className="mt-1 list-disc list-inside">
                    {selectedEmployees.slice(0, 3).map(id => {
                      const employee = employees.find(emp => emp.id === id)
                      return (
                        <li key={id}>{employee?.name || 'Unknown Employee'}</li>
                      )
                    })}
                    {selectedEmployees.length > 3 && (
                      <li>And {selectedEmployees.length - 3} more employee(s)</li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
            <ModalFooter className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => setShowBulkDeleteModal(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                size="md"
                onClick={handleBulkDelete}
                disabled={isDeleting}
                isLoading={isDeleting}
              >
                Delete {selectedEmployees.length} Employee(s)
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* CSV Import Modal */}
        <EmployeeCsvImport
          isOpen={showCsvImport}
          onClose={() => setShowCsvImport(false)}
          onSuccess={handleCsvImportSuccess}
        />
      </div>
    </AdminLayout>
  )
}

