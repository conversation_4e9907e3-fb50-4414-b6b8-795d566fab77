import { useSetPasswordFunc } from '@/hooks/useSetPasswordFunc';
import { 
  GlassCard, 
  GlassCardHeader, 
  GlassCardContent,
  Button,
  PasswordInput,
  GradientText,
  Alert
} from '@/components/ui';

// Shield User Icon Component (for set password)
const ShieldUserIcon = () => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9S13.1 11 12 11 10 10.1 10 9 10.9 7 12 7ZM12 17C10.33 17 8.94 16.16 8.21 14.9C8.21 13.62 10.67 13 12 13S15.79 13.62 15.79 14.9C15.06 16.16 13.67 17 12 17Z" fill="white"/>
  </svg>
);

// Security Icon Component
const SecurityIcon = () => (
  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// User Icon Component for role badge
const UserIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M16 7C16 9.2 14.2 11 12 11S8 9.2 8 7 9.8 3 12 3 16 4.8 16 7ZM12 14C16.42 14 20 15.79 20 18V21H4V18C4 15.79 7.58 14 12 14Z" stroke="#d2b37a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Success Icon Component
const SuccessIcon = () => (
  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
    <path d="M20 6L9 17l-5-5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Arrow Right Icon Component  
const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export default function SetPassword() {
  const {
    passwordData,
    tokenValid,
    showSuccess,
    role,
    isLoading,
    checkPasswordMatch,
    handleInputChange,
    handleSubmit,
    navigateToLogin,
  } = useSetPasswordFunc();

  // If no token or invalid token, show error
  if (tokenValid === false) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
        <GlassCard variant="default" decorativeTop={true} className="w-full max-w-[420px]">
          <GlassCardHeader>
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <GradientText gradient="goldRich" size="2xl" className="font-bold mb-2">
              Invalid Setup Link
            </GradientText>
            <p className="text-[15px] text-gray-500 font-normal">
              This account setup link is invalid or has expired
            </p>
          </GlassCardHeader>
          <GlassCardContent>
            <Button
              onClick={() => window.location.href = "/"}
              variant="default"
              fullWidth={true}
            >
              Go to Login
            </Button>
          </GlassCardContent>
        </GlassCard>
      </div>
    );
  }

  // Loading state while verifying token
  if (tokenValid === null) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
        <GlassCard variant="default" decorativeTop={true} className="w-full max-w-[420px]">
          <GlassCardHeader>
            <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
            <GradientText gradient="goldRich" size="2xl" className="font-bold mb-2">
              Verifying Invitation
            </GradientText>
            <p className="text-[15px] text-gray-500 font-normal">
              Please wait while we verify your invitation...
            </p>
          </GlassCardHeader>
        </GlassCard>
      </div>
    );
  }

  const isPasswordMismatch = passwordData.password && passwordData.confirmPassword && !checkPasswordMatch();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Set Password Card */}
      <GlassCard 
        variant="default" 
        decorativeTop={true}
        className="w-full max-w-[420px]"
      >
        <GlassCardHeader>
          <div className="w-16 h-16 bg-gradient-to-br from-[#d2b37a] to-[#c19648] rounded-full flex items-center justify-center mx-auto mb-6 shadow-[0_8px_20px_rgba(210,179,122,0.3)]">
            {showSuccess ? <SuccessIcon /> : <ShieldUserIcon />}
          </div>
          <GradientText 
            gradient="goldRich" 
            size="2xl" 
            className="font-bold mb-2"
          >
            {showSuccess ? "Account Activated!" : "Set Your Password"}
          </GradientText>
          <p className="text-[15px] text-gray-500 font-normal leading-relaxed max-w-80 mx-auto">
            {showSuccess 
              ? `Your ${role === 'consultant' ? 'consultant' : 'employee'} account has been successfully activated. You can now login with your credentials.`
              : `Create a secure password for your ${role === 'consultant' ? 'consultant' : 'employee'} account to get started`
            }
          </p>
        </GlassCardHeader>

        <GlassCardContent>
          {showSuccess && (
            <Alert 
              variant="default" 
              className="mb-6"
              title="Success"
              description="Your account has been activated successfully!"
            />
          )}

          {!showSuccess ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* New Password Field */}
              <PasswordInput
                label={
                  <>
                    New Password <span className="text-red-500">*</span>
                  </>
                }
                placeholder="Enter your new password"
                value={passwordData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                autoComplete="new-password"
              />

              {/* Confirm Password Field */}
              <PasswordInput
                label={
                  <>
                    Confirm New Password <span className="text-red-500">*</span>
                  </>
                }
                placeholder="Confirm your new password"
                value={passwordData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                autoComplete="new-password"
              />

              {/* Password Mismatch Error */}
              {isPasswordMismatch && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-xl flex items-center gap-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z" fill="#ef4444"/>
                  </svg>
                  <span className="text-[13px] text-red-500 font-medium">
                    Passwords do not match
                  </span>
                </div>
              )}

              {/* Password Requirements */}
              <div className="p-4 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
                <h4 className="text-sm font-semibold text-[#374151] mb-2">
                  Password Requirements:
                </h4>
                <ul className="text-xs text-[#6b7280] leading-relaxed space-y-1 list-disc list-inside">
                  <li>At least 8 characters long</li>
                  <li>Contains uppercase and lowercase letters</li>
                  <li>Contains at least one number</li>
                  <li>Contains at least one special character</li>
                  <li>Both passwords must match</li>
                </ul>
              </div>

              {/* Set Password Button */}
              <Button
                type="submit"
                variant="default"
                size="default"
                fullWidth={true}
                isLoading={isLoading}
                disabled={isPasswordMismatch || !passwordData.password || !passwordData.confirmPassword}
                rightIcon={!isLoading ? <ArrowRightIcon /> : undefined}
                className="mb-6"
              >
                {isLoading ? "Setting Password..." : "Save and Continue"}
              </Button>
            </form>
          ) : (
            <div className="space-y-6">
              {/* Success Actions */}
              <div className="space-y-3">
                <Button
                  onClick={navigateToLogin}
                  variant="default"
                  size="default"
                  fullWidth={true}
                  rightIcon={<ArrowRightIcon />}
                >
                  Go to Login
                </Button>
              </div>
            </div>
          )}
        </GlassCardContent>

        {/* Role Badge */}
        <div className="flex items-center justify-center gap-2 mt-6 p-3 bg-[rgba(210,179,122,0.05)] rounded-xl border border-[rgba(210,179,122,0.1)]">
          <UserIcon />
          <span className="text-xs text-[#6b7280] font-medium capitalize">
            {role === 'consultant' ? 'Consultant' : 'Employee'} Account Setup
          </span>
        </div>
      </GlassCard>

      {/* Privacy Disclaimer and Copyright */}
      <div className="mt-8 text-center max-w-[420px] px-5">
        <div className="p-5 bg-[rgba(210,179,122,0.03)] rounded-2xl border border-[rgba(210,179,122,0.1)] mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <SecurityIcon />
            <span className="text-sm font-semibold text-[#d2b37a]">
              Privacy & Security
            </span>
          </div>
          <p className="text-[13px] text-gray-500 leading-relaxed">
            Sehatti respects your privacy and protects your information with the highest standards of security and confidentiality
          </p>
        </div>
        
        <div className="p-4 bg-[rgba(0,0,0,0.02)] rounded-xl border border-[rgba(0,0,0,0.05)]">
          <p className="text-xs text-gray-400 font-medium">
            All rights reserved © Sehatti. An Elevate Academy ecosystem.
          </p>
        </div>
      </div>
    </div>
  );
} 