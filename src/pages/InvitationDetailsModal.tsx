import React from 'react'
import { Badge } from '../components/ui/Badge'
import { Separator } from '../components/ui/Separator'
import { 
  Calendar, 
  Clock, 
  Globe, 
  MapPin, 
  Users, 
  User,
  MessageSquare,
  Languages
} from 'lucide-react'
import { format } from 'date-fns'

import type { Invitation } from '@/store/api'

interface InvitationDetailsModalProps {
  invitation: Invitation
}

const InvitationDetailsModal: React.FC<InvitationDetailsModalProps> = ({ invitation }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getLanguageLabel = (language: string) => {
    switch (language) {
      case 'en':
        return 'English'
      case 'ar':
        return 'Arabic'
      case 'ur':
        return 'Urdu'
      default:
        return language
    }
  }

  return (
    <div className="space-y-6">
      {/* Header with Status */}
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{invitation.title}</h3>
          <p className="text-sm text-gray-500 mt-1">
            Created on {format(new Date(invitation.createdAt), 'PPP')}
          </p>
        </div>
        <Badge className={getStatusColor(invitation.status)}>
          {invitation.status}
        </Badge>
      </div>

      <Separator />

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Basic Information</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {invitation.type === 'online' ? (
                  <Globe className="h-4 w-4 text-blue-500" />
                ) : (
                  <MapPin className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm text-gray-600">Type:</span>
                <span className="text-sm font-medium capitalize">{invitation.type}</span>
              </div>

              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-purple-500" />
                <span className="text-sm text-gray-600">Session Type:</span>
                <span className="text-sm font-medium">{invitation.sessionType}</span>
              </div>

              <div className="flex items-center gap-2">
                <Languages className="h-4 w-4 text-orange-500" />
                <span className="text-sm text-gray-600">Language:</span>
                <span className="text-sm font-medium">{getLanguageLabel(invitation.language)}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Participants</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-indigo-500" />
                <span className="text-sm text-gray-600">Recipients:</span>
                <span className="text-sm font-medium">{invitation.recipients?.length || 0}</span>
              </div>
              
              {invitation.participants && invitation.participants.length > 0 && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-gray-600">Participants:</span>
                  <span className="text-sm font-medium">{invitation.participants.length}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Schedule</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-600">Date:</span>
                <span className="text-sm font-medium">
                  {format(new Date(invitation.time), 'PPP')}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-gray-600">Time:</span>
                <span className="text-sm font-medium">
                  {format(new Date(invitation.time), 'p')}
                </span>
              </div>
            </div>
          </div>

          {/* Location for physical sessions */}
          {invitation.type === 'physical' && (invitation.latitude || invitation.longitude) && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Location</h4>
              <div className="space-y-2">
                {invitation.latitude && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-600">Latitude:</span>
                    <span className="text-sm font-medium">{invitation.latitude}</span>
                  </div>
                )}
                {invitation.longitude && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-600">Longitude:</span>
                    <span className="text-sm font-medium">{invitation.longitude}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Consultant/Event Manager Info */}
          {(invitation.consultantId || invitation.eventManagerId) && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                {invitation.type === 'online' ? 'Consultant' : 'Event Manager'}
              </h4>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-purple-500" />
                <span className="text-sm text-gray-600">ID:</span>
                <span className="text-sm font-medium">
                  {invitation.consultantId || invitation.eventManagerId}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Description */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-700 whitespace-pre-wrap">
            {invitation.description}
          </p>
        </div>
      </div>

      {/* Recipient Base */}
      {invitation.recipientBase && invitation.recipientBase.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Recipient Base</h4>
          <div className="flex flex-wrap gap-2">
            {invitation.recipientBase.map((base, index) => (
              <Badge key={index} variant="outline">
                {base}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Google Maps for physical locations */}
      {invitation.type === 'physical' && invitation.latitude && invitation.longitude && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Location Map</h4>
          <div className="rounded-lg overflow-hidden border">
            <iframe
              src={`https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d29203.581321715606!2d${invitation.longitude}!3d${invitation.latitude}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sbd!4v1741131025595!5m2!1sen!2sbd`}
              width="100%"
              height="200"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            />
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="pt-4 border-t">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
          <div>
            <span className="font-medium">Created:</span> {format(new Date(invitation.createdAt), 'PPp')}
          </div>
          <div>
            <span className="font-medium">Updated:</span> {format(new Date(invitation.updatedAt), 'PPp')}
          </div>
        </div>
      </div>
    </div>
  )
}

export default InvitationDetailsModal
