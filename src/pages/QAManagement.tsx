import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import * as XLSX from 'xlsx';
import { 
  FaSitemap, 
  FaBullseye, 
  FaQuestionCircle,
  FaPlus,
  FaSearch,
  FaFilter,
  FaSync,
  FaEye,
  FaEdit,
  FaTrash,
  FaLanguage,
  FaCog,
  FaSpinner,
  FaInfoCircle,
  FaGlobe,
  FaChartBar,
  FaExclamationTriangle,
  FaExclamationCircle,
  FaCheckCircle,
  FaUpload,
  FaDownload,
  FaFileExcel,
  FaTimes
} from "react-icons/fa";
import { 
  Card,
  Button,
  Select,
  Badge,
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  GradientText,
  PageLoader,
  Container,
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Tabs,
  TabList,
  <PERSON><PERSON>,
  <PERSON>bPanels,
  TabPanel,
  <PERSON>ert,
  SearchInput,
  StatCard,
  FilterSelect,
  ActionButton,
  EmptyState,
  FileUpload,
  CorporateSelector
} from "../components/ui";


import { AdminLayout } from "../components/layout/AdminLayout";
import { useAppSelector } from "../store/store";
import { selectAuth } from "../features/auth/auth-slice";

// QA Service API imports - Updated for actual backend structure
import {
  useGetDivisionsQuery,
  useGetTargetsQuery,
  useGetQuestionsQuery,
  useCreateDivisionMutation,
  useUpdateDivisionMutation,
  useDeleteDivisionMutation,
  useCreateTargetMutation,
  useUpdateTargetMutation,
  useDeleteTargetMutation,
  useCreateQuestionMutation,
  useUpdateQuestionMutation,
  useDeleteQuestionMutation,
  useFixMissingTranslationsMutation,
} from "../features/API/qaServiceApi";

// Corporate API import
import { useGetCompaniesQuery } from "@/store/api";

// Form components
import {
  QADivisionForm,
  QATargetForm,
  QAQuestionForm
} from "../components/qa";

// Types
import type { 
  EntityType, 
  Division, 
  Target, 
  Question,
  QuestionTypeValue,
  QuestionStatusValue,
  QAStats,
  CreateDivisionRequest,
  UpdateDivisionRequest,
  CreateTargetRequest,
  UpdateTargetRequest,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionType
} from "../types/qa";

const QAManagement: React.FC = () => {
  // Navigation and auth
  const navigate = useNavigate();
  const authState = useAppSelector(selectAuth);
  const user = authState?.user;
  const isAuthLoading = authState?.loading;

  // State management
  const [activeTab, setActiveTab] = useState<EntityType>('divisions');
  const [questionTypeFilter, setQuestionTypeFilter] = useState<QuestionTypeValue | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDivisionId, setSelectedDivisionId] = useState("");
  const [selectedTargetId, setSelectedTargetId] = useState("");
  
  // Form submit ref
  const formSubmitRef = React.useRef<(() => void) | null>(null);
  
  // Modal states
  const [modals, setModals] = useState({
    create: false,
    edit: false,
    view: false,
    delete: false,
    bulkUpload: false,
  });
  const [selectedEntity, setSelectedEntity] = useState<any>(null);
  
  // Bulk upload state
  const [isBulkUploading, setIsBulkUploading] = useState(false);

  // API queries
  const { data: divisions = [], isLoading: divisionsLoading, refetch: refetchDivisions } = useGetDivisionsQuery({
    search: activeTab === 'divisions' ? searchTerm : undefined
  });

  const { data: targets = [], isLoading: targetsLoading, refetch: refetchTargets } = useGetTargetsQuery({
    division_id: selectedDivisionId || undefined,
    search: activeTab === 'targets' ? searchTerm : undefined
  });

  const { data: questions = [], isLoading: questionsLoading, refetch: refetchQuestions } = useGetQuestionsQuery({
    division_id: selectedDivisionId || undefined,
    subdivision_id: selectedTargetId || undefined,
    question_type: questionTypeFilter !== 'all' ? questionTypeFilter : undefined,
    search: activeTab === 'questions' ? searchTerm : undefined
  });

  // Corporate data for displaying names instead of IDs
  const { data: corporatesData, isLoading: corporatesLoading, error: corporatesError } = useGetCompaniesQuery();

  // Mutations
  const [createDivision, { isLoading: isCreatingDivision }] = useCreateDivisionMutation();
  const [updateDivision, { isLoading: isUpdatingDivision }] = useUpdateDivisionMutation();
  const [deleteDivision, { isLoading: isDeletingDivision }] = useDeleteDivisionMutation();
  const [createTarget, { isLoading: isCreatingTarget }] = useCreateTargetMutation();
  const [updateTarget, { isLoading: isUpdatingTarget }] = useUpdateTargetMutation();
  const [deleteTarget, { isLoading: isDeletingTarget }] = useDeleteTargetMutation();
  const [createQuestion, { isLoading: isCreatingQuestion }] = useCreateQuestionMutation();
  const [updateQuestion, { isLoading: isUpdatingQuestion }] = useUpdateQuestionMutation();
  const [deleteQuestion, { isLoading: isDeletingQuestion }] = useDeleteQuestionMutation();
  const [fixTranslations, { isLoading: isFixingTranslations }] = useFixMissingTranslationsMutation();

  // Calculate stats
  const stats: QAStats = useMemo(() => {
    const activeDivisions = divisions.filter(d => d.is_active);
    const activeTargets = targets.filter(t => t.is_active);
    const activeQuestions = questions.filter(q => q.status === 'active');
    
    const questionsByType = {
      pre_assessment: questions.filter(q => q.question_type === 'pre_assessment').length,
      post_assessment: questions.filter(q => q.question_type === 'post_assessment').length,
      ongoing: questions.filter(q => q.question_type === 'ongoing').length,
    };

    return {
      totalDivisions: divisions.length,
      totalTargets: targets.length,
      totalQuestions: questions.length,
      activeDivisions: activeDivisions.length,
      activeTargets: activeTargets.length,
      activeQuestions: activeQuestions.length,
      questionsByType,
    };
  }, [divisions, targets, questions]);

  // Get current data based on active tab
  const getCurrentData = () => {
    switch (activeTab) {
      case 'divisions': return divisions;
      case 'targets': return targets;
      case 'questions': return questions;
      default: return [];
    }
  };

  const getCurrentLoading = () => {
    switch (activeTab) {
      case 'divisions': return divisionsLoading;
      case 'targets': return targetsLoading;
      case 'questions': return questionsLoading;
      default: return false;
    }
  };

  // Modal handlers
  const openModal = (type: keyof typeof modals, entity?: any) => {
    setSelectedEntity(entity || null);
    setModals(prev => ({ ...prev, [type]: true }));
  };

  const closeModal = (type: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [type]: false }));
    setSelectedEntity(null);
  };

  const closeAllModals = () => {
    setModals({
      create: false,
      edit: false,
      view: false,
      delete: false,
      bulkUpload: false,
    });
    setSelectedEntity(null);
  };

  // CRUD handlers
  const handleCreate = async (data: any) => {
    try {
      let result;
      switch (activeTab) {
        case 'divisions':
          result = await createDivision(data).unwrap();
          break;
        case 'targets':
          result = await createTarget(data).unwrap();
          break;
        case 'questions':
          result = await createQuestion(data).unwrap();
          break;
        default:
          return;
      }
      toast.success(`${activeTab.slice(0, -1)} created successfully!`);
      closeAllModals();
      refetchData();
    } catch (error: any) {
      toast.error(error?.data?.detail || `Failed to create ${activeTab.slice(0, -1)}`);
    }
  };

  const handleUpdate = async (id: string, data: any) => {
    try {
      let result;
      switch (activeTab) {
        case 'divisions':
          result = await updateDivision({ id, data }).unwrap();
          break;
        case 'targets':
          result = await updateTarget({ id, data }).unwrap();
          break;
        case 'questions':
          result = await updateQuestion({ id, data }).unwrap();
          break;
        default:
          return;
      }
      toast.success(`${activeTab.slice(0, -1)} updated successfully!`);
      closeAllModals();
      refetchData();
    } catch (error: any) {
      toast.error(error?.data?.detail || `Failed to update ${activeTab.slice(0, -1)}`);
    }
  };

  const handleDelete = async (id: string, hardDelete = false) => {
    try {
      let result;
      switch (activeTab) {
        case 'divisions':
          result = await deleteDivision({ id, hard_delete: hardDelete }).unwrap();
          break;
        case 'targets':
          result = await deleteTarget({ id, hard_delete: hardDelete }).unwrap();
          break;
        case 'questions':
          result = await deleteQuestion({ id, hard_delete: hardDelete }).unwrap();
          break;
        default:
          return;
      }
      toast.success(`${activeTab.slice(0, -1)} ${hardDelete ? 'deleted' : 'deactivated'} successfully!`);
      closeAllModals();
      refetchData();
    } catch (error: any) {
      toast.error(error?.data?.detail || `Failed to delete ${activeTab.slice(0, -1)}`);
    }
  };

  const refetchData = () => {
    refetchDivisions();
    refetchTargets();
    refetchQuestions();
  };

  // Bulk upload handlers
  const handleBulkUpload = async (file: File, corporateIds: string[]) => {
    setIsBulkUploading(true);
    try {
      const data = await parseExcelFile(file);
      const results = await processBulkQuestions(data, corporateIds);
      
      if (results.success > 0 && results.errors === 0 && results.validationWarnings === 0) {
        toast.success(`Successfully uploaded ${results.success} questions!`);
      } else if (results.success > 0 && results.errors === 0 && results.validationWarnings > 0) {
        toast.success(`Uploaded ${results.success} questions with ${results.validationWarnings} validation warnings. Check console for details.`);
      } else if (results.success > 0 && results.errors > 0) {
        toast.success(`Uploaded ${results.success} questions with ${results.errors} errors and ${results.validationWarnings} warnings. Check console for details.`);
      } else {
        toast.error(`Upload failed: ${results.errors} errors occurred. Check console for details.`);
      }
      closeAllModals();
      refetchData();
    } catch (error) {
      toast.error('Failed to process bulk upload');
      console.error('Bulk upload error:', error);
    } finally {
      setIsBulkUploading(false);
    }
  };

  // Parse Excel file
  const parseExcelFile = async (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  // Process bulk questions
  const processBulkQuestions = async (data: any[], corporateIds: string[]) => {
    let success = 0;
    let errors = 0;
    let validationWarnings = 0;
    
    console.log(`Starting bulk upload processing for ${data.length - 1} rows...`);
    
    // Skip header row
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length < 3) continue; // Minimum: division, subdivision, question
      
      try {
        const [divisionName, subdivisionName, questionText, questionType, status, ...optionData] = row;
        
        if (!divisionName || !subdivisionName || !questionText) {
          console.warn(`Skipping row ${i}: Missing required fields (division, subdivision, or question)`);
          continue;
        }
        
        // Find division
        let division = divisions.find(d => 
          d.name?.en?.toLowerCase() === divisionName.toString().toLowerCase().trim()
        );
        if (!division) {
          console.warn(`Row ${i}: Division "${divisionName}" not found. Please create it first.`);
          errors++;
          continue;
        }
        
        // Find subdivision (target)
        let subdivision = targets.find(t => 
          t.name?.en?.toLowerCase() === subdivisionName.toString().toLowerCase().trim() &&
          t.division_id === division.id
        );
        if (!subdivision) {
          console.warn(`Row ${i}: Subdivision "${subdivisionName}" not found in division "${divisionName}". Please create it first.`);
          errors++;
          continue;
        }
        
        // Validate and set question type (default to 'ongoing' if not provided or invalid)
        let validQuestionType: 'pre_assessment' | 'post_assessment' | 'ongoing' = 'ongoing';
        if (questionType) {
          const typeStr = questionType.toString().toLowerCase().trim();
          if (typeStr === 'pre_assessment' || typeStr === 'post_assessment' || typeStr === 'ongoing') {
            validQuestionType = typeStr as 'pre_assessment' | 'post_assessment' | 'ongoing';
          } else {
            console.warn(`Row ${i}: Invalid question type "${questionType}", defaulting to "ongoing"`);
            validationWarnings++;
          }
        }
        
        // Validate and set status (default to 'active' if not provided or invalid)
        let validStatus: 'active' | 'draft' | 'inactive' = 'active';
        if (status) {
          const statusStr = status.toString().toLowerCase().trim();
          if (statusStr === 'active' || statusStr === 'draft' || statusStr === 'inactive') {
            validStatus = statusStr as 'active' | 'draft' | 'inactive';
          } else {
            console.warn(`Row ${i}: Invalid status "${status}", defaulting to "active"`);
            validationWarnings++;
          }
        }
        
        // Parse and validate options from Excel columns
        const options = [];
        const optionValues = new Set();
        const optionTexts = new Set();
        let hasValidationErrors = false;
        
        for (let j = 0; j < optionData.length; j += 2) {
          const optionText = optionData[j];
          const optionValue = optionData[j + 1];
          
          // Skip if both text and value are empty
          if ((!optionText || optionText.toString().trim() === '') && 
              (optionValue === undefined || optionValue === null || optionValue === '')) {
            continue;
          }
          
          // Validate option text
          if (!optionText || optionText.toString().trim() === '') {
            console.error(`Row ${i}: Option ${Math.floor(j/2) + 1} has empty text but has value "${optionValue}"`);
            hasValidationErrors = true;
            continue;
          }
          
          // Validate option value
          if (optionValue === undefined || optionValue === null || optionValue === '') {
            console.error(`Row ${i}: Option ${Math.floor(j/2) + 1} "${optionText}" has empty value`);
            hasValidationErrors = true;
            continue;
          }
          
          const numericValue = parseInt(optionValue.toString());
          if (isNaN(numericValue)) {
            console.error(`Row ${i}: Option ${Math.floor(j/2) + 1} "${optionText}" has invalid numeric value "${optionValue}"`);
            hasValidationErrors = true;
            continue;
          }
          
          const trimmedText = optionText.toString().trim();
          
          // Check for duplicate option texts
          if (optionTexts.has(trimmedText.toLowerCase())) {
            console.error(`Row ${i}: Duplicate option text "${trimmedText}"`);
            hasValidationErrors = true;
            continue;
          }
          
          // Check for duplicate option values
          if (optionValues.has(numericValue)) {
            console.error(`Row ${i}: Duplicate option value ${numericValue} for text "${trimmedText}"`);
            hasValidationErrors = true;
            continue;
          }
          
          // Add to tracking sets
          optionTexts.add(trimmedText.toLowerCase());
          optionValues.add(numericValue);
          
          // Add valid option
          options.push({
            text: trimmedText,
            value: numericValue
          });
        }
        
        // Validation: Check minimum options requirement
        if (options.length === 0) {
          if (hasValidationErrors) {
            console.error(`Row ${i}: No valid options after validation errors, using default Likert scale`);
            validationWarnings++;
          } else {
            console.warn(`Row ${i}: No options provided, using default Likert scale`);
            validationWarnings++;
          }
          options.push(
            { text: 'Strongly Disagree', value: 1 },
            { text: 'Disagree', value: 2 },
            { text: 'Neutral', value: 3 },
            { text: 'Agree', value: 4 },
            { text: 'Strongly Agree', value: 5 }
          );
        } else if (options.length === 1) {
          console.error(`Row ${i}: Only 1 option provided, questions need at least 2 options. Adding default second option.`);
          options.push({ text: 'Other', value: options[0].value + 1 });
          validationWarnings++;
        }
        
        // Sort options by value for consistency
        options.sort((a, b) => a.value - b.value);
        
        console.log(`Row ${i}: Parsed ${options.length} valid options:`, options.map(o => `"${o.text}": ${o.value}`).join(', '));
        
        const questionData: CreateQuestionRequest = {
          division_id: division.id,
          subdivision_id: subdivision.id,
          question_type: validQuestionType,
          question_text: questionText.toString().trim(),
          options: options,
          corporate_ids: corporateIds,
          status: validStatus
        };
        
        console.log(`Processing row ${i}:`, questionData);
        const result = await createQuestion(questionData).unwrap();
        console.log(`Successfully created question for row ${i}:`, result.id);
        success++;
      } catch (error: any) {
        console.error(`Error processing row ${i}:`, error);
        if (error?.data?.detail) {
          console.error(`Backend error detail:`, error.data.detail);
        }
        errors++;
      }
    }
    
    console.log(`Bulk upload completed: ${success} successful, ${errors} errors, ${validationWarnings} validation warnings`);
    return { success, errors, validationWarnings };
  };

  const handleFixTranslations = async () => {
    try {
      await fixTranslations().unwrap();
      toast.success('Translation fixes applied successfully!');
      refetchQuestions();
    } catch (error: any) {
      toast.error(error?.data?.detail || 'Failed to fix translations');
    }
  };

  // Helper functions
  const getTranslatedText = (content: any) => {
    if (typeof content === 'string') return content;
    return content?.en || content?.ar || content?.hi || 'N/A';
  };

  // Get corporate name from ID
  const getCorporateName = (corporateId: string) => {
    if (corporatesLoading) {
      return `Loading...`;
    }
    
    if (corporatesError) {
      return `Unknown Corporate`;
    }
    
    if (!corporatesData) {
      return `Unknown Corporate`;
    }
    
    // Handle different data structures from the API
    let corporates: any[] = [];
    
    // Use the correct V2 API structure
    if (corporatesData?.items && Array.isArray(corporatesData.items)) {
      corporates = corporatesData.items;
    }
    
    if (corporates.length === 0) {
      return `Unknown Corporate`;
    }
    
    // Try to find the corporate with different possible ID fields
    const corporate = corporates.find((corp: any) => 
      corp.company_id === corporateId ||
      corp.id === corporateId ||
      String(corp.company_id) === String(corporateId) ||
      String(corp.id) === String(corporateId)
    );
    
    if (corporate) {
      // Try different possible name fields
      return corporate.name || 
             corporate.company_name || 
             corporate.companyName || 
             corporate.title || 
             `Unknown Corporate`;
    }
    
    // Corporate not found - likely deleted or data inconsistency
    return `Deleted Corporate`;
  };

  const getStatusBadge = (entity: any) => {
    const isActive = entity?.is_active !== undefined ? entity.is_active : entity?.status === 'active';
    return (
      <Badge 
        variant={isActive ? 'success' : 'destructive'}
        size="sm"
      >
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  const getQuestionTypeBadge = (type: QuestionTypeValue) => {
    const typeConfig = {
      pre_assessment: { 
        label: 'Pre', 
        fullLabel: 'Pre Assessment',
        className: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800/30' 
      },
      post_assessment: { 
        label: 'Post', 
        fullLabel: 'Post Assessment',
        className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800/30' 
      },
      ongoing: { 
        label: 'Ongoing', 
        fullLabel: 'Ongoing',
        className: 'bg-sehatti-gold-50 text-sehatti-gold-700 border-sehatti-gold-200 dark:bg-sehatti-gold-900/20 dark:text-sehatti-gold-300 dark:border-sehatti-gold-800/30' 
      },
    };
    
    const config = typeConfig[type];
    return (
      <Badge 
        variant="outline" 
        size="sm"
        className={`${config.className} font-semibold px-2.5 py-1 text-xs whitespace-nowrap`}
        title={config.fullLabel}
      >
        {config.label}
      </Badge>
    );
  };

  // Loading state
  if (isAuthLoading) {
    return <PageLoader />;
  }

  // Auth check
  if (!user) {
    navigate('/auth/login');
    return null;
  }

  // Logout handler
  const handleLogout = () => {
    localStorage.removeItem("hr-auth-token");
    window.location.href = "/login";
  };

  return (
    <AdminLayout
      user={user as any}
      onLogout={handleLogout}
      variant="glass"
      showNotifications={true}
      notificationCount={0}
    >
      <div className="min-h-screen bg-gradient-to-br from-sehatti-warm-gray-50 to-white dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900">
        <div className="space-y-4 sm:space-y-6 lg:space-y-8 pb-6 sm:pb-8">
        {/* Header - Mobile First */}
        <div className="space-y-4 sm:space-y-6">
          <div className="space-y-3 sm:space-y-4">
            <div className="text-center sm:text-left">
              <GradientText 
                as="h1" 
                gradient="gold" 
                size="2xl" 
                weight="bold"
                className="mb-2 sm:text-3xl"
              >
                QA Management
              </GradientText>
              <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 px-4 sm:px-0">
                Manage divisions, targets, and assessment questions
              </p>
            </div>
            
            {/* Mobile-First Action Buttons - Using Pre-styled Components */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 px-4 sm:px-0 sm:justify-end">
              <ActionButton
                intent="create"
                buttonSize="lg"
                icon={<FaPlus />}
                onClick={() => openModal('create')}
                fullWidth={true}
                className="sm:w-auto"
              >
                Create {activeTab.slice(0, -1)}
              </ActionButton>
              
              {activeTab === 'questions' && (
                <ActionButton
                  intent="upload"
                  buttonSize="lg"
                  icon={<FaUpload />}
                  onClick={() => openModal('bulkUpload')}
                  fullWidth={true}
                  className="sm:w-auto"
                >
                  Bulk Upload
                </ActionButton>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards - Using Pre-styled Components */}
        <div className="px-4 sm:px-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
            <StatCard
              title="Total Divisions"
              value={stats.totalDivisions}
              subtitle={`${stats.activeDivisions} active`}
              icon={<FaSitemap />}
            />

            <StatCard
              title="Total Targets"
              value={stats.totalTargets}
              subtitle={`${stats.activeTargets} active`}
              icon={<FaBullseye />}
            />

            <StatCard
              title="Total Questions"
              value={stats.totalQuestions}
              subtitle={`${stats.activeQuestions} active`}
              icon={<FaQuestionCircle />}
            />

            <StatCard
              title="Question Types"
              value={`Pre: ${stats.questionsByType.pre_assessment} | Post: ${stats.questionsByType.post_assessment}`}
              subtitle={`Ongoing: ${stats.questionsByType.ongoing}`}
              icon={<FaCog />}
            />
          </div>
        </div>

        {/* Main Content - Mobile First */}
        <Card className="overflow-hidden shadow-lg border-sehatti-warm-gray-200/60 dark:border-sehatti-warm-gray-700/60 mx-4 sm:mx-0">
          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as EntityType)}>
            <div className="p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-sehatti-gold-50/30 to-white dark:from-sehatti-gold-900/10 dark:to-sehatti-warm-gray-950 border-b border-sehatti-warm-gray-200/60 dark:border-sehatti-warm-gray-700/60">
              <TabList className="mb-4 sm:mb-6 bg-white/50 dark:bg-sehatti-warm-gray-900/50 rounded-xl p-1 backdrop-blur-sm overflow-x-auto">
                <Tab value="divisions" className="flex items-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-all duration-200 min-w-0 flex-shrink-0">
                  <FaSitemap className="w-3.5 h-3.5 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="font-medium text-sm sm:text-base whitespace-nowrap">Divisions</span>
                </Tab>
                <Tab value="targets" className="flex items-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-all duration-200 min-w-0 flex-shrink-0">
                  <FaBullseye className="w-3.5 h-3.5 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="font-medium text-sm sm:text-base whitespace-nowrap">Targets</span>
                </Tab>
                <Tab value="questions" className="flex items-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-all duration-200 min-w-0 flex-shrink-0">
                  <FaQuestionCircle className="w-3.5 h-3.5 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="font-medium text-sm sm:text-base whitespace-nowrap">Questions</span>
                </Tab>
              </TabList>

              {/* Search and Filters - Mobile First */}
              <div className="space-y-3 sm:space-y-4">
                {/* Search Input - Using Pre-styled Component */}
                <div>
                  <SearchInput
                    placeholder={`🔍 Search ${activeTab}...`}
                    value={searchTerm}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                    onClear={() => setSearchTerm("")}
                    variant="gold"
                    size="md"
                  />
                </div>
                
                {/* Filter Controls - Mobile/Desktop Optimized */}
                {(activeTab === 'targets' || activeTab === 'questions') && (
                  <>
                    {/* Mobile Filters - Stack Vertically */}
                    <div className="space-y-3 sm:hidden">
                      {/* Division Filter - Mobile */}
                      <div className="w-full">
                        <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                          Filter by Division
                        </label>
                        <div className="relative">
                          <select
                            value={selectedDivisionId}
                            onChange={(e) => setSelectedDivisionId(e.target.value)}
                            className="w-full bg-white dark:bg-sehatti-warm-gray-800 border border-sehatti-gold-200 dark:border-sehatti-gold-700 px-4 py-3 text-base rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500/50 focus:border-sehatti-gold-500 appearance-none pr-10"
                            style={{ 
                              WebkitAppearance: 'none',
                              MozAppearance: 'none',
                              backgroundImage: 'none',
                            }}
                          >
                            <option value="">All Divisions</option>
                            {divisions.map((division) => (
                              <option key={division.id} value={division.id}>
                                {getTranslatedText(division.name)}
                              </option>
                            ))}
                          </select>
                          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg className="h-5 w-5 text-sehatti-warm-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                      
                      {/* Question Filters - Mobile */}
                      {activeTab === 'questions' && (
                        <>
                          {/* Target Filter - Mobile */}
                          <div className="w-full">
                            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                              Filter by Target
                            </label>
                            <div className="relative">
                              <select
                                value={selectedTargetId}
                                onChange={(e) => setSelectedTargetId(e.target.value)}
                                disabled={!selectedDivisionId}
                                className="w-full bg-white dark:bg-sehatti-warm-gray-800 border border-sehatti-gold-200 dark:border-sehatti-gold-700 px-4 py-3 text-base rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500/50 focus:border-sehatti-gold-500 appearance-none pr-10 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 dark:disabled:bg-sehatti-warm-gray-700"
                                style={{ 
                                  WebkitAppearance: 'none',
                                  MozAppearance: 'none',
                                  backgroundImage: 'none',
                                }}
                              >
                                <option value="">All Targets</option>
                                {targets.map((target) => (
                                  <option key={target.id} value={target.id}>
                                    {getTranslatedText(target.name)}
                                  </option>
                                ))}
                              </select>
                              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg className="h-5 w-5 text-sehatti-warm-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                              </div>
                            </div>
                          </div>
                          
                          {/* Question Type Filter - Mobile */}
                          <div className="w-full">
                            <label className="block text-sm font-medium text-sehatti-warm-gray-700 dark:text-sehatti-warm-gray-300 mb-2">
                              Filter by Type
                            </label>
                            <div className="relative">
                              <select
                                value={questionTypeFilter}
                                onChange={(e) => setQuestionTypeFilter(e.target.value as QuestionTypeValue | 'all')}
                                className="w-full bg-white dark:bg-sehatti-warm-gray-800 border border-sehatti-gold-200 dark:border-sehatti-gold-700 px-4 py-3 text-base rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-sehatti-gold-500/50 focus:border-sehatti-gold-500 appearance-none pr-10"
                                style={{ 
                                  WebkitAppearance: 'none',
                                  MozAppearance: 'none',
                                  backgroundImage: 'none',
                                }}
                              >
                                <option value="all">All Types</option>
                                <option value="pre_assessment">Pre Assessment</option>
                                <option value="post_assessment">Post Assessment</option>
                                <option value="ongoing">Ongoing</option>
                              </select>
                              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg className="h-5 w-5 text-sehatti-warm-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>

                    {/* Desktop Filters - Horizontal Layout */}
                    <div className="hidden sm:flex gap-3">
                      {/* Division Filter - Desktop */}
                      <div className="flex-1">
                        <Select
                          value={selectedDivisionId}
                          onChange={(e) => setSelectedDivisionId(e.target.value)}
                          variant="gold"
                          selectSize="md"
                          className="w-full bg-white/80 dark:bg-sehatti-warm-gray-800/80 backdrop-blur-sm border-sehatti-gold-200/60 dark:border-sehatti-gold-700/60"
                        >
                          <option value="">All Divisions</option>
                          {divisions.map((division) => (
                            <option key={division.id} value={division.id}>
                              {getTranslatedText(division.name)}
                            </option>
                          ))}
                        </Select>
                      </div>
                      
                      {/* Question Filters - Desktop */}
                      {activeTab === 'questions' && (
                        <>
                          <div className="flex-1">
                            <Select
                              value={selectedTargetId}
                              onChange={(e) => setSelectedTargetId(e.target.value)}
                              disabled={!selectedDivisionId}
                              variant="gold"
                              selectSize="md"
                              className="w-full bg-white/80 dark:bg-sehatti-warm-gray-800/80 backdrop-blur-sm border-sehatti-gold-200/60 dark:border-sehatti-gold-700/60"
                            >
                              <option value="">All Targets</option>
                              {targets.map((target) => (
                                <option key={target.id} value={target.id}>
                                  {getTranslatedText(target.name)}
                                </option>
                              ))}
                            </Select>
                          </div>
                          
                          <div className="flex-1">
                            <Select
                              value={questionTypeFilter}
                              onChange={(e) => setQuestionTypeFilter(e.target.value as QuestionTypeValue | 'all')}
                              variant="gold"
                              selectSize="md"
                              className="w-full bg-white/80 dark:bg-sehatti-warm-gray-800/80 backdrop-blur-sm border-sehatti-gold-200/60 dark:border-sehatti-gold-700/60"
                            >
                              <option value="all">All Types</option>
                              <option value="pre_assessment">Pre Assessment</option>
                              <option value="post_assessment">Post Assessment</option>
                              <option value="ongoing">Ongoing</option>
                            </Select>
                          </div>
                        </>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
            
            {/* Tab Panels */}
            <TabPanels>
              {/* Divisions Tab - Mobile First */}
              <TabPanel value="divisions">
                {divisionsLoading ? (
                  <div className="flex justify-center py-6 sm:py-8">
                    <PageLoader />
                  </div>
                ) : (
                  <div className="overflow-x-auto -mx-3 sm:mx-0">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[200px] sm:w-[30%] text-xs sm:text-sm">Name</TableHead>
                          <TableHead className="min-w-[250px] sm:w-[40%] text-xs sm:text-sm hidden sm:table-cell">Description</TableHead>
                          <TableHead className="min-w-[80px] sm:w-[10%] text-xs sm:text-sm">Status</TableHead>
                          <TableHead className="min-w-[100px] sm:w-[15%] text-xs sm:text-sm hidden md:table-cell">Created</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[5%] text-xs sm:text-sm text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {divisions.map((division) => (
                          <TableRow key={division.id}>
                            <TableCell className="font-medium">
                              <div>
                                <p className="font-semibold text-sm sm:text-base">{getTranslatedText(division.name)}</p>
                                <p className="text-xs text-sehatti-warm-gray-500">ID: {division.id}</p>
                                {/* Show description on mobile */}
                                <p className="text-xs text-sehatti-warm-gray-600 mt-1 sm:hidden">
                                  {division.description ? getTranslatedText(division.description) : 'No description'}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell className="hidden sm:table-cell">
                              <p className="text-sm">
                                {division.description ? getTranslatedText(division.description) : 'No description'}
                              </p>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(division)}
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              <p className="text-sm">
                                {new Date(division.created_at).toLocaleDateString()}
                              </p>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-1 sm:gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openModal('view', division)}
                                  className="text-sehatti-gold-600 hover:text-sehatti-gold-700 p-2 sm:p-2.5"
                                >
                                  <FaEye className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openModal('edit', division)}
                                  className="text-blue-600 hover:text-blue-700 p-2 sm:p-2.5"
                                >
                                  <FaEdit className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openModal('delete', division)}
                                  className="text-red-600 hover:text-red-700 p-2 sm:p-2.5"
                                >
                                  <FaTrash className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
                
                {!divisionsLoading && divisions.length === 0 && (
                  <EmptyState
                    icon={<FaSitemap />}
                    title="No divisions found"
                    description="Get started by creating your first division to organize your QA content."
                    action={{
                      label: "Create First Division",
                      onClick: () => openModal('create')
                    }}
                  />
                )}
              </TabPanel>

              {/* Targets Tab - Mobile First */}
              <TabPanel value="targets">
                {targetsLoading ? (
                  <div className="flex justify-center py-6 sm:py-8">
                    <PageLoader />
                  </div>
                ) : (
                  <div className="overflow-x-auto -mx-3 sm:mx-0">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[200px] sm:w-[25%] text-xs sm:text-sm">Name</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[20%] text-xs sm:text-sm">Division</TableHead>
                          <TableHead className="min-w-[200px] sm:w-[30%] text-xs sm:text-sm hidden sm:table-cell">Description</TableHead>
                          <TableHead className="min-w-[80px] sm:w-[10%] text-xs sm:text-sm">Status</TableHead>
                          <TableHead className="min-w-[100px] sm:w-[10%] text-xs sm:text-sm hidden md:table-cell">Created</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[5%] text-xs sm:text-sm text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {targets.map((target) => {
                          const division = divisions.find(d => d.id === target.division_id);
                          return (
                            <TableRow key={target.id}>
                              <TableCell className="font-medium">
                                <div>
                                  <p className="font-semibold text-sm sm:text-base">{getTranslatedText(target.name)}</p>
                                  <p className="text-xs text-sehatti-warm-gray-500">ID: {target.id}</p>
                                  {/* Show description on mobile */}
                                  <p className="text-xs text-sehatti-warm-gray-600 mt-1 sm:hidden">
                                    {target.description ? getTranslatedText(target.description) : 'No description'}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge 
                                  variant="outline" 
                                  size="sm"
                                  className="w-full justify-center font-medium text-xs px-2 py-1 whitespace-nowrap overflow-hidden text-ellipsis"
                                  title={division ? getTranslatedText(division.name) : 'Unknown Division'}
                                >
                                  {division ? getTranslatedText(division.name) : 'Unknown'}
                                </Badge>
                              </TableCell>
                              <TableCell className="hidden sm:table-cell">
                                <p className="text-sm">
                                  {target.description ? getTranslatedText(target.description) : 'No description'}
                                </p>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(target)}
                              </TableCell>
                              <TableCell className="hidden md:table-cell">
                                <p className="text-sm">
                                  {new Date(target.created_at).toLocaleDateString()}
                                </p>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-1 sm:gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('view', target)}
                                    className="text-sehatti-gold-600 hover:text-sehatti-gold-700 p-2 sm:p-2.5"
                                  >
                                    <FaEye className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('edit', target)}
                                    className="text-blue-600 hover:text-blue-700 p-2 sm:p-2.5"
                                  >
                                    <FaEdit className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('delete', target)}
                                    className="text-red-600 hover:text-red-700 p-2 sm:p-2.5"
                                  >
                                    <FaTrash className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                )}
                
                {!targetsLoading && targets.length === 0 && (
                  <div className="text-center py-8 sm:py-12 px-4">
                    <FaBullseye className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-warm-gray-400 mx-auto mb-4" />
                    <p className="text-sehatti-warm-gray-600 text-sm sm:text-base mb-4">
                      {selectedDivisionId ? 'No targets found for selected division' : 'No targets found'}
                    </p>
                    <Button 
                      onClick={() => openModal('create')}
                      className="w-full sm:w-auto"
                      disabled={!divisions.length}
                      size="lg"
                    >
                      Create First Target
                    </Button>
                  </div>
                )}
              </TabPanel>

              {/* Questions Tab - Mobile First */}
              <TabPanel value="questions">
                {questionsLoading ? (
                  <div className="flex justify-center py-6 sm:py-8">
                    <PageLoader />
                  </div>
                ) : (
                  <div className="overflow-x-auto -mx-3 sm:mx-0">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[250px] sm:w-[25%] text-xs sm:text-sm">Question</TableHead>
                          <TableHead className="min-w-[100px] sm:w-[10%] text-xs sm:text-sm">Type</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[15%] text-xs sm:text-sm hidden lg:table-cell">Division</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[15%] text-xs sm:text-sm hidden lg:table-cell">Target</TableHead>
                          <TableHead className="min-w-[80px] sm:w-[8%] text-xs sm:text-sm hidden sm:table-cell">Options</TableHead>
                          <TableHead className="min-w-[80px] sm:w-[10%] text-xs sm:text-sm">Status</TableHead>
                          <TableHead className="min-w-[100px] sm:w-[12%] text-xs sm:text-sm hidden md:table-cell">Created</TableHead>
                          <TableHead className="min-w-[120px] sm:w-[5%] text-xs sm:text-sm text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {questions.map((question) => {
                          const division = divisions.find(d => d.id === question.division_id);
                          const target = targets.find(t => t.id === question.subdivision_id);
                          return (
                            <TableRow key={question.id}>
                              <TableCell className="font-medium">
                                <div>
                                  <p className="font-semibold text-sm sm:text-base line-clamp-2">
                                    {getTranslatedText(question.question_text)}
                                  </p>
                                  <p className="text-xs text-sehatti-warm-gray-500">ID: {question.id}</p>
                                  {/* Show additional info on mobile */}
                                  <div className="mt-1 space-y-1 lg:hidden">
                                    <div className="flex items-center gap-2 text-xs">
                                      <span className="text-sehatti-warm-gray-600">Division:</span>
                                      <Badge variant="outline" size="sm" className="text-xs px-1.5 py-0.5">
                                        {division ? getTranslatedText(division.name) : 'Unknown'}
                                      </Badge>
                                    </div>
                                    <div className="flex items-center gap-2 text-xs">
                                      <span className="text-sehatti-warm-gray-600">Target:</span>
                                      <Badge variant="outline" size="sm" className="text-xs px-1.5 py-0.5">
                                        {target ? getTranslatedText(target.name) : 'Unknown'}
                                      </Badge>
                                    </div>
                                    <div className="flex items-center gap-2 text-xs sm:hidden">
                                      <span className="text-sehatti-warm-gray-600">Options:</span>
                                      <span className="text-sehatti-warm-gray-500">{question.options.length}</span>
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {getQuestionTypeBadge(question.question_type)}
                              </TableCell>
                              <TableCell className="hidden lg:table-cell">
                                <Badge 
                                  variant="outline" 
                                  size="sm"
                                  className="w-full justify-center font-medium text-xs px-2 py-1 whitespace-nowrap overflow-hidden text-ellipsis"
                                  title={division ? getTranslatedText(division.name) : 'Unknown'}
                                >
                                  {division ? getTranslatedText(division.name) : 'Unknown'}
                                </Badge>
                              </TableCell>
                              <TableCell className="hidden lg:table-cell">
                                <Badge 
                                  variant="outline" 
                                  size="sm"
                                  className="w-full justify-center font-medium text-xs px-2 py-1 whitespace-nowrap overflow-hidden text-ellipsis"
                                  title={target ? getTranslatedText(target.name) : 'Unknown'}
                                >
                                  {target ? getTranslatedText(target.name) : 'Unknown'}
                                </Badge>
                              </TableCell>
                              <TableCell className="hidden sm:table-cell">
                                <p className="text-sm text-sehatti-warm-gray-600">
                                  {question.options.length}
                                </p>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(question)}
                              </TableCell>
                              <TableCell className="hidden md:table-cell">
                                <p className="text-sm">
                                  {new Date(question.created_at).toLocaleDateString()}
                                </p>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-1 sm:gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('view', question)}
                                    className="text-sehatti-gold-600 hover:text-sehatti-gold-700 p-2 sm:p-2.5"
                                  >
                                    <FaEye className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('edit', question)}
                                    className="text-blue-600 hover:text-blue-700 p-2 sm:p-2.5"
                                  >
                                    <FaEdit className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openModal('delete', question)}
                                    className="text-red-600 hover:text-red-700 p-2 sm:p-2.5"
                                  >
                                    <FaTrash className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                )}
                
                {!questionsLoading && questions.length === 0 && (
                  <div className="text-center py-8 sm:py-12 px-4">
                    <FaQuestionCircle className="w-12 h-12 sm:w-16 sm:h-16 text-sehatti-warm-gray-400 mx-auto mb-4" />
                    <p className="text-sehatti-warm-gray-600 text-sm sm:text-base mb-4">
                      {questionTypeFilter !== 'all' ? `No ${questionTypeFilter} questions found` : 'No questions found'}
                    </p>
                    <Button 
                      onClick={() => openModal('create')}
                      className="w-full sm:w-auto"
                      disabled={!divisions.length || !targets.length}
                      size="lg"
                    >
                      Create First Question
                    </Button>
                  </div>
                )}
              </TabPanel>
              </TabPanels>
          </Tabs>
        </Card>

        {/* Create/Edit Modals */}
        {(modals.create || modals.edit) && (
          <Modal 
            open={modals.create || modals.edit} 
            onClose={closeAllModals}
            size="xl"
          >
            <ModalHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-sehatti-gold-100 dark:bg-sehatti-gold-900/20 rounded-lg">
                  {activeTab === 'divisions' && <FaSitemap className="w-5 h-5 text-sehatti-gold-600 dark:text-sehatti-gold-400" />}
                  {activeTab === 'targets' && <FaBullseye className="w-5 h-5 text-sehatti-gold-600 dark:text-sehatti-gold-400" />}
                  {activeTab === 'questions' && <FaQuestionCircle className="w-5 h-5 text-sehatti-gold-600 dark:text-sehatti-gold-400" />}
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    {modals.create ? 'Create' : 'Edit'} {activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}
                  </h2>
                  <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                    {modals.create ? `Create a new ${activeTab.slice(0, -1)}` : `Update ${activeTab.slice(0, -1)} information`}
                  </p>
                </div>
              </div>
            </ModalHeader>
            
            <ModalContent className="py-0">
              <div className="py-2">
                {activeTab === 'divisions' && (
                  <QADivisionForm
                    initialData={selectedEntity}
                    onSubmit={modals.create ? handleCreate : (data) => handleUpdate(selectedEntity.id, data)}
                    onCancel={closeAllModals}
                    isLoading={isCreatingDivision || isUpdatingDivision}
                    onFormSubmit={(submitFn: () => void) => {
                      formSubmitRef.current = submitFn;
                    }}
                  />
                )}
                
                {activeTab === 'targets' && (
                  <QATargetForm
                    initialData={selectedEntity}
                    divisions={divisions}
                    onSubmit={modals.create ? handleCreate : (data: any) => handleUpdate(selectedEntity.id, data)}
                    onCancel={closeAllModals}
                    isLoading={isCreatingTarget || isUpdatingTarget}
                    onFormSubmit={(submitFn: () => void) => {
                      formSubmitRef.current = submitFn;
                    }}
                  />
                )}
                
                {activeTab === 'questions' && (
                  <QAQuestionForm
                    initialData={selectedEntity}
                    divisions={divisions}
                    targets={targets}
                    onSubmit={modals.create ? handleCreate : (data: any) => handleUpdate(selectedEntity.id, data)}
                    onCancel={closeAllModals}
                    isLoading={isCreatingQuestion || isUpdatingQuestion}
                    onFormSubmit={(submitFn: () => void) => {
                      formSubmitRef.current = submitFn;
                    }}
                  />
                )}
              </div>
            </ModalContent>
            
            <ModalFooter>
              <div className="flex justify-end gap-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={closeAllModals}
                  disabled={isCreatingDivision || isUpdatingDivision || isCreatingTarget || isUpdatingTarget || isCreatingQuestion || isUpdatingQuestion}
                >
                  Cancel
                </Button>
                
                <Button 
                  onClick={() => {
                    if (formSubmitRef.current) {
                      formSubmitRef.current();
                    }
                  }}
                  disabled={
                    isCreatingDivision || isUpdatingDivision || 
                    isCreatingTarget || isUpdatingTarget || 
                    isCreatingQuestion || isUpdatingQuestion ||
                    (activeTab === 'targets' && divisions.length === 0) ||
                    (activeTab === 'questions' && (divisions.length === 0 || targets.length === 0))
                  }
                  className="flex items-center gap-2"
                >
                  {(isCreatingDivision || isUpdatingDivision || isCreatingTarget || isUpdatingTarget || isCreatingQuestion || isUpdatingQuestion) && 
                    <FaSpinner className="w-4 h-4 animate-spin" />
                  }
                  {modals.create ? `Create ${activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}` : `Update ${activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}`}
                </Button>
              </div>
            </ModalFooter>
          </Modal>
        )}

        {/* View Modal */}
        {modals.view && selectedEntity && (
          <Modal 
            open={modals.view} 
            onClose={closeAllModals}
            size="2xl"
          >
            <ModalHeader>
              <h2 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                View {activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}
              </h2>
              <p className="text-sm text-sehatti-warm-gray-500 mt-1">
                {getTranslatedText(selectedEntity.name || selectedEntity.question_text)}
              </p>
            </ModalHeader>
            
            <ModalContent className="py-0">
              <div className="space-y-6 max-h-[70vh] overflow-y-auto py-4">
                {/* Basic Information Card */}
                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                  <h3 className="text-base font-semibold mb-3 flex items-center gap-2 text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    <FaInfoCircle className="w-4 h-4 text-sehatti-gold-600" />
                    Basic Information
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Entity ID</p>
                      <p className="text-sm font-mono bg-white dark:bg-sehatti-warm-gray-700 px-2 py-1 rounded border">
                        {selectedEntity.id}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Status</p>
                      <div className="flex items-center">
                        {getStatusBadge(selectedEntity)}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Created</p>
                      <p className="text-sm">{new Date(selectedEntity.created_at).toLocaleString()}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Last Updated</p>
                      <p className="text-sm">{new Date(selectedEntity.updated_at).toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                {/* Multilingual Content Card */}
                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                  <h3 className="text-base font-semibold mb-3 flex items-center gap-2 text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                    <FaGlobe className="w-4 h-4 text-sehatti-gold-600" />
                    Multilingual Content
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-2">
                        {activeTab === 'questions' ? 'Question Text' : 'Name/Title'}
                      </p>
                      <div className="space-y-2">
                        <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">🇺🇸</span>
                            <span className="text-xs font-medium text-blue-600">English</span>
                          </div>
                          <p className="text-sm">{selectedEntity.name?.en || selectedEntity.question_text?.en || 'Not available'}</p>
                        </div>
                        <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">🇸🇦</span>
                            <span className="text-xs font-medium text-green-600">Arabic</span>
                          </div>
                          <p className="text-sm" dir="rtl">{selectedEntity.name?.ar || selectedEntity.question_text?.ar || 'غير متوفر'}</p>
                        </div>
                        <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">🇮🇳</span>
                            <span className="text-xs font-medium text-orange-600">Hindi</span>
                          </div>
                          <p className="text-sm">{selectedEntity.name?.hi || selectedEntity.question_text?.hi || 'उपलब्ध नहीं'}</p>
                        </div>
                      </div>
                    </div>
                    
                    {selectedEntity.description && (
                      <div>
                        <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-2">Description</p>
                        <div className="space-y-2">
                          <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-lg">🇺🇸</span>
                              <span className="text-xs font-medium text-blue-600">English</span>
                            </div>
                            <p className="text-sm">{selectedEntity.description.en || 'No description'}</p>
                          </div>
                          <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-lg">🇸🇦</span>
                              <span className="text-xs font-medium text-green-600">Arabic</span>
                            </div>
                            <p className="text-sm" dir="rtl">{selectedEntity.description.ar || 'لا يوجد وصف'}</p>
                          </div>
                          <div className="bg-white dark:bg-sehatti-warm-gray-700 p-3 rounded border">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-lg">🇮🇳</span>
                              <span className="text-xs font-medium text-orange-600">Hindi</span>
                            </div>
                            <p className="text-sm">{selectedEntity.description.hi || 'कोई विवरण नहीं'}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Question-specific details */}
                {activeTab === 'questions' && (
                  <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                    <h3 className="text-base font-semibold mb-3 flex items-center gap-2 text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      <FaQuestionCircle className="w-4 h-4 text-sehatti-gold-600" />
                      Question Details
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Question Type</p>
                          <div className="mt-1">
                            {getQuestionTypeBadge(selectedEntity.question_type)}
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Division</p>
                          <div className="mt-1">
                            <Badge variant="outline" size="sm">
                              {(() => {
                                const division = divisions.find(d => d.id === selectedEntity.division_id);
                                return division ? getTranslatedText(division.name) : 'Unknown Division';
                              })()}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Target (SubDivision)</p>
                        <div className="mt-1">
                          <Badge variant="outline" size="sm">
                            {(() => {
                              const target = targets.find(t => t.id === selectedEntity.subdivision_id);
                              return target ? getTranslatedText(target.name) : 'Unknown Target';
                            })()}
                          </Badge>
                        </div>
                      </div>

                      {selectedEntity.options && selectedEntity.options.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mb-2">
                            Answer Options ({selectedEntity.options.length})
                          </p>
                          <div className="space-y-3">
                            {selectedEntity.options.map((option: any, index: number) => (
                              <div key={index} className="bg-white dark:bg-sehatti-warm-gray-700 rounded-lg p-3 border">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-sehatti-gold-600">Option {index + 1}</span>
                                  <Badge variant="outline" size="sm">Value: {option.value}</Badge>
                                </div>
                                <div className="space-y-1">
                                  <p className="text-xs"><span className="text-blue-600 font-medium">🇺🇸 EN:</span> {option.text.en}</p>
                                  <p className="text-xs" dir="rtl"><span className="text-green-600 font-medium">🇸🇦 AR:</span> {option.text.ar || 'غير متوفر'}</p>
                                  <p className="text-xs"><span className="text-orange-600 font-medium">🇮🇳 HI:</span> {option.text.hi || 'उपलब्ध नहीं'}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div>
                        <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Corporate Assignment</p>
                        <div className="mt-1">
                          {corporatesLoading && (
                            <div className="flex items-center gap-2 text-sm text-sehatti-warm-gray-500">
                              <FaSpinner className="w-4 h-4 animate-spin" />
                              Loading corporate data...
                            </div>
                          )}
                          {corporatesError && (
                            <div className="flex items-center gap-2 text-sm text-red-500">
                              <FaExclamationCircle className="w-4 h-4" />
                              Error loading corporate data
                            </div>
                          )}
                          {!corporatesLoading && !corporatesError && selectedEntity.corporate_ids && selectedEntity.corporate_ids.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {selectedEntity.corporate_ids.map((corpId: string, index: number) => {
                                const corporateName = getCorporateName(corpId);
                                return (
                                  <Badge 
                                    key={index} 
                                    variant="outline" 
                                    size="sm"
                                    className={`flex items-center gap-2 px-3 py-2 ${
                                      corporateName === 'Deleted Corporate' 
                                        ? 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800/30' 
                                        : corporateName === 'Unknown Corporate'
                                        ? 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800/30'
                                        : ''
                                    }`}
                                    title={`Corporate ID: ${corpId} | Name: ${corporateName}`}
                                  >
                                    <FaGlobe className={`w-3 h-3 ${
                                      corporateName === 'Deleted Corporate' 
                                        ? 'text-red-600 dark:text-red-400' 
                                        : corporateName === 'Unknown Corporate'
                                        ? 'text-orange-600 dark:text-orange-400'
                                        : 'text-sehatti-gold-600'
                                    }`} />
                                    <span className="font-medium">{corporateName}</span>
                                  </Badge>
                                );
                              })}
                            </div>
                          ) : !corporatesLoading && !corporatesError ? (
                            <p className="text-sm text-sehatti-warm-gray-500">Available to all corporates</p>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Target-specific details */}
                {activeTab === 'targets' && selectedEntity.division_id && (
                  <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                    <h3 className="text-base font-semibold mb-3 flex items-center gap-2 text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                      <FaBullseye className="w-4 h-4 text-sehatti-gold-600" />
                      Target Details
                    </h3>
                    <div>
                      <p className="text-sm font-medium text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">Parent Division</p>
                      <div className="mt-1">
                        <Badge variant="outline" size="sm">
                          {(() => {
                            const division = divisions.find(d => d.id === selectedEntity.division_id);
                            return division ? getTranslatedText(division.name) : 'Unknown Division';
                          })()}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}

                {/* Stats Card */}
                <div className="bg-sehatti-gold-50 dark:bg-sehatti-gold-900/20 rounded-lg p-4 border border-sehatti-gold-200 dark:border-sehatti-gold-800/30">
                  <h3 className="text-base font-semibold mb-3 flex items-center gap-2 text-sehatti-gold-800 dark:text-sehatti-gold-300">
                    <FaChartBar className="w-4 h-4" />
                    Statistics
                  </h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {activeTab === 'divisions' && (
                      <>
                        <div>
                          <p className="font-medium text-sehatti-gold-700 dark:text-sehatti-gold-400">Related Targets</p>
                          <p className="text-lg font-bold text-sehatti-gold-800 dark:text-sehatti-gold-300">
                            {targets.filter(t => t.division_id === selectedEntity.id).length}
                          </p>
                        </div>
                        <div>
                          <p className="font-medium text-sehatti-gold-700 dark:text-sehatti-gold-400">Related Questions</p>
                          <p className="text-lg font-bold text-sehatti-gold-800 dark:text-sehatti-gold-300">
                            {questions.filter(q => q.division_id === selectedEntity.id).length}
                          </p>
                        </div>
                      </>
                    )}
                    {activeTab === 'targets' && (
                      <div>
                        <p className="font-medium text-sehatti-gold-700 dark:text-sehatti-gold-400">Related Questions</p>
                        <p className="text-lg font-bold text-sehatti-gold-800 dark:text-sehatti-gold-300">
                          {questions.filter(q => q.subdivision_id === selectedEntity.id).length}
                        </p>
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-sehatti-gold-700 dark:text-sehatti-gold-400">Days Active</p>
                      <p className="text-lg font-bold text-sehatti-gold-800 dark:text-sehatti-gold-300">
                        {Math.ceil((Date.now() - new Date(selectedEntity.created_at).getTime()) / (1000 * 60 * 60 * 24))}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ModalContent>
            
            <ModalFooter>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      closeAllModals();
                      setTimeout(() => openModal('edit', selectedEntity), 100);
                    }}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <FaEdit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  <Button 
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      closeAllModals();
                      setTimeout(() => openModal('delete', selectedEntity), 100);
                    }}
                    className="text-red-600 hover:text-red-700"
                  >
                    <FaTrash className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </div>
                <Button variant="outline" onClick={closeAllModals}>
                  Close
                </Button>
              </div>
            </ModalFooter>
          </Modal>
        )}

        {/* Delete Modal */}
        {modals.delete && selectedEntity && (
          <Modal 
            open={modals.delete} 
            onClose={closeAllModals}
            size="lg"
          >
            <ModalHeader>
              <div className="text-center">
                <FaExclamationTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                  Delete {activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}
                </h2>
                <p className="text-sm text-sehatti-warm-gray-500 mt-1">
                  This action requires confirmation
                </p>
              </div>
            </ModalHeader>
            
            <ModalContent className="py-0">
              <div className="space-y-6 py-4">
                <Alert variant="destructive" icon={<FaInfoCircle />}>
                  <div>
                    <p className="font-medium">Warning</p>
                    <p className="text-sm mt-1">
                      You are about to delete this {activeTab.slice(0, -1)}. This action affects the following:
                    </p>
                  </div>
                </Alert>

                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4 border-l-4 border-sehatti-gold-500">
                  <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-2">
                    Item to be deleted:
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm">
                      <span className="font-medium">Name:</span> {getTranslatedText(selectedEntity.name || selectedEntity.question_text)}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">ID:</span> 
                      <code className="bg-white dark:bg-sehatti-warm-gray-700 px-2 py-1 rounded ml-1">
                        {selectedEntity.id}
                      </code>
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Created:</span> {new Date(selectedEntity.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {/* Impact Warning */}
                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800/30">
                  <h4 className="font-medium text-red-800 dark:text-red-300 mb-2 flex items-center gap-2">
                    <FaExclamationCircle className="w-4 h-4" />
                    Deletion Impact
                  </h4>
                  <div className="text-sm text-red-700 dark:text-red-400 space-y-1">
                    {activeTab === 'divisions' && (
                      <>
                        <p>• All targets ({targets.filter(t => t.division_id === selectedEntity.id).length}) under this division will be affected</p>
                        <p>• All questions ({questions.filter(q => q.division_id === selectedEntity.id).length}) under this division will be affected</p>
                      </>
                    )}
                    {activeTab === 'targets' && (
                      <p>• All questions ({questions.filter(q => q.subdivision_id === selectedEntity.id).length}) under this target will be affected</p>
                    )}
                    {activeTab === 'questions' && (
                      <p>• This question and all its answer options will be permanently removed</p>
                    )}
                  </div>
                </div>

                {/* Deletion Options */}
                <div className="bg-sehatti-warm-gray-50 dark:bg-sehatti-warm-gray-800 rounded-lg p-4">
                  <h4 className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100 mb-3">
                    Deletion Options
                  </h4>
                  <div className="space-y-3">
                    <label className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border hover:bg-white dark:hover:bg-sehatti-warm-gray-700 transition-colors">
                      <input 
                        type="radio" 
                        name="deleteType" 
                        value="soft"
                        className="mt-1 text-sehatti-gold-600"
                        defaultChecked
                      />
                      <div>
                        <p className="font-medium text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
                          Soft Delete (Recommended)
                        </p>
                        <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                          Deactivate the item. It will be hidden but can be recovered later.
                        </p>
                      </div>
                    </label>
                    <label className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border hover:bg-white dark:hover:bg-sehatti-warm-gray-700 transition-colors">
                      <input 
                        type="radio" 
                        name="deleteType" 
                        value="hard"
                        className="mt-1 text-red-600"
                      />
                      <div>
                        <p className="font-medium text-red-600">
                          Permanent Delete
                        </p>
                        <p className="text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400">
                          Completely remove from database. This action cannot be undone.
                        </p>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Confirmation */}
                <div className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800/30">
                  <h4 className="font-medium text-amber-800 dark:text-amber-300 mb-2 flex items-center gap-2">
                    <FaCheckCircle className="w-4 h-4" />
                    Confirmation Required
                  </h4>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input 
                      type="checkbox" 
                      id="confirmDelete" 
                      className="rounded text-red-600 focus:ring-red-500"
                    />
                    <span className="text-sm text-amber-700 dark:text-amber-400">
                      I understand the consequences and want to proceed with deletion
                    </span>
                  </label>
                </div>
              </div>
            </ModalContent>
            
            <ModalFooter>
              <div className="flex items-center justify-between w-full">
                <div className="text-xs text-sehatti-warm-gray-500">
                  Action will be logged for audit purposes
                </div>
                <div className="flex items-center gap-3">
                  <Button variant="outline" onClick={closeAllModals}>
                    Cancel
                  </Button>
                  <Button 
                    variant="destructive" 
                    onClick={() => {
                      const confirmCheckbox = document.getElementById('confirmDelete') as HTMLInputElement;
                      const hardDeleteRadio = document.querySelector('input[name="deleteType"]:checked') as HTMLInputElement;
                      
                      if (!confirmCheckbox?.checked) {
                        alert('Please confirm that you understand the consequences of this action.');
                        return;
                      }
                      
                      const hardDelete = hardDeleteRadio?.value === 'hard';
                      handleDelete(selectedEntity.id, hardDelete);
                    }}
                    disabled={isDeletingDivision || isDeletingTarget || isDeletingQuestion}
                    className="flex items-center gap-2"
                  >
                    {(isDeletingDivision || isDeletingTarget || isDeletingQuestion) ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Deleting...
                      </>
                    ) : (
                      <>
                        <FaTrash className="w-4 h-4" />
                        Confirm Delete
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </ModalFooter>
          </Modal>
        )}

        {/* Bulk Upload Modal */}
        {modals.bulkUpload && (
          <BulkUploadModal
            isOpen={modals.bulkUpload}
            onClose={() => setModals(prev => ({ ...prev, bulkUpload: false }))}
            onSubmit={handleBulkUpload}
            corporates={corporatesData?.items || []}
            isLoading={isBulkUploading}
          />
        )}
        </div>
      </div>
    </AdminLayout>
  );
};

// Bulk Upload Modal Component
interface BulkUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (file: File, corporateIds: string[]) => void;
  corporates: any[];
  isLoading: boolean;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  corporates,
  isLoading
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCorporates, setSelectedCorporates] = useState<string[]>([]);
  const [fileError, setFileError] = useState<string>('');



  const handleFileSelect = (file: File | null) => {
    if (file) {
      setSelectedFile(file);
      setFileError('');
    } else {
      setSelectedFile(null);
      setFileError('Please select a valid Excel file (.xlsx or .xls)');
    }
  };

  const handleCorporateSelection = (corporateIds: string[]) => {
    setSelectedCorporates(corporateIds);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      toast.error('Please select an Excel file');
      return;
    }
    
    if (selectedCorporates.length === 0) {
      toast.error('Please select at least one corporate');
      return;
    }

    onSubmit(selectedFile, selectedCorporates);
  };

  const downloadTemplate = () => {
    // Create Excel template that exactly matches the old implementation
    const templateData = [
      ['Division', 'Subdivision', 'Question', 'Question Type', 'Status', 'Option 1 Text', 'Option 1 Value', 'Option 2 Text', 'Option 2 Value', 'Option 3 Text', 'Option 3 Value', 'Option 4 Text', 'Option 4 Value', 'Option 5 Text', 'Option 5 Value', 'Option 6 Text', 'Option 6 Value', 'Option 7 Text', 'Option 7 Value'],
      ['Physical Wellbeing', 'Energy & Nutrition', 'How energized did your meals make you feel today?', 'pre_assessment', 'active', 'Not at all energized', 1, 'Slightly energized', 2, 'Moderately energized', 3, 'Very energized', 4, 'Extremely energized', 5, '', '', '', ''],
      ['Physical Wellbeing', 'Sleep Quality', 'How well did you sleep last night?', 'ongoing', 'active', 'Very poorly', 1, 'Poorly', 2, 'Average', 3, 'Well', 4, 'Very well', 5, '', '', '', ''],
      ['Emotional Wellbeing', 'Emotional Awareness', 'How emotionally balanced do you feel today?', 'post_assessment', 'active', 'Very distressed', 1, 'Distressed', 2, 'Neutral', 3, 'Balanced', 4, 'Very balanced', 5, '', '', '', ''],
      ['Social Wellbeing', 'Supportive Relationships', 'How positive were your social interactions today?', 'ongoing', 'active', 'Not at all positive', 1, 'Slightly positive', 2, 'Moderately positive', 3, 'Very positive', 4, 'Extremely positive', 5, '', '', '', ''],
      ['Mental Wellbeing', 'Focus & Concentration', 'How focused were you during work today?', 'pre_assessment', 'draft', 'Not focused at all', 1, 'Slightly focused', 2, 'Moderately focused', 3, 'Very focused', 4, 'Extremely focused', 5, '', '', '', ''],
      ['Work Satisfaction', 'Task Completion', 'Did you complete your main tasks today?', 'ongoing', 'active', 'No', 0, 'Yes', 1, '', '', '', '', '', '', '', '', '', ''],
      ['Health Habits', 'Exercise', 'How many minutes did you exercise today?', 'ongoing', 'active', '0 minutes', 0, '1-15 minutes', 15, '16-30 minutes', 30, '31-60 minutes', 60, 'More than 60 minutes', 90, '', '', '', '']
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    
    // Add column width for better readability
    ws['!cols'] = [
      { wch: 20 }, // Division
      { wch: 25 }, // Subdivision  
      { wch: 50 }, // Question
      { wch: 18 }, // Question Type
      { wch: 12 }, // Status
      { wch: 20 }, // Option 1 Text
      { wch: 8 },  // Option 1 Value
      { wch: 20 }, // Option 2 Text
      { wch: 8 },  // Option 2 Value
      { wch: 20 }, // Option 3 Text
      { wch: 8 },  // Option 3 Value
      { wch: 20 }, // Option 4 Text
      { wch: 8 },  // Option 4 Value
      { wch: 20 }, // Option 5 Text
      { wch: 8 },  // Option 5 Value
      { wch: 20 }, // Option 6 Text
      { wch: 8 },  // Option 6 Value
      { wch: 20 }, // Option 7 Text
      { wch: 8 }   // Option 7 Value
    ];
    
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Questions Template');
    XLSX.writeFile(wb, 'questions_template.xlsx');
  };

  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onClose={onClose} size="xl">
      <ModalHeader>
        <div className="flex flex-col sm:flex-row sm:items-center gap-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg self-start">
            <FaUpload className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div className="flex-1">
            <h2 className="text-lg sm:text-xl font-semibold text-sehatti-warm-gray-900 dark:text-sehatti-warm-gray-100">
              Bulk Upload Questions
            </h2>
            <p className="text-xs sm:text-sm text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-400 mt-1">
              Upload multiple questions from an Excel file
            </p>
          </div>
        </div>
      </ModalHeader>

      <ModalContent className="py-0">
        <div className="space-y-4 sm:space-y-6 max-h-[60vh] sm:max-h-[70vh] overflow-y-auto py-4">
          {/* Template Download - Improved Mobile Layout */}
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-800/30 rounded-lg p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row sm:items-start gap-3">
              <div className="p-2 bg-blue-600 rounded-lg self-start">
                <FaFileExcel className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <div className="flex-1 space-y-3">
                <div>
                  <h3 className="font-semibold text-blue-900 dark:text-blue-300 text-sm sm:text-base">
                    📋 Excel Template Required
                  </h3>
                  <p className="text-xs sm:text-sm text-blue-700 dark:text-blue-400 mt-1">
                    Download our template to ensure correct formatting
                  </p>
                </div>
                
                {/* Template Format - Collapsible on Mobile */}
                <details className="group">
                  <summary className="cursor-pointer text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-300 list-none flex items-center gap-2">
                    <span>📑 Required Columns</span>
                    <span className="transform group-open:rotate-90 transition-transform">▶</span>
                  </summary>
                  <div className="mt-2 space-y-2">
                                         <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                       <div className="bg-white/60 dark:bg-sehatti-warm-gray-800/60 p-2 rounded border">
                         <span className="font-medium text-blue-900 dark:text-blue-300">Division:</span>
                         <span className="text-blue-700 dark:text-blue-400 ml-1">Must exist in system</span>
                       </div>
                       <div className="bg-white/60 dark:bg-sehatti-warm-gray-800/60 p-2 rounded border">
                         <span className="font-medium text-blue-900 dark:text-blue-300">Subdivision:</span>
                         <span className="text-blue-700 dark:text-blue-400 ml-1">Must exist in system</span>
                       </div>
                       <div className="bg-white/60 dark:bg-sehatti-warm-gray-800/60 p-2 rounded border">
                         <span className="font-medium text-blue-900 dark:text-blue-300">Question:</span>
                         <span className="text-blue-700 dark:text-blue-400 ml-1">Question text</span>
                       </div>
                       <div className="bg-white/60 dark:bg-sehatti-warm-gray-800/60 p-2 rounded border">
                         <span className="font-medium text-blue-900 dark:text-blue-300">Type:</span>
                         <span className="text-blue-700 dark:text-blue-400 ml-1">pre/post/ongoing</span>
                       </div>
                     </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400 bg-white/40 dark:bg-sehatti-warm-gray-800/40 p-2 rounded">
                      <strong>💡 Tips:</strong> 2-7 options per question • Default Likert scale if no options • Numeric values (1,2,3...)
                    </div>
                  </div>
                </details>
                
                <ActionButton
                  intent="download"
                  buttonSize="sm"
                  icon={<FaDownload />}
                  onClick={downloadTemplate}
                  fullWidth={true}
                  className="sm:w-auto"
                >
                  Download Template
                </ActionButton>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* File Upload - Using Pre-styled Component */}
            <FileUpload
              selectedFile={selectedFile}
              onFileSelect={handleFileSelect}
              acceptedTypes={['.xlsx', '.xls']}
              maxSize={10}
              errorMessage={fileError}
            />

            {/* Corporate Selection - Using Pre-styled Component */}
            <CorporateSelector
              corporates={corporates}
              selectedCorporates={selectedCorporates}
              onSelectionChange={handleCorporateSelection}
              required={true}
            />
          </form>
        </div>
      </ModalContent>
      
      <ModalFooter>
        <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 w-full">
          <ActionButton
            intent="view"
            buttonSize="md"
            onClick={onClose}
            disabled={isLoading}
            fullWidth={true}
            className="sm:w-auto order-2 sm:order-1"
          >
            Cancel
          </ActionButton>
          
          <ActionButton
            intent="upload"
            buttonSize="md"
            icon={<FaUpload />}
            onClick={(e) => {
              e.preventDefault();
              const form = e.currentTarget.closest('.bg-white')?.querySelector('form') as HTMLFormElement;
              if (form) {
                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(submitEvent);
              }
            }}
            disabled={isLoading || !selectedFile || selectedCorporates.length === 0}
            isLoading={isLoading}
            fullWidth={true}
            className="sm:w-auto order-1 sm:order-2"
          >
            {isLoading ? 'Processing...' : `Upload ${selectedCorporates.length > 0 ? `to ${selectedCorporates.length} corporate${selectedCorporates.length > 1 ? 's' : ''}` : 'Questions'}`}
          </ActionButton>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default QAManagement; 