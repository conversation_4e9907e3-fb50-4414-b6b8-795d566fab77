// API Testing Utility
// Use this to verify that your frontend properly connects to backend services

import { getApiConfig } from '../config/api';

/**
 * Test all API endpoints to verify connectivity
 */
export const testApiConnectivity = async () => {
  const config = getApiConfig();
  
  console.log('🔧 Current API Configuration:', config);
  
  const tests = [
    {
      name: 'Main API Health Check',
      url: `${config.apiUrl}/api/health`,
      expectedPath: '/api/health → API Service (Node.js)'
    },
    {
      name: 'QA Service Health Check',
      url: `${config.qaServiceUrl}/api/qa/health`,
      expectedPath: '/api/qa/health → QA Service (FastAPI)'
    },
    {
      name: 'Media Service Health Check', 
      url: `${config.mediaServiceUrl}/api/media/health`,
      expectedPath: '/api/media/health → Media Service (FastAPI)'
    }
  ];

  console.log('🚀 Testing API connectivity...\n');

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      console.log(`URL: ${test.url}`);
      console.log(`Expected: ${test.expectedPath}`);
      
      const response = await fetch(test.url, {
        method: 'GET',
        mode: 'cors',
        cache: 'no-cache',
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${test.name}`);
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, data);
      } else {
        console.log(`❌ FAILED: ${test.name}`);
        console.log(`Status: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${test.name}`);
      console.log(`Error:`, error);
    }
    console.log('---\n');
  }
};

/**
 * Quick test for current environment detection
 */
export const testEnvironmentDetection = () => {
  const config = getApiConfig();
  
  console.log('🌍 Environment Detection Test:');
  console.log(`Environment: ${config.environment}`);
  console.log(`API URL: ${config.apiUrl}`);
  console.log(`Media Service URL: ${config.mediaServiceUrl}`);
  console.log(`QA Service URL: ${config.qaServiceUrl}`);
  console.log(`WebSocket URL: ${config.wsUrl}`);
  
  // Check environment variables
  console.log('\n📋 Environment Variables:');
  console.log(`VITE_ENV: ${import.meta.env.VITE_ENV || 'Not set'}`);
  console.log(`VITE_API_URL: ${import.meta.env.VITE_API_URL || 'Not set'}`);
  console.log(`VITE_MEDIA_SERVICE_URL: ${import.meta.env.VITE_MEDIA_SERVICE_URL || 'Not set'}`);
  console.log(`VITE_QA_SERVICE_URL: ${import.meta.env.VITE_QA_SERVICE_URL || 'Not set'}`);
};

/**
 * Test routing to ensure ALB forwards requests correctly
 */
export const testALBRouting = async () => {
  const config = getApiConfig();
  
  console.log('🔀 Testing ALB Routing...\n');
  
  const routingTests = [
    {
      description: 'API Service (Priority 300 - Catch All)',
      url: `${config.apiUrl}/api/v1/auth/test`,
      expectedService: 'API Service (Node.js/NestJS)'
    },
    {
      description: 'QA Service (Priority 100)', 
      url: `${config.apiUrl}/api/qa/health`,
      expectedService: 'QA Service (FastAPI)'
    },
    {
      description: 'Media Service (Priority 200)',
      url: `${config.apiUrl}/api/media/health`,
      expectedService: 'Media Service (FastAPI)'
    }
  ];

  for (const test of routingTests) {
    console.log(`Testing: ${test.description}`);
    console.log(`URL: ${test.url}`);
    console.log(`Expected Service: ${test.expectedService}`);
    console.log('---');
  }
  
  console.log('\n💡 To run these tests, open browser console and call:');
  console.log('testApiConnectivity() or testALBRouting()');
}; 