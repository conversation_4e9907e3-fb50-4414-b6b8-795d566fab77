/**
 * Onboarding utilities for managing employee onboarding completion status
 */

export const ONBOARDING_COMPLETION_KEY = 'sehatti-onboarding-completed'
export const ONBOARDING_PROGRESS_KEY = 'sehatti-onboarding-progress'

/**
 * Check if employee has completed onboarding
 * Uses both the simple completion flag and detailed progress for backward compatibility
 */
export const isOnboardingCompleted = (): boolean => {
  // First check the simple completion flag
  const isCompleted = localStorage.getItem(ONBOARDING_COMPLETION_KEY) === 'true'
  
  if (isCompleted) {
    return true
  }
  
  // Check detailed progress for backward compatibility
  const progressStr = localStorage.getItem(ONBOARDING_PROGRESS_KEY)
  if (progressStr) {
    try {
      const progress = JSON.parse(progressStr)
      const isCompletedFromProgress = progress.currentStep === 'completed' || progress.completedAt
      
      // If completed according to progress, set the completion flag for future checks
      if (isCompletedFromProgress) {
        markOnboardingCompleted()
        return true
      }
    } catch (error) {
      console.error('Failed to parse onboarding progress:', error)
    }
  }
  
  return false
}

/**
 * Mark onboarding as completed
 */
export const markOnboardingCompleted = (): void => {
  localStorage.setItem(ONBOARDING_COMPLETION_KEY, 'true')
}

/**
 * Reset onboarding completion status (for testing or admin purposes)
 */
export const resetOnboardingCompletion = (): void => {
  localStorage.removeItem(ONBOARDING_COMPLETION_KEY)
  localStorage.removeItem(ONBOARDING_PROGRESS_KEY)
}

/**
 * Get onboarding progress
 */
export const getOnboardingProgress = (): any | null => {
  const progressStr = localStorage.getItem(ONBOARDING_PROGRESS_KEY)
  if (progressStr) {
    try {
      return JSON.parse(progressStr)
    } catch (error) {
      console.error('Failed to parse onboarding progress:', error)
    }
  }
  return null
}

/**
 * Save onboarding progress
 */
export const saveOnboardingProgress = (progress: any): void => {
  localStorage.setItem(ONBOARDING_PROGRESS_KEY, JSON.stringify(progress))
  
  // If marking as completed, also set the completion flag
  if (progress.currentStep === 'completed' || progress.completedAt) {
    markOnboardingCompleted()
  }
} 