/**
 * Comprehensive translations for Employee Onboarding Flow
 * Enterprise-grade translations for Arabic, English, and Hindi
 */

export interface OnboardingTranslations {
  // Header and Progress
  welcomeTitle: string
  onboardingSubtitle: string
  progressTitle: string
  
  // Step titles
  steps: {
    language: string
    welcome: string
    webinar: string
    feedback: string
    assessment: string
  }
  
  // Language Selection Step
  languageSelection: {
    title: string
    subtitle: string
    greeting: string
    description: string
  }
  
  // Intro Video Step
  introVideo: {
    title: string
    subtitle: string
    videoTitle: string
    videoDescription: string
    welcomeMessage: string
    journeyDescription: string
    previous: string
    mustWatchComplete: string
    videoCompleted: string
    mustCompleteVideo: string
    videoCompletedProceed: string
  }
  
  // Webinar Booking Step
  webinarBooking: {
    title: string
    subtitle: string
    duration: string
    spots: string
    selectSlot: string
    select: string
    booking: string
    previous: string
  }
  
  // Feedback Step
  feedback: {
    title: string
    subtitle: string
    ratingLabel: string
    commentsLabel: string
    commentsPlaceholder: string
    submitFeedback: string
    submitting: string
    previous: string
  }
  
  // Assessment Step
  assessment: {
    title: string
    subtitle: string
    questionLabel: string
    submitAssessment: string
    submitting: string
    previous: string
  }
  
  // Completion Step
  completion: {
    title: string
    subtitle: string
    accessDashboard: string
  }
  
  // Common
  common: {
    loading: string
    error: string
    success: string
    next: string
    previous: string
    complete: string
    skip: string
    pleaseProvideRating: string
    pleaseSelectAnswer: string
  }
  
  // Toast Messages
  toasts: {
    languageSaved: string
    videoCompleted: string
    webinarBooked: string
    feedbackSubmitted: string
    assessmentCompleted: string
    onboardingCompleted: string
  }
}

export const onboardingTranslations: Record<'EN' | 'AR' | 'HI', OnboardingTranslations> = {
  EN: {
    welcomeTitle: 'Welcome to Sehatti',
    onboardingSubtitle: 'Complete your onboarding to access your wellness dashboard',
    progressTitle: 'Onboarding Progress',
    
    steps: {
      language: 'Language',
      welcome: 'Welcome',
      webinar: 'Webinar',
      feedback: 'Feedback',
      assessment: 'Assessment'
    },
    
    languageSelection: {
      title: 'Select Your Language',
      subtitle: 'Choose your preferred language for the best experience',
      greeting: 'Welcome to Sehatti!',
      description: 'Your corporate wellness journey begins here'
    },
    
    introVideo: {
      title: 'Welcome to Your Wellness Journey',
      subtitle: 'Learn about our comprehensive corporate wellness program',
      videoTitle: 'Corporate Wellness Introduction',
      videoDescription: 'Click to play (70MB video - 3min 52sec)',
      welcomeMessage: 'Welcome to Your Wellness Journey',
      journeyDescription: 'Learn about our comprehensive corporate wellness program',
      previous: 'Previous',
      mustWatchComplete: 'You must watch the entire video to continue',
      videoCompleted: 'Video completed! You can now proceed to the next step.',
      mustCompleteVideo: 'Please watch the entire video before proceeding',
      videoCompletedProceed: 'Video completed! You can now proceed.'
    },
    
    webinarBooking: {
      title: 'Book Your Webinar',
      subtitle: 'Select a convenient time slot for your personalized training session',
      duration: 'Duration',
      spots: 'Available Spots',
      selectSlot: 'Select Time Slot',
      select: 'Select',
      booking: 'Booking...',
      previous: 'Previous'
    },
    
    feedback: {
      title: 'Share Your Feedback',
      subtitle: 'Help us improve your wellness experience',
      ratingLabel: 'How would you rate your onboarding experience?',
      commentsLabel: 'Additional Comments',
      commentsPlaceholder: 'Share your thoughts about the onboarding process...',
      submitFeedback: 'Submit Feedback',
      submitting: 'Submitting...',
      previous: 'Previous'
    },
    
    assessment: {
      title: 'Quick Assessment',
      subtitle: 'Help us understand your current wellness state',
      questionLabel: 'Assessment Question',
      submitAssessment: 'Complete Assessment',
      submitting: 'Submitting...',
      previous: 'Previous'
    },
    
    completion: {
      title: 'Onboarding Complete!',
      subtitle: 'Welcome to your wellness journey. You\'re all set to explore your dashboard.',
      accessDashboard: 'Access My Dashboard'
    },
    
    common: {
      loading: 'Loading...',
      error: 'Something went wrong',
      success: 'Success!',
      next: 'Next',
      previous: 'Previous',
      complete: 'Complete',
      skip: 'Skip',
      pleaseProvideRating: 'Please provide a rating',
      pleaseSelectAnswer: 'Please select an answer'
    },
    
    toasts: {
      languageSaved: 'Language preference saved!',
      videoCompleted: 'Welcome video completed!',
      webinarBooked: 'Webinar slot booked successfully!',
      feedbackSubmitted: 'Thank you for your feedback!',
      assessmentCompleted: 'Assessment completed successfully!',
      onboardingCompleted: 'Onboarding completed! Welcome to Sehatti!'
    }
  },
  
  AR: {
    welcomeTitle: 'أهلاً بك في صحتي',
    onboardingSubtitle: 'أكمل عملية التأهيل للوصول إلى لوحة تحكم الصحة والعافية',
    progressTitle: 'تقدم التأهيل',
    
    steps: {
      language: 'اللغة',
      welcome: 'ترحيب',
      webinar: 'ندوة',
      feedback: 'تقييم',
      assessment: 'تقييم'
    },
    
    languageSelection: {
      title: 'اختر لغتك',
      subtitle: 'اختر لغتك المفضلة للحصول على أفضل تجربة',
      greeting: 'أهلاً بك في صحتي!',
      description: 'تبدأ رحلة الصحة المؤسسية هنا'
    },
    
    introVideo: {
      title: 'أهلاً بك في صحتي!',
      subtitle: 'تبدأ رحلة الصحة المؤسسية هنا',
      videoTitle: 'مقدمة الصحة المؤسسية',
      videoDescription: 'انقر للتشغيل (فيديو 70 ميجابايت - 3 دقائق و 52 ثانية)',
      welcomeMessage: 'أهلاً بك في رحلة الصحة والعافية',
      journeyDescription: 'تعرف على برنامج الصحة المؤسسية الشامل',
      previous: 'السابق',
      mustWatchComplete: 'يجب مشاهدة الفيديو كاملاً للمتابعة',
      videoCompleted: 'تم إكمال الفيديو! يمكنك الآن الانتقال للخطوة التالية.',
      mustCompleteVideo: 'يرجى مشاهدة الفيديو كاملاً قبل المتابعة',
      videoCompletedProceed: 'تم إكمال الفيديو! يمكنك الآن المتابعة.'
    },
    
    webinarBooking: {
      title: 'احجز ندوتك',
      subtitle: 'اختر وقتاً مناسباً لجلسة التدريب الشخصية',
      duration: 'المدة',
      spots: 'الأماكن المتاحة',
      selectSlot: 'اختر الوقت',
      select: 'اختر',
      booking: 'جاري الحجز...',
      previous: 'السابق'
    },
    
    feedback: {
      title: 'شارك تقييمك',
      subtitle: 'ساعدنا في تحسين تجربة الصحة والعافية',
      ratingLabel: 'كيف تقيم تجربة التأهيل؟',
      commentsLabel: 'تعليقات إضافية',
      commentsPlaceholder: 'شارك أفكارك حول عملية التأهيل...',
      submitFeedback: 'إرسال التقييم',
      submitting: 'جاري الإرسال...',
      previous: 'السابق'
    },
    
    assessment: {
      title: 'تقييم سريع',
      subtitle: 'ساعدنا في فهم حالة الصحة والعافية الحالية',
      questionLabel: 'سؤال التقييم',
      submitAssessment: 'إكمال التقييم',
      submitting: 'جاري الإرسال...',
      previous: 'السابق'
    },
    
    completion: {
      title: 'تم إكمال التأهيل!',
      subtitle: 'أهلاً بك في رحلة الصحة والعافية. أنت جاهز لاستكشاف لوحة التحكم.',
      accessDashboard: 'الوصول إلى لوحة التحكم'
    },
    
    common: {
      loading: 'جاري التحميل...',
      error: 'حدث خطأ ما',
      success: 'نجح!',
      next: 'التالي',
      previous: 'السابق',
      complete: 'إكمال',
      skip: 'تخطي',
      pleaseProvideRating: 'يرجى تقييم تجربة التأهيل',
      pleaseSelectAnswer: 'يرجى اختيار إجابة'
    },
    
    toasts: {
      languageSaved: 'تم حفظ تفضيل اللغة!',
      videoCompleted: 'تم إكمال فيديو الترحيب!',
      webinarBooked: 'تم حجز موعد الندوة بنجاح!',
      feedbackSubmitted: 'شكراً لك على تقييمك!',
      assessmentCompleted: 'تم إكمال التقييم بنجاح!',
      onboardingCompleted: 'تم إكمال التأهيل! أهلاً بك في صحتي!'
    }
  },
  
  HI: {
    welcomeTitle: 'सेहत्ति में आपका स्वागत है',
    onboardingSubtitle: 'अपने कल्याण डैशबोर्ड तक पहुंचने के लिए अपना ऑनबोर्डिंग पूरा करें',
    progressTitle: 'ऑनबोर्डिंग प्रगति',
    
    steps: {
      language: 'भाषा',
      welcome: 'स्वागत',
      webinar: 'वेबिनार',
      feedback: 'फीडबैक',
      assessment: 'मूल्यांकन'
    },
    
    languageSelection: {
      title: 'अपनी भाषा चुनें',
      subtitle: 'सर्वोत्तम अनुभव के लिए अपनी पसंदीदा भाषा चुनें',
      greeting: 'सेहत्ति में आपका स्वागत है!',
      description: 'आपकी कॉर्पोरेट कल्याण यात्रा यहां शुरू होती है'
    },
    
    introVideo: {
      title: 'सेहत्ति में आपका स्वागत है!',
      subtitle: 'आपकी कॉर्पोरेट कल्याण यात्रा यहां शुरू होती है',
      videoTitle: 'कॉर्पोरेट कल्याण परिचय',
      videoDescription: 'चलाने के लिए क्लिक करें (70MB वीडियो - 3मिन 52सेक)',
      welcomeMessage: 'आपकी कल्याण यात्रा में आपका स्वागत है',
      journeyDescription: 'हमारे व्यापक कॉर्पोरेट कल्याण कार्यक्रम के बारे में जानें',
      previous: 'पिछला',
      mustWatchComplete: 'आगे बढ़ने के लिए आपको पूरा वीडियो देखना होगा',
      videoCompleted: 'वीडियो पूरा हुआ! अब आप अगले चरण पर जा सकते हैं।',
      mustCompleteVideo: 'कृपया आगे बढ़ने से पहले पूरा वीडियो देखें',
      videoCompletedProceed: 'वीडियो पूरा हुआ! अब आप आगे बढ़ सकते हैं।'
    },
    
    webinarBooking: {
      title: 'अपना वेबिनार बुक करें',
      subtitle: 'अपने व्यक्तिगत प्रशिक्षण सत्र के लिए सुविधाजनक समय स्लॉट चुनें',
      duration: 'अवधि',
      spots: 'उपलब्ध स्थान',
      selectSlot: 'समय स्लॉट चुनें',
      select: 'चुनें',
      booking: 'बुकिंग...',
      previous: 'पिछला'
    },
    
    feedback: {
      title: 'अपना फीडबैक साझा करें',
      subtitle: 'अपने कल्याण अनुभव को बेहतर बनाने में हमारी मदद करें',
      ratingLabel: 'आप अपने ऑनबोर्डिंग अनुभव को कैसे रेट करेंगे?',
      commentsLabel: 'अतिरिक्त टिप्पणियां',
      commentsPlaceholder: 'ऑनबोर्डिंग प्रक्रिया के बारे में अपने विचार साझा करें...',
      submitFeedback: 'फीडबैक सबमिट करें',
      submitting: 'सबमिट कर रहे हैं...',
      previous: 'पिछला'
    },
    
    assessment: {
      title: 'त्वरित मूल्यांकन',
      subtitle: 'अपनी वर्तमान कल्याण स्थिति को समझने में हमारी मदद करें',
      questionLabel: 'मूल्यांकन प्रश्न',
      submitAssessment: 'मूल्यांकन पूरा करें',
      submitting: 'सबमिट कर रहे हैं...',
      previous: 'पिछला'
    },
    
    completion: {
      title: 'ऑनबोर्डिंग पूर्ण!',
      subtitle: 'आपकी कल्याण यात्रा में आपका स्वागत है। आप अपना डैशबोर्ड एक्सप्लोर करने के लिए तैयार हैं।',
      accessDashboard: 'मेरा डैशबोर्ड एक्सेस करें'
    },
    
    common: {
      loading: 'लोड हो रहा है...',
      error: 'कुछ गलत हुआ',
      success: 'सफलता!',
      next: 'अगला',
      previous: 'पिछला',
      complete: 'पूर्ण',
      skip: 'छोड़ें',
      pleaseProvideRating: 'कृपया रेटिंग दें',
      pleaseSelectAnswer: 'कृपया एक उत्तर चुनें'
    },
    
    toasts: {
      languageSaved: 'भाषा प्राथमिकता सहेजी गई!',
      videoCompleted: 'स्वागत वीडियो पूरा हुआ!',
      webinarBooked: 'वेबिनार स्लॉट सफलतापूर्वक बुक किया गया!',
      feedbackSubmitted: 'आपके फीडबैक के लिए धन्यवाद!',
      assessmentCompleted: 'मूल्यांकन सफलतापूर्वक पूरा हुआ!',
      onboardingCompleted: 'ऑनबोर्डिंग पूर्ण! सेहत्ति में आपका स्वागत है!'
    }
  }
} 