import type { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import type { SerializedError } from '@reduxjs/toolkit';

/**
 * Type guard to check if error is a FetchBaseQueryError
 */
function isFetchBaseQueryError(error: unknown): error is FetchBaseQueryError {
  return typeof error === 'object' && error != null && 'status' in error;
}

/**
 * Type guard to check if error is a SerializedError
 */
function isSerializedError(error: unknown): error is SerializedError {
  return typeof error === 'object' && error != null && 'message' in error;
}

/**
 * Safely extract error message from RTK Query error
 */
export function getErrorMessage(error: FetchBaseQueryError | SerializedError | undefined): string {
  if (!error) return 'An unknown error occurred';

  if (isFetchBaseQueryError(error)) {
    // Handle FetchBaseQueryError
    if (error.data && typeof error.data === 'object') {
      // Try to extract message from various possible structures
      const data = error.data as any;
      
      if ('message' in data) {
        return String(data.message);
      }
      
      if ('detail' in data) {
        // Handle FastAPI validation errors
        if (Array.isArray(data.detail)) {
          const firstError = data.detail[0];
          if (firstError && firstError.msg) {
            return `${firstError.loc?.join(' ') || 'Field'}: ${firstError.msg}`;
          }
        }
        return String(data.detail);
      }
      
      if ('error' in data) {
        return String(data.error);
      }
      
      // If it's an object but no recognizable message, show a generic error
      return `Error ${error.status}: Unable to process request`;
    }
    
    // Handle specific HTTP status codes with user-friendly messages
    if (error.status === 404) {
      return 'No account found with this email address';
    }
    if (error.status === 429) {
      return 'Too many requests. Please try again later';
    }
    if (error.status === 500) {
      return 'Server error. Please try again later';
    }
    
    // For non-object data, convert safely to string
    return `Error ${error.status}: ${error.data || 'Request failed'}`;
  }

  if (isSerializedError(error)) {
    // Handle SerializedError
    return error.message || 'An error occurred';
  }

  return 'An unknown error occurred';
} 