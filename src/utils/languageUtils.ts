import React from 'react'

// Language utility for handling multi-language support across the app
export type SupportedLanguage = 'EN' | 'AR' | 'HI'

export interface LanguageConfig {
  code: SupportedLanguage
  name: string
  nativeName: string
  flag: string
  isRTL: boolean
  direction: 'ltr' | 'rtl'
}

// Supported languages configuration
export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  {
    code: 'EN',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    isRTL: false,
    direction: 'ltr'
  },
  {
    code: 'AR',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    isRTL: true,
    direction: 'rtl'
  },
  {
    code: 'HI',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    isRTL: false,
    direction: 'ltr'
  }
]

// Storage key for persisting language preference
const LANGUAGE_STORAGE_KEY = 'sehatti-user-language'

// Language utility class
export class LanguageManager {
  private static instance: LanguageManager
  private currentLanguage: SupportedLanguage = 'EN'
  private listeners: ((language: SupportedLanguage) => void)[] = []

  private constructor() {
    this.loadPersistedLanguage()
  }

  static getInstance(): LanguageManager {
    if (!LanguageManager.instance) {
      LanguageManager.instance = new LanguageManager()
    }
    return LanguageManager.instance
  }

  // Load language from localStorage
  private loadPersistedLanguage(): void {
    try {
      const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (stored && this.isValidLanguage(stored)) {
        this.currentLanguage = stored as SupportedLanguage
      } else {
        // Auto-detect browser language
        this.currentLanguage = this.detectBrowserLanguage()
      }
    } catch (error) {
      console.warn('Failed to load persisted language:', error)
      this.currentLanguage = 'EN'
    }
  }

  // Detect browser language
  private detectBrowserLanguage(): SupportedLanguage {
    const browserLang = navigator.language || navigator.languages?.[0] || 'en'
    
    if (browserLang.startsWith('ar')) return 'AR'
    if (browserLang.startsWith('hi')) return 'HI'
    return 'EN'
  }

  // Validate if language is supported
  private isValidLanguage(lang: string): boolean {
    return SUPPORTED_LANGUAGES.some(config => config.code === lang)
  }

  // Get current language
  getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage
  }

  // Get language configuration
  getLanguageConfig(language?: SupportedLanguage): LanguageConfig {
    const lang = language || this.currentLanguage
    return SUPPORTED_LANGUAGES.find(config => config.code === lang) || SUPPORTED_LANGUAGES[0]
  }

  // Set language and persist
  setLanguage(language: SupportedLanguage): void {
    if (!this.isValidLanguage(language)) {
      console.warn(`Invalid language: ${language}`)
      return
    }

    // Prevent unnecessary updates if language is already set
    if (this.currentLanguage === language) {
      return
    }

    this.currentLanguage = language
    
    // Persist to localStorage
    try {
      localStorage.setItem(LANGUAGE_STORAGE_KEY, language)
    } catch (error) {
      console.warn('Failed to persist language:', error)
    }

    // Use requestAnimationFrame to prevent blocking the main thread
    requestAnimationFrame(() => {
      // Update document direction for RTL support
      this.updateDocumentDirection()

      // Notify listeners
      this.notifyListeners()

      // In real implementation, would call API to update user preference
      this.updateUserLanguagePreference(language)
    })
  }

  // Update document direction for RTL support
  private updateDocumentDirection(): void {
    const config = this.getLanguageConfig()
    
    // Force immediate DOM updates
    document.documentElement.dir = config.direction
    document.documentElement.lang = config.code.toLowerCase()
    
    // Add RTL class to both html and body for CSS styling
    if (config.isRTL) {
      document.documentElement.classList.add('rtl')
      document.documentElement.classList.remove('ltr')
      document.body.classList.add('rtl')
      document.body.classList.remove('ltr')
      
      // Force CSS recalculation
      document.body.style.direction = 'rtl'
    } else {
      document.documentElement.classList.add('ltr')
      document.documentElement.classList.remove('rtl')
      document.body.classList.add('ltr')
      document.body.classList.remove('rtl')
      
      // Force CSS recalculation
      document.body.style.direction = 'ltr'
    }
    
    // Force browser to repaint
    document.body.offsetHeight // Trigger reflow
  }

  // Update user language preference via API
  private async updateUserLanguagePreference(language: SupportedLanguage): Promise<void> {
    try {
      // In real implementation, this would call the API
      // await fetch('/api/v2/users/language-preference', {
      //   method: 'PATCH',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${getAuthToken()}`
      //   },
      //   body: JSON.stringify({ language })
      // })
      
      console.log(`Language preference updated to: ${language}`)
    } catch (error) {
      console.error('Failed to update language preference:', error)
    }
  }

  // Subscribe to language changes
  subscribe(callback: (language: SupportedLanguage) => void): () => void {
    this.listeners.push(callback)
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback)
    }
  }

  // Notify all listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentLanguage)
      } catch (error) {
        console.error('Error in language change listener:', error)
      }
    })
  }

  // Check if current language is RTL
  isRTL(): boolean {
    return this.getLanguageConfig().isRTL
  }

  // Get RTL-aware CSS classes
  getRTLClasses(): {
    direction: string
    textAlign: string
    flexDirection: string
    spaceDirection: string
  } {
    const isRTL = this.isRTL()
    return {
      direction: isRTL ? 'rtl' : 'ltr',
      textAlign: isRTL ? 'text-right' : 'text-left',
      flexDirection: isRTL ? 'flex-row-reverse' : 'flex-row',
      spaceDirection: isRTL ? 'space-x-reverse' : ''
    }
  }

  // Format text based on language direction
  formatText(text: string): string {
    const config = this.getLanguageConfig()
    
    // Add any language-specific text formatting here
    if (config.code === 'AR') {
      // Arabic text formatting
      return text
    } else if (config.code === 'HI') {
      // Hindi text formatting  
      return text
    }
    
    return text
  }

  // Get language-specific number formatting
  formatNumber(number: number): string {
    const config = this.getLanguageConfig()
    
    try {
      return new Intl.NumberFormat(config.code === 'AR' ? 'ar-SA' : 
                                   config.code === 'HI' ? 'hi-IN' : 'en-US')
        .format(number)
    } catch (error) {
      return number.toString()
    }
  }

  // Get language-specific date formatting
  formatDate(date: Date): string {
    const config = this.getLanguageConfig()
    
    try {
      return new Intl.DateTimeFormat(config.code === 'AR' ? 'ar-SA' : 
                                     config.code === 'HI' ? 'hi-IN' : 'en-US')
        .format(date)
    } catch (error) {
      return date.toLocaleDateString()
    }
  }

  // Initialize language support for the app
  initialize(): void {
    this.updateDocumentDirection()
    // Removed MutationObserver to prevent infinite loop when changing language
  }
}

// Utility functions for easy access
export const languageManager = LanguageManager.getInstance()

export const getCurrentLanguage = (): SupportedLanguage => {
  return languageManager.getCurrentLanguage()
}

export const setLanguage = (language: SupportedLanguage): void => {
  languageManager.setLanguage(language)
}

export const isRTL = (): boolean => {
  return languageManager.isRTL()
}

export const getRTLClasses = () => {
  return languageManager.getRTLClasses()
}

export const getLanguageConfig = (language?: SupportedLanguage): LanguageConfig => {
  return languageManager.getLanguageConfig(language)
}

export const subscribeToLanguageChanges = (callback: (language: SupportedLanguage) => void): (() => void) => {
  return languageManager.subscribe(callback)
}

// Hook for React components
export const useLanguage = () => {
  const [currentLanguage, setCurrentLanguage] = React.useState<SupportedLanguage>(
    languageManager.getCurrentLanguage()
  )

  React.useEffect(() => {
    const unsubscribe = languageManager.subscribe(setCurrentLanguage)
    return unsubscribe
  }, [])

  const changeLanguage = (language: SupportedLanguage) => {
    languageManager.setLanguage(language)
  }

  const config = languageManager.getLanguageConfig()
  const rtlClasses = languageManager.getRTLClasses()

  return {
    currentLanguage,
    changeLanguage,
    isRTL: config.isRTL,
    config,
    rtlClasses,
    formatNumber: languageManager.formatNumber.bind(languageManager),
    formatDate: languageManager.formatDate.bind(languageManager),
    formatText: languageManager.formatText.bind(languageManager)
  }
}

// Initialize language manager
languageManager.initialize() 