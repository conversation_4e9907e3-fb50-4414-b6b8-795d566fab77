// Navigation translations for all user roles
export interface NavigationTranslations {
  [key: string]: {
    EN: string
    AR: string
    HI: string
  }
}

export const NAV_TRANSLATIONS: NavigationTranslations = {
  // Common items
  dashboard: {
    EN: 'Dashboard',
    AR: 'لوحة التحكم',
    HI: 'डैशबोर्ड'
  },
  analytics: {
    EN: 'Analytics',
    AR: 'التحليلات',
    HI: 'एनालिटिक्स'
  },
  
  // System Admin
  corporates: {
    EN: 'Corporates',
    AR: 'الشركات',
    HI: 'कंपनियां'
  },
  qa: {
    EN: 'QA',
    AR: 'ضمان الجودة',
    HI: 'क्यूए'
  },
  media: {
    EN: 'Media',
    AR: 'الوسائط',
    HI: 'मीडिया'
  },
  automation: {
    EN: 'Automation',
    AR: 'الأتمتة',
    HI: 'ऑटोमेशन'
  },
  consultants: {
    EN: 'Consultants',
    AR: 'المستشارون',
    HI: 'सलाहकार'
  },
  invitations: {
    EN: 'Invitations',
    AR: 'الدعوات',
    HI: 'निमंत्रण'
  },
  recommendations: {
    EN: 'Recommendations',
    AR: 'التوصيات',
    HI: 'सिफारिशें'
  },
  
  // HR Admin
  employees: {
    EN: 'Employees',
    AR: 'الموظفون',
    HI: 'कर्मचारी'
  },
  notifications: {
    EN: 'Notifications',
    AR: 'الإشعارات',
    HI: 'सूचनाएं'
  },
  
  // Employee
  dailyCheckin: {
    EN: 'Daily Check-in',
    AR: 'التسجيل اليومي',
    HI: 'दैनिक चेक-इन'
  },
  mediaHub: {
    EN: 'Media Hub',
    AR: 'مركز الوسائط',
    HI: 'मीडिया हब'
  },
  consultantChat: {
    EN: 'Consultant Chat',
    AR: 'محادثة المستشار',
    HI: 'सलाहकार चैट'
  },
  myProfile: {
    EN: 'My Profile',
    AR: 'ملفي الشخصي',
    HI: 'मेरी प्रोफ़ाइल'
  }
};

// Translation helper function
export const getNavTranslation = (key: string, language: 'EN' | 'AR' | 'HI'): string => {
  return NAV_TRANSLATIONS[key]?.[language] || NAV_TRANSLATIONS[key]?.EN || key;
}; 