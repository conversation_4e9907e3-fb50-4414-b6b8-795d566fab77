interface EnvironmentConfig {
  apiUrl: string;
  wsUrl: string;
  mediaServiceUrl: string;
  qaServiceUrl: string;
  env: string;
}

const environments: Record<string, EnvironmentConfig> = {
  development: {
    apiUrl: 'https://apis.dev.sehatti.app',
    wsUrl: 'wss://apis.dev.sehatti.app/ws',
    mediaServiceUrl: 'https://apis.dev.sehatti.app',  // Updated: base URL only
    qaServiceUrl: 'https://apis.dev.sehatti.app',     // Updated: base URL only
    env: 'development',
  },
  production: {
    apiUrl: 'https://apis.main.sehatti.app',
    wsUrl: 'wss://apis.main.sehatti.app/ws',
    mediaServiceUrl: 'https://apis.main.sehatti.app',  // Updated: base URL only
    qaServiceUrl: 'https://apis.main.sehatti.app',     // Updated: base URL only
    env: 'production',
  },
};

/**
 * Get the current environment based on VITE_ENV environment variable
 * Priority: VITE_ENV > hostname detection > default to development
 */
export const getEnvironment = (): string => {
  // Check for environment variable first (set by GitHub Actions)
  if (import.meta.env.VITE_ENV) {
    const env = import.meta.env.VITE_ENV as string;
    console.log(`🌍 Environment detected from VITE_ENV: ${env}`);
    return env;
  }

  // Fallback to hostname-based detection for local development
  const hostname = window.location.hostname;
  
  if (hostname === 'dev.sehatti.app' || hostname.includes('dev.sehatti.app')) {
    console.log('🌍 Environment detected from hostname: development');
    return 'development';
  }
  
  if (hostname === 'sehatti.app' || hostname === 'main.sehatti.app' || hostname.includes('main.sehatti.app')) {
    console.log('🌍 Environment detected from hostname: production');
    return 'production';
  }

  // Default to development for unknown hostnames
  console.log('🌍 Environment defaulted to: development');
  return 'development';
};

/**
 * Get the API URL for the current environment
 * Priority: VITE_API_URL (from GitHub Actions) > environment config
 */
export const getApiUrl = (): string => {
  // If VITE_API_URL is set (by GitHub Actions), use it directly
  if (import.meta.env.VITE_API_URL) {
    const apiUrl = import.meta.env.VITE_API_URL as string;
    console.log(`🔗 API URL from VITE_API_URL: ${apiUrl}`);
    return apiUrl;
  }
  
  // Otherwise use environment-based URL
  const env = getEnvironment();
  const apiUrl = environments[env]?.apiUrl || environments.development.apiUrl;
  console.log(`🔗 API URL from environment config: ${apiUrl}`);
  return apiUrl;
};

/**
 * Get the WebSocket URL for the current environment
 */
export const getWsUrl = (): string => {
  // If explicit WS URL is set, use it
  if (import.meta.env.VITE_WS_URL) {
    return import.meta.env.VITE_WS_URL as string;
  }
  
  // Otherwise derive from API URL or environment config
  const apiUrl = getApiUrl();
  const wsUrl = apiUrl.replace(/^https?:\/\//, 'wss://') + '/ws';
  console.log(`🔌 WebSocket URL: ${wsUrl}`);
  return wsUrl;
};

/**
 * Get the Media Service URL for the current environment
 * Media service is accessible at: {baseUrl}/api/media/*
 */
export const getMediaServiceUrl = (): string => {
  // If explicit Media Service URL is set, use it
  if (import.meta.env.VITE_MEDIA_SERVICE_URL) {
    return import.meta.env.VITE_MEDIA_SERVICE_URL as string;
  }
  
  // Return base URL - the /api/media path will be added in the API service
  const apiUrl = getApiUrl();
  console.log(`📺 Media Service Base URL: ${apiUrl}`);
  return apiUrl;
};

/**
 * Get the QA Service URL for the current environment  
 * QA service is accessible at: {baseUrl}/api/qa/*
 */
export const getQaServiceUrl = (): string => {
  // If explicit QA Service URL is set, use it
  if (import.meta.env.VITE_QA_SERVICE_URL) {
    return import.meta.env.VITE_QA_SERVICE_URL as string;
  }
  
  // Return base URL - the /api/qa/v1 path will be added in the API service
  const apiUrl = getApiUrl();
  console.log(`❓ QA Service Base URL: ${apiUrl}`);
  return apiUrl;
};

/**
 * Get the full API configuration for the current environment
 */
export const getApiConfig = () => {
  return {
    apiUrl: getApiUrl(),
    wsUrl: getWsUrl(),
    mediaServiceUrl: getMediaServiceUrl(),
    qaServiceUrl: getQaServiceUrl(),
    environment: getEnvironment(),
  };
};

// Helper functions for media URLs
export const getVideoStreamUrl = (mediaId: string) => {
  return `${getMediaServiceUrl()}/api/media/v1/stream/video/${mediaId}`;
};

export const getThumbnailStreamUrl = (mediaId: string) => {
  return `${getMediaServiceUrl()}/api/media/v1/stream/video/${mediaId}/thumbnail`;
};

// Export the full config for the current environment
export const config = (): EnvironmentConfig => {
  const env = getEnvironment();
  return environments[env] || environments.development;
}; 