import type { ReactNode } from 'react'
import { useMemo } from 'react'
import { Navigate } from 'react-router-dom'
import { useGetMeQuery } from '@/store/api'
import { useAppSelector } from '@/store/store'
import { selectAuth } from '@/features/auth/auth-slice'
import { Spinner } from '@/components/ui/Spinner'

interface ProtectedRouteProps {
  children: ReactNode
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isAuthenticated, user, token } = useAppSelector(selectAuth)
  
  // Memoize the skip condition to prevent infinite re-renders
  const shouldSkipQuery = useMemo(() => {
    return !!(isAuthenticated && user?.id && token)
  }, [isAuthenticated, user?.id, token])

  // Always call API query hook, skip if local auth
  const { data: userData, isLoading } = useGetMeQuery({}, { skip: shouldSkipQuery })

  // console.log('ProtectedRoute - Auth state:', { isAuthenticated, user: user?.id, token: !!token })
  // console.log('ProtectedRoute - Should skip query:', shouldSkipQuery)
  // console.log('ProtectedRoute - Fallback API data:', userData)
  // console.log('ProtectedRoute - Fallback API loading:', isLoading)
  // console.log('ProtectedRoute - Fallback API error:', error)

  // If we have local auth state, use it (following old working pattern)
  if (shouldSkipQuery) {
    console.log('ProtectedRoute - Using local auth state, rendering children')
    return <>{children}</>
  }



  // Show loading spinner while checking authentication via API
  if (isLoading) {
    console.log('ProtectedRoute - Showing loading spinner for API fallback')
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  // If API succeeds, check the nested user data
  if (userData?.data?.user) {
    const user = userData.data.user
    console.log('ProtectedRoute - API fallback successful, user:', user)
    return <>{children}</>
  }

  // No auth found - redirect to login
  console.log('ProtectedRoute - No authentication found, redirecting to login')
  return <Navigate to="/login" replace />
}

export default ProtectedRoute 