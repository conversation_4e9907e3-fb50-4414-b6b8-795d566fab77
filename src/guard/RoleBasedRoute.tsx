import type { ReactNode } from 'react'
import { useMemo } from 'react'
import { Navigate } from 'react-router-dom'
import { useGetMeQuery } from '@/store/api'
import { useAppSelector } from '@/store/store'
import { selectAuth } from '@/features/auth/auth-slice'
import { Spinner } from '@/components/ui/Spinner'
import { Alert } from '@/components/ui/Alert'

interface RoleBasedRouteProps {
  children: ReactNode
  allowedRoles: string[]
  fallbackPath?: string
}

const RoleBasedRoute = ({ 
  children, 
  allowedRoles, 
  fallbackPath = '/dashboard' 
}: RoleBasedRouteProps) => {
  const { isAuthenticated, user, token } = useAppSelector(selectAuth)
  
  // Memoize the skip condition to prevent infinite re-renders
  const shouldSkipQuery = useMemo(() => {
    return !!(isAuthenticated && user?.id && token)
  }, [isAuthenticated, user?.id, token])

  // Always call API query hook, skip if local auth
  const { data: userData, isLoading, error } = useGetMeQuery({}, { skip: shouldSkipQuery })

  // If we have local auth state, use it
  if (shouldSkipQuery && user) {
    const userRole = user.role
    
    if (allowedRoles.includes(userRole)) {
      return <>{children}</>
    } else {
      // Redirect to fallback path if role not allowed
      return <Navigate to={fallbackPath} replace />
    }
  }

  // Show loading spinner while checking authentication via API
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  // If API succeeds, check the nested user data
  if (userData?.data?.user) {
    const userRole = userData.data.user.role
    
    if (allowedRoles.includes(userRole)) {
      return <>{children}</>
    } else {
      // Redirect to fallback path if role not allowed
      return <Navigate to={fallbackPath} replace />
    }
  }

  // No auth found or error - redirect to login
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sehatti-warm-gray-50 dark:from-sehatti-warm-gray-950 dark:to-sehatti-warm-gray-900 flex items-center justify-center p-4">
        <Alert variant="destructive" title="Authentication Error">
          Failed to verify your permissions. Please log in again.
        </Alert>
      </div>
    )
  }

  return <Navigate to="/login" replace />
}

export default RoleBasedRoute
