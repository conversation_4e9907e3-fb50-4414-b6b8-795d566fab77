import { configureStore } from '@reduxjs/toolkit'
import { useDispatch, useSelector } from 'react-redux'
import { setupListeners } from '@reduxjs/toolkit/query'

// Unified API
import { baseApi, qaServiceApi, mediaServiceApi } from './api/baseApi'

// Slices
import authSlice from '../features/auth/auth-slice'

// Legacy APIs (to be migrated)
import { qaServiceApi as legacyQaServiceApi } from '../features/API/qaServiceApi'
import { mediaServiceApi as legacyMediaServiceApi } from '../features/API/mediaServiceApi'

export const store = configureStore({
  reducer: {
    // Auth slice
    auth: authSlice,
    
    // New unified API services
    [baseApi.reducerPath]: baseApi.reducer,
    [qaServiceApi.reducerPath]: qaServiceApi.reducer,
    [mediaServiceApi.reducerPath]: mediaServiceApi.reducer,
    
    // Legacy APIs (to be removed after migration)
    [legacyQaServiceApi.reducerPath]: legacyQaServiceApi.reducer,
    [legacyMediaServiceApi.reducerPath]: legacyMediaServiceApi.reducer,
  },
  
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // RTK Query actions
          'api/executeQuery/pending',
          'api/executeQuery/fulfilled',
          'api/executeQuery/rejected',
          'api/executeMutation/pending',
          'api/executeMutation/fulfilled',
          'api/executeMutation/rejected',
        ],
      },
    })
      .concat(baseApi.middleware)
      .concat(qaServiceApi.middleware)
      .concat(mediaServiceApi.middleware)
      .concat(legacyQaServiceApi.middleware)
      .concat(legacyMediaServiceApi.middleware),
})

// Enable listener behavior for the store
setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Typed hooks
export const useAppDispatch = useDispatch.withTypes<AppDispatch>()
export const useAppSelector = useSelector.withTypes<RootState>() 