import type { BaseQueryFn } from '@reduxjs/toolkit/query';
import axios, { type AxiosRequestConfig, type AxiosError } from 'axios';

interface BaseQueryConfig {
  baseUrl: string;
}

export const axiosBaseQuery = (
  { baseUrl }: BaseQueryConfig
): BaseQueryFn<
  {
    url: string;
    method?: AxiosRequestConfig['method'];
    data?: AxiosRequestConfig['data'];
    params?: AxiosRequestConfig['params'];
    headers?: AxiosRequestConfig['headers'];
  },
  unknown,
  unknown
> =>
  async ({ url, method = 'GET', data, params, headers }) => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('hr-auth-token');
      
      // Debug authentication
      console.log('🔍 BaseQuery Debug:', {
        url,
        method,
        hasToken: !!token,
        tokenPreview: token ? `${token.substring(0, 20)}...` : null
      });
      
      // Properly handle URL concatenation to avoid double slashes
      const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
      const normalizedUrl = url.startsWith('/') ? url : `/${url}`;
      const fullUrl = normalizedBaseUrl + normalizedUrl;

      // Prepare headers - don't set Content-Type for FormData
      const requestHeaders: Record<string, string> = {
        ...(token && { Authorization: `Bearer ${token}` }),
        ...(headers as Record<string, string>),
      };
      
      // Only set Content-Type for non-FormData requests
      if (!(data instanceof FormData)) {
        requestHeaders['Content-Type'] = 'application/json';
      }

      const result = await axios({
        url: fullUrl,
        method,
        data,
        params,
        headers: requestHeaders,
      });
      
      console.log('✅ BaseQuery Success:', { url, status: result.status });
      return { data: result.data };
    } catch (axiosError) {
      const err = axiosError as AxiosError;
      console.error('❌ BaseQuery Error:', {
        url,
        status: err.response?.status,
        message: err.message,
        data: err.response?.data
      });
      
      // Handle 401 Unauthorized - clear invalid token and redirect to login
      if (err.response?.status === 401) {
        console.warn('🔒 Token expired or invalid, clearing auth state...');
        
        // Clear invalid token from localStorage
        localStorage.removeItem('hr-auth-token');
        localStorage.removeItem('hr-auth-user');
        localStorage.removeItem('hr-auth-support');
        
        // Redirect to login page if not already there
        if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
          console.log('🔄 Redirecting to login page...');
          window.location.href = '/login';
        }
      }
      
      return {
        error: {
          status: err.response?.status,
          data: err.response?.data || err.message,
        },
      };
    }
  }; 