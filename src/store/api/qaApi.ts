import { qaService<PERSON><PERSON> } from './baseApi';
import type { ApiResponse } from '../../types/api';

// Translated Content Interface
export interface TranslatedContent {
  en: string;
  ar?: string;
  hi?: string;
}

// Division Types
export interface Division {
  id: string;
  name: TranslatedContent;
  description?: TranslatedContent;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateDivisionRequest {
  name: string; // English name, auto-translated to other languages
  description?: string; // English description, auto-translated
}

export interface UpdateDivisionRequest {
  name?: Record<string, string>; // Multilingual name
  description?: Record<string, string>; // Multilingual description
  is_active?: boolean;
}

// SubDivision Types
export interface SubDivision {
  id: string;
  division_id: string;
  name: TranslatedContent;
  description?: TranslatedContent;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateSubDivisionRequest {
  division_id: string;
  name: string; // English name, auto-translated
  description?: string; // English description, auto-translated
}

export interface UpdateSubDivisionRequest {
  division_id?: string;
  name?: Record<string, string>;
  description?: Record<string, string>;
  is_active?: boolean;
}

// Question Types
export type QuestionType = 'pre_assessment' | 'post_assessment' | 'ongoing';
export type QuestionStatus = 'active' | 'draft' | 'inactive';

export interface QuestionOption {
  text: TranslatedContent;
  value: number;
}

export interface Question {
  id: string;
  division_id: string;
  subdivision_id: string;
  question_type: QuestionType;
  question_text: TranslatedContent;
  options: QuestionOption[];
  corporate_ids: string[];
  status: QuestionStatus;
  created_at: string;
  updated_at: string;
  division?: Division;
  subdivision?: SubDivision;
}

export interface QuestionOptionRequest {
  text: string; // English text, auto-translated
  value: number;
}

export interface CreateQuestionRequest {
  division_name?: string; // Auto-creates division if not exists
  subdivision_name?: string; // Auto-creates subdivision if not exists
  question_type: QuestionType;
  question_text: string; // English text, auto-translated
  options: QuestionOptionRequest[];
  corporate_ids?: string[];
  status?: QuestionStatus;
}

export interface UpdateQuestionRequest {
  division_id?: string;
  subdivision_id?: string;
  question_type?: QuestionType;
  question_text?: TranslatedContent;
  options?: QuestionOptionRequest[];
  corporate_ids?: string[];
  status?: QuestionStatus;
}

// Broadcast Campaign Types
export type BroadcastStatus = 'active' | 'inactive' | 'completed';
export type RecurrenceType = 'daily' | 'weekly' | 'custom';
export type WeekdayPattern = 'work_days_5' | 'full_week_7' | 'custom_days';

export interface TimeSlotInfo {
  time: string; // HH:MM format
  questions_count: number;
}

export interface BroadcastCampaign {
  id: string;
  name: TranslatedContent;
  description?: TranslatedContent;
  status: BroadcastStatus;
  question_pool_ids: string[];
  schedule: Record<string, any>; // Schedule configuration
  distribution: Record<string, any>; // Distribution configuration
  created_at: string;
  updated_at: string;
}

export interface CreateBroadcastCampaignRequest {
  name: string; // English name, auto-translated
  description?: string; // English description, auto-translated
  question_pool_ids: string[];
  schedule: {
    recurrence_type: RecurrenceType;
    start_date: string; // YYYY-MM-DD
    end_date?: string; // YYYY-MM-DD
    weekday_pattern?: WeekdayPattern;
    custom_weekdays?: number[]; // 0=Sunday, 1=Monday, etc.
    time_slots?: TimeSlotInfo[];
  };
  distribution: {
    corporate_ids: string[];
    target_audience?: string;
  };
}

export interface UpdateBroadcastCampaignRequest {
  name?: TranslatedContent;
  description?: TranslatedContent;
  status?: BroadcastStatus;
  question_pool_ids?: string[];
  schedule?: Record<string, any>;
  distribution?: Record<string, any>;
}

// QA API endpoints
export const qaApi = qaServiceApi.injectEndpoints({
  endpoints: (builder) => ({
    // Divisions Management
    getDivisions: builder.query<Division[], void>({
      query: () => ({
        url: '/divisions/',
        method: 'GET',
      }),
      providesTags: ['QADivision'],
    }),

    getDivision: builder.query<Division, string>({
      query: (divisionId) => ({
        url: `/divisions/${divisionId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'QADivision', id }],
    }),

    createDivision: builder.mutation<Division, CreateDivisionRequest>({
      query: (data) => ({
        url: '/divisions/',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['QADivision'],
    }),

    updateDivision: builder.mutation<void, { divisionId: string; data: UpdateDivisionRequest }>({
      query: ({ divisionId, data }) => ({
        url: `/divisions/${divisionId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: ['QADivision'],
    }),

    deleteDivision: builder.mutation<void, { divisionId: string; hardDelete?: boolean }>({
      query: ({ divisionId, hardDelete = false }) => ({
        url: `/divisions/${divisionId}`,
        method: 'DELETE',
        params: { hard_delete: hardDelete },
      }),
      invalidatesTags: ['QADivision'],
    }),

    // SubDivisions Management
    getSubDivisions: builder.query<SubDivision[], { divisionId?: string }>({
      query: (params) => ({
        url: '/subdivisions/',
        method: 'GET',
        params,
      }),
      providesTags: ['QASubDivision'],
    }),

    getSubDivision: builder.query<SubDivision, string>({
      query: (subdivisionId) => ({
        url: `/subdivisions/${subdivisionId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'QASubDivision', id }],
    }),

    createSubDivision: builder.mutation<SubDivision, CreateSubDivisionRequest>({
      query: (data) => ({
        url: '/subdivisions/',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['QASubDivision', 'QADivision'],
    }),

    updateSubDivision: builder.mutation<void, { subdivisionId: string; data: UpdateSubDivisionRequest }>({
      query: ({ subdivisionId, data }) => ({
        url: `/subdivisions/${subdivisionId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: ['QASubDivision'],
    }),

    deleteSubDivision: builder.mutation<void, { subdivisionId: string; hardDelete?: boolean }>({
      query: ({ subdivisionId, hardDelete = false }) => ({
        url: `/subdivisions/${subdivisionId}`,
        method: 'DELETE',
        params: { hard_delete: hardDelete },
      }),
      invalidatesTags: ['QASubDivision'],
    }),

    // Questions Management
    getQuestions: builder.query<Question[], {
      division_id?: string;
      subdivision_id?: string;
      question_type?: QuestionType;
      status?: QuestionStatus;
      corporate_id?: string;
      page?: number;
      page_size?: number;
    }>({
      query: (params) => ({
        url: '/questions/',
        method: 'GET',
        params,
      }),
      providesTags: ['QAQuestion'],
    }),

    getQuestion: builder.query<Question, string>({
      query: (questionId) => ({
        url: `/questions/${questionId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'QAQuestion', id }],
    }),

    createQuestion: builder.mutation<Question, CreateQuestionRequest>({
      query: (data) => ({
        url: '/questions/',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['QAQuestion', 'QADivision', 'QASubDivision'],
    }),

    updateQuestion: builder.mutation<void, { questionId: string; data: UpdateQuestionRequest }>({
      query: ({ questionId, data }) => ({
        url: `/questions/${questionId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: ['QAQuestion'],
    }),

    deleteQuestion: builder.mutation<void, { questionId: string; hardDelete?: boolean }>({
      query: ({ questionId, hardDelete = false }) => ({
        url: `/questions/${questionId}`,
        method: 'DELETE',
        params: { hard_delete: hardDelete },
      }),
      invalidatesTags: ['QAQuestion'],
    }),

    // Broadcast Campaigns Management
    getBroadcastCampaigns: builder.query<BroadcastCampaign[], {
      corporate_id?: string;
      status?: string;
      page?: number;
      page_size?: number;
    }>({
      query: (params) => ({
        url: '/broadcast/campaigns/',
        method: 'GET',
        params,
      }),
      providesTags: ['QABroadcast'],
    }),

    getBroadcastCampaign: builder.query<BroadcastCampaign, string>({
      query: (campaignId) => ({
        url: `/broadcast/campaigns/${campaignId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'QABroadcast', id }],
    }),

    createBroadcastCampaign: builder.mutation<BroadcastCampaign, CreateBroadcastCampaignRequest>({
      query: (data) => ({
        url: '/broadcast/campaigns/',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    updateBroadcastCampaign: builder.mutation<void, { campaignId: string; data: UpdateBroadcastCampaignRequest }>({
      query: ({ campaignId, data }) => ({
        url: `/broadcast/campaigns/${campaignId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    deleteBroadcastCampaign: builder.mutation<void, { campaignId: string; hardDelete?: boolean }>({
      query: ({ campaignId, hardDelete = false }) => ({
        url: `/broadcast/campaigns/${campaignId}`,
        method: 'DELETE',
        params: { hard_delete: hardDelete },
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    // Campaign Actions
    startBroadcastCampaign: builder.mutation<void, string>({
      query: (campaignId) => ({
        url: `/broadcast/campaigns/${campaignId}/start`,
        method: 'POST',
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    stopBroadcastCampaign: builder.mutation<void, string>({
      query: (campaignId) => ({
        url: `/broadcast/campaigns/${campaignId}/stop`,
        method: 'POST',
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    pauseBroadcastCampaign: builder.mutation<void, string>({
      query: (campaignId) => ({
        url: `/broadcast/campaigns/${campaignId}/pause`,
        method: 'POST',
      }),
      invalidatesTags: ['QABroadcast'],
    }),

    resumeBroadcastCampaign: builder.mutation<void, string>({
      query: (campaignId) => ({
        url: `/broadcast/campaigns/${campaignId}/resume`,
        method: 'POST',
      }),
      invalidatesTags: ['QABroadcast'],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  // Divisions
  useGetDivisionsQuery,
  useGetDivisionQuery,
  useCreateDivisionMutation,
  useUpdateDivisionMutation,
  useDeleteDivisionMutation,

  // SubDivisions
  useGetSubDivisionsQuery,
  useGetSubDivisionQuery,
  useCreateSubDivisionMutation,
  useUpdateSubDivisionMutation,
  useDeleteSubDivisionMutation,

  // Questions
  useGetQuestionsQuery,
  useGetQuestionQuery,
  useCreateQuestionMutation,
  useUpdateQuestionMutation,
  useDeleteQuestionMutation,

  // Broadcast Campaigns
  useGetBroadcastCampaignsQuery,
  useGetBroadcastCampaignQuery,
  useCreateBroadcastCampaignMutation,
  useUpdateBroadcastCampaignMutation,
  useDeleteBroadcastCampaignMutation,

  // Campaign Actions
  useStartBroadcastCampaignMutation,
  useStopBroadcastCampaignMutation,
  usePauseBroadcastCampaignMutation,
  useResumeBroadcastCampaignMutation,
} = qaApi; 