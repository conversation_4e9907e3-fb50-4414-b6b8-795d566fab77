import { baseApi } from './baseApi';

// User API endpoints
export const userApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get Users with pagination and filters
    getUsers: builder.query<any, {
      page?: number;
      limit?: number;
      company_id?: string;
      role?: string;
      status?: string;
      search?: string;
    }>({
      query: (params) => ({
        url: '/users',
        method: 'GET',
        params,
      }),
      providesTags: ['User'],
    }),

    // Get User by ID
    getUser: builder.query<any, string>({
      query: (userId) => ({
        url: `/users/${userId}`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Create User
    createUser: builder.mutation<any, any>({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
    }),

    // Update User
    updateUser: builder.mutation<any, { userId: string; data: any }>({
      query: ({ userId, data }) => ({
        url: `/users/${userId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    // Delete User
    deleteUser: builder.mutation<any, string>({
      query: (userId) => ({
        url: `/users/${userId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
    }),

    // Get User by Email
    getUserByEmail: builder.query<any, string>({
      query: (email) => ({
        url: `/users/email/${email}`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Get User by Phone
    getUserByPhone: builder.query<any, string>({
      query: (phone) => ({
        url: `/users/phone/${phone}`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Get Users by Company
    getUsersByCompany: builder.query<any, string>({
      query: (companyId) => ({
        url: `/users/company/${companyId}`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Get Users by Role and Status
    getUsersByRoleAndStatus: builder.query<any, { role: string; status: string }>({
      query: ({ role, status }) => ({
        url: `/users/role/${role}/status/${status}`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Update User Status
    updateUserStatus: builder.mutation<any, { userId: string; new_status: string }>({
      query: ({ userId, new_status }) => ({
        url: `/users/${userId}/status`,
        method: 'PATCH',
        params: { new_status },
      }),
      invalidatesTags: ['User'],
    }),

    // Get User Count with filters
    getUserCount: builder.query<any, {
      company_id?: string;
      role?: string;
      status?: string;
    }>({
      query: (params) => ({
        url: '/users/statistics/count',
        method: 'GET',
        params,
      }),
      providesTags: ['User'],
    }),

    // Get User Roles
    getUserRoles: builder.query<any, void>({
      query: () => ({
        url: '/users/roles/list',
        method: 'GET',
      }),
    }),

    // Get User Statuses
    getUserStatuses: builder.query<any, void>({
      query: () => ({
        url: '/users/statuses/list',
        method: 'GET',
      }),
    }),

    // Get User Languages
    getUserLanguages: builder.query<any, void>({
      query: () => ({
        url: '/users/languages/list',
        method: 'GET',
      }),
    }),

    // Bulk Create Users
    createUsersBulk: builder.mutation<any, any[]>({
      query: (usersData) => ({
        url: '/users/bulk',
        method: 'POST',
        body: usersData,
      }),
      invalidatesTags: ['User'],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useGetUserByEmailQuery,
  useGetUserByPhoneQuery,
  useGetUsersByCompanyQuery,
  useGetUsersByRoleAndStatusQuery,
  useUpdateUserStatusMutation,
  useGetUserCountQuery,
  useGetUserRolesQuery,
  useGetUserStatusesQuery,
  useGetUserLanguagesQuery,
  useCreateUsersBulkMutation,
} = userApi; 