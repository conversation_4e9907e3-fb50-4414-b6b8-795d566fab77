import { baseApi } from './baseApi';
import type { ApiResponse } from '../../types/api';

// Invitation types
export interface Invitation {
  id: string;
  email: string;
  role: 'HR_ADMIN' | 'EMPLOYEE' | 'CONSULTANT';
  companyId: string;
  invitedBy: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED';
  expiresAt: string;
  acceptedAt?: string;
  rejectedAt?: string;
  createdAt: string;
  updatedAt: string;
  company?: {
    id: string;
    name: string;
  };
  inviter?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface InvitationCreateRequest {
  email: string;
  role: 'HR_ADMIN' | 'EMPLOYEE' | 'CONSULTANT';
  companyId?: string;
}

export interface InvitationResponse {
  invitation: Invitation;
  message: string;
}

// Invitation API endpoints
export const invitationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get invitations
    getInvitations: builder.query<ApiResponse<Invitation[]>, { 
      company_id: string;
      page?: number;
      limit?: number;
      search?: string;
      type?: string;
      session_type?: string;
      status?: string;
      priority?: string;
      language?: string;
      consultant_id?: string;
      event_manager_id?: string;
      category?: string;
      start_date?: string;
      end_date?: string;
      sort_by?: string;
      sort_order?: 'asc' | 'desc';
    }>({
      query: (params) => ({
        url: 'invitations',
        method: 'GET',
        params,
      }),
      providesTags: ['Invitation'],
    }),

    // Get single invitation
    getInvitation: builder.query<ApiResponse<Invitation>, { id: string; company_id: string }>({
      query: ({ id, company_id }) => ({
        url: `invitations/${id}`,
        method: 'GET',
        params: { company_id },
      }),
      providesTags: (result, error, { id }) => [{ type: 'Invitation', id }],
    }),

    // Create invitation
    createInvitation: builder.mutation<ApiResponse<InvitationResponse>, InvitationCreateRequest>({
      query: (data) => ({
        url: 'invitations',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Update invitation
    updateInvitation: builder.mutation<ApiResponse<Invitation>, { 
      id: string; 
      company_id: string;
      data: Partial<InvitationCreateRequest> 
    }>({
      query: ({ id, company_id, data }) => ({
        url: `invitations/${id}`,
        method: 'PATCH',
        params: { company_id },
        data,
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Resend invitation
    resendInvitation: builder.mutation<ApiResponse<InvitationResponse>, { 
      id: string; 
      company_id: string 
    }>({
      query: ({ id, company_id }) => ({
        url: `invitations/${id}/resend`,
        method: 'POST',
        params: { company_id },
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Cancel invitation
    cancelInvitation: builder.mutation<ApiResponse<void>, { 
      id: string; 
      company_id: string 
    }>({
      query: ({ id, company_id }) => ({
        url: `invitations/${id}/cancel`,
        method: 'POST',
        params: { company_id },
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Accept invitation
    acceptInvitation: builder.mutation<ApiResponse<{ token: string; user: any }>, { 
      invitationId: string; 
      company_id: string;
      password: string; 
      name: string;
    }>({
      query: ({ invitationId, company_id, ...data }) => ({
        url: `invitations/${invitationId}/accept`,
        method: 'POST',
        params: { company_id },
        data,
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Reject invitation
    rejectInvitation: builder.mutation<ApiResponse<void>, { 
      id: string; 
      company_id: string 
    }>({
      query: ({ id, company_id }) => ({
        url: `invitations/${id}/reject`,
        method: 'POST',
        params: { company_id },
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Get invitation by token (for public access)
    getInvitationByToken: builder.query<ApiResponse<Invitation>, string>({
      query: (token) => ({
        url: `invitations/token/${token}`,
        method: 'GET',
      }),
    }),

    // Delete invitation
    deleteInvitation: builder.mutation<ApiResponse<void>, { 
      id: string; 
      company_id: string 
    }>({
      query: ({ id, company_id }) => ({
        url: `invitations/${id}`,
        method: 'DELETE',
        params: { company_id },
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Bulk invite
    bulkInvite: builder.mutation<ApiResponse<{ 
      successful: InvitationResponse[];
      failed: { email: string; error: string }[];
    }>, {
      invitations: InvitationCreateRequest[];
    }>({
      query: (data) => ({
        url: 'invitations/bulk',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Invitation'],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useGetInvitationsQuery,
  useGetInvitationQuery,
  useCreateInvitationMutation,
  useUpdateInvitationMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useDeleteInvitationMutation,
  useAcceptInvitationMutation,
  useRejectInvitationMutation,
  useGetInvitationByTokenQuery,
  useBulkInviteMutation,
} = invitationApi; 