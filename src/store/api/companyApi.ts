import { baseApi } from './baseApi';

// Company types based on backend HR_ADMIN users
// Backend uses shared.UserStatus enum: PENDING, IN_PROGRESS, ACTIVE, REJECTED
export interface Company {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'ACTIVE' | 'REJECTED'; // Updated to match backend enum
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  company_settings?: CompanySettings;
}

export interface CompaniesResponse {
  items: Company[];
  total: number;
  limit: number;
  offset: number;
  has_next: boolean;
  has_previous: boolean;
}

// V2 Company Settings types
export interface CompanySettings {
  id: string;
  company_id: string; // Use snake_case to match backend
  name: string;
  logo?: string;
  noc?: string;
  start_date: number; // Unix timestamp
  end_date: number;   // Unix timestamp
  is_share_data: boolean; // Use snake_case to match backend
  is_enable_support: boolean;
  pre_webinar_banner?: string;
  post_webinar_banner?: string;
  consultants: string[];
  created_at: number;
  updated_at: number;
}

export interface CompanySettingsCreate {
  company_id: string;
  name: string;
  logo?: string;
  noc?: string;
  start_date: string;
  end_date: string;
  is_share_data: boolean;
  is_enable_support: boolean;
  pre_webinar_banner?: string;
  post_webinar_banner?: string;
  consultants?: string[];
}

export interface CompanySettingsUpdate {
  name?: string;
  logo?: string;
  noc?: string;
  start_date?: string;
  end_date?: string;
  is_share_data?: boolean;
  is_enable_support?: boolean;
  pre_webinar_banner?: string;
  post_webinar_banner?: string;
  consultants?: string[];
}

export interface CompanyStatusUpdateRequest {
  status: 'PENDING' | 'IN_PROGRESS' | 'ACTIVE' | 'REJECTED'; // Updated to match backend enum
}

// Company API endpoints
export const companyApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // ===== SYSTEM ADMIN ENDPOINTS (for company management) =====

    // Get all companies (HR_ADMIN users) with pagination and search
    getCompanies: builder.query<CompaniesResponse, {
      page?: number;
      limit?: number;
      search?: string;
      sort_by?: string;
      sort_order?: 'asc' | 'desc';
    }>({
      query: (params = {}) => ({
        url: '/system-admins/companies',
        method: 'GET',
        params: {
          page: params.page || 1,
          limit: params.limit || 10,
          search: params.search,
          sort_by: params.sort_by || 'created_at',
          sort_order: params.sort_order || 'desc',
        },
      }),
      providesTags: ['Company'],
    }),

    // Get specific company details
    getCompany: builder.query<Company, string>({
      query: (companyId) => ({
        url: `/system-admins/companies/${companyId}`,
        method: 'GET',
      }),
      providesTags: (result, error, companyId) => [{ type: 'Company', id: companyId }],
    }),

    // Update company status (triggers email notifications for pending → active)
    updateCompanyStatus: builder.mutation<Company, { companyId: string; status: string }>({
      query: ({ companyId, status }) => ({
        url: `/system-admins/companies/${companyId}/status`,
        method: 'PATCH',
        data: { status }, // Backend expects uppercase: "ACTIVE", "PENDING", etc.
      }),
      invalidatesTags: (result, error, { companyId }) => [
        { type: 'Company', id: companyId },
        'Company'
      ],
    }),

    // Get company employees 
    getCompanyEmployees: builder.query<any, {
      companyId: string;
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      department?: string;
    }>({
      query: ({ companyId, ...params }) => ({
        url: `/system-admins/companies/${companyId}/employees`,
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { companyId }) => [
        { type: 'Company', id: `employees-${companyId}` },
        'Employee'
      ],
    }),

    // Delete company (soft delete)
    deleteCompany: builder.mutation<{ message: string }, string>({
      query: (companyId) => ({
        url: `/system-admins/companies/${companyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Company'],
    }),

    // ===== LEGACY COMPANY SETTINGS ENDPOINTS =====

    // Get all company settings (legacy - for backwards compatibility)
    getAllCompanySettings: builder.query<CompanySettings[], void>({
      query: () => ({
        url: '/company-settings',
        method: 'GET',
      }),
      providesTags: ['CompanySettings'],
    }),

    // Create company settings
    createCompanySettings: builder.mutation<CompanySettings, CompanySettingsCreate>({
      query: (data) => ({
        url: '/company-settings',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['CompanySettings'],
    }),

    // Get company settings by ID
    getCompanySettingsById: builder.query<CompanySettings, string>({
      query: (settingsId) => ({
        url: `/company-settings/${settingsId}`,
        method: 'GET',
      }),
      providesTags: (result, error, settingsId) => [{ type: 'CompanySettings', id: settingsId }],
    }),

    // Get company settings by company ID
    getCompanySettingsByCompanyId: builder.query<CompanySettings, string>({
      query: (companyId) => ({
        url: `/company-settings/company/${companyId}`,
        method: 'GET',
      }),
      providesTags: (result, error, companyId) => [{ type: 'CompanySettings', id: `company-${companyId}` }],
    }),

    // Update company settings by company ID
    updateCompanySettingsByCompanyId: builder.mutation<CompanySettings, {
      companyId: string;
      data: CompanySettingsUpdate
    }>({
      query: ({ companyId, data }) => ({
        url: `/company-settings/company/${companyId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: (result, error, { companyId }) => [
        { type: 'CompanySettings', id: `company-${companyId}` },
        'CompanySettings'
      ],
    }),

    // Add consultant to company
    addConsultantToCompany: builder.mutation<any, { companyId: string; consultantId: string }>({
      query: ({ companyId, consultantId }) => ({
        url: `/company-settings/company/${companyId}/consultants/${consultantId}`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { companyId }) => [
        { type: 'CompanySettings', id: `company-${companyId}` },
        'CompanySettings'
      ],
    }),

    // Remove consultant from company
    removeConsultantFromCompany: builder.mutation<any, { companyId: string; consultantId: string }>({
      query: ({ companyId, consultantId }) => ({
        url: `/company-settings/company/${companyId}/consultants/${consultantId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { companyId }) => [
        { type: 'CompanySettings', id: `company-${companyId}` },
        'CompanySettings'
      ],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  // System Admin endpoints
  useGetCompaniesQuery,
  useLazyGetCompaniesQuery,
  useGetCompanyQuery,
  useUpdateCompanyStatusMutation,
  useGetCompanyEmployeesQuery,
  useDeleteCompanyMutation,

  // Legacy company settings endpoints
  useGetAllCompanySettingsQuery,
  useCreateCompanySettingsMutation,
  useGetCompanySettingsByIdQuery,
  useGetCompanySettingsByCompanyIdQuery,
  useUpdateCompanySettingsByCompanyIdMutation,
  useAddConsultantToCompanyMutation,
  useRemoveConsultantFromCompanyMutation,
} = companyApi;

// Legacy exports for backwards compatibility
export const {
  useGetCompaniesQuery: useGetCompaniesWithConsultantsQuery,
  useUpdateCompanyStatusMutation: useChangeCompanyStatusMutation,
  useDeleteCompanyMutation: useDeleteCorporateMutation,
} = companyApi; 