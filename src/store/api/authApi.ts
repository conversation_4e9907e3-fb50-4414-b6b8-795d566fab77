import { baseApi } from './baseApi';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  SetPasswordRequest,
  SetPasswordResponse,
  ChangePasswordRequest,
  ChangePasswordResponse,
  VerifyTokenRequest,
  VerifyTokenResponse,
  RefreshTokenResponse,
  UpdateProfileRequest,
  UpdateProfileResponse,
  User,
  ApiResponse,
} from '../../types/api';

// Auth API endpoints
export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        data: credentials,
      }),
      invalidatesTags: ['User', 'Auth'],
    }),

    register: builder.mutation<ApiResponse<RegisterResponse>, RegisterRequest>({
      query: (data) => ({
        url: '/hr-admins/register',
        method: 'POST',
        data: {
          name: data.name,
          company_name: data.company_name,
          email: data.email,
          phone: data.phone,
          password: data.password,
          confirmPassword: data.confirmPassword,
          is_share_data: data.is_share_data,
        },
      }),
      invalidatesTags: ['User', 'Auth'],
    }),

    getMe: builder.query<ApiResponse<{ user: User }>, void>({
      query: () => ({
        url: '/auth/me',
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    forgotPassword: builder.mutation<ApiResponse<ForgotPasswordResponse>, ForgotPasswordRequest>({
      query: (data) => ({
        url: '/auth/forgot-password',
        method: 'POST',
        data,
      }),
    }),

    resetPassword: builder.mutation<ApiResponse<ResetPasswordResponse>, ResetPasswordRequest>({
      query: (data) => ({
        url: '/auth/reset-password',
        method: 'POST',
        data,
      }),
    }),

    setPassword: builder.mutation<ApiResponse<SetPasswordResponse>, SetPasswordRequest & { role: 'employee' | 'consultant' }>({
      query: ({ role, ...data }) => ({
        url: `/${role}s/set-password`,
        method: 'POST',
        data,
      }),
    }),

    verifyToken: builder.mutation<ApiResponse<VerifyTokenResponse>, VerifyTokenRequest>({
      query: (data) => ({
        url: '/auth/verify-token',
        method: 'POST',
        data,
      }),
    }),

    changePassword: builder.mutation<ApiResponse<ChangePasswordResponse>, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        data,
      }),
    }),

    refreshToken: builder.mutation<ApiResponse<RefreshTokenResponse>, void>({
      query: () => ({
        url: '/auth/refresh',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Auth'],
    }),

    logout: builder.mutation<ApiResponse<void>, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Auth'],
    }),

    updateProfile: builder.mutation<ApiResponse<UpdateProfileResponse>, UpdateProfileRequest>({
      query: (data) => {
        if (data.avatar) {
          const formData = new FormData();
          if (data.name) formData.append('name', data.name);
          if (data.phone) formData.append('phone', data.phone);
          formData.append('avatar', data.avatar);

          return {
            url: '/auth/update-info',
            method: 'PATCH',
            data: formData,
          };
        }

        return {
          url: '/auth/update-info',
          method: 'PATCH',
          data,
        };
      },
      invalidatesTags: ['User'],
    }),

    updateAvatar: builder.mutation<ApiResponse<{ avatar: string }>, File>({
      query: (file) => {
        const formData = new FormData();
        formData.append('avatar', file);

        return {
          url: '/auth/update-avatar',
          method: 'PATCH',
          data: formData,
        };
      },
      invalidatesTags: ['User'],
    }),

    // Admin endpoints
    checkAdmin: builder.query<ApiResponse<{ exists: boolean }>, void>({
      query: () => ({
        url: '/system-admins/check',
        method: 'GET',
      }),
    }),

    createAdmin: builder.mutation<ApiResponse<User>, { name: string; email: string; password: string }>({
      query: (data) => ({
        url: '/system-admins/default',
        method: 'POST',
        data,
      }),
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useLoginMutation,
  useLogoutMutation,
  useRegisterMutation,
  useGetMeQuery,
  useLazyGetMeQuery,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useSetPasswordMutation,
  useVerifyTokenMutation,
  useChangePasswordMutation,
  useRefreshTokenMutation,
  useUpdateProfileMutation,
  useUpdateAvatarMutation,
  useCheckAdminQuery,
  useCreateAdminMutation,
} = authApi; 