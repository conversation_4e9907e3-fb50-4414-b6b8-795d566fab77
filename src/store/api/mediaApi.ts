import { mediaService<PERSON>pi } from './baseApi';
import type { ApiResponse } from '../../types/api';

// Media Types
export interface MediaFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  width?: number;
  height?: number;
  companyId: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface MediaUploadResponse {
  file: MediaFile;
  uploadUrl?: string;
}

export interface StreamingSession {
  id: string;
  title: string;
  description?: string;
  streamUrl: string;
  playbackUrl: string;
  status: 'preparing' | 'live' | 'ended' | 'error';
  companyId: string;
  createdBy: string;
  startTime?: string;
  endTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Caption {
  id: string;
  mediaId: string;
  language: string;
  content: string;
  startTime: number;
  endTime: number;
  createdAt: string;
  updatedAt: string;
}

// Content Types based on real API structure
export interface ContentItem {
  content_id: string;
  hub_id: string;
  title: {
    en: string;
    ar: string;
    hi: string;
  };
  description: {
    en: string;
    ar: string;
    hi: string;
  };
  tags: {
    en: string[];
    ar: string[];
    hi: string[];
  };
  video_id: string;
  thumbnail_id: string;
  video_url: string;
  thumbnail_url: string;
  subtitle_urls: Record<string, any>;
  subtitles_available: string[];
  available_languages: string[];
  gender: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ContentListResponse {
  data: ContentItem[];
  total: number;
  limit: number;
  last_key: string | null;
  has_more: boolean;
}

// Hub Types based on real API structure
export interface HubContent {
  title: string;
  description: string;
  translation_status: string;
  is_original: boolean;
  created_at: string;
  updated_at: string;
}

export interface Hub {
  hub_id: string;
  content: {
    en: HubContent;
    ar: HubContent;
    hi: HubContent;
  };
  image_url: string | null;
  category: string;
  status: string;
  content_count: number;
  created_at: string;
}

export interface HubCreateRequest {
  title: string;
  description?: string;
  category?: string;
  tags?: string[];
  visibility?: 'public' | 'private' | 'restricted';
  created_by?: string;
  auto_translate?: boolean;
  image?: File;
}

export interface HubUpdateRequest {
  id: string;
  data: {
    title?: string;
    description?: string;
    category?: string;
    tags?: string[];
    visibility?: 'public' | 'private' | 'restricted';
    image?: File;
  };
  language?: string;
}

export interface HubListResponse {
  data: Hub[];
  total: number;
  last_key?: string;
}

// Media API endpoints
export const mediaApi = mediaServiceApi.injectEndpoints({
  endpoints: (builder) => ({
    // File Upload
    uploadMedia: builder.mutation<ApiResponse<MediaUploadResponse>, FormData>({
      query: (formData) => ({
        url: 'upload',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['Media'],
    }),

    // Get Media Files
    getMediaFiles: builder.query<ApiResponse<MediaFile[]>, { companyId?: string; type?: string; page?: number; limit?: number }>({
      query: (params) => ({
        url: 'files',
        method: 'GET',
        params,
      }),
      providesTags: ['Media'],
    }),

    // Get Single Media File
    getMediaFile: builder.query<ApiResponse<MediaFile>, string>({
      query: (id) => ({
        url: `files/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Media', id }],
    }),

    // Delete Media File
    deleteMediaFile: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `files/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Media'],
    }),

    // Streaming
    createStreamingSession: builder.mutation<ApiResponse<StreamingSession>, Partial<StreamingSession>>({
      query: (data) => ({
        url: 'streaming/sessions',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Stream'],
    }),

    getStreamingSessions: builder.query<ApiResponse<StreamingSession[]>, { companyId?: string; status?: string }>({
      query: (params) => ({
        url: 'streaming/sessions',
        method: 'GET',
        params,
      }),
      providesTags: ['Stream'],
    }),

    getStreamingSession: builder.query<ApiResponse<StreamingSession>, string>({
      query: (id) => ({
        url: `streaming/sessions/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Stream', id }],
    }),

    updateStreamingSession: builder.mutation<ApiResponse<StreamingSession>, { id: string; data: Partial<StreamingSession> }>({
      query: ({ id, data }) => ({
        url: `streaming/sessions/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: ['Stream'],
    }),

    endStreamingSession: builder.mutation<ApiResponse<StreamingSession>, string>({
      query: (id) => ({
        url: `streaming/sessions/${id}/end`,
        method: 'POST',
      }),
      invalidatesTags: ['Stream'],
    }),

    // Captions
    getCaptions: builder.query<ApiResponse<Caption[]>, { mediaId: string; language?: string }>({
      query: (params) => ({
        url: 'captions',
        method: 'GET',
        params,
      }),
      providesTags: ['Caption'],
    }),

    createCaption: builder.mutation<ApiResponse<Caption>, Partial<Caption>>({
      query: (data) => ({
        url: 'captions',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Caption'],
    }),

    generateCaptions: builder.mutation<ApiResponse<Caption[]>, { mediaId: string; languages: string[] }>({
      query: (data) => ({
        url: 'captions/generate',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Caption'],
    }),

    // Processing
    processVideo: builder.mutation<ApiResponse<{ jobId: string }>, { mediaId: string; options?: any }>({
      query: (data) => ({
        url: 'processing/video',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Media'],
    }),

    getProcessingStatus: builder.query<ApiResponse<{ status: string; progress: number }>, string>({
      query: (jobId) => ({
        url: `processing/status/${jobId}`,
        method: 'GET',
      }),
    }),

    // Hubs
    getHubs: builder.query<ApiResponse<HubListResponse>, { limit?: number; last_key?: string; category?: string; status?: string; created_by?: string }>({
      query: (params) => ({
        url: 'v1/hubs',
        method: 'GET',
        params,
      }),
      providesTags: ['Hub'],
    }),

    getHub: builder.query<ApiResponse<Hub>, string>({
      query: (hubId) => ({
        url: `v1/hubs/${hubId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Hub', id }],
    }),

    addHub: builder.mutation<ApiResponse<Hub>, HubCreateRequest>({
      query: (data) => {
        const formData = new FormData();
        formData.append('title', data.title);
        if (data.description) formData.append('description', data.description);
        if (data.category) formData.append('category', data.category);
        if (data.tags && data.tags.length > 0) formData.append('tags', JSON.stringify(data.tags));
        if (data.visibility) formData.append('visibility', data.visibility);
        if (data.created_by) formData.append('created_by', data.created_by);
        if (data.auto_translate !== undefined) formData.append('auto_translate', data.auto_translate.toString());
        if (data.image) formData.append('image', data.image);

        return {
          url: 'v1/hubs',
          method: 'POST',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: ['Hub'],
    }),

    updateHub: builder.mutation<ApiResponse<Hub>, HubUpdateRequest>({
      query: ({ id, data, language = 'en' }) => {
        const formData = new FormData();
        formData.append('language', language);
        if (data.title) formData.append('title', data.title);
        if (data.description !== undefined) formData.append('description', data.description);
        if (data.category) formData.append('category', data.category);
        if (data.tags && data.tags.length > 0) formData.append('tags', JSON.stringify(data.tags));
        if (data.visibility) formData.append('visibility', data.visibility);
        if (data.image) formData.append('image', data.image);

        return {
          url: `v1/hubs/${id}`,
          method: 'PATCH',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: ['Hub'],
    }),

    deleteHub: builder.mutation<ApiResponse<void>, string>({
      query: (hubId) => ({
        url: `v1/hubs/${hubId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Hub'],
    }),

    // Content Endpoints
    getContent: builder.query<ContentListResponse, { page?: number; limit?: number; category?: string; status?: string }>({
      query: (params) => ({
        url: 'v1/media/content',
        method: 'GET',
        params,
      }),
      providesTags: ['Media'],
      transformResponse: (response: any) => {
        // Handle both direct data and wrapped response formats
        if (response.data) {
          return response; // Already in correct format
        }
        // If response is direct array, wrap it
        return {
          data: Array.isArray(response) ? response : [],
          total: Array.isArray(response) ? response.length : 0,
          limit: 20,
          last_key: null,
          has_more: false
        };
      },
    }),

    getContentById: builder.query<ApiResponse<ContentItem>, string>({
      query: (contentId) => ({
        url: `v1/content/${contentId}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Media', id }],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useUploadMediaMutation,
  useGetMediaFilesQuery,
  useGetMediaFileQuery,
  useDeleteMediaFileMutation,
  useCreateStreamingSessionMutation,
  useGetStreamingSessionsQuery,
  useGetStreamingSessionQuery,
  useUpdateStreamingSessionMutation,
  useEndStreamingSessionMutation,
  useGetCaptionsQuery,
  useCreateCaptionMutation,
  useGenerateCaptionsMutation,
  useProcessVideoMutation,
  useGetProcessingStatusQuery,
  // Hub hooks
  useGetHubsQuery,
  useGetHubQuery,
  useAddHubMutation,
  useUpdateHubMutation,
  useDeleteHubMutation,
  // Content hooks
  useGetContentQuery,
  useGetContentByIdQuery,
} = mediaApi; 