import { baseApi } from './baseApi';
import type { Employee, InviteEmployeeRequest, InviteEmployeeResponse, ApiResponse } from '../../types/api';

// Additional types for notifications
export interface NotificationRequest {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'urgent';
  targetEmployees: string[];
  sendToAll?: boolean;
}

export interface NotificationHistory {
  id: string;
  title: string;
  message: string;
  type: string;
  sentTo: number;
  sentAt: string;
  status: 'sent' | 'pending' | 'failed';
  deliveryRate: number;
}

export interface EmployeeStatsResponse {
  totalEmployees: number;
  activeEmployees: number;
  pendingEmployees: number;
  inactiveEmployees: number;
  suspendedEmployees: number;
}

// QR Code related types
export interface EmployeeQRCodeDto {
  id: string
  employee_id: string
  company_id: string
  qr_code_url: string
  qr_code_data: string
  verification_code: string
  is_active: boolean
  created_at: string
  expires_at?: string
}

export interface GenerateQRCodeDto {
  size?: number
  error_correction?: 'L' | 'M' | 'Q' | 'H'
  include_employee_data?: boolean
  expires_in_days?: number
}

export interface QRCodeVerificationDto {
  verification_code: string
  employee_id?: string
  company_id?: string
}

export interface QRCodeResponseDto {
  success: boolean
  message: string
  qr_code?: EmployeeQRCodeDto
}

// Employee API endpoints
export const employeeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all employees
    getEmployees: builder.query<ApiResponse<Employee[]>, { page?: number; limit?: number; search?: string }>({
      query: (params = {}) => ({
        url: '/hr-admins/my-employees',
        method: 'GET',
        params,
      }),
      providesTags: ['Employee'],
    }),

    // Get my employees (alias for getEmployees)
    getMyEmployees: builder.query<ApiResponse<Employee[]>, { page?: number; limit?: number; search?: string; status?: string }>({
      query: (params = {}) => ({
        url: '/hr-admins/my-employees',
        method: 'GET',
        params,
      }),
      providesTags: ['Employee'],
    }),

    // Get employee by ID
    getEmployee: builder.query<ApiResponse<Employee>, string>({
      query: (id) => ({
        url: `/hr-admins/my-employees/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Employee', id }],
    }),

    // Create employee (with correct payload structure)
    createEmployee: builder.mutation<ApiResponse<Employee>, { 
      name: string; 
      email: string; 
      phone?: string; 
      department?: string;
      company_id: string;
    }>({
      query: (data) => ({
        url: '/hr-admins/my-employees',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Employee'],
    }),

    // Update employee - Full employee update endpoint
    updateEmployee: builder.mutation<ApiResponse<Employee>, { 
      id: string; 
      name?: string; 
      email?: string;
      phone?: string; 
      department?: string;
      company_id?: string;
    }>({
      query: ({ id, ...data }) => ({
        url: `/hr-admins/my-employees/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Employee', id }, 'Employee'],
    }),

    // Update employee status only
    updateEmployeeStatus: builder.mutation<ApiResponse<Employee>, { id: string; status: string }>({
      query: ({ id, status }) => ({
        url: `/hr-admins/my-employees/${id}/status`,
        method: 'PATCH',
        data: { status },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Employee', id }, 'Employee'],
    }),

    // Delete employee
    deleteEmployee: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/hr-admins/my-employees/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Employee'],
    }),

    // Make employee leader - Fixed payload structure
    makeEmployeeLeader: builder.mutation<ApiResponse<Employee>, { ids: string[] }>({
      query: (data) => ({
        url: '/hr-admins/my-employees/make-leader',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Employee'],
    }),

    // Remove employee leader - Fixed payload structure
    removeEmployeeLeader: builder.mutation<ApiResponse<Employee>, { ids: string[] }>({
      query: (data) => ({
        url: '/hr-admins/my-employees/remove-leader',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Employee'],
    }),

    // Upload employees CSV - Fixed to handle FormData properly
    uploadEmployeesCsv: builder.mutation<ApiResponse<{ success: number; failed: number; errors: any[] }>, FormData>({
      query: (formData) => ({
        url: '/hr-admins/my-employees/upload',
        method: 'POST',
        data: formData,
        headers: {
          // Don't set Content-Type, let the browser set it with boundary for FormData
        },
      }),
      invalidatesTags: ['Employee'],
    }),

    // Send notification to employees via WebSocket
    sendNotification: builder.mutation<ApiResponse<void>, NotificationRequest>({
      query: (data) => ({
        url: '/socket/send-notification',
        method: 'POST',
        data: {
          title: data.title,
          message: data.message,
          type: data.type,
          target: data.sendToAll ? 'company' : 'user',
          userIds: data.sendToAll ? undefined : data.targetEmployees,
        },
      }),
      invalidatesTags: ['Notification'],
    }),

    // Get notification history
    getNotificationHistory: builder.query<ApiResponse<NotificationHistory[]>, { page?: number; limit?: number }>({
      query: (params = {}) => ({
        url: '/hr-admins/notifications/history',
        method: 'GET',
        params,
      }),
      providesTags: ['Notification'],
    }),

    // Get employee statistics
    getEmployeeStats: builder.query<EmployeeStatsResponse, void>({
      query: () => ({
        url: '/hr-admins/my-employees/stats',
        method: 'GET',
      }),
      providesTags: ['Employee'],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
const {
  useGetEmployeesQuery,
  useGetMyEmployeesQuery,
  useGetEmployeeQuery,
  useCreateEmployeeMutation,
  useUpdateEmployeeMutation,
  useUpdateEmployeeStatusMutation,
  useDeleteEmployeeMutation,
  useMakeEmployeeLeaderMutation,
  useRemoveEmployeeLeaderMutation,
  useUploadEmployeesCsvMutation,
  useSendNotificationMutation,
  useGetNotificationHistoryQuery,
  useGetEmployeeStatsQuery,
} = employeeApi;

// Export with aliases for component compatibility
export {
  useGetEmployeesQuery,
  useGetMyEmployeesQuery,
  useGetEmployeeQuery,
  useCreateEmployeeMutation,
  useUpdateEmployeeMutation,
  useUpdateEmployeeStatusMutation,
  useDeleteEmployeeMutation,
  useMakeEmployeeLeaderMutation,
  useRemoveEmployeeLeaderMutation,
  useUploadEmployeesCsvMutation,
  useSendNotificationMutation,
  useGetNotificationHistoryQuery,
  useGetEmployeeStatsQuery,
  // Aliases for component compatibility
  useGetEmployeesQuery as useGetAllEmployeesQuery,
  useGetEmployeeQuery as useGetEmployeeByIdQuery,
  useUpdateEmployeeStatusMutation as useChangeEmployeeStatusMutation,
};

// Add QR code endpoints to existing employee API
const employeeApiWithQR = employeeApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get QR code for current employee
    getMyQRCode: builder.query<EmployeeQRCodeDto, GenerateQRCodeDto | void>({
      query: (options) => ({
        url: '/employees/me/qr-code',
        method: 'GET',
        params: options || {}
      }),
      providesTags: ['Employee']
    }),

    // Get QR code for specific employee
    getEmployeeQRCode: builder.query<EmployeeQRCodeDto, { employeeId: string; options?: GenerateQRCodeDto }>({
      query: ({ employeeId, options }) => ({
        url: `/employees/${employeeId}/qr-code`,
        method: 'GET',
        params: options || {}
      }),
      providesTags: (result, error, { employeeId }) => [{ type: 'Employee', id: employeeId }]
    }),

    // Regenerate QR code for specific employee (admin only)
    regenerateEmployeeQRCode: builder.mutation<EmployeeQRCodeDto, { employeeId: string; options: GenerateQRCodeDto }>({
      query: ({ employeeId, options }) => ({
        url: `/employees/${employeeId}/qr-code/regenerate`,
        method: 'POST',
        body: options
      }),
      invalidatesTags: (result, error, { employeeId }) => [
        { type: 'Employee', id: employeeId },
        'Employee'
      ]
    }),

    // Verify QR code
    verifyQRCode: builder.mutation<QRCodeResponseDto, QRCodeVerificationDto>({
      query: (verificationData) => ({
        url: '/qr-codes/verify',
        method: 'POST',
        body: verificationData
      })
    })
  })
})

export const {
  useGetMyQRCodeQuery,
  useGetEmployeeQRCodeQuery,
  useRegenerateEmployeeQRCodeMutation,
  useVerifyQRCodeMutation
} = employeeApiWithQR
