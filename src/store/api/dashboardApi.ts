import { baseApi } from './baseApi';

// Dashboard API endpoints
export const dashboardApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // System Admin Dashboard
    getSystemAdminDashboard: builder.query<any, {
      date_from?: string;
      date_to?: string;
      status?: string;
      include_inactive?: boolean;
    }>({
      query: (params) => ({
        url: '/dashboard/system-admin',
        method: 'GET',
        params,
      }),
      providesTags: ['Dashboard'],
    }),

    // HR Admin Dashboard
    getHrAdminDashboard: builder.query<any, string>({
      query: (companyId) => ({
        url: `/dashboard/hr-admin/${companyId}`,
        method: 'GET',
      }),
      providesTags: ['Dashboard'],
    }),

    // Employee Dashboard
    getEmployeeDashboard: builder.query<any, {
      date_from?: string;
      date_to?: string;
    }>({
      query: (params) => ({
        url: '/dashboard/employee',
        method: 'GET',
        params,
      }),
      providesTags: ['Dashboard'],
    }),

    // Question Count for Company
    getQuestionCountForCompany: builder.query<any, string>({
      query: (companyId) => ({
        url: `/dashboard/hr-admin/${companyId}/question-count`,
        method: 'GET',
      }),
      providesTags: ['Dashboard'],
    }),

    // Dashboard Health Check
    getDashboardHealth: builder.query<any, void>({
      query: () => ({
        url: '/dashboard/health',
        method: 'GET',
      }),
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useGetSystemAdminDashboardQuery,
  useGetHrAdminDashboardQuery,
  useGetEmployeeDashboardQuery,
  useGetQuestionCountForCompanyQuery,
  useGetDashboardHealthQuery,
} = dashboardApi; 