// Re-export types
export type { ApiTag } from './baseApi';

// Main V2 API - Core business logic
export * from './authApi';
export * from './userApi';
export * from './employeeApi';
export * from './companyApi';
export * from './invitationApi';
export * from './consultantApi';
export * from './dashboardApi';
export * from './surveyApi';

// QA Service API - Quality Assurance management  
export * from './qaApi';

// Media Service API - Media management
export * from './mediaApi';

// Base APIs (for advanced usage)
export { baseApi, qaServiceApi, mediaServiceApi } from './baseApi';

// Employee API exports
export {
  useGetAllEmployeesQuery,
  useGetEmployeeByIdQuery,
  useCreateEmployeeMutation,
  useUpdateEmployeeMutation,
  useDeleteEmployeeMutation,
  useUploadEmployeesCsvMutation,
  useChangeEmployeeStatusMutation,
  useGetMyQRCodeQuery,
  useGetEmployeeQRCodeQuery,
  useRegenerateEmployeeQRCodeMutation,
  useVerifyQRCodeMutation
} from './employeeApi'; 