import { baseApi } from './baseApi';
import type { ApiResponse } from '../../types/api';

// Consultant types
export interface Consultant {
  id: string;
  email: string;
  name: string;
  phone?: string;
  bio?: string;
  major?: string;
  avatar?: string;
  specializations: string[];
  experience: number;
  rating: number;
  totalReviews: number;
  isVerified: boolean;
  isActive: boolean;
  availableHours: {
    start: string;
    end: string;
    timezone: string;
  };
  hourlyRate?: number;
  languages: string[];
  createdAt: string;
  updatedAt: string;
  companies?: string[];
}

export interface ConsultantCreateRequest {
  email: string;
  name: string;
  phone?: string;
  bio?: string;
  major: string;
  specializations: string[];
  experience: number;
  availableHours: {
    start: string;
    end: string;
    timezone: string;
  };
  hourlyRate?: number;
  languages: string[];
}

export interface ConsultantUpdateRequest extends Partial<ConsultantCreateRequest> {
  isVerified?: boolean;
  isActive?: boolean;
}

// Consultant API endpoints
export const consultantApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get consultants
    getConsultants: builder.query<ApiResponse<Consultant[]>, {
      page?: number;
      limit?: number;
      specialization?: string;
      isVerified?: boolean;
      isActive?: boolean;
      search?: string;
    }>({
      query: (params) => ({
        url: 'consultants',
        method: 'GET',
        params,
      }),
      providesTags: ['Consultant'],
    }),

    // Get single consultant
    getConsultant: builder.query<ApiResponse<Consultant>, string>({
      query: (id) => ({
        url: `consultants/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Consultant', id }],
    }),

    // Create consultant
    createConsultant: builder.mutation<ApiResponse<Consultant>, ConsultantCreateRequest>({
      query: (data) => ({
        url: 'consultants',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Update consultant
    updateConsultant: builder.mutation<ApiResponse<Consultant>, { 
      id: string; 
      data: ConsultantUpdateRequest;
    }>({
      query: ({ id, data }) => ({
        url: `consultants/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Delete consultant
    deleteConsultant: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `consultants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Verify consultant
    verifyConsultant: builder.mutation<ApiResponse<Consultant>, string>({
      query: (id) => ({
        url: `consultants/${id}/verify`,
        method: 'POST',
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Unverify consultant
    unverifyConsultant: builder.mutation<ApiResponse<Consultant>, string>({
      query: (id) => ({
        url: `consultants/${id}/unverify`,
        method: 'POST',
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Activate consultant
    activateConsultant: builder.mutation<ApiResponse<Consultant>, string>({
      query: (id) => ({
        url: `consultants/${id}/activate`,
        method: 'POST',
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Deactivate consultant
    deactivateConsultant: builder.mutation<ApiResponse<Consultant>, string>({
      query: (id) => ({
        url: `consultants/${id}/deactivate`,
        method: 'POST',
      }),
      invalidatesTags: ['Consultant'],
    }),

    // Get consultant companies
    getConsultantCompanies: builder.query<ApiResponse<any[]>, string>({
      query: (consultantId) => ({
        url: `consultants/${consultantId}/companies`,
        method: 'GET',
      }),
      providesTags: (result, error, consultantId) => [{ type: 'Consultant', id: consultantId }],
    }),

    // Assign consultant to company
    assignConsultantToCompany: builder.mutation<ApiResponse<void>, {
      consultantId: string;
      companyId: string;
    }>({
      query: ({ consultantId, companyId }) => ({
        url: `consultants/${consultantId}/companies/${companyId}`,
        method: 'POST',
      }),
      invalidatesTags: ['Consultant', 'Company'],
    }),

    // Remove consultant from company
    removeConsultantFromCompany: builder.mutation<ApiResponse<void>, {
      consultantId: string;
      companyId: string;
    }>({
      query: ({ consultantId, companyId }) => ({
        url: `consultants/${consultantId}/companies/${companyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Consultant', 'Company'],
    }),

    // Get consultant statistics
    getConsultantStats: builder.query<ApiResponse<{
      totalConsultants: number;
      verifiedConsultants: number;
      activeConsultants: number;
      averageRating: number;
    }>, void>({
      query: () => ({
        url: 'consultants/stats',
        method: 'GET',
      }),
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useGetConsultantsQuery,
  useGetConsultantQuery,
  useCreateConsultantMutation,
  useUpdateConsultantMutation,
  useDeleteConsultantMutation,
  useVerifyConsultantMutation,
  useUnverifyConsultantMutation,
  useActivateConsultantMutation,
  useDeactivateConsultantMutation,
  useGetConsultantCompaniesQuery,
  useAssignConsultantToCompanyMutation,
  useRemoveConsultantFromCompanyMutation: useRemoveConsultantFromCompanyByConsultantMutation,
  useGetConsultantStatsQuery,
} = consultantApi; 