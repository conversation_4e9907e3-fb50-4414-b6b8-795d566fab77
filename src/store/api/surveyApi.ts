import { baseApi } from './baseApi';

// Survey API endpoints
export const surveyApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get Survey Statistics
    getSurveyStatistics: builder.query<any, void>({
      query: () => ({
        url: '/surveys/statistics/overview',
        method: 'GET',
      }),
      providesTags: ['Survey'],
    }),

    // Get Surveys with pagination
    getSurveys: builder.query<any, {
      page?: number;
      limit?: number;
      parent_id?: string;
      survey_type?: string;
      search?: string;
    }>({
      query: (params) => ({
        url: '/surveys',
        method: 'GET',
        params,
      }),
      providesTags: ['Survey'],
    }),

    // Get Survey by ID
    getSurvey: builder.query<any, string>({
      query: (surveyId) => ({
        url: `/surveys/${surveyId}`,
        method: 'GET',
      }),
      providesTags: ['Survey'],
    }),

    // Create Survey
    createSurvey: builder.mutation<any, any>({
      query: (surveyData) => ({
        url: '/surveys',
        method: 'POST',
        body: surveyData,
      }),
      invalidatesTags: ['Survey'],
    }),

    // Update Survey
    updateSurvey: builder.mutation<any, { surveyId: string; data: any }>({
      query: ({ surveyId, data }) => ({
        url: `/surveys/${surveyId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Survey'],
    }),

    // Delete Survey
    deleteSurvey: builder.mutation<any, string>({
      query: (surveyId) => ({
        url: `/surveys/${surveyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Survey'],
    }),

    // Get Survey Analytics
    getSurveyAnalytics: builder.query<any, string>({
      query: (surveyId) => ({
        url: `/surveys/${surveyId}/analytics`,
        method: 'GET',
      }),
      providesTags: ['Survey'],
    }),

    // Get Active Surveys
    getActiveSurveys: builder.query<any, { parent_id?: string }>({
      query: (params) => ({
        url: '/surveys/active/list',
        method: 'GET',
        params,
      }),
      providesTags: ['Survey'],
    }),

    // Get Survey Types
    getSurveyTypes: builder.query<any, void>({
      query: () => ({
        url: '/surveys/types/list',
        method: 'GET',
      }),
    }),

    // Update Survey Status
    updateSurveyStatus: builder.mutation<any, { surveyId: string; is_active: boolean }>({
      query: ({ surveyId, is_active }) => ({
        url: `/surveys/${surveyId}/status`,
        method: 'PATCH',
        params: { is_active },
      }),
      invalidatesTags: ['Survey'],
    }),
  }),
  overrideExisting: false,
});

// Export hooks
export const {
  useGetSurveyStatisticsQuery,
  useGetSurveysQuery,
  useGetSurveyQuery,
  useCreateSurveyMutation,
  useUpdateSurveyMutation,
  useDeleteSurveyMutation,
  useGetSurveyAnalyticsQuery,
  useGetActiveSurveysQuery,
  useGetSurveyTypesQuery,
  useUpdateSurveyStatusMutation,
} = surveyApi; 