import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './baseQuery';
import { getApiUrl, getQaServiceUrl, getMediaServiceUrl } from '../../config/api';

// API Tags for cache invalidation
export type ApiTag = 
  | 'Auth' 
  | 'User' 
  | 'Employee' 
  | 'Company' 
  | 'CompanySettings'
  | 'Survey' 
  | 'Review' 
  | 'Recommendation' 
  | 'Invitation' 
  | 'Chat' 
  | 'Message' 
  | 'Certificate' 
  | 'Report'
  | 'Consultant'
  | 'Dashboard'
  | 'QA'
  | 'Media';

// Main V2 API - Core business logic (Environment-based URL)
export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: axiosBaseQuery({
    baseUrl: `${getApiUrl()}/api/v2/`,
  }),
  tagTypes: ['Auth', 'User', 'Employee', 'Company', 'CompanySettings', 'Survey', 'Review', 'Recommendation', 'Invitation', 'Chat', 'Message', 'Certificate', 'Report', 'Consultant', 'Dashboard'],
  endpoints: () => ({}),
});

// QA Service API - Quality Assurance management (Environment-based URL)
export const qaServiceApi = createApi({
  reducerPath: 'qaServiceApi',
  baseQuery: axiosBaseQuery({
    baseUrl: `${getQaServiceUrl()}/api/qa/v1/`,
  }),
  tagTypes: ['QA', 'Division', 'Question', 'Assessment', 'Target', 'Broadcast'],
  endpoints: () => ({}),
});

// Media Service API - Media management (Environment-based URL)
export const mediaServiceApi = createApi({
  reducerPath: 'mediaServiceApi', 
  baseQuery: axiosBaseQuery({
    baseUrl: `${getMediaServiceUrl()}/api/media/`,
  }),
  tagTypes: ['Media', 'Upload', 'Stream', 'Caption', 'Hub'],
  endpoints: () => ({}),
});

// Export hooks and utilities
export const {
  // We'll add specific hooks as we inject endpoints
  util: apiUtils,
  reducer: apiReducer,
  middleware: apiMiddleware,
} = baseApi; 