import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { authApi } from '../api/authApi'
import { setAuthToken, removeAuthToken, getAuthToken } from '../../lib/axios'
import type { User } from '../../types/api'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  isEnableSupport?: boolean
}

const initialState: AuthState = {
  user: null,
  token: getAuthToken(),
  isAuthenticated: !!getAuthToken(),
  isLoading: false,
  isEnableSupport: false,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginSuccess: (state, action: PayloadAction<{ user: User; token: string; isEnableSupport?: boolean }>) => {
      const { user, token, isEnableSupport } = action.payload
      state.user = user
      state.token = token
      state.isAuthenticated = true
      state.isLoading = false
      state.isEnableSupport = isEnableSupport
      
      // Store token in localStorage
      setAuthToken(token)
    },
    logout: (state) => {
      state.user = null
      state.token = null
      state.isAuthenticated = false
      state.isLoading = false
      state.isEnableSupport = false
      
      // Remove token from storage
      removeAuthToken()
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload }
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    clearError: () => {
      // Clear any error state if needed
    },
  },
  extraReducers: (builder) => {
    // Handle login
    builder
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.isLoading = true
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state, action: any) => {
        const { user, token, isEnableSupport } = action.payload
        state.user = user
        state.token = token
        state.isAuthenticated = true
        state.isLoading = false
        state.isEnableSupport = isEnableSupport
        setAuthToken(token)
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state) => {
        state.isLoading = false
        state.isAuthenticated = false
        state.user = null
        state.token = null
        removeAuthToken()
      })

    // Handle register
    builder
      .addMatcher(authApi.endpoints.register.matchPending, (state) => {
        state.isLoading = true
      })
      .addMatcher(authApi.endpoints.register.matchFulfilled, (state, action: any) => {
        const { user, token } = action.payload.data
        state.user = user
        state.token = token
        state.isAuthenticated = true
        state.isLoading = false
        setAuthToken(token)
      })
      .addMatcher(authApi.endpoints.register.matchRejected, (state) => {
        state.isLoading = false
      })

    // Handle getMe
    builder
      .addMatcher(authApi.endpoints.getMe.matchFulfilled, (state, action: any) => {
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addMatcher(authApi.endpoints.getMe.matchRejected, (state) => {
        state.user = null
        state.token = null
        state.isAuthenticated = false
        removeAuthToken()
      })

    // Handle refresh token
    builder
      .addMatcher(authApi.endpoints.refreshToken.matchFulfilled, (state, action: any) => {
        const { user, token } = action.payload.data
        state.user = user
        state.token = token
        state.isAuthenticated = true
        setAuthToken(token)
      })
      .addMatcher(authApi.endpoints.refreshToken.matchRejected, (state) => {
        state.user = null
        state.token = null
        state.isAuthenticated = false
        removeAuthToken()
      })

    // Handle logout
    builder.addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
      state.user = null
      state.token = null
      state.isAuthenticated = false
      state.isLoading = false
      state.isEnableSupport = false
      removeAuthToken()
    })

    // Handle profile updates
    builder.addMatcher(authApi.endpoints.updateProfile.matchFulfilled, (state, action: any) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload.data.user }
      }
    })

    // Handle avatar updates
    builder.addMatcher(authApi.endpoints.updateAvatar.matchFulfilled, (state, action: any) => {
      if (state.user) {
        state.user = { ...state.user, avatar: action.payload.data.avatar }
      }
    })
  },
})

export const { loginSuccess, logout, updateUser, setLoading, clearError } = authSlice.actions
export default authSlice.reducer 