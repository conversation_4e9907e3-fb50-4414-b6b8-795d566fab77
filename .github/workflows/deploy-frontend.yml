# GitHub Actions workflow for <PERSON><PERSON><PERSON> Frontend Deployment
# Build Vite app, deploy to S3, invalidate CloudFront cache

name: Deploy <PERSON><PERSON><PERSON> Frontend

on:
  push:
    branches: 
      - main
      - dev
  workflow_dispatch:

env:
  AWS_REGION: ${{ vars.AWS_DEFAULT_REGION }}
  S3_BUCKET: ${{ vars.S3_BUCKET }}
  CLOUDFRONT_DISTRIBUTION: ${{ vars.CLOUDFRONT_DISTRIBUTION }}

jobs:
  deploy:
    name: Build and Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'main' && 'main' || 'dev' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Build application for development
      if: github.ref_name == 'dev'
      run: |
        export VITE_ENV=development
        export VITE_API_URL=https://apis.dev.sehatti.app
        yarn run build

    - name: Build application for production
      if: github.ref_name == 'main'
      run: |
        export VITE_ENV=production
        export VITE_API_URL=https://apis.main.sehatti.app
        yarn run build

    - name: Configure AWS credentials for S3 (Account Level)
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Deploy to S3
      run: |
        echo "Deploying to S3 bucket: ${{ env.S3_BUCKET }}"
        aws s3 sync dist/ s3://${{ env.S3_BUCKET }}/ --delete
        echo "✅ Frontend deployed to S3 bucket: ${{ env.S3_BUCKET }}"

    - name: Configure AWS credentials for CloudFront (Root Account)
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.ROOT_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.ROOT_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1  # CloudFront requires us-east-1

    - name: Invalidate CloudFront Cache
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.ROOT_AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.ROOT_AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: us-east-1
      run: |
        echo "Invalidating CloudFront distribution: ${{ env.CLOUDFRONT_DISTRIBUTION }}"
        aws cloudfront create-invalidation \
          --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION }} \
          --paths "/*"
        echo "🌐 CloudFront cache invalidated for distribution: ${{ env.CLOUDFRONT_DISTRIBUTION }}"

    - name: Deployment Summary
      run: |
        echo "🎉 Frontend deployment completed successfully!"
        echo "📦 S3 Bucket: ${{ env.S3_BUCKET }}"
        echo "🌐 CloudFront Distribution: ${{ env.CLOUDFRONT_DISTRIBUTION }}"
        echo "🔄 Cache invalidated"
        echo "🌍 Environment: ${{ github.ref_name == 'main' && 'Production' || 'Development' }}" 