# API Architecture Migration Guide

## Problem Statement

The previous codebase had multiple conflicting API layers:
- `src/lib/axios.ts` - Raw axios instance
- `src/features/API/API.ts` - RTK Query with fetchBaseQuery  
- `src/store/api/authApi.ts` - RTK Query with axiosBaseQuery
- Multiple other API files with inconsistent patterns

This created:
- **Code Duplication**: Multiple base URL configurations, auth handling
- **Inconsistent Error Handling**: Different approaches across modules
- **Poor Type Safety**: Inconsistent typing patterns
- **Cache Invalidation Issues**: Multiple cache systems
- **Maintenance Nightmare**: Changes required in multiple places

## New Unified Architecture

### Core Principle: Single Source of Truth

```
baseApi (RTK Query) → injectEndpoints → Export Hooks
```

### File Structure

```
src/store/api/
├── baseApi.ts          # Core API configuration
├── baseQuery.ts        # Axios base query implementation
├── authApi.ts          # Auth endpoints
├── employeeApi.ts      # Employee endpoints
├── companyApi.ts       # Company endpoints (to be created)
├── surveyApi.ts        # Survey endpoints (to be created)
└── index.ts            # Centralized exports
```

### Benefits

1. **Single HTTP Client**: One axios instance with unified config
2. **Consistent Error Handling**: Centralized error processing
3. **Automatic Caching**: RTK Query handles cache invalidation
4. **Type Safety**: Full TypeScript support
5. **DevTools Integration**: Redux DevTools support
6. **Optimistic Updates**: Built-in support
7. **Background Refetching**: Automatic data freshness

## Implementation Pattern

### 1. Base API Configuration

```typescript
// src/store/api/baseApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './baseQuery';

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: axiosBaseQuery,
  tagTypes: ['User', 'Employee', 'Company', 'Survey'],
  endpoints: () => ({}),
});
```

### 2. Module-Specific APIs

```typescript
// src/store/api/authApi.ts
import { baseApi } from './baseApi';

export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        data: credentials,
      }),
      invalidatesTags: ['User'],
    }),
    // ... other endpoints
  }),
});

export const { useLoginMutation } = authApi;
```

### 3. Store Configuration

```typescript
// src/store/store.ts
import { baseApi } from './api/baseApi';

export const store = configureStore({
  reducer: {
    [baseApi.reducerPath]: baseApi.reducer,
    auth: authSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(baseApi.middleware),
});
```

### 4. Component Usage

```typescript
// In components
import { useLoginMutation, useGetMeQuery } from '@/store/api';

function LoginComponent() {
  const [login, { isLoading, error }] = useLoginMutation();
  const { data: user } = useGetMeQuery();
  
  // Clean, typed API calls
}
```

## Migration Steps

### Phase 1: Core Infrastructure ✅
- [x] Create baseApi with unified configuration
- [x] Implement axiosBaseQuery for consistent HTTP handling
- [x] Update store configuration
- [x] Create authApi and employeeApi modules

### Phase 2: API Modules (In Progress)
- [ ] Create companyApi module
- [ ] Create surveyApi module  
- [ ] Create consultantApi module
- [ ] Create chatApi module
- [ ] Create eventApi module

### Phase 3: Component Migration
- [ ] Update login/auth components
- [ ] Update employee management components
- [ ] Update dashboard components
- [ ] Remove legacy API imports

### Phase 4: Cleanup
- [ ] Remove old API files (API.ts, individual authApi.ts)
- [ ] Update all imports to use centralized exports
- [ ] Remove duplicate axios configurations

## Best Practices

### 1. Endpoint Organization
```typescript
// Group related endpoints in same module
// Use consistent naming: get*, create*, update*, delete*
// Implement proper cache invalidation
```

### 2. Type Safety
```typescript
// Always type requests and responses
// Use shared types from types/api.ts
// Leverage TypeScript inference
```

### 3. Error Handling
```typescript
// Let baseQuery handle HTTP errors
// Use RTK Query error patterns
// Implement user-friendly error messages
```

### 4. Cache Management
```typescript
// Use providesTags for data fetching
// Use invalidatesTags for mutations
// Implement optimistic updates where appropriate
```

## Legacy API Removal

### Files to Remove After Migration:
- `src/features/API/API.ts`
- `src/store/api/consultantApi.ts` (old version)
- `src/store/api/companyApi.ts` (old version)
- `src/services/api.ts`
- `src/services/mediaApi.ts`
- `src/services/articlesApi.ts`

### Import Updates:
```typescript
// OLD
import { useLoginMutation } from '../store/api/authApi';
import { useGetEmployeesQuery } from '../store/api/employeeApi';

// NEW
import { useLoginMutation, useGetEmployeesQuery } from '@/store/api';
```

## Conclusion

This unified architecture provides:
- **Maintainability**: Single place for API configuration
- **Consistency**: Standardized patterns across all modules
- **Performance**: Optimized caching and request handling
- **Developer Experience**: Better TypeScript support and DevTools
- **Scalability**: Easy to add new endpoints and modules

The migration ensures we follow Redux Toolkit Query best practices while maintaining the existing functionality and improving the overall codebase quality. 