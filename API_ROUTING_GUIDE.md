# Sehatti Frontend API Routing Guide

## 🏗️ **Backend Infrastructure Overview**

### **Application Load Balancer (ALB) Routing**
Your AWS ECS services are routed through a single ALB with path-based routing:

```
ALB: apis.dev.sehatti.app / apis.main.sehatti.app
├── Priority 100: QA Service     → ["/qa/*", "/api/qa/*"] 
├── Priority 200: Media Service  → ["/media/*", "/api/media/*"]
└── Priority 300: API Service    → ["/api/*", "/*"] (catch-all)
```

### **Service Endpoints**
- **Development**: `https://apis.dev.sehatti.app`
- **Production**: `https://apis.main.sehatti.app`

### **Health Check Endpoints**
- **API Service**: `/api/health` (Node.js/NestJS)
- **QA Service**: `/qa/health` (FastAPI)
- **Media Service**: `/media/health` (FastAPI)

## 🔧 **Frontend Configuration**

### **Environment Variables (GitHub Actions)**
Your frontend deployment uses these environment variables:

#### Development (`dev` branch):
```bash
VITE_ENV=development
VITE_API_URL=https://apis.dev.sehatti.app
```

#### Production (`main` branch):
```bash
VITE_ENV=production  
VITE_API_URL=https://apis.main.sehatti.app
```

### **Configuration Priority**
1. **VITE_API_URL** (from GitHub Actions) - Highest priority
2. **Environment detection** (hostname-based)
3. **Default fallback** (development)

## 📱 **Frontend API Clients**

### **Main Axios Client** (`src/lib/axios.ts`)
- **Base URL**: `${VITE_API_URL}/api/v1`
- **Routes to**: API Service (Node.js/NestJS)
- **Used for**: Authentication, user management, main business logic

```typescript
// Example usage:
import { apiClient } from '../lib/axios';
const response = await apiClient.get('/users'); // → https://apis.dev.sehatti.app/api/v1/users
```

### **Media Service Client** 
- **Base URL**: `${VITE_API_URL}/media`
- **Routes to**: Media Service (FastAPI)
- **Used for**: Video uploads, processing, captions

```typescript
// Example usage:
import { getMediaServiceUrl } from '../config/api';
const baseUrl = getMediaServiceUrl(); // → https://apis.dev.sehatti.app/media
```

### **QA Service Client**
- **Base URL**: `${VITE_API_URL}/api/qa/v1`
- **Routes to**: QA Service (FastAPI)  
- **Used for**: Questions, assessments, employee flows

```typescript
// Example usage:
import { getQaServiceUrl } from '../config/api';
const baseUrl = getQaServiceUrl() + '/api/qa/v1'; // → https://apis.dev.sehatti.app/api/qa/v1
```

## 🛣️ **Request Flow Examples**

### **API Service Request**
```
Frontend Request: apiClient.get('/auth/login')
↓
Full URL: https://apis.dev.sehatti.app/api/v1/auth/login
↓
ALB Routing: /api/* → Priority 300 → API Service
↓
API Service: Handles authentication
```

### **QA Service Request**  
```
Frontend Request: fetch(`${getQaServiceUrl()}/api/qa/v1/divisions`)
↓
Full URL: https://apis.dev.sehatti.app/api/qa/v1/divisions
↓
ALB Routing: /api/qa/* → Priority 100 → QA Service  
↓
QA Service: Returns divisions data
```

### **Media Service Request**
```
Frontend Request: fetch(`${getMediaServiceUrl()}/upload`)
↓
Full URL: https://apis.dev.sehatti.app/media/upload
↓
ALB Routing: /media/* → Priority 200 → Media Service
↓
Media Service: Handles file upload
```

## 🔍 **Testing & Debugging**

### **Quick Environment Test**
Add this to your browser console:

```javascript
// Test current configuration
import { testEnvironmentDetection } from './src/utils/apiTest';
testEnvironmentDetection();
```

### **API Connectivity Test**
```javascript
// Test all service health endpoints
import { testApiConnectivity } from './src/utils/apiTest';
await testApiConnectivity();
```

### **Expected Results**
- **Environment**: Should show `development` or `production`
- **API URL**: Should show correct environment URL from VITE_API_URL
- **Health Checks**: All services should return 200 status

## ✅ **Configuration Fixes Applied**

### **Fixed Issues**:
1. ✅ **Removed localhost support** - No longer using local development
2. ✅ **Consolidated configuration** - Single source of truth in `src/config/api.ts`
3. ✅ **Fixed service URLs** - Removed redundant `/api` prefixes for media/qa services
4. ✅ **Environment variable priority** - VITE_API_URL takes precedence
5. ✅ **Updated GitHub Actions** - Node.js 20 compatibility for react-router-dom@7.6.2
6. ✅ **Unified imports** - All services use centralized configuration

### **Files Modified**:
- `src/config/api.ts` - Consolidated and cleaned up
- `src/config/environment.ts` - **DELETED** (duplicate)
- `src/services/*.ts` - Updated imports
- `src/features/API/*.ts` - Updated imports
- `.github/workflows/deploy-frontend.yml` - Node.js 20 update

## 🚀 **Deployment Workflow**

### **GitHub Actions Process**:
1. **Environment Detection**: Based on branch (`dev` → development, `main` → production)
2. **Environment Variables**: Set VITE_ENV and VITE_API_URL
3. **Build**: Frontend built with correct API endpoints
4. **Deploy**: To S3 bucket with CloudFront invalidation

### **Frontend Startup**:
1. **Environment Detection**: Check VITE_ENV variable
2. **API Configuration**: Use VITE_API_URL or fallback to hostname detection
3. **Service Initialization**: All API clients use centralized configuration
4. **Routing**: Requests routed through ALB to appropriate backend services

## 📋 **Troubleshooting**

### **Common Issues**:
- **CORS Errors**: Check ALB and backend CORS configuration
- **404 Errors**: Verify ALB path patterns match your API routes
- **Auth Issues**: Ensure token interceptor is working with correct endpoints

### **Debug Steps**:
1. Check browser console for environment detection logs
2. Verify VITE_API_URL is set correctly
3. Test health endpoints manually
4. Check network tab for actual request URLs

Your frontend should now correctly route all API requests through your ALB to the appropriate backend services! 🎉 