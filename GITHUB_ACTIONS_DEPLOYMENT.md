# Sehatti Modern Frontend GitHub Actions Deployment

## Overview

This document describes the GitHub Actions workflow for deploying the Sehatti modern frontend to AWS S3 and invalidating CloudFront cache.

## Architecture

The deployment follows a multi-account setup:

- **S3 Deployment**: Uses account-level credentials (dev/prod accounts)
- **CloudFront Invalidation**: Uses root account credentials (where CloudFront distribution is hosted)

## Workflow File

Location: `.github/workflows/deploy-frontend.yml`

## Environment Variables

The following variables are automatically configured via Terraform for the `sehatti-frontend` repository:

### Repository Variables
- `AWS_DEFAULT_REGION`: AWS region (me-central-1)
- `S3_BUCKET`: Target S3 bucket name
- `CLOUDFRONT_DISTRIBUTION`: CloudFront distribution ID

### Environment Secrets
- `AWS_ACCESS_KEY_ID`: Account-level credentials for S3 deployment
- `AWS_SECRET_ACCESS_KEY`: Account-level credentials for S3 deployment
- `ROOT_AWS_ACCESS_KEY_ID`: Root account credentials for CloudFront invalidation
- `ROOT_AWS_SECRET_ACCESS_KEY`: Root account credentials for CloudFront invalidation

## Deployment Process

1. **Trigger**: Automatically runs on push to `dev` or `main` branches
2. **Build**: 
   - Development: Uses base API URL `https://apis.dev.sehatti.app` (frontend handles service path mapping)
   - Production: Uses base API URL `https://apis.main.sehatti.app` (frontend handles service path mapping)
3. **Deploy**: Syncs built files to S3 bucket
4. **Invalidate**: Clears CloudFront cache for immediate updates

## Environment-Specific Configuration

### Development (dev branch)
- Base API URL: `https://apis.dev.sehatti.app`
- S3 Bucket: `sehatti-dev-frontend`
- Environment: `development`

### Production (main branch)
- Base API URL: `https://apis.main.sehatti.app`
- S3 Bucket: `sehatti-main-frontend` (when main workspace is used)
- Environment: `production`

### Service Endpoints (handled by frontend)
All services are accessible through the base API URL:
- Main API: `/api/health`, `/api/v1/...`
- QA Service: `/api/qa/v1/...`
- Media Service: `/api/media/v1/...`
- WebSocket: `/ws`

## Manual Deployment

The workflow can also be triggered manually via GitHub Actions UI using the `workflow_dispatch` trigger.

## Project Structure

This workflow deploys the modern frontend located in the `sehatti-modern-frontend` directory to the infrastructure configured for the `sehatti-frontend` repository.

- **Source Code**: `sehatti-app/sehatti-modern-frontend/`
- **Repository Configuration**: Managed as `sehatti-frontend` in Terraform
- **Deployment Target**: S3 bucket configured for frontend hosting

## Terraform Configuration

The deployment variables are automatically managed by Terraform in:
- File: `sehatti-app/sehatti-app-infra/github.tf`
- Module: `github_repo_configuration["sehatti-frontend"]`

## Security

- All AWS credentials are stored as GitHub environment secrets
- Account-level credentials are used for S3 operations
- Root account credentials are only used for CloudFront invalidation
- Credentials are automatically rotated via Terraform when needed

## Build Configuration

The workflow uses npm for package management and Vite for building:
- Node.js version: 18
- Package manager: npm
- Build command: `npm run build`
- Output directory: `dist/` 