// Clear authentication tokens and state
console.log('Clearing authentication tokens...');

// Clear localStorage
if (typeof localStorage !== 'undefined') {
  localStorage.removeItem('hr-auth-token');
  localStorage.removeItem('auth-token');
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('authState');
  console.log('LocalStorage cleared');
}

// Clear sessionStorage
if (typeof sessionStorage !== 'undefined') {
  sessionStorage.removeItem('hr-auth-token');
  sessionStorage.removeItem('auth-token');
  sessionStorage.removeItem('token');
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('authState');
  console.log('SessionStorage cleared');
}

console.log('Authentication cleared. Please refresh the page and try logging in again.'); 