# Sehatti Modern Frontend

A modern, responsive frontend application for the Sehatti platform built with React, TypeScript, Vite, Tailwind CSS, and Shadcn UI components.

## Features

- **Modern UI Components**: Pre-styled components with gold-themed design
- **Responsive Design**: Mobile-first approach for all screen sizes
- **Dark Mode Support**: Automatic and manual theme switching
- **TypeScript**: Type-safe development experience
- **Performance Optimized**: Built with Vite for fast development and production builds

## UI Component Library

The project includes a comprehensive set of pre-styled UI components:

- **Badge**: Multi-variant system (default/gold, secondary, outline, subtle)
- **Table**: Complete table system with header, body, footer, row, cell components
- **Avatar**: Size variants, border options, fallback display
- **Checkbox**: Styled checkboxes with label, description, and error states
- **Tabs**: Four variant styles (default, pill, underlined, card)
- **Alert**: Multiple variants with icon support, titles, and descriptions
- **Card**: Extensive styling options (default, gold, glass, goldGlass)
- **Modal**: Customizable dialog system with backdrop
- **Spinner**: Loading indicators with size, speed, and color variants
- **Select**: Form select with label, helper text, and validation states
- **Tooltip**: Position-aware tooltips with multiple style options
- **Divider**: Horizontal/vertical separators with label support
- **Container**: Layout container with responsive sizing options
- **GlassCard**: Specialized card with glass morphism effects
- **GradientText**: Text with gradient color effects

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd sehatti-modern-frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Build

```bash
# Build for production
npm run build

# Preview the production build
npm run preview
```

## Project Structure

```
sehatti-modern-frontend/
├── public/              # Static assets
├── src/
│   ├── components/      # Reusable UI components
│   │   └── ui/          # Base UI components
│   ├── features/        # Feature-based modules
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility functions
│   ├── pages/           # Page components
│   ├── store/           # Redux store setup
│   └── types/           # TypeScript type definitions
├── index.html           # HTML entry point
└── package.json         # Project dependencies
```

## Design Principles

- **Gold-themed styling**: Using Sehatti's color palette (#D2B37A, #C19648)
- **Glass morphism effects**: With backdrop blur for modern UI
- **Dark mode support**: Consistent experience in both light and dark modes
- **Consistent spacing and typography**: Following design system guidelines
- **Accessibility features**: Ensuring usable interfaces for all users
- **TypeScript typing**: Using React's forwardRef pattern for component types
- **Variant system**: Using class-variance-authority (cva) for component variants

## License

This project is proprietary and confidential. Unauthorized copying, transferring, or reproduction of the contents of this project, via any medium, is strictly prohibited.

© Sehatti. An Elevate Academy ecosystem.

## Company Management API Integration

### Overview
The frontend is properly connected to the backend V2 APIs for complete company management functionality.

### Key Features

#### 1. Companies Management (`/src/pages/CorporatesManagement.tsx`)
- **List Companies**: View all HR_ADMIN users (companies) with pagination and search
- **Create Company**: Register new HR admin + auto-create company + send invitation email
- **Update Status**: Change company status (PENDING → ACTIVE triggers welcome email)
- **Delete Company**: Soft delete companies
- **View Details**: Display complete company information

#### 2. API Endpoints Connected

**System Admin Endpoints:**
- `GET /api/v2/system-admins/companies` - List all companies with pagination
- `GET /api/v2/system-admins/companies/{id}` - Get specific company details  
- `PATCH /api/v2/system-admins/companies/{id}/status` - Update company status
- `DELETE /api/v2/system-admins/companies/{id}` - Delete company
- `GET /api/v2/system-admins/companies/{id}/employees` - Get company employees

**HR Admin Registration:**
- `POST /api/v2/hr-admins/register` - Creates company + HR admin + sends email

**Company Settings (Legacy):**
- `GET /api/v2/company-settings` - List all company settings
- `POST /api/v2/company-settings` - Create company settings
- `PUT /api/v2/company-settings/company/{id}` - Update company settings

#### 3. Complete Workflow

1. **System Admin** creates new company via "Add Corporate" button
2. **Backend** creates:
   - New HR_ADMIN user with PENDING status
   - Company settings record
   - Links HR admin to company
   - Sends password setup email to HR admin
3. **System Admin** can update status from PENDING → ACTIVE
4. **Backend** sends welcome email to HR admin upon activation
5. **HR Admin** receives email, sets password, and can login

#### 4. Frontend Components Updated

- **CorporatesManagement.tsx**: Main company management interface
- **store/api/companyApi.ts**: All API endpoints properly connected
- **store/api/authApi.ts**: HR admin registration endpoint
- **types/api.ts**: Proper TypeScript interfaces

#### 5. Data Flow

```
Frontend ↔ Backend V2 APIs ↔ DynamoDB
    ↕           ↕              ↕
Company UI → System Admin → HR_ADMIN Users
Settings UI → Company API → Company Settings
```

### Usage

1. **Login as System Admin**
2. **Navigate to Corporate Management**
3. **View all companies** with real-time status
4. **Create new companies** with automatic email invitations
5. **Manage company status** with email notifications
6. **Access company settings** for detailed configuration

### Status Management

- **PENDING**: Newly created, HR admin hasn't logged in yet
- **ACTIVE**: HR admin has access, company is operational  
- **INACTIVE**: Temporarily disabled
- **BLOCKED**: Permanently restricted

The system automatically handles email notifications for status changes and provides a complete audit trail.
