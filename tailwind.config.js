/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Primary gold colors
        "sehatti-gold": {
          50: '#FFF9E6',
          100: '#FFF0C2',
          200: '#FFE499',
          300: '#FFD870',
          400: '#F8C648',
          500: '#D2B37A', // Main gold
          600: '#C19648', // Darker gold
          700: '#A37A2D',
          800: '#7D5E22',
          900: '#5A4319',
          950: '#3A2C10',
        },
        // Warm gray palette
        "sehatti-warm-gray": {
          50: '#FAF9F7',
          100: '#F5F3EF',
          200: '#E8E4DC',
          300: '#D5CEC3',
          400: '#B8AE9E',
          500: '#9C9080',
          600: '#7D7265',
          700: '#645A4F',
          800: '#4A433B',
          900: '#332E29',
          950: '#1F1C18',
        },
        // Semantic colors
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        'gold-sm': '0 1px 2px 0 rgba(210, 179, 122, 0.05)',
        'gold': '0 1px 3px 0 rgba(210, 179, 122, 0.1), 0 1px 2px 0 rgba(210, 179, 122, 0.06)',
        'gold-md': '0 4px 6px -1px rgba(210, 179, 122, 0.1), 0 2px 4px -1px rgba(210, 179, 122, 0.06)',
        'gold-lg': '0 10px 15px -3px rgba(210, 179, 122, 0.1), 0 4px 6px -2px rgba(210, 179, 122, 0.05)',
        'gold-xl': '0 20px 25px -5px rgba(210, 179, 122, 0.1), 0 10px 10px -5px rgba(210, 179, 122, 0.04)',
        'gold-2xl': '0 25px 50px -12px rgba(210, 179, 122, 0.25)',
        'gold-glow': '0 0 15px rgba(210, 179, 122, 0.3)',
        'gold-inner': 'inset 0 2px 4px 0 rgba(210, 179, 122, 0.06)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "fade-in": {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
        "fade-out": {
          from: { opacity: 1 },
          to: { opacity: 0 },
        },
        "slide-in-right": {
          from: { transform: "translateX(100%)" },
          to: { transform: "translateX(0)" },
        },
        "slide-out-right": {
          from: { transform: "translateX(0)" },
          to: { transform: "translateX(100%)" },
        },
        "slide-in-bottom": {
          from: { transform: "translateY(100%)" },
          to: { transform: "translateY(0)" },
        },
        "slide-out-bottom": {
          from: { transform: "translateY(0)" },
          to: { transform: "translateY(100%)" },
        },
        "shimmer": {
          from: { backgroundPosition: "200% 0" },
          to: { backgroundPosition: "-200% 0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.2s ease-out",
        "fade-out": "fade-out 0.2s ease-out",
        "slide-in-right": "slide-in-right 0.3s ease-out",
        "slide-out-right": "slide-out-right 0.3s ease-out",
        "slide-in-bottom": "slide-in-bottom 0.3s ease-out",
        "slide-out-bottom": "slide-out-bottom 0.3s ease-out",
        "shimmer": "shimmer 2s infinite linear",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} 