#!/bin/bash

# Se<PERSON>ti Frontend Build Script
# This script builds the frontend application with proper environment configuration

set -e

echo "🚀 Starting Sehatti Frontend Build Process..."

# Parse command line arguments
ENVIRONMENT=${1:-development}
echo "📦 Building for environment: $ENVIRONMENT"

# Set environment variables based on the target environment
case $ENVIRONMENT in
  development)
    export VITE_ENV=development
    export VITE_API_URL=https://apis.dev.sehatti.app
    ;;
  production)
    export VITE_ENV=production
    export VITE_API_URL=https://apis.main.sehatti.app
    ;;
  *)
    echo "❌ Invalid environment: $ENVIRONMENT"
    echo "Usage: ./build.sh [development|production]"
    exit 1
    ;;
esac

echo "🔧 Environment variables set:"
echo "  VITE_ENV: $VITE_ENV"
echo "  VITE_API_URL: $VITE_API_URL"

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "📦 Installing dependencies..."
  yarn install --frozen-lockfile
fi

# Run the build
echo "🔨 Building application..."
yarn run build

# Check if build was successful
if [ $? -eq 0 ]; then
  echo "✅ Build completed successfully!"
  echo "📁 Build output is available in: ./dist"
  
  # Show build summary
  echo ""
  echo "📊 Build Summary:"
  du -sh dist/
  echo "Files created: $(find dist -type f | wc -l)"
  
else
  echo "❌ Build failed!"
  exit 1
fi

echo "🎉 Frontend build process completed!" 