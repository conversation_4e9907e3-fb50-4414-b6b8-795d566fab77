// Debug authentication state
console.log('=== Authentication Debug ===');
console.log('Token:', localStorage.getItem('hr-auth-token'));
console.log('Refresh Token:', localStorage.getItem('hr-refresh-token'));

// Try to decode JWT if exists
const token = localStorage.getItem('hr-auth-token');
if (token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log('JWT Payload:', payload);
    console.log('Token Expires:', new Date(payload.exp * 1000));
    console.log('Token Expired?', payload.exp < Date.now() / 1000);
  } catch (e) {
    console.log('Invalid JWT format:', e.message);
  }
}
